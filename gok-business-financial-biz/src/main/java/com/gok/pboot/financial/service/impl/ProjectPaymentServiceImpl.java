package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.upms.dto.DeptCacheDto;
import com.gok.bcp.upms.dto.DeptDetailDto;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteOutService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.base.ApiResult;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.*;
import com.gok.pboot.financial.config.MergeCellStrategyHandler;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.EducationPayment;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.db.entity.ProjectPaymentClaim;
import com.gok.pboot.financial.db.mapper.ProjectPaymentClaimMapper;
import com.gok.pboot.financial.db.mapper.ProjectPaymentMapper;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.service.IEducationPaymentService;
import com.gok.pboot.financial.service.IProjectPaymentService;
import com.gok.pboot.financial.util.*;
import com.gok.pboot.financial.vo.*;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 项目回款跟踪 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectPaymentServiceImpl extends ServiceImpl<ProjectPaymentMapper, ProjectPayment>
        implements IProjectPaymentService {

    private final ProjectPaymentClaimMapper projectPaymentClaimMapper;

    private final IEducationPaymentService educationPaymentService;

    private final StringRedisTemplate stringRedisTemplate;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final RemoteOutService remoteOutService;

    private final RemoteMailService remoteMailService;

    private final IAccountingSetService accountingSetService;

    private final LogRecordUtils logRecordUtils;

    private final OpenApi openApi;

    private final DBApi dbApi;

    private final RemoteRoleService remoteRoleService;

    private final RemoteDeptService remoteDeptService;

    private static final String CONTENT_TYPE_SHEET = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String PARAM_CONTENT_DISPOSITION = "Content-disposition";
    private static final String CONTENT_DISPOSITION = "attachment;filename*=utf-8''";
    private static final String XLSX_SUFFIX = ".xlsx";

    /**
     * 模糊查询带分页
     *
     * @param page              {@link Page}
     * @param dto {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPaymentVO}>
     */
    @Override
    public Page<ProjectPaymentVO> findPageV1(Page<ProjectPayment> page, ProjectPaymentDTO dto) {
        Page<ProjectPaymentVO> projectPaymentVoPage = new Page<>(page.getCurrent(), page.getSize());
        // 1、条件封装 单据状态
        questionStatus(dto);
        // 2、【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<ProjectPaymentVO> projectPaymentVOList = new ArrayList<>();
        List<ProjectPayment> projectPaymentListByAuth = baseMapper.listByAuth(condition(dto));
        if (CollUtil.isNotEmpty(projectPaymentListByAuth)) {
            projectPaymentVOList.addAll(this.addVoToList(projectPaymentListByAuth));
        }
        // 3、分页查询
        List<ProjectPayment> projectPaymentList = baseMapper.findPage(condition(dto));
        if (CollUtil.isNotEmpty(projectPaymentList)) {
            projectPaymentVOList.addAll(this.addVoToList(projectPaymentList));
        }
        // 4、设置数量总数
        projectPaymentVOList = projectPaymentVOList
                .stream()
                .distinct()
                .sorted(Comparator.comparing(ProjectPaymentVO::getDocumentNumber, Comparator.nullsLast(String::compareTo)).reversed())
                .collect(Collectors.toList());
        projectPaymentVoPage.setTotal(projectPaymentVOList.size());

        // 5、设置数据内容
        projectPaymentVOList = projectPaymentVOList
                .stream()
                .skip((dto.getCurrent() - NumberUtils.INTEGER_ONE) * dto.getSize())
                .limit(dto.getSize())
                .collect(Collectors.toList());
        projectPaymentVoPage.setRecords(projectPaymentVOList);

        return projectPaymentVoPage;
    }

    /**
     * 锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link  Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean lock(ProjectPaymentDTO projectPaymentDTO) {
        // 业务一体化
        //if (UserUtils.getUser() != null) {
        //    projectPaymentDTO.setUserId(UserUtils.getUser().getId());
        //    if (Boolean.FALSE.equals(iRemotePaymentService.lock(projectPaymentDTO).getData())) {
        //        throw new BusinessException("业务一体化锁定/取消锁定操作失败");
        //    }
        //}
        List<ProjectPayment> projectPaymentList = new ArrayList<>();
        // 无需查询锁定状态
        Integer lockStatus = projectPaymentDTO.getLockStatus();
        projectPaymentDTO.setLockStatus(null);
        // 单据状态
        questionStatus(projectPaymentDTO);
        List<ProjectPayment> projectPaymentPage = baseMapper.findPage(projectPaymentDTO);
        if (CollUtil.isNotEmpty(projectPaymentPage)) {
            StringBuilder sb = new StringBuilder();
            projectPaymentPage.forEach(p -> {
                // 更改锁定状态
                ProjectPayment projectPayment = new ProjectPayment();
                BeanUtil.copyProperties(p, projectPayment, CopyOptions.create().setIgnoreNullValue(true));
                projectPayment.setLockStatus(lockStatus);
                // 如果原先是待锁定并且当前日期超过收款日期30天 则后续不再进行锁定
                if (LockStatusEnum.NO.getValue().equals(p.getLockStatus())
                        && DateUtils.difference(p.getPaymentDate()) > DateUtils.THIRTY_DAYS) {
                    projectPayment.setAutoLock(AutoLockEnum.NO.getValue());
                }
                projectPaymentList.add(projectPayment);
                sb.append("【").append(p.getDocumentNumber()).append("】");
            });
            // 记录日志
            if (lockStatus.equals(LockStatusEnum.YES.getValue())) {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.LOCK_PAYMENT, OperationEnum.LOCK_PAYMENT.getName() + sb.toString());
            } else {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.UNLOCK_PAYMENT, OperationEnum.UNLOCK_PAYMENT.getName() + sb.toString());
            }

            return this.updateBatchById(projectPaymentList);
        }
        return Boolean.TRUE;
    }

    /**
     * 导出Excel选中数据
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    public List exportV1(ProjectPaymentDTO projectPaymentDTO) {
        List<ProjectPaymentVO> projectPaymentVOList;
        // 导出模板表
        if (CollUtil.isNotEmpty(projectPaymentDTO.getIds())) {
            List<ProjectPaymentExcelVO> projectPaymentExcelVOList = new ArrayList<>();
            if (NumberUtils.LONG_MINUS_ONE.equals(projectPaymentDTO.getIds().get(NumberUtils.INTEGER_ZERO))) {
                ProjectPaymentExcelVO projectPaymentExcelVO = ProjectPaymentExcelVO.builder()
                        .paymentDate(new SimpleDateFormat(DateUtils.SIMPLE_DATE_FORMAT_2).format(new Date()))
                        .build();
                projectPaymentExcelVOList.add(projectPaymentExcelVO);
                return projectPaymentExcelVOList;
            }
        }
        // 条件查询 单据状态
        questionStatus(projectPaymentDTO);
        //【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<ProjectPayment> listByNoCustomerName = baseMapper.listByAuth(condition(projectPaymentDTO));
        List<ProjectPayment> projectPaymentList = baseMapper.findPage(condition(projectPaymentDTO));
        // 记录日志
        List<Long> resultList = new ArrayList<>();
        resultList.addAll(listByNoCustomerName.stream().map(ProjectPayment::getId).collect(Collectors.toList()));
        resultList.addAll(projectPaymentList.stream().map(ProjectPayment::getId).collect(Collectors.toList()));
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                OperationEnum.EXPORT,
                OperationEnum.EXPORT.getName() + "【" + resultList.stream().distinct().count() + "条】");
        // 封装展示统一返回
        projectPaymentVOList = this.addVoToList(projectPaymentList);
        projectPaymentVOList.addAll(this.addVoToList(listByNoCustomerName));
        return projectPaymentVOList
                .stream()
                .distinct()
                .sorted(Comparator.comparing(ProjectPaymentVO::getDocumentNumber, Comparator.nullsLast(String::compareTo)).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@code int}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public int save(PaymentDTO paymentDTO) {
        // 生成分布式id
        Long id = IdUtil.getSnowflake().nextId();
        paymentDTO.setId(id);
        paymentDTO.setUserId(UserUtils.getUser().getId());
        paymentDTO.setUsername(UserUtils.getUser().getName());
        // 基础信息赋值
        ProjectPayment projectPayment = BeanUtil.copyProperties(paymentDTO, ProjectPayment.class);
        projectPayment.setId(id);
        projectPayment.setCreatorId(UserUtils.getUser().getId());
        projectPayment.setCreatorName(UserUtils.getUser().getName());
        // 单据编号
        String number = RedisConstant.PROJECT_DOCUMENT_NUMBER_PREFIX +
                new SimpleDateFormat(DateUtils.YEAR_MONTH).format(new Date()) +
                String.format("%04d", stringRedisTemplate.opsForValue().increment(RedisConstant.PROJECT_DOCUMENT_NUMBER));
        projectPayment.setDocumentNumber(number);
        paymentDTO.setDocumentNumber(number);
        // 业务一体化
        //if (Boolean.FALSE.equals(iRemotePaymentService.save(paymentDTO).getData())) {
        //    throw new BusinessException("业务一体化新增数据异常");
        //}
        // 认领/锁定/推送状态
        projectPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());
        projectPayment.setLockStatus(LockStatusEnum.NO.getValue());
        projectPayment.setPushStatus(PushStatusEnum.WAIT_PUSH.getValue());
        // 认领日期自动锁定
        if (DateUtils.difference(paymentDTO.getPaymentDate()) > DateUtils.THIRTY_DAYS) {
            projectPayment.setLockStatus(LockStatusEnum.YES.getValue());
            projectPayment.setAutoLock(AutoLockEnum.YES.getValue());
        }
        // 记录日志 与 消息推送
        // this.pushMessage(paymentDTO.getCustomerName(), projectPayment, paymentDTO.getPaymentDate(), number);
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                OperationEnum.ADD_PAYMENT, OperationEnum.ADD_PAYMENT.getName() + "【" + number + "】");

        return baseMapper.insert(projectPayment);
    }

    /**
     * 根据id获取详情
     *
     * @param id 项目id
     * @return {@link ProjectPaymentClaimVO}
     */
    @Override
    public ProjectPaymentClaimVO getOne(Long id) {
        ProjectPaymentClaimVO projectPaymentClaimVO = new ProjectPaymentClaimVO();
        // 获取项目回款跟踪
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getId, id)
        );
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 获得项目回款认领详情
            ProjectPaymentClaim projectPaymentClaim = projectPaymentClaimMapper.selectOne(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                    .eq(ProjectPaymentClaim::getProjectPaymentId, id)
            );
            // 封装详情信息与枚举
            projectPaymentClaimVO = BeanUtil.copyProperties(projectPayment, ProjectPaymentClaimVO.class);
            projectPaymentClaimVO.setPaymentAmount(MoneyUtils.getInstance().transType(projectPayment.getPaymentAmount()));
            projectPaymentClaimVO.setBudgetCollectionAmount(MoneyUtils.getInstance().transType(projectPayment.getBudgetCollectionAmount()));
            projectPaymentClaimVO.setPaymentCompanyTxt(attributableSubjectMap.getOrDefault(projectPayment.getPaymentCompany(), StrUtil.EMPTY));
            BeanUtil.copyProperties(projectPaymentClaim, projectPaymentClaimVO, "id");
            projectPaymentClaimVO.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, projectPaymentClaim.getPaymentType()));
            projectPaymentClaimVO.setMarginTypeText(EnumUtils.getNameByValue(MarginTypeEnum.class, projectPaymentClaim.getMarginType()));

        }

        return projectPaymentClaimVO;
    }

    /**
     * 根据id更新数据
     *
     * @param projectPaymentInfoDTO {@link ProjectPaymentClaimDTO}
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean update(ProjectPaymentInfoDTO projectPaymentInfoDTO) {
        // 1、获取项目回款跟踪
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getId, projectPaymentInfoDTO.getId())
        );
        CopyOptions projectPaymentOption = CopyOptions.create()
                .setIgnoreNullValue(true)
                .setIgnoreError(true);
        BeanUtil.copyProperties(projectPaymentInfoDTO, projectPayment, projectPaymentOption);
        projectPayment.setCustomerId(projectPaymentInfoDTO.getCustomerId());
        projectPayment.setCustomerName(projectPaymentInfoDTO.getCustomerName());
        projectPayment.setEnterpriseName(projectPaymentInfoDTO.getEnterpriseName());
        projectPayment.setPaymentNote(projectPaymentInfoDTO.getPaymentNote());
        // 2、认领日期自动锁定
        if (DateUtils.difference(projectPaymentInfoDTO.getPaymentDate()) > DateUtils.THIRTY_DAYS
                && projectPayment.getClaimStatus().equals(ClaimStatusEnum.NO.getValue())
                && projectPayment.getAutoLock().equals(AutoLockEnum.YES.getValue())) {
            projectPayment.setLockStatus(LockStatusEnum.YES.getValue());
        }
        // 3、客户台帐对应备案人
        String customerName = projectPaymentInfoDTO.getCustomerName();
        if (CharSequenceUtil.isNotBlank(customerName)) {
            Map<String, CustomerAccountVO> customerAccountMap = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY)
                    .stream()
                    .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));
            CustomerAccountVO customerAccountVO = customerAccountMap.get(customerName);
            if (Optional.ofNullable(customerAccountVO).isPresent()) {
                projectPayment.setRecordManId(customerAccountVO.getRecordManId());
            }
        }
        //
        if (UserUtils.getUser() != null) {
            // 4、记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.EDIT_PAYMENT,
                    OperationEnum.EDIT_PAYMENT.getName() + "【" + projectPayment.getDocumentNumber() + "】");
            // 5、业务一体化
            //if (Boolean.FALSE.equals(iRemotePaymentService.update(projectPaymentClaimDTO).getData())) {
            //    throw new BusinessException("业务一体化更新编辑操作异常");
            //}
        }

        return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO;
    }

    /**
     * 删除
     *
     * @param ids {@link List}
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> delete(List<Long> ids) {
        // 非推送成功状况才可删除
        List<ProjectPayment> projectPaymentList = baseMapper
                .selectList(Wrappers.<ProjectPayment>lambdaQuery()
                        .in(CollUtil.isNotEmpty(ids), ProjectPayment::getId, ids)
                )
                .stream()
                .filter(p -> PushStatusEnum.WAIT_PUSH.getValue().equals(p.getPushStatus())
                        || PushStatusEnum.FAIL_PUSH.getValue().equals(p.getPushStatus()))
                .collect(Collectors.toList());
        if (projectPaymentList.isEmpty()) {
            return R.failed("无数据删除");
        }
        //// 业务一体化
        //int code = iRemotePaymentService
        //        .delete(PaymentDTO.builder().ids(ids).build())
        //        .getCode();
        //if (code != CommonConstants.SUCCESS) {
        //    throw new BusinessException("业务一体化删除数据失败");
        //}
        // 记录日志
        CompletableFuture.runAsync(() -> {
            StringBuilder sb = new StringBuilder();
            projectPaymentList.forEach(p -> sb.append("【").append(p.getDocumentNumber()).append("】"));
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.DELETE_PAYMENT, OperationEnum.DELETE_PAYMENT.getName() + sb.toString());
        });
        // 项目回款认领数据
        List<Long> projectPaymentClaimIdList = projectPaymentClaimMapper
                .selectList(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                        .in(CollUtil.isNotEmpty(ids), ProjectPaymentClaim::getProjectPaymentId, ids)
                )
                .stream()
                .map(ProjectPaymentClaim::getId)
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(projectPaymentClaimIdList)) {
            return baseMapper.deleteBatchIds(projectPaymentList) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.deleteBatchIds(projectPaymentClaimIdList) > NumberUtils.INTEGER_ZERO
                    ? R.ok("删除回款跟踪数据成功") : R.failed("删除回款跟踪数据失败");
        }

        // 判断是否为汇总过来的教育回款 如果是的话就需要将教育回款改为待汇总
        List<EducationPayment> educationPaymentList = new ArrayList<>();
        projectPaymentList.forEach(p -> {
            String educationIds = p.getEducationIds();
            if (CharSequenceUtil.isNotBlank(educationIds)) {
                List<Long> educationIdList = Arrays.stream(educationIds.split(StrPool.COMMA))
                        .filter(educationId -> !CharSequenceUtil.EMPTY.equals(educationId))
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                educationIdList.forEach(e -> {
                    EducationPayment educationPayment = EducationPayment.builder()
                            .id(e)
                            .summaryStatus(SummaryStatusEnum.NO.getValue())
                            .build();
                    educationPaymentList.add(educationPayment);
                });
                educationPaymentService.updateBatchById(educationPaymentList);
            }
        });

        return baseMapper.deleteBatchIds(projectPaymentList) > NumberUtils.INTEGER_ZERO
                ? R.ok("删除数据成功") : R.failed("删除数据失败");
    }

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean claim(Long id) {
        // 已认领状态 + 待锁定状态
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getClaimStatus, ClaimStatusEnum.YES.getValue())
                .eq(ProjectPayment::getLockStatus, LockStatusEnum.NO.getValue())
                .eq(ProjectPayment::getId, id)
        );
        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 更换认领状态
            projectPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());

            LambdaQueryWrapper<ProjectPaymentClaim> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProjectPaymentClaim::getProjectPaymentId, id);
            List<ProjectPaymentClaim> claimList = projectPaymentClaimMapper.selectList(queryWrapper);
            if (UserUtils.getUser() != null) {
                // 记录日志
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.UN_CLAIM_PAYMENT,
                        "【" + projectPayment.getDocumentNumber() + "】" + OperationEnum.UN_CLAIM_PAYMENT.getName());
            }
            dbApi.updateSalesReceipt(claimList.stream().map(ProjectPaymentClaim::getContractPaymentId).collect(Collectors.toList()));
            return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.delete(queryWrapper) > NumberUtils.INTEGER_ZERO;
        }
        return Boolean.TRUE;
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param dto {@link ProjectPaymentClaimDTO}
     * @return {@link Boolean}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean claim(ProjectPaymentClaimDTO dto) {

        // 1、待认领状态 + 待锁定状态
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getClaimStatus, ClaimStatusEnum.NO.getValue())
                .eq(ProjectPayment::getLockStatus, LockStatusEnum.NO.getValue())
                .eq(ProjectPayment::getId, dto.getId())
        );
        // 2、由于认领和编辑是同一个接口 所以如果有认领数据就是更新 无认领数据就是插入
        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 2.1、处理项目回款数据
            CopyOptions projectPaymentOption = CopyOptions.create()
                    .setIgnoreNullValue(true)
                    .setIgnoreError(true);
            BeanUtil.copyProperties(dto, projectPayment, projectPaymentOption);
            projectPayment.setClaimStatus(ClaimStatusEnum.YES.getValue());
            // 根据客户名称的备案人进行消息推送
            this.pushMessage(dto.getCustomerName(), projectPayment, dto.getPaymentDate(), projectPayment.getDocumentNumber());
            // 2.2、处理项目回款认领数据 后续可以采用saveOrUpdate()优化
            ProjectPaymentClaim projectPaymentClaim = projectPaymentClaimMapper.selectOne(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                    .eq(ProjectPaymentClaim::getProjectPaymentId, projectPayment.getId())
            );
            // 2.1、更新项目回款跟踪
            if (Optional.ofNullable(projectPaymentClaim).isPresent()) {
                this.getIdByName(projectPaymentClaim, dto);
                CopyOptions projectPaymentClaimOption = CopyOptions.create()
                                .setIgnoreNullValue(true)
                                .setIgnoreError(true)
                                .setIgnoreProperties("id", "claimantDate", "paymentDeptId", "paymentSecondaryDeptId");
                BeanUtil.copyProperties(dto, projectPaymentClaim, projectPaymentClaimOption);
                // 记录日志
                if (UserUtils.getUser() != null) {
                    projectPaymentClaim.setClaimantId(UserUtils.getUser().getId());
                    projectPaymentClaim.setClaimantName(UserUtils.getUser().getName());
                    logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                            OperationEnum.EDIT_PAYMENT, "【" + projectPayment.getDocumentNumber() + "】"
                                    + projectPaymentClaim.getClaimantName() + "认领回款成功");
                } else {
                    projectPaymentClaim.setClaimantId(dto.getUserId());
                    projectPaymentClaim.setClaimantName(dto.getUsername());
                }

                return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                        && projectPaymentClaimMapper.updateById(projectPaymentClaim) > NumberUtils.INTEGER_ZERO;
            }
            // 无认领数据做插入操作
            projectPaymentClaim = BeanUtil.copyProperties(dto, ProjectPaymentClaim.class, "id", "claimantDate");
            // 赋值部分id
            projectPaymentClaim.setProjectPaymentId(projectPayment.getId());
            projectPaymentClaim.setPaymentDept(null);
            projectPaymentClaim.setPaymentSecondaryDept(null);
            this.getIdByName(projectPaymentClaim, dto);
            if (UserUtils.getUser() != null) {
                projectPaymentClaim.setClaimantId(UserUtils.getUser().getId());
                projectPaymentClaim.setClaimantName(UserUtils.getUser().getName());
                // 记录日志
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.CLAIM_PAYMENT, "【" + projectPayment.getDocumentNumber() + "】"
                                + projectPaymentClaim.getClaimantName() + "认领回款成功");
            } else {
                projectPaymentClaim.setClaimantId(dto.getUserId());
                projectPaymentClaim.setClaimantName(dto.getUsername());
            }
            // 赋值认领日期
            String claimantDate = dto.getClaimantDate();
            if (CharSequenceUtil.isNotBlank(claimantDate)) {
                DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtils.DATETIME_FORMAT);
                claimantDate = dto.getClaimantDate() + DateUtils.BLACK_HOUR_MINUTE_SECOND;
                projectPaymentClaim.setClaimantDate(LocalDateTime.parse(claimantDate, df));
            }

            return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                    && projectPaymentClaimMapper.insert(projectPaymentClaim) > NumberUtils.INTEGER_ZERO;
        }
        return Boolean.TRUE;
    }

    /**
     * 导入Excel
     *
     * @param projectPaymentExcelDTOList {@link List}<{@link ProjectPaymentExcelDTO}>
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> importExcel(List<ProjectPaymentExcelDTO> projectPaymentExcelDTOList) {
        // 遍历封装后插入数据库
        List<ProjectPayment> projectPaymentList = new ArrayList<>();
        if (CollUtil.isNotEmpty(projectPaymentExcelDTOList)) {
            StringBuilder error = new StringBuilder();
            AtomicInteger i = new AtomicInteger(2);
            List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
            Map<String, Long> customerAccountMap = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY)
                    .stream()
                    .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, CustomerAccountVO::getCustomerId, (a, b) -> a));
            projectPaymentExcelDTOList.forEach(p -> {
                // 校验
                this.checkImport(p, error, i.getAndIncrement());
                // 1、项目回款
                ProjectPayment projectPayment = BeanUtil.copyProperties(p, ProjectPayment.class);
                // 单据编号
                String number = RedisConstant.PROJECT_DOCUMENT_NUMBER_PREFIX +
                        new SimpleDateFormat(DateUtils.YEAR_MONTH).format(new Date()) +
                        String.format("%04d", stringRedisTemplate.opsForValue().increment(RedisConstant.PROJECT_DOCUMENT_NUMBER));
                projectPayment.setDocumentNumber(number);
                // 根据客户名称的情况下需要查找其id并进行消息推送
                String customerName = p.getCustomerName();
                this.getIdByName(projectPayment, customerName,customerAccountMap);
                this.pushMessage(customerName, projectPayment, p.getPaymentDate(), number);
                Map<String,Integer > attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getName,OaDictVO::getDisorder, (b, c)->b));

                Integer paymentCompany = attributableSubjectMap.getOrDefault(p.getPaymentCompanyTxt(),null);
                if (Optional.ofNullable(paymentCompany).isPresent()) {
                    projectPayment.setPaymentCompany(paymentCompany);
                } else {
                    projectPayment.setPaymentCompany(null);
                }
                projectPayment.setClaimStatus(EnumUtils.getValueByName(ClaimStatusEnum.class, p.getClaimStatusTxt()));
                projectPayment.setLockStatus(EnumUtils.getValueByName(LockStatusEnum.class, p.getLockStatusTxt()));
                projectPayment.setPushStatus(EnumUtils.getValueByName(PushStatusEnum.class, p.getPushStatusTxt()));
                // 填充固定属性
                projectPayment.setCreatorId(UserUtils.getUser().getId());
                projectPayment.setCreatorName(UserUtils.getUser().getName());
                // 转换日期格式
                projectPayment.setPaymentDate(DateUtils.formatTime(p.getPaymentDate()).replaceAll(StrPool.SLASH, StrPool.DASHED));
                projectPaymentList.add(projectPayment);
            });
            if (error.toString().contains("空")) {
                return R.failed("导入失败,原因是" + error.toString());
            }

            // 2、记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.IMPORT,
                    OperationEnum.IMPORT.getName() + "【" + projectPaymentExcelDTOList.size() + "条】");

            // 3、业务一体化
            //List<ProjectPaymentBO> boList = new ArrayList<>();
            //projectPaymentList.forEach(p -> boList.add(BeanUtil.copyProperties(p, ProjectPaymentBO.class)));
            //if (Boolean.FALSE.equals(iRemotePaymentService.saveBatch(boList).getData())) {
            //    throw new BusinessException("业务一体化导入数据异常");
            //}

            return this.saveBatch(projectPaymentList) ? R.ok("导入成功") : R.failed("导入失败");
        }

        return R.ok("无数据导入");
    }


    public PushResultVO pushV1(List<Long> ids) {
        StringBuilder sb = new StringBuilder();
        AtomicInteger succeed = new AtomicInteger();
        AtomicInteger error = new AtomicInteger();
        List<PushErrorReasonVO> errorReasonList = new ArrayList<>();
        // 1、获取待推送项目详情
        List<ProjectPaymentPushVO> pushVOList = baseMapper.selPushVo(ids);
        if (CollUtil.isEmpty(pushVOList)) {
            List<PushErrorReasonVO> errorList = new ArrayList<>();
            for (Long id : ids) {
                ProjectPayment projectPayment = baseMapper.selectById(id);
                if (Optional.ofNullable(projectPayment).isPresent()) {
                    projectPayment.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    baseMapper.updateById(projectPayment);
                    // 记录日志
                    sb.append("【").append(projectPayment.getDocumentNumber()).append("】【")
                            .append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                }
                PushErrorReasonVO errorReason = PushErrorReasonVO.builder()
                        .id(String.valueOf(id))
                        .errorReason(VoucherRecordEnum.ERROR_STR.getName())
                        .build();
                errorList.add(errorReason);
            }
            // 日志记录
            if (sb.length() > NumberUtils.INTEGER_ZERO) {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.PUSH,
                        OperationEnum.PUSH.getName() + sb.toString());
            }
            return PushResultVO.builder()
                    .success(NumberUtils.INTEGER_ZERO)
                    .error(ids.size())
                    .errorReasonList(errorList).build();
        }
        //传入的ids和获取到的项目数据个数是否一致
        Integer notPushNum = NumberUtils.INTEGER_ZERO;
        if (ids.size() != pushVOList.size()){
            notPushNum = ids.size() - pushVOList.size();
        }
        //获取所有发薪主体
        List<Integer> salaryPaidSubjectList = pushVOList.stream().map(ProjectPaymentPushVO::getPaymentCompany).distinct().collect(Collectors.toList());
        //根据发薪主体分组
        Map<Integer, List<ProjectPaymentPushVO>> listMap = pushVOList.stream().collect(Collectors.groupingBy(ProjectPaymentPushVO::getPaymentCompany));

        //获取帐套信息,key:发薪主体字典id，value:帐套信息
        Map<String, AccountingSet> accountingSetMap = accountingSetService.getAccountingSet();
        //获取银行信息,key:银行付款字典值，value:银行信息
        List<BackAccountVO> bankAccountList = dbApi.getBankAccount(CharSequenceUtil.EMPTY);
        Map<String, String> tBankAccountMap = bankAccountList.stream().collect(Collectors.toMap(BackAccountVO::getName, BackAccountVO::getCode, (a, b) -> a));
        //获取客户信息,key:客户id，value:客户信息
        List<CustomerAccountVO> customerAccountList = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY);
        Map<Long, CustomerAccountVO> tCustomerAccountMap = customerAccountList.stream().collect(Collectors.toMap(CustomerAccountVO::getCustomerId, a -> a, (a, b) -> a));

        //获取认领信息详情
        List<ProjectPaymentClaim> paymentClaimList = projectPaymentClaimMapper.selectList(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                .in(ProjectPaymentClaim::getProjectPaymentId, ids));
        Map<Long, List<ProjectPaymentClaim>> claimMap = paymentClaimList.stream().collect(Collectors.groupingBy(ProjectPaymentClaim::getProjectPaymentId));

        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        salaryPaidSubjectList.forEach(s -> {
            List<ProjectPaymentPushVO> projectPayments = listMap.get(s);
            //获取帐套信息
            AccountingSet accountingSet = accountingSetMap.get(attributableSubjectMap.getOrDefault(s,null));
            if (Optional.ofNullable(accountingSet).isPresent()) {
                //获取登录T+token
                String token = openApi.login(accountingSet.getAccountingAccount(), accountingSet.getAccountingPassword(), accountingSet.getAccountingCode());
                projectPayments.forEach(p -> {
                    //凭证实体数据组装
                    VoucherDTO.VoucherDTOBuilder voucherDTOBuilder = VoucherDTO.builder();
                    //制单日期为归属月份的最后一日天
                    LocalDate date = LocalDate.parse(p.getPaymentDate().replace("-", ""), DateTimeFormatter.BASIC_ISO_DATE);
                    LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
                    voucherDTOBuilder
                            .ExternalCode(String.valueOf(p.getId()))
                            .VoucherDate(lastDay.toString())
                            .DocType(VoucherDocTypeDTO.builder().Code(VoucherRecordEnum.DOCTYPE.getName()).build());
                    //凭证创建辅佐项集合
                    List<AuxiliaryDTO> auxiliaryDTOList = new ArrayList<>();
                    CustomerAccountVO tCustomer = tCustomerAccountMap.get(p.getCustomerId());
                    AuxiliaryDTO auxiliaryDto = new AuxiliaryDTO();
                    if (Optional.ofNullable(tCustomer).isPresent()) {
                        auxiliaryDto.setAuxAccCustomer(AuxAccCustomerDTO.builder().Code(tCustomer.getCustomerCode()).build());
                    } else {
                        //如果客户名称不存在。来往单位  默认-厦门硕翔计算机技术有限公司
                        auxiliaryDto.setAuxAccCustomer(AuxAccCustomerDTO.builder().Code("CW-************").build());
                    }
                    auxiliaryDTOList.add(auxiliaryDto);
                    //凭证明细
                    ArrayList<VoucherDetailDTO> detailDtoArrayList = new ArrayList<>();
                    //认领信息
                    List<ProjectPaymentClaim> claimList = claimMap.getOrDefault(p.getId(), ImmutableList.of());
                    List<ProjectPaymentClaim> marginList = claimList.stream()
                            .filter(claim -> PaymentTypeEnum.MARGIN.getValue().equals(claim.getPaymentType())).collect(Collectors.toList());

                    //摘要取客户名称/企业名称，先取客户名称，无值取企业名称
                    String customer = Optional.ofNullable(p.getCustomerName()).isPresent()  ? p.getCustomerName() : p.getEnterpriseName();
                    //场景二：【回款类型】值全部等于保证金
                    if (claimList.size() == marginList.size()){
                        //借方凭证：@【银行账户】，取值【收款金额】
                        VoucherDetailDTO marginBankDr = VoucherDetailDTO.builder()
                                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                                .Account(EntryAccountDTO.builder().Code(Optional.ofNullable(p.getBankAccount()).isPresent()?tBankAccountMap.getOrDefault(p.getBankAccount(),CharSequenceUtil.EMPTY):CharSequenceUtil.EMPTY).build())
                                .AmountDr(MoneyUtils.getInstance().decrypt(p.getPaymentAmount()))
                                .Summary(VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                                .build();
                        detailDtoArrayList.add(marginBankDr);
                    }else {
                        //借方凭证：@【银行账户】，取值【收款金额】
                        VoucherDetailDTO collectionBankDr = VoucherDetailDTO.builder()
                                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                                .Account(EntryAccountDTO.builder().Code(Optional.ofNullable(p.getBankAccount()).isPresent()?tBankAccountMap.getOrDefault(p.getBankAccount(),CharSequenceUtil.EMPTY):CharSequenceUtil.EMPTY).build())
                                .AmountDr(MoneyUtils.getInstance().decrypt(p.getPaymentAmount()))
                                .Summary(VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                                .build();
                        detailDtoArrayList.add(collectionBankDr);
                    }
                    if (CollectionUtil.isNotEmpty(claimList)) {
                        claimList.forEach(c ->{
                            //场景一：【回款类型】值不等于保证金
                            if (!PaymentTypeEnum.MARGIN.getValue().equals(c.getPaymentType())) {
                                //贷方凭证：@【应收账款-非关联单位】，取值【收款金额】
                                VoucherDetailDTO accountsReceivableCr = VoucherDetailDTO.builder()
                                        .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                                        .Account(EntryAccountDTO.builder().Code(VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName()).build())
                                        .AmountCr(MoneyUtils.getInstance().decrypt(c.getClaimMoney()))
                                        .Summary(VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                                        .AuxInfos(auxiliaryDTOList)
                                        .build();
                                detailDtoArrayList.add(accountsReceivableCr);
                            } else {
                                //贷方凭证：@【其他应收款-非关联单位】，取值【收款金额】
                                VoucherDetailDTO otherAccountsReceivableCr = VoucherDetailDTO.builder()
                                        .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                                        .Account(EntryAccountDTO.builder().Code(VoucherSubjectIdEnum.OTHER_ACCOUNTS_RECEIVABLE.getName()).build())
                                        .AmountCr(MoneyUtils.getInstance().decrypt(c.getClaimMoney()))
                                        .Summary(VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                                        .AuxInfos(auxiliaryDTOList)
                                        .build();
                                detailDtoArrayList.add(otherAccountsReceivableCr);
                            }
                        });
                    }

                    VoucherDTO voucher = voucherDTOBuilder.Entrys(detailDtoArrayList).build();
                    //凭证创建
                    try {
                        String result = openApi.createVoucher(token, voucher);
                        ProjectPayment projectPayment = BeanUtil.copyProperties(p, ProjectPayment.class);
                        if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                            projectPayment.setPushStatus(PushStatusEnum.SUCCESS_PUSH.getValue());
                            baseMapper.updateById(projectPayment);
                            succeed.incrementAndGet();
                            // 记录日志
                            sb.append("【").append(p.getDocumentNumber()).append("】【").append(PushStatusEnum.SUCCESS_PUSH.getName()).append("】");
                        } else {
                            projectPayment.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                            baseMapper.updateById(projectPayment);
                            error.incrementAndGet();
                            errorReasonList.add(PushErrorReasonVO.builder()
                                    .id(String.valueOf(projectPayment.getId()))
                                    .errorReason(result)
                                    .build());
                            // 记录日志
                            sb.append("【").append(p.getDocumentNumber()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                        }
                    } catch (Exception e) {
                        throw new BusinessException("凭证创建失败");
                    }
                });
            } else {
                projectPayments.stream().forEach(p->{
                    ProjectPayment projectPayment = BeanUtil.copyProperties(p, ProjectPayment.class);
                    projectPayment.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    baseMapper.updateById(projectPayment);
                    errorReasonList.add(PushErrorReasonVO.builder()
                            .id(String.valueOf(projectPayment.getId()))
                            .errorReason(VoucherRecordEnum.ACCOUNTING_ERROR.getName())
                            .build());
                    // 记录日志
                    sb.append("【").append(p.getDocumentNumber()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                    error.incrementAndGet();
                });
            }
        });
        // 日志记录
        if (sb.length() > NumberUtils.INTEGER_ZERO) {
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.PUSH,
                    OperationEnum.PUSH.getName() + sb.toString());
        }
        return PushResultVO.builder()
                .success(succeed.get())
                .error(error.get()+notPushNum)
                .errorReasonList(errorReasonList).build();
    }

    /**
     * 定时任务每日更新认领时间
     *
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<Void> updateLockStatus() {
        List<ProjectPayment> projectPaymentList = this.list(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getClaimStatus, ClaimStatusEnum.NO.getValue())
                .eq(ProjectPayment::getAutoLock, AutoLockEnum.YES.getValue())
        );
        projectPaymentList.forEach(p -> {
            if (DateUtils.difference(p.getPaymentDate()) > DateUtils.THIRTY_DAYS
                    && ClaimStatusEnum.NO.getValue().equals(p.getClaimStatus())) {
                p.setLockStatus(LockStatusEnum.YES.getValue());
                p.setAutoLock(LockStatusEnum.NO.getValue());
            }
        });
        return this.updateBatchById(projectPaymentList) ? R.ok() : R.failed();
    }

    /**
     * 获取人员信息
     *
     * @param username {@link String} 名称
     * @return {@link List}<{@link UserVO}>
     */
    @Override
    public List<UserVO> userInfo(String username) {
        List<UserVO> userList = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(username)) {
            remoteOutService.getUserList().getData().stream()
                    .filter(user -> user.getUsername().contains(username))
                    .forEach(u -> {
                        UserVO userVO = UserVO.builder()
                                .userId(u.getUserId())
                                .username(u.getUsername())
                                .build();
                        userList.add(userVO);
                    });
            return userList;
        }
        remoteOutService.getUserList().getData()
                .forEach(u -> {
                    UserVO userVO = UserVO.builder()
                            .userId(u.getUserId())
                            .username(u.getUsername())
                            .build();
                    userList.add(userVO);
                });
        return userList;
    }

    /**
     * 根据合同名称搜索归属合同收款明细
     *
     * @param contractName {@link String} 合同名称
     * @return {@link ContractPaymentVO}>
     */
    @Override
    public List<ContractInfoVO> getContractName(String contractName) {
        // 1、获取业务板块键值对
        Map<Integer, String> businessBlockMap = dbApi.projectDict(DictConstant.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        // 2、获取技术类型键值对
        Map<Integer, String> skillTypeMap = dbApi.projectDict(DictConstant.SKILL_TYPE_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        // 3、封装返回数据 根据id去重过滤数据
        List<ContractInfoVO> voList = new ArrayList<>();
        Set<Long> idSet = new HashSet<>();
        dbApi.getContractName(contractName).forEach(c -> {
            if (idSet.add(c.getId())) {
                voList.add(c);
            }
        });
        voList.forEach(c -> {
            c.setBusinessBlockTxt(businessBlockMap.get(c.getBusinessBlock()));
            c.setSkillTypeTxt(skillTypeMap.get(c.getSkillType()));
        });
        return voList;
    }

    /**
     * 单据状态条件
     *
     * @param dto {@link ProjectPaymentDTO}
     */
    private void questionStatus(ProjectPaymentDTO dto) {
        List<String> documentStatus = dto.getDocumentStatus();
        if (CollUtil.isNotEmpty(documentStatus)) {
            List<Integer> claimStatusList = new ArrayList<>();
            List<Integer> lockStatusList = new ArrayList<>();
            List<Integer> pushStatusList = new ArrayList<>();
            documentStatus.forEach(d -> {
                claimStatusList.add(EnumUtils.getValueByName(ClaimStatusEnum.class, d));
                lockStatusList.add(EnumUtils.getValueByName(LockStatusEnum.class, d));
                pushStatusList.add(EnumUtils.getValueByName(PushStatusEnum.class, d));
            });
            dto.setClaimStatusByDocument(claimStatusList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            dto.setLockStatusByDocument(lockStatusList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            dto.setPushStatusByDocument(pushStatusList.stream().filter(Objects::nonNull).collect(Collectors.toList()));
        }
    }

    /**
     * 封装实体集合展示
     *
     * @param projectPaymentList {@link List}<{@link ProjectPayment}>
     * @return {@link List}<{@link ProjectPaymentVO}>
     */
    private List<ProjectPaymentVO> addVoToList(List<ProjectPayment> projectPaymentList) {
        List<ProjectPaymentVO> projectPaymentVOList = new ArrayList<>();
        // 1、获取 OA 的【项目台帐】
        List<ProjectAccountVO> projectAccountList = dbApi.getProjectAccountByName(projectPaymentList.stream()
                .filter(p -> Optional.ofNullable(p.getProjectPaymentClaim().getProjectName()).isPresent())
                .map(p -> p.getProjectPaymentClaim().getProjectName())
                .collect(Collectors.toList()));
        Map<Long, ProjectAccountVO> projectAccountVOMap = projectAccountList.stream().collect(Collectors.toMap(ProjectAccountVO::getProjectId, a -> a, (a, b) -> a));
        // 2、获取 OA 的【归属合同收款明细】
        List<ContractInfoVO> ContractInfoList = dbApi.getContractName(null);
        Map<Long, ContractInfoVO> contractInfoVOMap = ContractInfoList.stream().collect(Collectors.toMap(ContractInfoVO::getId, a -> a, (a, b) -> a));
        // 3、获取业务板块字典值
        Map<Integer, String> businessBlockDict = dbApi.projectDict(DictConstant.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        // 4、获取技术类型字典值
        Map<Integer, String> skillTypeDict = dbApi.projectDict(DictConstant.SKILL_TYPE_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        // 5、展示封装
        projectPaymentList.forEach(p -> {
            ProjectPaymentVO projectPaymentVO = BeanUtil.copyProperties(p, ProjectPaymentVO.class);
            // 5.1、枚举与金额处理
            projectPaymentVO.setPaymentCompanyTxt(attributableSubjectMap.getOrDefault(p.getPaymentCompany(),StrUtil.EMPTY));
            projectPaymentVO.setClaimStatusTxt(EnumUtils.getNameByValue(ClaimStatusEnum.class, p.getClaimStatus()));
            projectPaymentVO.setLockStatusTxt(EnumUtils.getNameByValue(LockStatusEnum.class, p.getLockStatus()));
            projectPaymentVO.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
            projectPaymentVO.setPaymentAmount(MoneyUtils.getInstance().transType(p.getPaymentAmount()));
            // 5.2、明细认领处理
            ProjectPaymentClaim projectPaymentClaim = p.getProjectPaymentClaim();
            // 5.2.1、OA【项目台帐】
            ProjectAccountVO projectAccount = projectAccountVOMap.getOrDefault(projectPaymentClaim.getProjectId(), null);
            if (Optional.ofNullable(projectAccount).isPresent()) {
                projectPaymentVO.setProjectName(projectAccount.getProjectName());
                projectPaymentVO.setProjectNumber(projectAccount.getProjectNumber());
            }
            // 5.2.2、OA【归属合同明细】
            ContractInfoVO contractInfoVO = contractInfoVOMap.getOrDefault(projectPaymentClaim.getContractId(), null);
            if (Optional.ofNullable(contractInfoVO).isPresent()) {
                projectPaymentVO.setContractName(contractInfoVO.getContractName());
                projectPaymentVO.setContractNumber(contractInfoVO.getContractNumber());
                projectPaymentVO.setBusinessBlock(contractInfoVO.getBusinessBlock());
                projectPaymentVO.setBusinessBlockTxt(businessBlockDict.get(contractInfoVO.getBusinessBlock()));
                projectPaymentVO.setSkillType(contractInfoVO.getSkillType());
                projectPaymentVO.setSkillTypeTxt(skillTypeDict.get(contractInfoVO.getSkillType()));
            }
            projectPaymentVO.setClaimMoney(MoneyUtils.getInstance().transType(projectPaymentClaim.getClaimMoney()));
            projectPaymentVOList.add(this.setProjectPaymentClaim(projectPaymentVO, projectPaymentClaim));
        });

        return projectPaymentVOList;
    }

    /**
     * 根据传入的名字赋值id
     *
     * @param projectPayment {@link ProjectPayment}
     * @param customerName     {@link PaymentDTO}
     */
    private void getIdByName(ProjectPayment projectPayment, String customerName,Map<String, Long> customerAccountMap) {
        // 客户名称
        if (CharSequenceUtil.isNotBlank(customerName)) {
            if (CharSequenceUtil.isEmpty(customerName)) {
                projectPayment.setCustomerId(null);
            } else{
                projectPayment.setCustomerId(customerAccountMap.get(customerName));
            }
        }
    }

    /**
     * 根据传入的名字赋值id
     *
     * @param projectPaymentClaim    {@link ProjectPaymentClaim}
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     */
    private void getIdByName(ProjectPaymentClaim projectPaymentClaim, ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        // 1、认领日期
        projectPaymentClaim.setClaimantDate(LocalDateTime.now());

        // 4、部门id
        if (CharSequenceUtil.isEmpty(projectPaymentClaimDTO.getPaymentDept())) {
            projectPaymentClaim.setPaymentDeptId(null);
        } else if (!projectPaymentClaimDTO.getPaymentDept().equals(projectPaymentClaim.getPaymentDept())) {
            // 4.1、中台部门组织架构
            Map<String, Long> firstDeptMap = remoteOutMultiDeptService
                    .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, null)
                    .getData()
                    .stream()
                    .collect(Collectors.toMap(MultiDimensionDeptDto::getName, MultiDimensionDeptDto::getDeptId, (a, b) -> a));
            // 4.1.1、一级部门
            String paymentDept = projectPaymentClaimDTO.getPaymentDept();
            if (CharSequenceUtil.isNotBlank(paymentDept)) {
                projectPaymentClaim.setPaymentDept(paymentDept);
                projectPaymentClaim.setPaymentDeptId(firstDeptMap.get(paymentDept));
                // 4.1.2、二级部门
                String paymentSecondaryDept = projectPaymentClaimDTO.getPaymentSecondaryDept();
                if (CharSequenceUtil.isNotBlank(paymentSecondaryDept)) {
                    projectPaymentClaim.setPaymentSecondaryDept(paymentSecondaryDept);
                    projectPaymentClaim.setPaymentSecondaryDeptId(firstDeptMap.get(paymentSecondaryDept));
                }
            }
        }

    }

    /**
     * 校验导入参数
     *
     * @param dto {@link EducationPaymentExcelDTO}
     * @param error {@link StringBuilder}
     * @param row 行数
     */
    private void checkImport(ProjectPaymentExcelDTO dto, StringBuilder error, Integer row) {
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getPaymentCompanyTxt())) {
            error.append("收款公司不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getPaymentDate())) {
            error.append("收款日期不能为空。");
        }
        if (!Optional.ofNullable(dto.getPaymentAmount()).isPresent()) {
            error.append("收款金额不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getPaymentPlatform())) {
            error.append("收款平台不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getBankAccount())) {
            error.append("银行账户不能为空");
        }
    }

    /**
     * 消息推送前提判断客户名称不能为空
     *
     * @param customerName 客户名称
     * @param projectPayment {@link ProjectPayment}
     * @param paymentDate 收款日期
     * @param documentNumber 单据编号
     */
    private void pushMessage(String customerName, ProjectPayment projectPayment, String paymentDate, String documentNumber) {
        if (CharSequenceUtil.isNotBlank(customerName)) {
            Map<String, CustomerAccountVO> customerAccountMap = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY)
                    .stream()
                    .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));
            CustomerAccountVO customerAccountVO = customerAccountMap.get(customerName);
            if (Optional.ofNullable(customerAccountVO).isPresent()) {
                this.pushMessage(paymentDate, documentNumber, customerAccountVO.getRecordManId(), customerAccountVO.getRecordMan());
                projectPayment.setRecordManId(customerAccountVO.getRecordManId());
            }
        }
    }

    /**
     * 消息推送
     *
     * @param paymentDate    收款日期
     * @param documentNumber 单据编号
     * @param targetId       目标人员id
     * @param targetName     目标人员姓名
     */
    private void pushMessage(String paymentDate, String documentNumber, Long targetId, String targetName) {
        // 没有目标人信息直接返回
        if (!Optional.ofNullable(targetId).isPresent() || !Optional.ofNullable(targetName).isPresent()) {
            return;
        }

        // 消息内容
        final String content = "你有一笔【" + paymentDate + "】的项目回款【" + documentNumber + "】等待认领，请及时处理~";
        // 消息实体
        MailModel model = new MailModel();
        model.setSource(SourceEnum.PROJECT.getValue());
        model.setType(MsgTypeEnum.TEXT_MSG.getValue());
        model.setTitle("业务一体化项目回款待认领");
        model.setContent(content);
        model.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
        model.setSender("业务一体化系统");
        model.setTargetType(TargetTypeEnum.USERS.getValue());
        model.setRedirectUrl(FinancePropertiesUtils.PROJECT_PAYMENT_URL + "?documentNumber=" + documentNumber);
        // 接受消息的人列表
        List<BcpMessageTargetDTO> list = new ArrayList<>();

        BcpMessageTargetDTO person = new BcpMessageTargetDTO();
        person.setTargetId(targetId.toString());
        person.setTargetName(targetName);
        list.add(person);

        model.setTargetList(list);

        try {
            remoteMailService.sendMsg(model);
        } catch (Exception e) {
            log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", targetId, targetName);
        }
    }

    /**
     * 回款认领基础字段赋值
     *
     * @param vo {@link ProjectPaymentVO}
     * @param projectPaymentClaim {@link ProjectPaymentClaim}
     * @return {@link ProjectPaymentVO}
     */
    private ProjectPaymentVO setProjectPaymentClaim(ProjectPaymentVO vo, ProjectPaymentClaim projectPaymentClaim) {
        BeanUtil.copyProperties(projectPaymentClaim,vo);
        if (Optional.ofNullable(projectPaymentClaim.getPaymentMoney()).isPresent()){
            vo.setPaymentMoney(MoneyUtils.getInstance().transType(new BigDecimal(projectPaymentClaim.getPaymentMoney().replace(",",""))));
        }
        vo.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, projectPaymentClaim.getPaymentType()));
        vo.setMarginTypeTxt(EnumUtils.getNameByValue(MarginTypeEnum.class, projectPaymentClaim.getMarginType()));
        vo.setClaimantDate(DateUtils.getStringDate(projectPaymentClaim.getClaimantDate(),DateUtils.SIMPLE_DATE_FORMAT));
        return vo;
    }

    /**
     * 数据权限
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link EducationDTO}
     */
    private ProjectPaymentDTO condition(ProjectPaymentDTO projectPaymentDTO) {
        List<DeptCacheDto> allDeptList = remoteDeptService.getAllDeptList(false);

        // 数据权限
        SysUserRoleDataVo auth = remoteRoleService.getRoleDataDetailByUserId(projectPaymentDTO.getClientId(),
                UserUtils.getUser().getId(), projectPaymentDTO.getMenuCode()).getData();
        log.info("教育回款数据权限: {}", auth);
        // 为全部权限
        projectPaymentDTO.setAuthority(Boolean.TRUE);
        if (Boolean.FALSE.equals(auth.getIsAll())) {
            projectPaymentDTO.setAuthority(Boolean.FALSE);
            // 可以查看到下级人员id列表(中台已返回自身id)
            projectPaymentDTO.setAuthUserIdList(auth.getUserIdList());
            if (CollectionUtil.isNotEmpty(auth.getDeptIdList()) && !allDeptList.containsAll(auth.getDeptIdList())){
                // 可以查看到付款公司的主体
                List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
                Map<String, Integer> attributableSubjectMap = oaDictVOS.stream()
                        .collect(Collectors.toMap(OaDictVO::getName, OaDictVO::getDisorder,(b, c)->b));
                List<Integer> authSubjectIdList = auth.getDeptIdList().stream().map(e -> {
                    DeptDetailDto deptDetail = remoteDeptService.getDeptDetail(e).getData();
                    Integer attributableSubject = attributableSubjectMap.getOrDefault(deptDetail.getName(),null);
                    return attributableSubject;
                }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
                projectPaymentDTO.setAuthSubjectIdList(authSubjectIdList);
            }
            // 可以查看到部门id列表
            //List<Long> deptIdList = auth.getDeptIdList();
            //deptIdList.add(NumberUtils.LONG_MINUS_ONE);
            //educationDTO.setDeptId(deptIdList);
        }
        // 根据userid查询项目台账/合同台账的客户id
        List<ProjectCustomerVO> projectCustomerVOS = dbApi.selCustomerId(UserUtils.getUser().getId());
        if (CollectionUtil.isNotEmpty(projectCustomerVOS)) {
            List<Long> customerIds = projectCustomerVOS.stream().map(ProjectCustomerVO::getCustomerId).collect(Collectors.toList());
            projectPaymentDTO.setCustomerIds(customerIds);
        }
        return projectPaymentDTO;
    }

    @Override
    public ProjectPaymentInfoDetailVO getPaymentInfo(Long id) {
        ProjectPaymentInfoDetailVO paymentInfoDetailVO = new ProjectPaymentInfoDetailVO();
        paymentInfoDetailVO.setId(id);
        // 获取项目回款跟踪
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getId, id)
        );
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        if (Optional.ofNullable(projectPayment).isPresent()) {
            // 封装详情信息与枚举
            ProjectPaymentDetailVO paymentDetailVO = BeanUtil.copyProperties(projectPayment, ProjectPaymentDetailVO.class);
            paymentDetailVO.setPaymentAmount(MoneyUtils.getInstance().transType(projectPayment.getPaymentAmount()));
            paymentDetailVO.setBudgetCollectionAmount(MoneyUtils.getInstance().transString(projectPayment.getBudgetCollectionAmount()));
            paymentDetailVO.setPaymentCompanyTxt(attributableSubjectMap.getOrDefault(projectPayment.getPaymentCompany(),StrUtil.EMPTY));
            paymentInfoDetailVO.setPaymentDetailVo(paymentDetailVO);
            // 获得项目回款认领详情
            List<ProjectPaymentClaim> claimList = projectPaymentClaimMapper.selectList(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                    .eq(ProjectPaymentClaim::getProjectPaymentId, id)
            );
            if (CollectionUtil.isNotEmpty(claimList)) {
                List<ProjectPaymentClaimDetailVO> claimDetailVOList = claimList.stream().map(claim -> {
                    ProjectPaymentClaimDetailVO claimDetailVO = BeanUtil.copyProperties(claim, ProjectPaymentClaimDetailVO.class);
                    claimDetailVO.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, claimDetailVO.getPaymentType()));
                    claimDetailVO.setMarginTypeText(EnumUtils.getNameByValue(MarginTypeEnum.class, claimDetailVO.getMarginType()));
                    claimDetailVO.setClaimMoney(MoneyUtils.getInstance().transType(claim.getClaimMoney()));
                    if (Optional.ofNullable(claim.getPaymentMoney()).isPresent()){
                        claimDetailVO.setPaymentMoney(MoneyUtils.getInstance().transType(new BigDecimal(claim.getPaymentMoney().replace(",",""))));
                    }
                    return claimDetailVO;
                }).collect(Collectors.toList());
                paymentInfoDetailVO.setClaimDetailList(claimDetailVOList);
            }
        }

        return paymentInfoDetailVO;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean batchClaim(ProjectPaymentClaimListDTO claimListDTO) {

        // 1、待认领状态 + 待锁定状态
        ProjectPayment projectPayment = baseMapper.selectOne(Wrappers.<ProjectPayment>lambdaQuery()
                .eq(ProjectPayment::getLockStatus, LockStatusEnum.NO.getValue())
                .eq(ProjectPayment::getId, claimListDTO.getId())
        );
        // 2、如果有认领数据就是更新 无认领数据就是插入
        if (!Optional.ofNullable(projectPayment).isPresent()) {
            return Boolean.FALSE;
        }
        List<ProjectPaymentClaimInfoDTO> claimDTOList = claimListDTO.getClaimDTOList();
        // 2.1、处理项目回款数据
        projectPayment.setClaimStatus(ClaimStatusEnum.YES.getValue());
        // 2.2、处理项目回款认领数据
        List<ProjectPaymentClaim> paymentClaimList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(claimDTOList)) {
            paymentClaimList = claimDTOList.stream().map(c -> {
                ProjectPaymentClaim projectPaymentClaim = BeanUtil.copyProperties(c, ProjectPaymentClaim.class);
                // 赋值部分id
                projectPaymentClaim.setProjectPaymentId(projectPayment.getId());
                // 1、认领日期
                projectPaymentClaim.setClaimantDate(LocalDateTime.now());
                // 2、部门id
                // 中台部门组织架构
               Map<String, Long> firstDeptMap = remoteOutMultiDeptService
                       .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, null)
                       .getData()
                       .stream()
                       .collect(Collectors.toMap(MultiDimensionDeptDto::getName, MultiDimensionDeptDto::getDeptId, (a, b) -> a));
                // 2.1.1、一级部门
                String paymentDept = c.getPaymentDept();
                if (CharSequenceUtil.isNotBlank(paymentDept)) {
                    projectPaymentClaim.setPaymentDept(paymentDept);
                    projectPaymentClaim.setPaymentDeptId(firstDeptMap.get(paymentDept));
                }
                // 2.1.2、二级部门
                String paymentSecondaryDept = c.getPaymentSecondaryDept();
                if (CharSequenceUtil.isNotBlank(paymentSecondaryDept)) {
                    projectPaymentClaim.setPaymentSecondaryDept(paymentSecondaryDept);
                    projectPaymentClaim.setPaymentSecondaryDeptId(firstDeptMap.get(paymentSecondaryDept));
                }
               projectPaymentClaim.setClaimantId(UserUtils.getUser().getId());
               projectPaymentClaim.setClaimantName(UserUtils.getUser().getName());

               return projectPaymentClaim;
            }).collect(Collectors.toList());
        }
        // 记录日志
        int delete = projectPaymentClaimMapper.delete(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                .eq(ProjectPaymentClaim::getProjectPaymentId, claimListDTO.getId())
                .eq(ProjectPaymentClaim::getDelFlag,NumberUtils.INTEGER_ZERO));
        if (delete > NumberUtils.INTEGER_ZERO){
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.AGAIN_CLAIM_PAYMENT, "【" + projectPayment.getDocumentNumber() + "】"
                            + UserUtils.getUser().getName() +OperationEnum.AGAIN_CLAIM_PAYMENT.getName()+ "成功");
        } else {
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.CLAIM_PAYMENT, "【" + projectPayment.getDocumentNumber() + "】"
                            + UserUtils.getUser().getName() +OperationEnum.CLAIM_PAYMENT.getName()+ "成功");
        }
        return baseMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                && projectPaymentClaimMapper.saveBatch(paymentClaimList);
    }

    /**
     * 模糊查询带分页
     *
     * @param page              {@link Page}
     * @param dto {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPaymentVO}>
     */
    @Override
    public StatisticsPage<ProjectPaymentPageVO> findPage(Page<ProjectPayment> page, ProjectPaymentDTO dto) {
        StatisticsPage<ProjectPaymentPageVO> projectPaymentVoPage = new StatisticsPage<>(page.getCurrent(), page.getSize());
        // 1、条件封装 单据状态
        questionStatus(dto);
        // 2、查询数据 包括【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<ProjectPaymentPageVO> projectPaymentVOList = new ArrayList<>();
        //Page<Long> pageIds = baseMapper.findPageIds(page, condition(dto));
        Page<Long> pageIds = baseMapper.findPageIdsV1(page, condition(dto));
        List<Long> ids = pageIds.getRecords();
        if (CollectionUtil.isEmpty(ids)){
            return projectPaymentVoPage;
        }
        List<ProjectPayment> projectPaymentList = baseMapper.selectList(Wrappers.<ProjectPayment>lambdaQuery()
                .in(ProjectPayment::getId, ids));
        List<ProjectPaymentClaim> claimList = projectPaymentClaimMapper.selectList(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                .in(ProjectPaymentClaim::getProjectPaymentId, ids));
        Map<Long, List<ProjectPaymentClaim>> claimMap = claimList.stream().collect(Collectors.groupingBy(ProjectPaymentClaim::getProjectPaymentId));
        // 1、获取 OA 的【项目台帐】
        List<ProjectAccountVO> projectAccountList = dbApi.getProjectAccountByName(ImmutableList.of());
        Map<Long, ProjectAccountVO> projectAccountVOMap = projectAccountList.stream().collect(Collectors.toMap(ProjectAccountVO::getProjectId, a -> a, (a, b) -> a));
        // 2.1、获取 OA 的【合同台账】
        List<ContractInfoVO> ContractInfoList = dbApi.getContractName(null);
        Map<Long, ContractInfoVO> contractInfoVOMap = ContractInfoList.stream().collect(Collectors.toMap(ContractInfoVO::getId, a -> a, (a, b) -> a));
        // 2.2、获取 OA 的【合同台账明细】
        List<ContractPaymentVO> contractPaymentList = dbApi.getContractPaymentByNumber(ImmutableList.of());
        Map<Long, ContractPaymentVO> contractPaymentVOMap = contractPaymentList.stream().collect(Collectors.toMap(ContractPaymentVO::getId, a -> a, (a, b) -> a));

        // 3、获取业务板块字典值
        Map<Integer, String> businessBlockDict = dbApi.projectDict(DictConstant.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        // 4、获取技术类型字典值
        Map<Integer, String> skillTypeDict = dbApi.projectDict(DictConstant.SKILL_TYPE_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        if (CollectionUtil.isNotEmpty(projectPaymentList)) {
            projectPaymentVOList = projectPaymentList.stream().map(p ->{
                ProjectPaymentPageVO pageVO = BeanUtil.copyProperties(p, ProjectPaymentPageVO.class);
                pageVO.setPaymentCompanyTxt(attributableSubjectMap.getOrDefault(p.getPaymentCompany(),StrUtil.EMPTY));
                pageVO.setClaimStatusTxt(EnumUtils.getNameByValue(ClaimStatusEnum.class, p.getClaimStatus()));
                pageVO.setLockStatusTxt(EnumUtils.getNameByValue(LockStatusEnum.class, p.getLockStatus()));
                pageVO.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
                pageVO.setPaymentAmount(MoneyUtils.getInstance().transType(p.getPaymentAmount()));
                List<ProjectPaymentClaim> paymentClaimList = claimMap.getOrDefault(p.getId(), ImmutableList.of());
                //组装认领信息
                if (CollectionUtil.isNotEmpty(paymentClaimList)) {
                    List<ProjectPaymentClaimPageVO> claimPageVOList = paymentClaimList.stream().map(c -> {
                        ProjectPaymentClaimPageVO claimPageVO = BeanUtil.copyProperties(c, ProjectPaymentClaimPageVO.class);
                        // 5.2.1、OA【项目台帐】
                        ProjectAccountVO projectAccount = projectAccountVOMap.getOrDefault(c.getProjectId(), null);
                        if (Optional.ofNullable(projectAccount).isPresent()) {
                            claimPageVO.setProjectName(projectAccount.getProjectName());
                            claimPageVO.setProjectNumber(projectAccount.getProjectNumber());
                        }
                        claimPageVO.setBusinessBlockTxt(businessBlockDict.get(c.getBusinessBlock()));
                        claimPageVO.setSkillTypeTxt(skillTypeDict.get(c.getSkillType()));
                        // 5.2.2、OA【合同台账】
                        ContractInfoVO contractInfoVO = contractInfoVOMap.getOrDefault(c.getContractId(), null);
                        if (Optional.ofNullable(contractInfoVO).isPresent()) {
                            claimPageVO.setContractName(contractInfoVO.getContractName());
                            claimPageVO.setContractNumber(contractInfoVO.getContractNumber());
                            if (Optional.ofNullable(contractInfoVO.getBusinessBlock()).isPresent()) {
                                claimPageVO.setBusinessBlock(contractInfoVO.getBusinessBlock());
                                claimPageVO.setBusinessBlockTxt(businessBlockDict.get(contractInfoVO.getBusinessBlock()));
                            }
                            if (Optional.ofNullable(contractInfoVO.getSkillType()).isPresent()) {
                                claimPageVO.setSkillType(contractInfoVO.getSkillType());
                                claimPageVO.setSkillTypeTxt(skillTypeDict.get(contractInfoVO.getSkillType()));
                            }

                        }
                        // 5.2.3、OA【合同台账明细】
                        ContractPaymentVO contractPaymentVO = contractPaymentVOMap.getOrDefault(c.getContractPaymentId(), null);
                        if (Optional.ofNullable(contractPaymentVO).isPresent()) {
                            claimPageVO.setContractPayment(contractPaymentVO.getPaymentName());
                            claimPageVO.setPaymentMoney(MoneyUtils.getInstance().transType(contractPaymentVO.getPrice()));
                        }
                        claimPageVO.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, c.getPaymentType()));
                        claimPageVO.setMarginTypeTxt(EnumUtils.getNameByValue(MarginTypeEnum.class, c.getMarginType()));
                        claimPageVO.setClaimantDate(DateUtils.getStringDate(c.getClaimantDate(),DateUtils.SIMPLE_DATE_FORMAT));
                        claimPageVO.setClaimMoney(MoneyUtils.getInstance().strTransType(claimPageVO.getClaimMoney()));
                        return claimPageVO;
                    }).collect(Collectors.toList());
                    pageVO.setClaimPageVOList(claimPageVOList);
                }

                return pageVO;
            }).sorted(Comparator.comparing(ProjectPaymentPageVO::getPaymentDate, Comparator.nullsLast(String::compareTo)).reversed())
                    .collect(Collectors.toList());
        }

        projectPaymentVoPage.setRecords(projectPaymentVOList);
        projectPaymentVoPage.setTotal(pageIds.getTotal());
        statistics(projectPaymentVoPage, dto,contractPaymentVOMap);
        return projectPaymentVoPage;
    }

    private void statistics(StatisticsPage<ProjectPaymentPageVO> projectPaymentVoPage,
                            ProjectPaymentDTO dto, Map<Long,
                            ContractPaymentVO> contractPaymentVoMap) {
        if (projectPaymentVoPage.getTotal() == 0) {
            return;
        }

        List<Long> ids = baseMapper.findPageIdsV1(dto);

        // 设置批次大小
        int batchSize = 3000;
        List<List<Long>> partitionedIds = Lists.partition(ids, batchSize);
        Map<String, BigDecimal> resultMap = new ConcurrentHashMap<>(3);
        // 分批次查询 ProjectPayment
        for (List<Long> batchIds : partitionedIds) {
            List<ProjectPayment> projectPaymentList = baseMapper.selectList(
                    Wrappers.<ProjectPayment>lambdaQuery().in(ProjectPayment::getId, batchIds));

            List<ProjectPaymentClaim> claimList = projectPaymentClaimMapper.selectList(
                    Wrappers.<ProjectPaymentClaim>lambdaQuery().in(ProjectPaymentClaim::getProjectPaymentId, batchIds));
            Map<Long, List<ProjectPaymentClaim>> claimMap = CollStreamUtil.groupByKey(claimList,ProjectPaymentClaim::getProjectPaymentId);

            resultMap.put("paymentAmountTotal", BigDecimal.ZERO);
            resultMap.put("paymentMoneyTotal", BigDecimal.ZERO);
            resultMap.put("claimMoneyTotal", BigDecimal.ZERO);
            projectPaymentList.forEach(p -> {
                BigDecimal paymentAmount = p.getPaymentAmount() == null ? BigDecimal.ZERO : p.getPaymentAmount();
                resultMap.computeIfPresent("paymentAmountTotal", (key, oldValue) -> oldValue.add(paymentAmount));
                List<ProjectPaymentClaim> paymentClaimList = claimMap.getOrDefault(p.getId(), ImmutableList.of());
                paymentClaimList.forEach(c -> {
                    BigDecimal claimMoney = c.getClaimMoney() == null ? BigDecimal.ZERO : c.getClaimMoney();
                    resultMap.computeIfPresent("claimMoneyTotal", (key, oldValue) -> oldValue.add(claimMoney));
                    ContractPaymentVO contractPaymentVO = contractPaymentVoMap.get(c.getContractPaymentId());
                    BigDecimal paymentMoney;
                    if (contractPaymentVO != null) {
                        paymentMoney = contractPaymentVO.getPrice() == null ? BigDecimal.ZERO : contractPaymentVO.getPrice();
                    }else {
                        paymentMoney = StringUtils.isBlank(c.getPaymentMoney()) ? BigDecimal.ZERO : new BigDecimal(c.getPaymentMoney());
                    }
                    resultMap.computeIfPresent("paymentMoneyTotal", (key, oldValue) -> oldValue.add(paymentMoney));
                });
            });
        }
        projectPaymentVoPage.setStatistics(resultMap);
    }

    /**
     * 导出Excel选中数据
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    @Override
    public List export(ProjectPaymentDTO projectPaymentDTO) {
        List<ProjectPaymentClaimExcelVO> projectPaymentVOList = new ArrayList<>();
        // 导出模板表
        if (CollUtil.isNotEmpty(projectPaymentDTO.getIds())) {
            List<ProjectPaymentExcelVO> projectPaymentExcelVOList = new ArrayList<>();
            if (NumberUtils.LONG_MINUS_ONE.equals(projectPaymentDTO.getIds().get(NumberUtils.INTEGER_ZERO))) {
                ProjectPaymentExcelVO projectPaymentExcelVO = ProjectPaymentExcelVO.builder()
                        .paymentDate(new SimpleDateFormat(DateUtils.SIMPLE_DATE_FORMAT_2).format(new Date()))
                        .build();
                projectPaymentExcelVOList.add(projectPaymentExcelVO);
                return projectPaymentExcelVOList;
            }
        }
        // 条件查询 单据状态
        questionStatus(projectPaymentDTO);
        //【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<Long> pageIds = baseMapper.findPageIds(condition(projectPaymentDTO));
        if (CollectionUtil.isEmpty(pageIds)) {
            return projectPaymentVOList;
        }
        // 1、获取 OA 的【项目台帐】
        List<ProjectAccountVO> projectAccountList = dbApi.getProjectAccountByName(ImmutableList.of());
        Map<Long, ProjectAccountVO> projectAccountVOMap = projectAccountList.stream().collect(Collectors.toMap(ProjectAccountVO::getProjectId, a -> a, (a, b) -> a));
        // 2、获取 OA 的【归属合同收款明细】
        List<ContractInfoVO> ContractInfoList = dbApi.getContractName(null);
        Map<Long, ContractInfoVO> contractInfoVOMap = ContractInfoList.stream().collect(Collectors.toMap(ContractInfoVO::getId, a -> a, (a, b) -> a));
        // 3、获取业务板块字典值
        Map<Integer, String> businessBlockDict = dbApi.projectDict(DictConstant.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        // 4、获取技术类型字典值
        Map<Integer, String> skillTypeDict = dbApi.projectDict(DictConstant.SKILL_TYPE_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        projectPaymentVOList = baseMapper.findListByIds(pageIds);
        if (CollectionUtil.isNotEmpty(projectPaymentVOList)){
            projectPaymentVOList.stream().forEach(p->{
                p.setPaymentCompanyTxt(attributableSubjectMap.getOrDefault(p.getPaymentCompany(),StrUtil.EMPTY));
                p.setClaimStatusTxt(EnumUtils.getNameByValue(ClaimStatusEnum.class, p.getClaimStatus()));
                p.setLockStatusTxt(EnumUtils.getNameByValue(LockStatusEnum.class, p.getLockStatus()));
                p.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));

                // 5.2.1、OA【项目台帐】
                ProjectAccountVO projectAccount = projectAccountVOMap.getOrDefault(p.getProjectId(), null);
                if (Optional.ofNullable(projectAccount).isPresent()) {
                    p.setProjectName(projectAccount.getProjectName());
                    p.setProjectNumber(projectAccount.getProjectNumber());
                }
                // 5.2.2、OA【归属合同明细】
                ContractInfoVO contractInfoVO = contractInfoVOMap.getOrDefault(p.getContractId(), null);
                if (Optional.ofNullable(contractInfoVO).isPresent()) {
                    p.setContractName(contractInfoVO.getContractName());
                    p.setContractNumber(contractInfoVO.getContractNumber());
                    p.setBusinessBlock(contractInfoVO.getBusinessBlock());
                    p.setBusinessBlockTxt(businessBlockDict.get(contractInfoVO.getBusinessBlock()));
                    p.setSkillType(contractInfoVO.getSkillType());
                    p.setSkillTypeTxt(skillTypeDict.get(contractInfoVO.getSkillType()));
                }
                p.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, p.getPaymentType()));
                p.setMarginTypeTxt(EnumUtils.getNameByValue(MarginTypeEnum.class, p.getMarginType()));
            });
        }

        return projectPaymentVOList
                .stream()
                .distinct()
                .sorted(Comparator.comparing(ProjectPaymentClaimExcelVO::getDocumentNumber, Comparator.nullsLast(String::compareTo)).reversed())
                .collect(Collectors.toList());
    }


    @Override
    public ApiResult synchronizeData(){
        List<ProjectPayment> projectPayments = dbApi.selPayment();

        List<ProjectPaymentClaim> projectPaymentClaims = dbApi.selPaymentClaim();
        Map<Long, ProjectPaymentClaim> claimMap = projectPaymentClaims.stream().collect(Collectors.toMap(ProjectPaymentClaim::getId,a->a,(a,b)->a));

        projectPayments.forEach(projectPayment ->{
            ProjectPaymentClaim claim = claimMap.getOrDefault(projectPayment.getId(), null);
            projectPayment.setId(IdWorker.getId());

            projectPayment.setDocumentNumber(RedisConstant.PROJECT_DOCUMENT_NUMBER_PREFIX +
                    projectPayment.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtils.YEAR_MONTH)) +
                    String.format("%04d", stringRedisTemplate.opsForValue().increment(RedisConstant.PROJECT_DOCUMENT_NUMBER))
        );
            projectPayment.setSourceOa(NumberUtils.INTEGER_ONE);
            baseMapper.insert(projectPayment);

            if (Optional.ofNullable(claim).isPresent()) {
                claim.setId(IdWorker.getId());
                claim.setProjectPaymentId(projectPayment.getId());
                claim.setSourceOa(NumberUtils.INTEGER_ONE);
                projectPaymentClaimMapper.insert(claim);
            }
        });

        return ApiResult.success("项目回款跟踪表-处理成功！" );
    }

    @Override
    public PushResultVO push(List<Long> ids) {
        StringBuilder sb = new StringBuilder();
        AtomicInteger succeed = new AtomicInteger();
        AtomicInteger error = new AtomicInteger();
        List<PushErrorReasonVO> errorReasonList = new ArrayList<>();
        // 1、获取待推送项目详情
        List<ProjectPaymentPushVO> pushVOList = baseMapper.selPushVo(ids);
        if (CollUtil.isEmpty(pushVOList)) {
            List<PushErrorReasonVO> errorList = new ArrayList<>();
            for (Long id : ids) {
                ProjectPayment projectPayment = baseMapper.selectById(id);
                if (Optional.ofNullable(projectPayment).isPresent()) {
                    projectPayment.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    baseMapper.updateById(projectPayment);
                    // 记录日志
                    sb.append("【").append(projectPayment.getDocumentNumber()).append("】【")
                            .append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                }
                PushErrorReasonVO errorReason = PushErrorReasonVO.builder()
                        .id(String.valueOf(id))
                        .errorReason(VoucherRecordEnum.ERROR_STR.getName())
                        .build();
                errorList.add(errorReason);
            }
            // 日志记录
            if (sb.length() > NumberUtils.INTEGER_ZERO) {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.PUSH,
                        OperationEnum.PUSH.getName() + sb.toString());
            }
            return PushResultVO.builder()
                    .success(NumberUtils.INTEGER_ZERO)
                    .error(ids.size())
                    .errorReasonList(errorList).build();
        }
        //传入的ids和获取到的项目数据个数是否一致
        Integer notPushNum = NumberUtils.INTEGER_ZERO;
        if (ids.size() != pushVOList.size()){
            notPushNum = ids.size() - pushVOList.size();
        }
        //获取所有发薪主体
        List<Integer> salaryPaidSubjectList = pushVOList.stream().map(ProjectPaymentPushVO::getPaymentCompany).distinct().collect(Collectors.toList());
        //根据发薪主体分组
        Map<Integer, List<ProjectPaymentPushVO>> listMap = pushVOList.stream().collect(Collectors.groupingBy(ProjectPaymentPushVO::getPaymentCompany));

        //获取帐套信息,key:发薪主体字典id，value:帐套信息
        Map<String, AccountingSet> accountingSetMap = accountingSetService.getAccountingSet();
        //获取客户信息,key:客户id，value:客户信息
        List<CustomerAccountVO> customerAccountList = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY);
        Map<String, CustomerAccountVO> tCustomerAccountMap = customerAccountList.stream().collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));

        //获取认领信息详情
        List<ProjectPaymentClaim> paymentClaimList = projectPaymentClaimMapper.selectList(Wrappers.<ProjectPaymentClaim>lambdaQuery()
                .in(ProjectPaymentClaim::getProjectPaymentId, ids));
        Map<Long, List<ProjectPaymentClaim>> claimMap = paymentClaimList.stream().collect(Collectors.groupingBy(ProjectPaymentClaim::getProjectPaymentId));

        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        salaryPaidSubjectList.forEach(s -> {
            List<ProjectPaymentPushVO> projectPayments = listMap.get(s);
            //获取帐套信息
            AccountingSet accountingSet = accountingSetMap.get(attributableSubjectMap.getOrDefault(s,null));
            if (Optional.ofNullable(accountingSet).isPresent()) {
                //获取登录T+token
                String token = openApi.login(accountingSet.getAccountingAccount(), accountingSet.getAccountingPassword(), accountingSet.getAccountingCode());
                projectPayments.forEach(p -> {
                    //凭证实体数据组装
                    VoucherDTO.VoucherDTOBuilder voucherDTOBuilder = VoucherDTO.builder();
                    //制单日期为归属月份的最后一日天
                    LocalDate date = LocalDate.parse(p.getPaymentDate().replace("-", ""), DateTimeFormatter.BASIC_ISO_DATE);
                    LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
                    voucherDTOBuilder
                            .ExternalCode(String.valueOf(p.getId()))
                            .VoucherDate(lastDay.toString())
                            .DocType(VoucherDocTypeDTO.builder().Code(VoucherRecordEnum.DOCTYPE.getName()).build());

                    //凭证明细
                    List<VoucherDetailDTO> detailDtoArrayList = new ArrayList<>();
                    //认领信息
                    List<ProjectPaymentClaim> claimList = claimMap.getOrDefault(p.getId(), ImmutableList.of());
                    //保证金
                    List<ProjectPaymentClaim> marginList = claimList.stream()
                            .filter(claim -> PaymentTypeEnum.MARGIN.getValue().equals(claim.getPaymentType())).collect(Collectors.toList());
                    //c端回款
                    List<ProjectPaymentClaim> cPaymentList = claimList.stream()
                            .filter(claim -> PaymentTypeEnum.C_PAYMENT.getValue().equals(claim.getPaymentType())).collect(Collectors.toList());
                    //其他回款
                    List<ProjectPaymentClaim> otherPaymentList = claimList.stream()
                            .filter(claim -> PaymentTypeEnum.OTHER_PAYMENT.getValue().equals(claim.getPaymentType())).collect(Collectors.toList());
                    //利息
                    List<ProjectPaymentClaim> interestList = claimList.stream()
                            .filter(claim -> PaymentTypeEnum.INTEREST.getValue().equals(claim.getPaymentType())).collect(Collectors.toList());


                    //摘要取客户名称/企业名称，先取客户名称，无值取企业名称
                    String customer = Optional.ofNullable(p.getCustomerName()).isPresent()  ? p.getCustomerName() : p.getEnterpriseName();
                    //借方凭证
                    if (marginList.size()==claimList.size()){
                        debitVoucher(detailDtoArrayList,p, VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName());
                    }else if (cPaymentList.size()==claimList.size()){
                        debitVoucher(detailDtoArrayList,p, VoucherRecordEnum.PAYMENT_C_SUMMARY.getName());
                    }else if (otherPaymentList.size()==claimList.size()){
                        debitVoucher(detailDtoArrayList,p, VoucherRecordEnum.PAYMENT_OTHER_SUMMARY.getName());
                    }else if (interestList.size()==claimList.size()){
                        debitVoucher(detailDtoArrayList,p, VoucherRecordEnum.PAYMENT_INTEREST_SUMMARY.getName());
                    }else if (interestList.size() == 0){
                        debitVoucher(detailDtoArrayList,p, VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName());
                    }else {
                        debitVoucherTwo(detailDtoArrayList,p, interestList);
                    }
                    // 贷方凭证
                    if (CollectionUtil.isNotEmpty(claimList)) {
                        claimList.forEach(c ->{
                            creditVoucher(detailDtoArrayList, p, c,tCustomerAccountMap);
                        });
                    }
                    VoucherDTO voucher = voucherDTOBuilder.Entrys(detailDtoArrayList).build();
                    //凭证创建
                    try {
                        String result = openApi.createVoucher(token, voucher);
                        ProjectPayment projectPayment = BeanUtil.copyProperties(p, ProjectPayment.class);
                        if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                            projectPayment.setPushStatus(PushStatusEnum.SUCCESS_PUSH.getValue());
                            baseMapper.updateById(projectPayment);
                            succeed.incrementAndGet();
                            // 记录日志
                            sb.append("【").append(p.getDocumentNumber()).append("】【").append(PushStatusEnum.SUCCESS_PUSH.getName()).append("】");
                        } else {
                            projectPayment.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                            baseMapper.updateById(projectPayment);
                            error.incrementAndGet();
                            errorReasonList.add(PushErrorReasonVO.builder()
                                    .id(String.valueOf(projectPayment.getId()))
                                    .errorReason(result)
                                    .build());
                            // 记录日志
                            sb.append("【").append(p.getDocumentNumber()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                        }
                    } catch (Exception e) {
                        throw new BusinessException("凭证创建失败");
                    }
                });
            } else {
                projectPayments.stream().forEach(p->{
                    ProjectPayment projectPayment = BeanUtil.copyProperties(p, ProjectPayment.class);
                    projectPayment.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    baseMapper.updateById(projectPayment);
                    errorReasonList.add(PushErrorReasonVO.builder()
                            .id(String.valueOf(projectPayment.getId()))
                            .errorReason(VoucherRecordEnum.ACCOUNTING_ERROR.getName())
                            .build());
                    // 记录日志
                    sb.append("【").append(p.getDocumentNumber()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                    error.incrementAndGet();
                });
            }
        });
        // 日志记录
        if (sb.length() > NumberUtils.INTEGER_ZERO) {
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.PUSH,
                    OperationEnum.PUSH.getName() + sb.toString());
        }
        return PushResultVO.builder()
                .success(succeed.get())
                .error(error.get()+notPushNum)
                .errorReasonList(errorReasonList).build();
    }

    /**
     * 借方凭证（多条）
     * @param detailDtoArrayList
     * @param p
     * @param interestList
     * @return
     */
    private List<VoucherDetailDTO> debitVoucherTwo(List<VoucherDetailDTO> detailDtoArrayList, ProjectPaymentPushVO p, List<ProjectPaymentClaim> interestList) {
        //获取银行信息,key:银行付款字典值，value:银行信息
        List<BackAccountVO> bankAccountList = dbApi.getBankAccount(CharSequenceUtil.EMPTY);
        Map<String, String> tBankAccountMap = bankAccountList.stream().collect(Collectors.toMap(BackAccountVO::getName, BackAccountVO::getCode, (a, b) -> a));
        //摘要取客户名称/企业名称，先取客户名称，无值取企业名称
        String customer = Optional.ofNullable(p.getCustomerName()).isPresent()  ? p.getCustomerName() : p.getEnterpriseName();
        // 项目回款
        VoucherDetailDTO collectionVoucherDetailDTO = VoucherDetailDTO.builder()
                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                .Account(EntryAccountDTO.builder().Code(Optional.ofNullable(p.getBankAccount()).isPresent() ? tBankAccountMap.getOrDefault(p.getBankAccount(), CharSequenceUtil.EMPTY) : CharSequenceUtil.EMPTY).build())
                .AmountDr(MoneyUtils.getInstance().keepTwoDecimal(p.getPaymentAmount()))
                .Summary(VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                .build();
        detailDtoArrayList.add(collectionVoucherDetailDTO);
        //保证金利息收入
        BigDecimal interest = interestList.stream()
                .filter(c -> null != c.getClaimMoney())
                .map(ProjectPaymentClaim::getClaimMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        VoucherDetailDTO interestVoucherDetailDTO = VoucherDetailDTO.builder()
                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                .Account(EntryAccountDTO.builder().Code(Optional.ofNullable(p.getBankAccount()).isPresent() ? tBankAccountMap.getOrDefault(p.getBankAccount(), CharSequenceUtil.EMPTY) : CharSequenceUtil.EMPTY).build())
                .AmountDr(MoneyUtils.getInstance().keepTwoDecimal(interest.negate()))
                .Summary(VoucherRecordEnum.PAYMENT_INTEREST_SUMMARY.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                .build();
        detailDtoArrayList.add(interestVoucherDetailDTO);
        return detailDtoArrayList;
    }


    /**
     * 借方凭证（单条）
     * @param detailDtoArrayList
     * @param p
     * @param paymentType
     * @return
     */
    public List<VoucherDetailDTO> debitVoucher(List<VoucherDetailDTO> detailDtoArrayList,ProjectPaymentPushVO p,String paymentType){
        //获取银行信息,key:银行付款字典值，value:银行信息
        List<BackAccountVO> bankAccountList = dbApi.getBankAccount(CharSequenceUtil.EMPTY);
        Map<String, String> tBankAccountMap = bankAccountList.stream().collect(Collectors.toMap(BackAccountVO::getName, BackAccountVO::getCode, (a, b) -> a));
        //摘要取客户名称/企业名称，先取客户名称，无值取企业名称
        String customer = Optional.ofNullable(p.getCustomerName()).isPresent()  ? p.getCustomerName() : p.getEnterpriseName();

        VoucherDetailDTO voucherDetailDTO = VoucherDetailDTO.builder()
                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                .Account(EntryAccountDTO.builder().Code(Optional.ofNullable(p.getBankAccount()).isPresent() ? tBankAccountMap.getOrDefault(p.getBankAccount(), CharSequenceUtil.EMPTY) : CharSequenceUtil.EMPTY).build())
                .AmountDr(MoneyUtils.getInstance().keepTwoDecimal(p.getPaymentAmount()))
                .Summary(paymentType + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getPaymentDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + customer)
                .build();
        detailDtoArrayList.add(voucherDetailDTO);
        return detailDtoArrayList;
    }

    /**
     * 贷方凭证
     * @param detailDtoArrayList
     * @param p
     * @param c
     * @param tCustomerAccountMap
     * @return
     */
    public List<VoucherDetailDTO> creditVoucher(List<VoucherDetailDTO> detailDtoArrayList,ProjectPaymentPushVO p,ProjectPaymentClaim c,Map<String, CustomerAccountVO> tCustomerAccountMap){
        //认领信息关联客户
        String relatedCustomerName = c.getRelatedCustomerName();
        List<AuxiliaryDTO> relatedCustomerAuxiliary = auxiliary(relatedCustomerName, c, tCustomerAccountMap);
        //回款信息客户名称/企业名称，先取客户名称，无值取企业名称
        String customer = Optional.ofNullable(p.getCustomerName()).isPresent()  ? p.getCustomerName() : p.getEnterpriseName();
        List<AuxiliaryDTO> customerAuxiliary = auxiliary(customer, c, tCustomerAccountMap);

        if (customer.equals(relatedCustomerName)){
            //场景一：【回款类型】等于项目回款
            if (PaymentTypeEnum.PROJECT_PAYMENT.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName(),
                        p.getPaymentDate(),relatedCustomerAuxiliary);
            } else if (PaymentTypeEnum.MARGIN.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.OTHER_ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName(),
                        p.getPaymentDate(),relatedCustomerAuxiliary);
            } else if (PaymentTypeEnum.C_PAYMENT.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        customer,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_C_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
            } else if (PaymentTypeEnum.OTHER_PAYMENT.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        customer,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_OTHER_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
            }
        }else{
            if (PaymentTypeEnum.PROJECT_PAYMENT.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney().negate(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_COLLECTION_SUMMARY.getName(),
                        p.getPaymentDate(),relatedCustomerAuxiliary);
            } else if (PaymentTypeEnum.MARGIN.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.OTHER_ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney().negate(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.OTHER_ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        relatedCustomerName,
                        VoucherSubjectIdEnum.OTHER_ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_MARGIN_SUMMARY.getName(),
                        p.getPaymentDate(),relatedCustomerAuxiliary);
            } else if (PaymentTypeEnum.C_PAYMENT.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        customer,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_C_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
            } else if (PaymentTypeEnum.OTHER_PAYMENT.getValue().equals(c.getPaymentType())) {
                creditVoucherDetail(detailDtoArrayList,
                        c.getClaimMoney(),
                        c.getProjectName(),
                        customer,
                        VoucherSubjectIdEnum.ACCOUNTS_RECEIVABLE.getName(),
                        VoucherRecordEnum.PAYMENT_OTHER_SUMMARY.getName(),
                        p.getPaymentDate(),customerAuxiliary);
            }
        }
        return detailDtoArrayList;
    }

    /**
     * 贷方凭证明细
     * @param detailDtoArrayList
     * @param claimMoney
     * @param relatedCustomersName
     * @param voucherSubject
     * @param paymentType
     * @param paymentDate
     * @param auxiliaryDTOList
     * @return
     */
    public List<VoucherDetailDTO> creditVoucherDetail(List<VoucherDetailDTO> detailDtoArrayList, BigDecimal claimMoney, String projectName, String relatedCustomersName, String voucherSubject, String paymentType, String paymentDate, List<AuxiliaryDTO> auxiliaryDTOList){
        VoucherDetailDTO accountsReceivableCr = VoucherDetailDTO.builder()
                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                .Account(EntryAccountDTO.builder().Code(voucherSubject).build())
                .AmountCr(MoneyUtils.getInstance().keepTwoDecimal(claimMoney))
                .Summary(paymentType + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + paymentDate + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + relatedCustomersName + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + projectName)
                .AuxInfos(auxiliaryDTOList)
                .build();
        detailDtoArrayList.add(accountsReceivableCr);
        return detailDtoArrayList;
    }

    /**
     * 辅助项
     * @param customerName
     * @param c
     * @param tCustomerAccountMap
     * @return
     */
    public List<AuxiliaryDTO> auxiliary(String customerName,ProjectPaymentClaim c, Map<String, CustomerAccountVO> tCustomerAccountMap){
        //凭证创建辅佐项集合
        List<AuxiliaryDTO> auxiliaryDTOList = new ArrayList<>();
        CustomerAccountVO tCustomer = tCustomerAccountMap.get(customerName);
        AuxiliaryDTO auxiliaryDto = new AuxiliaryDTO();
        if (Optional.ofNullable(tCustomer).isPresent()) {
            auxiliaryDto.setAuxAccCustomer(AuxAccCustomerDTO.builder().Code(tCustomer.getCustomerCode()).build());
        } else {
            //如果客户名称不存在。来往单位  默认-厦门硕翔计算机技术有限公司
            auxiliaryDto.setAuxAccCustomer(AuxAccCustomerDTO.builder().Code("CW-************").build());
        }
        auxiliaryDto.setAuxAccProject(AuxAccProjectDTO.builder().Code(c.getProjectCode()).build());
        auxiliaryDto.setExAuxAcc1(ExAuxAcc1.builder().Code("01").build());

        auxiliaryDTOList.add(auxiliaryDto);
        return auxiliaryDTOList;
    }

    public void exportResponse(HttpServletResponse response, ProjectPaymentDTO projectPaymentDTO) {
        List<ProjectPaymentClaimExcelVO> projectPaymentVOList = new ArrayList<>();
        // 导出模板表
        if (CollUtil.isNotEmpty(projectPaymentDTO.getIds())) {
            List<ProjectPaymentExcelVO> projectPaymentExcelVOList = new ArrayList<>();
            if (NumberUtils.LONG_MINUS_ONE.equals(projectPaymentDTO.getIds().get(NumberUtils.INTEGER_ZERO))) {
                ProjectPaymentExcelVO projectPaymentExcelVO = ProjectPaymentExcelVO.builder()
                        .paymentDate(new SimpleDateFormat(DateUtils.SIMPLE_DATE_FORMAT_2).format(new Date()))
                        .build();
                projectPaymentExcelVOList.add(projectPaymentExcelVO);
                // 文件导出
                WriteSheet sheet1 = EasyExcel.writerSheet(0, "项目回款跟踪表模版")
                        .head(ProjectPaymentExcelVO.class)
                        .build();
                try {
                    // 文件名
                    String fileName = URLEncoder.encode("项目回款跟踪表模版", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
                    // 写入响应体信息
                    response.setContentType(CONTENT_TYPE_SHEET);
                    response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                    response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
                    // 设置sheet名
                    ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
                    // 写入excelWriter
                    excelWriter.write(projectPaymentExcelVOList, sheet1);
                    // 关闭excelWriter
                    excelWriter.finish();
                    response.flushBuffer();
                    return;
                } catch (Exception e) {
                    log.error("项目回款跟踪表模版失败", e);
                }
            }
        }
        // 条件查询 单据状态
        questionStatus(projectPaymentDTO);
        //【客户名称】无值且项目干系人未认领，该数据所有人可见
        List<Long> pageIds = baseMapper.findPageIds(condition(projectPaymentDTO));
        if (CollectionUtil.isNotEmpty(pageIds)) {
            // 1、获取 OA 的【项目台帐】
            List<ProjectAccountVO> projectAccountList = dbApi.getProjectAccountByName(ImmutableList.of());
            Map<Long, ProjectAccountVO> projectAccountVOMap = projectAccountList.stream().collect(Collectors.toMap(ProjectAccountVO::getProjectId, a -> a, (a, b) -> a));
            // 2、获取 OA 的【归属合同收款明细】
            List<ContractInfoVO> ContractInfoList = dbApi.getContractName(null);
            Map<Long, ContractInfoVO> contractInfoVOMap = ContractInfoList.stream().collect(Collectors.toMap(ContractInfoVO::getId, a -> a, (a, b) -> a));
            // 3、获取业务板块字典值
            Map<Integer, String> businessBlockDict = dbApi.projectDict(DictConstant.BUSINESS_BLOCK_ID)
                    .stream()
                    .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
            // 4、获取技术类型字典值
            Map<Integer, String> skillTypeDict = dbApi.projectDict(DictConstant.SKILL_TYPE_ID)
                    .stream()
                    .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
            List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
            Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

            projectPaymentVOList = baseMapper.findListByIds(pageIds);
            if (CollectionUtil.isNotEmpty(projectPaymentVOList)){
                projectPaymentVOList.stream().forEach(p->{
                    p.setPaymentCompanyTxt(attributableSubjectMap.getOrDefault(p.getPaymentCompany(),StrUtil.EMPTY));
                    p.setClaimStatusTxt(EnumUtils.getNameByValue(ClaimStatusEnum.class, p.getClaimStatus()));
                    p.setLockStatusTxt(EnumUtils.getNameByValue(LockStatusEnum.class, p.getLockStatus()));
                    p.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));

                    // 5.2.1、OA【项目台帐】
                    ProjectAccountVO projectAccount = projectAccountVOMap.getOrDefault(p.getProjectId(), null);
                    if (Optional.ofNullable(projectAccount).isPresent()) {
                        p.setProjectName(projectAccount.getProjectName());
                        p.setProjectNumber(projectAccount.getProjectNumber());
                    }
                    // 5.2.2、OA【归属合同明细】
                    ContractInfoVO contractInfoVO = contractInfoVOMap.getOrDefault(p.getContractId(), null);
                    if (Optional.ofNullable(contractInfoVO).isPresent()) {
                        p.setContractName(contractInfoVO.getContractName());
                        p.setContractNumber(contractInfoVO.getContractNumber());
                        p.setBusinessBlock(contractInfoVO.getBusinessBlock());
                        p.setBusinessBlockTxt(businessBlockDict.get(contractInfoVO.getBusinessBlock()));
                        p.setSkillType(contractInfoVO.getSkillType());
                        p.setSkillTypeTxt(skillTypeDict.get(contractInfoVO.getSkillType()));
                    }
                    p.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, p.getPaymentType()));
                    p.setMarginTypeTxt(EnumUtils.getNameByValue(MarginTypeEnum.class, p.getMarginType()));
                });
            }

            projectPaymentVOList
                    .stream()
                    .distinct()
                    .sorted(Comparator.comparing(ProjectPaymentClaimExcelVO::getDocumentNumber, Comparator.nullsLast(String::compareTo)).reversed())
                    .collect(Collectors.toList());
        }
        // 文件导出
        WriteSheet sheet1 = EasyExcel.writerSheet(0, "项目回款跟踪表")
                .head(ProjectPaymentClaimExcelVO.class)
                .registerWriteHandler(new MergeCellStrategyHandler(true, 1, Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 25, 26, 27, 28, 29, 30, 31)))
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .build();
        try {
            // 文件名
            String fileName = URLEncoder.encode("项目回款跟踪表", StandardCharsets.UTF_8.name()).replaceAll("\\+", "%20");
            // 写入响应体信息
            response.setContentType(CONTENT_TYPE_SHEET);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.setHeader(PARAM_CONTENT_DISPOSITION, CONTENT_DISPOSITION + fileName + XLSX_SUFFIX);
            // 设置sheet名
            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream()).autoCloseStream(Boolean.FALSE).build();
            // 写入excelWriter
            excelWriter.write(projectPaymentVOList, sheet1);
            // 关闭excelWriter
            excelWriter.finish();
            response.flushBuffer();
        } catch (Exception e) {
            log.error("导出项目回款跟踪表失败", e);
        }
    }
}
