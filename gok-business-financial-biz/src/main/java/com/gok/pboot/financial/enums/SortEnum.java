package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 应收账款排序规则属性 Enum
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Getter
public enum SortEnum implements ValueEnum<Integer> {

    /**
     * 项目编码
     */
    DEFAULT(0, "project_no"),

    /**
     * 开票待回款金额
     */
    INVOICING_COLLECTED(1, "invoicing_collected"),

    /**
     * 合同待回款金额
     */
    CUSTOMER_COLLECTED(2, "customer_collected"),

    /**
     * 合同待开票金额
     */
    CUSTOMER_INVOICING(3, "customer_invoicing"),

    /**
     * 合同金额
     */
    CONTRACT_MONEY(4, "contract_money");

    private final Integer value;

    private final String name;

    SortEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
