package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 归属合同收款明细
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractInfoVO {

    /**
     * 合同id
     */
    private Long id;

    /**
     * 合同编码
     */
    private String contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 业务板块
     */
    private Integer businessBlock;

    /**
     * 业务板块
     */
    private String businessBlockTxt;

    /**
     * 技术类型
     */
    private Integer skillType;

    /**
     * 技术类型
     */
    private String skillTypeTxt;

    /**
     * 客户经理id
     */
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    private String salesmanUserName;


    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目编码
     */
    private String projectNumber;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 回款一级部门
     */
    private Long paymentDeptId;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private Long paymentSecondaryDeptId;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 客户名称
     */
    private String customerName;
}
