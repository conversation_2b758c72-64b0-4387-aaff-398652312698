package com.gok.pboot.financial.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.EducationPayment;
import com.gok.pboot.financial.dto.EducationDTO;
import com.gok.pboot.financial.dto.EducationPaymentClaimDTO;
import com.gok.pboot.financial.dto.EducationPaymentDTO;
import com.gok.pboot.financial.dto.EducationPaymentExcelDTO;
import com.gok.pboot.financial.service.IEducationPaymentService;
import com.gok.pboot.financial.vo.EducationPaymentClaimVO;
import com.gok.pboot.financial.vo.EducationVO;
import com.gok.pboot.financial.vo.PaymentCompanyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 教育回款跟踪
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@RestController
@RequestMapping("/education-payment")
@RequiredArgsConstructor
@Api(tags = "教育回款跟踪")
public class EducationPaymentController {

    private final IEducationPaymentService educationPaymentService;

    private final DBApi dbApi;

    /**
     * 模糊查询带分页
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link R}<{@link Page}<{@link EducationVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm','EDUCATION_COLLECTION_TRACKING')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<EducationVO>> findPage(@RequestBody EducationDTO educationDTO) {
        Page<EducationPayment> page = new Page<>(educationDTO.getCurrent(), educationDTO.getSize());
        return R.ok(educationPaymentService.findPage(page, educationDTO));
    }

    /**
     * 导出所选数据（异步导出）
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link List}
     */
    @ResponseExcel(async = true, name = "教育回款跟踪表", functionEnum = FunctionEnum.FINANCIAL_EDUCATION_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/export')")
    @ApiOperation(value = "导出所选数据", notes = "导出所选数据")
    public List export(@RequestBody EducationDTO educationDTO) {
        return educationPaymentService.export(educationDTO);
    }

    @ResponseExcel(name = "教育回款跟踪表", dynamicHeader = true)
    @PostMapping("pms/export")
    @PreAuthorize("@pms.hasPermission('pms/EducationCollectionTrackingForm/export')")
    @ApiOperation(value = "导出所选数据", notes = "导出所选数据")
    public List pmsExport(@RequestBody EducationDTO educationDTO) {
        return educationPaymentService.export(educationDTO);
    }

    /**
     * 新增教育回款
     *
     * @param educationPaymentDTO {@link EducationPaymentDTO}
     * @return {@link R}
     */
    @PostMapping("/save")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/add','pms/EducationCollectionTrackingForm/add')")
    @ApiOperation(value = "新增教育回款", notes = "新增教育回款")
    public R<String> save(@Valid @RequestBody EducationPaymentDTO educationPaymentDTO) {
        return educationPaymentService.save(educationPaymentDTO) > NumberUtils.INTEGER_ZERO ? R.ok("新增成功") : R.failed("新增失败");
    }

    /**
     * 汇总
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}
     */
    @PutMapping("/summary")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/summary','pms/EducationCollectionTrackingForm/summary')")
    @ApiOperation(value = "汇总", notes = "汇总")
    public R<String> summary(@Valid @RequestBody PaymentDTO paymentDTO) {
        return Boolean.TRUE.equals(educationPaymentService.summary(paymentDTO)) ? R.ok("更新成功") : R.failed("更新失败");
    }

    /**
     * 取消汇总
     *
     * @param id 教育回款id
     * @return {@link R}
     */
    @PutMapping("/unSummary/{id}")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/unsummary','pms/EducationCollectionTrackingForm/unsummary')")
    @ApiOperation(value = "取消汇总", notes = "取消汇总")
    public R<String> unSummary(@PathVariable("id") Long id) {
        return educationPaymentService.unSummary(id);
    }

    /**
     * 根据id获取教育回款详情
     *
     * @param id 教育回款id
     * @return {@link R}<{@link EducationPaymentClaimVO}>
     */
    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "根据id获取教育回款详情", notes = "根据id获取教育回款详情")
    public R<EducationPaymentClaimVO> getOne(@PathVariable("id") Long id) {
        return R.ok(educationPaymentService.getOne(id));
    }

    /**
     * 根据id更新教育回款详情
     *
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     * @return {@link R}
     */
    @PutMapping("/update")
    @ApiOperation(value = "根据id更新教育回款详情", notes = "根据id更新教育回款详情")
    public R<String> update(@RequestBody EducationPaymentClaimDTO educationPaymentClaimDTO) {
        return Boolean.TRUE.equals(educationPaymentService.update(educationPaymentClaimDTO)) ? R.ok("更新成功") : R.failed("更新失败");
    }

    /**
     * 取消认领（已认领 -> 待认领）
     *
     * @param id 教育回款id
     * @return {@link R}
     */
    @PutMapping("/claim/{id}")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/unclaim','pms/EducationCollectionTrackingForm/unclaim')")
    @ApiOperation(value = "认领（已认领 -> 待认领）", notes = "认领（已认领 -> 待认领）")
    public R<String> unClaim(@PathVariable("id") Long id) {
        return educationPaymentService.claim(id);
    }

    /**
     * 认领（待认领 -> 已认领）
     *
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     * @return {@link R}
     */
    @PostMapping("/claim")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/claim','pms/EducationCollectionTrackingForm/claim')")
    @ApiOperation(value = "认领（待认领 -> 已认领）", notes = "认领（待认领 -> 已认领）")
    public R<String> claim(@Valid @RequestBody EducationPaymentClaimDTO educationPaymentClaimDTO) {
        return Boolean.TRUE.equals(educationPaymentService.claim(educationPaymentClaimDTO)) ? R.ok("更新成功") : R.failed("更新失败");
    }

    /**
     * 锁定/取消锁定
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link R}
     */
    @PostMapping("/lock")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/lock','pms/EducationCollectionTrackingForm/lock')")
    @ApiOperation(value = "锁定/取消锁定", notes = "锁定/取消锁定")
    public R<String> lock(@RequestBody EducationDTO educationDTO) {
        return Boolean.TRUE.equals(educationPaymentService.lock(educationDTO)) ? R.ok("更新成功") : R.failed("更新失败");
    }

    /**
     * 删除
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/del','pms/EducationCollectionTrackingForm/del')")
    @ApiOperation(value = "删除", notes = "删除")
    public R<String> delete(@RequestBody JSONObject jsonObject) {
        return educationPaymentService.delete((List<Long>) jsonObject.get("ids"));
    }

    /**
     * 导入
     *
     * @param educationPaymentExcelDTOList {@link List}<{@link EducationPaymentExcelDTO}>
     * @return {@link R}
     */
    @PostMapping("/import")
    @PreAuthorize("@pms.hasPermission('EducationCollectionTrackingForm/import','pms/EducationCollectionTrackingForm/import')")
    @ApiOperation(value = "导入", notes = "导入")
    public R<String> importExcel(@RequestExcel List<EducationPaymentExcelDTO> educationPaymentExcelDTOList) {
        return educationPaymentService.importExcel(educationPaymentExcelDTOList);
    }

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    @Inner(false)
    @PostMapping("/updateLockStatus")
    public R<String> updateLockStatus() {
        return educationPaymentService.updateLockStatus();
    }

    /**
     * 凭证创建公司-拼接字典查询
     *
     * @return {@link R}<{@link List}>
     */
    @GetMapping("/paymentCompany")
    public R<List<String>> paymentCompany() {
        return R.ok(dbApi.getPaymentCompany().stream().map(PaymentCompanyVO::getPaymentCompany)
                .collect(Collectors.toList()));
    }

}
