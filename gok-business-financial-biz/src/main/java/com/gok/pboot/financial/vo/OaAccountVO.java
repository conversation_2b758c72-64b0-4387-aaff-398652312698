package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Oa账户
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OaAccountVO {

    /**
     * id
     */
    private Long id;

    /**
     * oaId
     */
    private Long oaId;

    /**
     * 账号
     */
    private String loginid;

    /**
     * lastname
     */
    private String lastname;

    /**
     * workcode
     */
    private String workcode;

    /**
     * departmentid
     */
    private String departmentid;

    /**
     * mobile
     */
    private String mobile;

    /**
     * locationid
     */
    private String locationid;

    /**
     * jobtitle
     */
    private String jobtitle;

}
