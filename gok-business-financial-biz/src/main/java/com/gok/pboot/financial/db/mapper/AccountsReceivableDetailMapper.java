package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.db.entity.AccountsReceivableDetail;
import com.gok.pboot.financial.vo.AccountsReceivableDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 应收账款详情 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface AccountsReceivableDetailMapper extends BaseMapper<AccountsReceivableDetail> {

    /**
     * 应收账款明细
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link List}<{@link AccountsReceivableDetailVO}>
     */
    List<AccountsReceivableDetail> selectListByProductId(@Param("projectId") Long projectId,
                                                         @Param("contractId") Long contractId);
}
