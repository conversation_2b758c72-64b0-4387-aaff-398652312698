package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 应收账款详情
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("accounts_receivable_detail")
@ApiModel("应收账款详情")
public class AccountsReceivableDetail extends Model<AccountsReceivableDetail> {

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 合同id
     */
    @ApiModelProperty("合同id")
    private Long contractId;

    /**
     * 统计时间
     */
    @ApiModelProperty("统计时间")
    private String statisticsTime;

    /**
     * 回款
     */
    @ApiModelProperty("回款")
    private BigDecimal payBack;

    /**
     * 开票
     */
    @ApiModelProperty("开票")
    private BigDecimal invoicing;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}