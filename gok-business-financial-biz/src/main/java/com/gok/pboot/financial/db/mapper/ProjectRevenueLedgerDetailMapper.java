package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail;
import com.gok.pboot.financial.dto.ProjectRevenueLedgerDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目收入台账明细数据访问层
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
public interface ProjectRevenueLedgerDetailMapper extends BaseMapper<ProjectRevenueLedgerDetail> {

    /**
     * 获取项目收入确认结项
     *
     * @param dto {@link ProjectRevenueLedgerDTO}
     * @return {@link List}<{@link ProjectRevenueLedgerDetail}>
     */
    List<ProjectRevenueLedgerDetail> getList(@Param("query") ProjectRevenueLedgerDTO dto);
}