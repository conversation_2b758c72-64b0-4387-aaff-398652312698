package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目人工成本分摊汇总
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_labor_cost")
@ApiModel("项目人工成本分摊汇总")
public class ProjectLaborCost extends Model<ProjectLaborCost> {

    /**
     * 归属月份
     */
    @ApiModelProperty("归属月份")
    private String yearMonthDate;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    @ApiModelProperty("项目状态")
    private String projectStatus;

    /**
     * 业务归属一级部门id
     */
    @ApiModelProperty("业务归属一级部门id")
    private Long incomeDeptId;

    /**
     * 业务归属一级部门
     */
    @ApiModelProperty("业务归属一级部门")
    private String incomeDept;

    /**
     * 业务归属二级部门id
     */
    @ApiModelProperty("业务归属二级部门id")
    private Long incomeSecondaryDeptId;

    /**
     * 人员归属二级部门id
     */
    @ApiModelProperty("人员归属二级部门id")
    private Long personnelSecondaryDeptId;

    /**
     * 人员归属二级部门
     */
    @ApiModelProperty("人员归属二级部门")
    private String personnelSecondaryDept;

    /**
     * 人员归属一级部门id
     */
    @ApiModelProperty("人员归属一级部门id")
    private Long personnelDeptId;

    /**
     * 人员归属一级部门
     */
    @ApiModelProperty("人员归属一级部门")
    private String personnelDept;

    /**
     * 社保成本
     */
    @ApiModelProperty("社保成本")
    private String socialSecurityCost;

    /**
     * 公积金成本
     */
    @ApiModelProperty("公积金成本")
    private String providentFundCost;

    /**
     * 工资成本
     */
    @ApiModelProperty("工资成本")
    private String payrollCost;

    /**
     * 合计成本
     */
    @ApiModelProperty("合计成本")
    private String costSum;

    /**
     * 发薪主体/工资发放主体
     */
    @ApiModelProperty("发薪主体")
    private String salaryPaidSubject;

    /**
     * 项目类型
     * {@link com.gok.pboot.financial.enums.ProjectTypeEnum}
     */
    @ApiModelProperty("项目类型")
    private Integer projectType;

    /**
     * 人员类型
     * {@link com.gok.pboot.financial.enums.PersonnelTypeEnum}
     */
    @ApiModelProperty("人员类型")
    private Integer personnelType;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ApiModelProperty("推送状态(0已推送,1待推送,2推送失败)")
    private Integer pushStatus;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}