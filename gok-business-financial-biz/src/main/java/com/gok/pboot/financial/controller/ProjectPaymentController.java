package com.gok.pboot.financial.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.base.ApiResult;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.dto.IdsDTO;
import com.gok.pboot.financial.dto.ProjectPaymentClaimListDTO;
import com.gok.pboot.financial.dto.ProjectPaymentExcelDTO;
import com.gok.pboot.financial.dto.ProjectPaymentInfoDTO;
import com.gok.pboot.financial.enums.BusinessLineEnum;
import com.gok.pboot.financial.service.IProjectPaymentClaimService;
import com.gok.pboot.financial.service.IProjectPaymentService;
import com.gok.pboot.financial.util.FinancePropertiesUtils;
import com.gok.pboot.financial.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目回款跟踪
 *
 * <AUTHOR>
 * @since 2023-09-26
 * @menu 项目回款跟踪
 */
@RestController
@RequestMapping("/project-payment")
@RequiredArgsConstructor
@Api(tags = "项目回款跟踪")
public class ProjectPaymentController {

    private final IProjectPaymentService projectPaymentService;

    private final IProjectPaymentClaimService projectPaymentClaimService;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final DBApi dbApi;

    /**
     * 模糊查询带分页
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}<{@link Page}<{@link ProjectPaymentVO}>>
     */
    @PostMapping("v1/findPage")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm','PROJECT_PAYBACK')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<ProjectPaymentVO>> findPageV1(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        Page<ProjectPayment> page = new Page<>(projectPaymentDTO.getCurrent(), projectPaymentDTO.getSize());
        return R.ok(projectPaymentService.findPageV1(page, projectPaymentDTO));
    }

    /**
     * 锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}
     */
    @PostMapping("/lock")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/lock','pms/ProjectPaymentTrackingForm/lock')")
    @ApiOperation(value = "锁定选中数据", notes = "锁定选中数据")
    public R<String> lock(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return Boolean.TRUE.equals(projectPaymentService.lock(projectPaymentDTO)) ? R.ok("修改成功") : R.failed("修改失败");
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    @ResponseExcel(async = true, name = "项目回款跟踪表", functionEnum = FunctionEnum.FINANCIAL_PROJECT_PAYMENT_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true,mergeSameColumn = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/export')")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List export(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return projectPaymentService.export(projectPaymentDTO);
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}
     */
    @PostMapping("pms/export")
    @PreAuthorize("@pms.hasPermission('pms/ProjectPaymentTrackingForm/export')")
    @ResponseExcel(name = "项目回款跟踪表", dynamicHeader = true,mergeSameColumn = true)
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List pmsExport(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return projectPaymentService.export(projectPaymentDTO);
    }

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}
     */
    @PostMapping("/save")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/add','pms/ProjectPaymentTrackingForm/add')")
    @ApiOperation(value = "插入数据", notes = "插入数据")
    public R<String> save(@Valid @RequestBody PaymentDTO paymentDTO) {
        return projectPaymentService.save(paymentDTO) > NumberUtils.INTEGER_ZERO
                ? R.ok("新增回款信息成功") : R.failed("新增回款信息失败");
    }

    /**
     * 根据id获取详情
     *
     * @param id 项目id
     * @return {@link R}<{@link ProjectPaymentClaimVO}>
     */
    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "根据id获取详情", notes = "根据id获取详情")
    public R<ProjectPaymentClaimVO> getOne(@PathVariable("id") Long id) {
        return R.ok(projectPaymentService.getOne(id));
    }

    /**
     * 根据id更新数据
     *
     * @param projectPaymentInfoDTO {@link ProjectPaymentInfoDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/update")
    @ApiOperation(value = "根据id更新数据", notes = "根据id更新数据")
    public R<Boolean> update(@Valid @RequestBody ProjectPaymentInfoDTO projectPaymentInfoDTO) {
        return Boolean.TRUE.equals(projectPaymentService.update(projectPaymentInfoDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 删除
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@link R}
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/del','pms/ProjectPaymentTrackingForm/del')")
    @ApiOperation(value = "删除选中数据", notes = "删除选中数据")
    public R<String> delete(@RequestBody PaymentDTO paymentDTO) {
        return projectPaymentService.delete(paymentDTO.getIds());
    }

    /**
     * 取消认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link R}
     */
    @PutMapping("/claim/{id}")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/unclaim','pms/ProjectPaymentTrackingForm/unclaim')")
    @ApiOperation(value = "认领操作(已认领 -> 待认领)", notes = "认领操作(已认领 -> 待认领)")
    public R<Boolean> unClaim(@PathVariable("id") Long id) {
        return Boolean.TRUE.equals(projectPaymentService.claim(id))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}
     */
    @PostMapping("/claim")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/claim','pms/ProjectPaymentTrackingForm/claim')")
    @ApiOperation(value = "认领操作(待认领 -> 已认领)", notes = "认领操作(待认领 -> 已认领)")
    public R<Boolean> claim(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        return Boolean.TRUE.equals(projectPaymentService.claim(projectPaymentClaimDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 导入Excel
     *
     * @param projectPaymentExcelDTOList {@link List}<{@link ProjectPaymentExcelDTO}>
     * @return {@link R}
     */
    @PostMapping("/import")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/import','pms/ProjectPaymentTrackingForm/import')")
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R<String> importExcel(@RequestExcel List<ProjectPaymentExcelDTO> projectPaymentExcelDTOList) {
        return projectPaymentService.importExcel(projectPaymentExcelDTOList);
    }

    /**
     * 收款公司列表
     *
     * @return {@link R}<{@link List}<{@link AttributableSubjectVO}>>
     */
    @GetMapping("/attributableSubject")
    @ApiOperation(value = "收款公司列表", notes = "收款公司列表")
    public R<List<AttributableSubjectVO>> attributableSubject() {
        List<AttributableSubjectVO> voList = new ArrayList<>();
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        if (CollectionUtil.isNotEmpty(oaDictVOS)){
            voList = oaDictVOS.stream().sorted(Comparator.comparing(OaDictVO::getDisorder))
                    .map(o ->{
                return new AttributableSubjectVO(String.valueOf(o.getDisorder()),o.getName());
            }).collect(Collectors.toList());
        }
        return R.ok(voList);
    }

    /**
     * 所属业务线列表
     *
     * @return {@link R}<{@link List}<{@link BusinessLineVO}>>
     */
    @GetMapping("/businessLine")
    @ApiOperation(value = "所属业务线列表", notes = "所属业务线列表")
    public R<List<BusinessLineVO>> businessLine() {
        List<BusinessLineVO> voList = new ArrayList<>();
        for (BusinessLineEnum businessLine: BusinessLineEnum.values()) {
            voList.add(new BusinessLineVO(businessLine.getValue(), businessLine.getName()));
        }
        return R.ok(voList);
    }

    /**
     * 客户台帐
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link CustomerAccountVO}>>
     */
    @PostMapping("/customerAccount")
    @ApiOperation(value = "客户台帐", notes = "客户台帐")
    public R<List<CustomerAccountVO>> customerAccount(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getCustomerAccount((String) jsonObject.get("customerName")));
    }

    /**
     * 银行台帐
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link BackAccountVO}>>
     */
    @PostMapping("/backAccount")
    @ApiOperation(value = "银行台帐", notes = "银行台帐")
    public R<List<BackAccountVO>> getBankAccount(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getBankAccount((String) jsonObject.get("name")));
    }

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO}>>
     */
    @PostMapping("/contractPayment")
    @ApiOperation(value = "归属合同收款明细", notes = "归属合同收款明细")
    public R<List<ContractPaymentVO>> contractPayment(@RequestBody JSONObject jsonObject) {
        List<ContractPaymentVO> contractPaymentVOList = dbApi.getContractPayment((String) jsonObject.get("id"))
                .stream()
                .filter(c -> ObjectUtils.isNotEmpty(c.getPaymentName()))
                .collect(Collectors.toList());
        return R.ok(contractPaymentVOList);
    }

    /**
     * 根据合同id搜索归属合同收款明细
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO}>>
     */
    @PostMapping("/contractName")
    @ApiOperation(value = "根据合同编码搜索归属合同收款明细", notes = "根据合同编码搜索归属合同收款明细")
    public R<List<ContractPaymentVO>> contractName(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getContractPayment((String) jsonObject.get("id")));
    }

    /**
     * 项目台帐
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ProjectAccountVO}>>
     */
    @PostMapping("/projectAccount")
    @ApiOperation(value = "项目台帐", notes = "项目台帐")
    public R<List<ProjectAccountVO>> projectAccount(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getProjectAccount((String) jsonObject.get("projectName")));
    }

    /**
     * 人员信息（id + 姓名）
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link UserVO}>>
     */
    @PostMapping("/info")
    @ApiOperation(value = "人员信息", notes = "人员信息")
    public R<List<UserVO>> userInfo(@RequestBody JSONObject jsonObject) {
        return R.ok(projectPaymentService.userInfo((String) jsonObject.get("username")));
    }

    /**
     * 行政部门信息（归属部门）
     *
     * @param deptName 组织名称
     * @param level 部门层级
     * @return {@link R}<{@link List}<{@link MultiDimensionDeptDto}>>
     */
    @Inner(value = false)
    @GetMapping("/innerDeptInfo")
    @ApiOperation(value = "行政部门信息（归属部门）", notes = "行政部门信息（归属部门）")
    public R<List<MultiDimensionDeptDto>> innerDeptInfo(@RequestParam(required = false, name = "deptName") String deptName,
                                                        @RequestParam(required = false, name = "level") Integer level) {
        if (CharSequenceUtil.isNotBlank(deptName)) {
            List<MultiDimensionDeptDto> collect = new ArrayList<>();
            // 获取全部部门数据
            List<MultiDimensionDeptDto> deptList = remoteOutMultiDeptService
                    .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, level)
                    .getData()
                    .stream().filter(d -> NumberUtils.INTEGER_ZERO.toString().equals(d.getStatus()))
                    .collect(Collectors.toList());
            // 用户输入的一级部门数据
            List<MultiDimensionDeptDto> deptDtoList = remoteOutMultiDeptService
                    .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, null)
                    .getData()
                    .stream().filter(dept -> dept.getName().equals(deptName))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deptDtoList)){
                MultiDimensionDeptDto multiDimensionDeptDto = deptDtoList.get(NumberUtils.INTEGER_ZERO);
                collect = deptList.stream().filter(dept -> dept.getParentId().equals(multiDimensionDeptDto.getDeptId())).collect(Collectors.toList());
            }

            return R.ok(collect);
        }
        return R.ok(remoteOutMultiDeptService.getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, deptName, level)
                .getData()
                .stream().filter(d -> NumberUtils.INTEGER_ZERO.toString().equals(d.getStatus()))
                .collect(Collectors.toList()));
    }

    /**
     * 虚拟部门信息（核算部门）
     *
     * @param deptName 组织名称
     * @param level 部门层级
     * @return {@link R}<{@link List}<{@link MultiDimensionDeptDto}>>
     */
    @GetMapping("/virtualDeptInfo")
    @ApiOperation(value = "虚拟部门信息（核算部门）", notes = "虚拟部门信息（核算部门）")
    public R<List<MultiDimensionDeptDto>> virtualDeptInfo(@RequestParam(required = false, name = "deptName") String deptName,
                                                          @RequestParam(required = false, name = "level") Integer level) {
        return remoteOutMultiDeptService.getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, deptName, level);
    }

    /**
     * 推送接口
     *
     * @param ids ids
     * @return PushResultVO
     */
    @PostMapping("/push")
    @ApiOperation(value = "推送", notes = "推送")
    public PushResultVO push(@Valid @RequestBody IdsDTO ids) {
        return projectPaymentService.push(ids.getIds());
    }

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    @Inner(false)
    @PostMapping("/updateLockStatus")
    public R<Void> updateLockStatus() {
        return projectPaymentService.updateLockStatus();
    }

    /**
     * 根据合同名称搜索合同台账
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link ContractPaymentVO}>>
     */
    @PostMapping("/getNames")
    @ApiOperation(value = "根据合同名称搜索合同台账", notes = "根据合同名称搜索合同台账")
    public R<List<ContractInfoVO>> getContractName(@RequestBody JSONObject jsonObject) {
        return R.ok(projectPaymentService.getContractName((String) jsonObject.get("contractName")));
    }

    /**
     * 获取列表中的业务板块
     *
     * @return {@link R}<{@link List}<{@link BusinessBlockVO}>>
     */
    @GetMapping("/queryBusinessBlock")
    @ApiOperation(value = "获取列表中的业务板块", notes = "获取列表中的业务板块")
    public R<List<BusinessBlockVO>> queryBusinessBlock() {
        return R.ok(projectPaymentClaimService.queryBusinessBlock());
    }

    /**
     * 保证金台账
     *
     * @param jsonObject {@link JSONObject}
     * @return {@link R}<{@link List}<{@link MarginVO}>>
     */
    @PostMapping("/getMarginVo")
    @ApiOperation(value = "保证金台账", notes = "保证金台账")
    public R<List<MarginVO>> getMarginVo(@RequestBody JSONObject jsonObject) {
        return R.ok(dbApi.getMarginVo((String) jsonObject.get("flowNo")));
    }

    /**
     * 项目回款认领信息
     *
     * @param idsDTO
     * @return {@link R}<{@link List}<{@link ProjectPaymentClaimVO}>>
     */
    @Inner(value = false)
    @PostMapping("/getClaim")
    @ApiOperation(value = "项目回款认领信息", notes = "项目回款认领信息")
    public R<List<ProjectPaymentClaimVO>> getClaim(@RequestBody IdsDTO idsDTO) {
        return R.ok(projectPaymentClaimService.getClaim(idsDTO));
    }

    /**
     * 项目回款认领信息详情
     *
     * @param id
     * @return {@link R}<{@link List}<{@link ProjectPaymentClaimVO}>>
     */
    @Inner(value = false)
    @GetMapping("/getPaymentInfo/{id}")
    @ApiOperation(value = "项目回款认领信息", notes = "项目回款认领信息")
    public R<ProjectPaymentInfoDetailVO> getPaymentInfo(@PathVariable("id") Long id) {
        return R.ok(projectPaymentService.getPaymentInfo(id));
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param claimListDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}
     */
    @PostMapping("batch/claim")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm/claim','pms/ProjectPaymentTrackingForm/claim')")
    @ApiOperation(value = "认领操作(待认领 -> 已认领)", notes = "认领操作(待认领 -> 已认领)")
    public R<Boolean> batchClaim(@Valid @RequestBody ProjectPaymentClaimListDTO claimListDTO) {
        return Boolean.TRUE.equals(projectPaymentService.batchClaim(claimListDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 模糊查询带分页
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}<{@link Page}<{@link ProjectPaymentVO}>>
     */
    @PostMapping("findPage")
    @PreAuthorize("@pms.hasPermission('ProjectPaymentTrackingForm','PROJECT_PAYBACK')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<ProjectPaymentPageVO>> findPage(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        Page<ProjectPayment> page = new Page<>(projectPaymentDTO.getCurrent(), projectPaymentDTO.getSize());
        return R.ok(projectPaymentService.findPage(page, projectPaymentDTO));
    }


    /**
     * 回款跟踪表同步
     *
     * @return
     */
    @Inner(value = false)
    @PostMapping("synchronizeData")
    public ApiResult synchronizeData() {
        return projectPaymentService.synchronizeData();
    }
}
