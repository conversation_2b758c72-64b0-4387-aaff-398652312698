package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.dto.ConfirmSettlementDTO;
import com.gok.pboot.financial.dto.FinancialProcessFormPageDTO;
import com.gok.pboot.financial.service.IFinanceProcessFormService;
import com.gok.pboot.financial.vo.ConfirmSettlementBatchVO;
import com.gok.pboot.financial.vo.FinanceProcessFormVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 前端枚举查询
 * @menu 财务处理单
 * @author: wengc
 * @create: 2023-11-30 15:57
 **/
@RestController
@RequestMapping("/finance-process-form")
@RequiredArgsConstructor
@Validated
public class FinanceProcessFormController {

    private final IFinanceProcessFormService financeProcessFormService;

    /**
     * 条件查询带分页
     *
     * @param dto 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link FinanceProcessFormVO}>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('financialProcessingDoc/all','financialProcessingDoc/approval','financialProcessingDoc/reviewed'" +
            ",'financialProcessingDoc/settlement','financialProcessingDoc/partSettlement','financialProcessingDoc/settled'" +
            ",'financialProcessingDoc/noSettlement')")
    @ApiOperation(value = "条件查询带分页", notes = "条件查询带分页")
    public R<StatisticsPage<FinanceProcessFormVO>> findPage(@RequestBody FinancialProcessFormPageDTO dto) {
        return R.ok(financeProcessFormService.findPage(dto));
    }


    /**
     *  条件导出
     * @param dto 导出条件封装实体
     * @return 导出信息
     */
//    @Inner(value = false)
    @PostMapping("/export")
    @ResponseExcel(async = true, name = "财务处理单", dynamicHeader = true)
    @PreAuthorize("@pms.hasPermission('financialProcessingDoc/export')")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List exportContractLedgerVoList(@RequestBody FinancialProcessFormPageDTO dto) {
        return financeProcessFormService.export(dto);
    }

    /**
     *  确认结算
     * @param id 确认结算财务处理单id
     * @return 响应信息
     */
    @PostMapping("/confirmSettlement/{id}")
    @PreAuthorize("@pms.hasPermission('financialProcessingDoc/handle/confirmSettlement')")
    public R<String> confirmSettlement( @PathVariable("id") Long id) {
        try {
            financeProcessFormService.confirmSettlement(id);
        } catch (Exception e) {
            return R.failed("结算失败：原因："+e.getMessage());
        }
        return R.ok("确认结算成功");
    }

    /**
     *  批量确认结算
     * @param dto 封装实体
     * @return 响应信息
     */
//    @Inner(value = false)
    @PostMapping("/confirmSettlementBatch")
    @PreAuthorize("@pms.hasPermission('financialProcessingDoc/confirmSettlement')")
    public R<ConfirmSettlementBatchVO> confirmSettlementBatch(@Validated @RequestBody ConfirmSettlementDTO dto) {
        return financeProcessFormService.confirmSettlementBatch(dto);
    }
}
