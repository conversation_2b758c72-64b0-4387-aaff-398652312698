package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.dto.AccountingSystemDTO;
import com.gok.pboot.financial.dto.AccountingSystemImportDTO;
import com.gok.pboot.financial.dto.AccountingSystemPageDTO;
import com.gok.pboot.financial.vo.AccountingSystemVO;

import java.util.List;

/**
 * 会计体系业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IAccountingSystemService extends IService<AccountingSystem> {

    /**
     * 分页数据
     * @param dto
     * @return
     */
    Page<AccountingSystemVO> findPage(AccountingSystemPageDTO dto);

    /**
     * 导出
     * @param dto
     * @return
     */
    List<AccountingSystemVO> exportExcel(AccountingSystemPageDTO dto);

    /**
     * 新增或编辑
     * @param dto
     * @return
     */
    R<String> addOrEdit(AccountingSystemDTO dto);

    /**
     * 删除
     * @param idList
     * @return
     */
    R<String> del(List<Long> idList);

    /**
     * 启用
     * @param dto
     * @return
     */
    boolean enable(AccountingSystemDTO dto);

    /**
     * 导入
     * @param importDTOList
     * @return
     */
    R<String> importExcel(List<AccountingSystemImportDTO> importDTOList);

    /**
     * 查看
     * @param id
     * @return
     */
    R<AccountingSystemVO> info(Long id);
}
