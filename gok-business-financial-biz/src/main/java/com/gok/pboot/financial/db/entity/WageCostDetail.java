package com.gok.pboot.financial.db.entity;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工资成本明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WageCostDetail {
    /**
     * 归属月份
     */
    private String yearMonthDate;

    /**
     * 发薪主体
     */
    private String salaryPaidSubject;

    /**
     * 当前行政部门id
     */
    private Long currentDeptId;

    /**
     * 当前行政部门
     */
    private String currentDept;

    /**
     * 一级部门id
     */
    private Long primaryDeptId;

    /**
     * 一级部门
     */
    private String primaryDept;

    /**
     * 二级部门id
     */
    private Long secondaryDeptId;

    /**
     * 二级部门
     */
    private String secondaryDept;

    /**
     * 三级部门id
     */
    private Long tertiaryDeptId;

    /**
     * 三级部门
     */
    private String tertiaryDept;

    /**
     * 应发合计
     */
    private String salary;

    /**
     * 个人所得税
     */
    private String individualIncomeTax;

    /**
     * 个人社保
     */
    private String individualSocialSecurity;

    /**
     * 个人公积金
     */
    private String individualProvidentFund;

    /**
     * 补发/补扣
     */
    private String backPay;

    /**
     * 上月个税差异
     */
    private String incomeTaxDifferenceFromLastMonth;

    /**
     * 实付合计
     */
    private String actualPay;

    /**
     * 单位社保
     */
    private String unitSocialSecurity;

    /**
     * 单位公积金
     */
    private String unitProvidentFund;

    /**
     * 残保金
     */
    private String disabledSecurityFund;

    /**
     * 工资成本
     */
    private String payrollCost;

    /**
     * 人数
     */
    private Integer peopleNum;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    private String delFlag;

    /**
     * 所属租户
     */
    private Long tenantId;
}
