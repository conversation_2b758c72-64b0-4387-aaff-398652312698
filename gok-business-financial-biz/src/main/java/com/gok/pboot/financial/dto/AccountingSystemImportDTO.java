package com.gok.pboot.financial.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 会计体系导入
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingSystemImportDTO {

    /**
     * 体系编码
     */
    @NotBlank(message = "体系编码不能为空")
    @ExcelProperty("体系编码")
    private String systemCode;

    /**
     * 体系名称
     */
    @NotBlank(message = "体系名称不能为空")
    @ExcelProperty("体系名称")
    private String systemName;

    /**
     * 账套类型
     */
    @NotBlank(message = "账套类型不能为空")
    @ExcelProperty("账套类型")
    private String accountingType;

    /**
     * 停用状态
     */
    @NotBlank(message = "停用状态不能为空")
    @ExcelProperty("停用状态")
    private String enableStatusStr;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String systemRemarks;
}
