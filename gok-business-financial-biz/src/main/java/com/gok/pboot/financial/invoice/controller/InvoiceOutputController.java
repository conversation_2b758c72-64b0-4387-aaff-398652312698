package com.gok.pboot.financial.invoice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputVO;
import com.gok.pboot.financial.invoice.entity.vo.InvoicePlanVO;
import com.gok.pboot.financial.invoice.service.IInvoiceOutputService;
import com.gok.pboot.financial.vo.OaFileInfoVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 销项发票台账前端控制器
 *
 * <AUTHOR>
 * @create 2025/06/09
 * @menu 销项发票台账
 **/
@RestController
@RequestMapping("/invoiceOutput")
@RequiredArgsConstructor
@Api(tags = "销项发票台账")
public class InvoiceOutputController {

    private final IInvoiceOutputService service;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link InvoiceOutputDTO}
     * @return {@link R}<{@link StatisticsPage}<{@link InvoicePlanVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<InvoiceOutputVO>> findPage(@RequestBody @Valid InvoiceOutputDTO dto) {
        return R.ok(service.findPage(dto));
    }

    /**
     * 导出Excel
     *
     * @param dto {@link InvoiceOutputDTO}
     * @return {@link Page}<{@link InvoiceOutputVO}>
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(async = true, name = "销项发票台账", nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<InvoiceOutputVO> exportExcel(@RequestBody InvoiceOutputDTO dto) {
        return service.exportExcel(dto);
    }

    /**
     * PMS导出Excel
     *
     * @param dto {@link InvoiceOutputDTO}
     * @return {@link Page}<{@link InvoiceOutputVO}>
     */
    @PostMapping("/pms/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(name = "销项发票台账", nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<InvoiceOutputVO> pmsExportExcel(@RequestBody InvoiceOutputDTO dto) {
        return service.exportExcel(dto);
    }

    /**
     * 获取销项发票附件集合
     *
     * @param id 主键ID
     * @return {@link Page}<{@link InvoiceOutputVO}>
     */
    @GetMapping("/attachments/{id}")
    @ApiOperation(value = "获取附件信息", notes = "获取附件信息")
    public R<List<OaFileInfoVo>> getAttachments(@PathVariable("id") Long id) {
        return R.ok(service.getAttachments(id));
    }

}
