package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 工资汇总DTO
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WageCostDTO {

    /**
     * 当前页
     */
    private Integer current;

    /**
     * 每页条数
     */
    private Integer size;

    /**
     * 导出Excel所需要id集合
     */
    private List<String> ids;

    /**
     * 所属月份
     * 2023-09
     */
    private String yearMonthDateStart;

    /**
     * 所属月份
     */
    private String yearMonthDateEnd;

    /**
     * 发薪主体
     */
    private String paySubject;

    /**
     * 归宿部门IDs
     */
    private List<Long> deptIds;

    /**
     * 条件查询部门id列表
     */
    private List<Long> conditionDeptIdList;

    /**
     * 推送状态
     * 全部不要传 0已推送 1待推送 2推送失败
     */
    private Long pushStatus;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    private List<String> includeExcel;

}
