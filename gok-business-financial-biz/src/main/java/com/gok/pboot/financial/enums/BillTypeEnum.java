package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 票据类型 Enum
 *
 * author caihf
 * since 2023-09-01
 */
@Getter
public enum BillTypeEnum implements ValueEnum<Integer> {

    // 付款申请单
    PAYMENT_APPLICATION(0," CW-02付款申请单")
    ,

    // 费用报销单
    EXPENSE_REIMBURSEMENT(1,"CW-03费用报销单")
    ,

    // 内部借款
    INTERNAL_LOAN(2,"CW-04内部借款流程")
    ,

    // 保证金
    DEPOSIT(3,"CW-08保证金申请单")
    ,

    // 团建费用报销
    TEAM_BUILDING_REIMBURSEMENT(4,"CW-11团建费用报销审批单")
    ,

        // 退款
    REFUND(5,"CW-12退款申请单")
    ,

    // 项目采购付款
     PROJECT_PROCUREMENT_PAYMENT(6,"XM-12项目采购付款")
   ,

    // 项目采购冲销
    PROJECT_PROCUREMENT_OFFSET(7,"XM-13项目采购冲销")
    ,

    // 项目借款
    PROJECT_LOAN(8,"XM-14项目借款")
    ,

    // 在建项目费用报销
    IN_PROGRESS_PROJECT_REIMBURSEMENT(9,"XM-15在建项目费用报销")
    ,

    // 商机项目费用报销
    BUSINESS_OPPORTUNITY_PROJECT_REIMBURSEMENT(10,"XM-28商机项目费用报销")
    ,

    // 工资、五险一金付款
    SALARY_AND_BENEFITS_PAYMENT(11,"CW-14工资、五险一金付款");
    

    

    private final Integer value;

    private final String name;

    BillTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
