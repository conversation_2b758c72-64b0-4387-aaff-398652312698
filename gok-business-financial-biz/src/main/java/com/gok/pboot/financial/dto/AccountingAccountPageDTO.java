package com.gok.pboot.financial.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 会计科目分页
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class AccountingAccountPageDTO implements Serializable {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * 科目类型ID
     */
    private Long accountTypeId;

    /**
     * 会计体系
     */
    private Long accountingSystemId;

    /**
     * 科目编码
     */
    private String accountCode;

    /**
     * 级次
     */
    private Integer level;

    /**
     * 余额方向
     */
    private Integer balanceDirection;

    /**
     * 现金分类
     */
    private Integer cashClassification;

    /**
     * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
     */
    private Integer enableStatus;

    /**
     * ids
     */
    private List<Long> idList;

    private List<String> includeExcel;

}
