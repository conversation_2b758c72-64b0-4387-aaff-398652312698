package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.components.common.util.R;
import com.gok.pboot.base.ApiResult;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.dto.ProjectPaymentClaimListDTO;
import com.gok.pboot.financial.dto.ProjectPaymentExcelDTO;
import com.gok.pboot.financial.dto.ProjectPaymentInfoDTO;
import com.gok.pboot.financial.vo.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 项目回款跟踪 Service
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface IProjectPaymentService extends IService<ProjectPayment> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPaymentVO}>
     */
    Page<ProjectPaymentVO> findPageV1(Page<ProjectPayment> page, ProjectPaymentDTO projectPaymentDTO);

    /**
     * 锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Boolean}
     */
    Boolean lock(ProjectPaymentDTO projectPaymentDTO);

    /**
     * 导出Excel选中数据
     * @param projectPaymentDTO
     */
    List export(ProjectPaymentDTO projectPaymentDTO);

    /**
     * 插入数据
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@code int}
     */
    int save(PaymentDTO paymentDTO);

    /**
     * 根据id获取详情
     *
     * @param id 项目id
     * @return {@link ProjectPaymentClaimVO}
     */
    ProjectPaymentClaimVO getOne(Long id);

    /**
     * 根据id更新数据
     *
     * @param projectPaymentInfoDTO {@link ProjectPaymentClaimDTO}
     * @return {@code Boolean}
     */
    Boolean update(ProjectPaymentInfoDTO projectPaymentInfoDTO);

    /**
     * 删除
     *
     * @param ids {@link List}
     * @return {@link R}
     */
    R<String> delete(List<Long> ids);

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link Boolean}
     */
    Boolean claim(Long id);

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link Boolean}
     */
    Boolean claim(ProjectPaymentClaimDTO projectPaymentClaimDTO);

    /**
     * 导入Excel
     *
     * @param projectPaymentExcelDTOList {@link List}<{@link ProjectPaymentExcelDTO}>
     * @return {@link R}
     */
    R<String> importExcel(List<ProjectPaymentExcelDTO> projectPaymentExcelDTOList);

    /**
     * 推送接口
     *
     * @param ids ids
     * @return PushResultVO
     */
    PushResultVO push(List<Long> ids);

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    R<Void> updateLockStatus();

    /**
     * 获取人员信息
     *
     * @param username {@link String} 名称
     * @return {@link List}<{@link UserVO}>
     */
    List<UserVO> userInfo(String username);

    /**
     * 根据合同名称搜索归属合同收款明细
     *
     * @param contractName {@link String} 合同名称
     * @return {@link ContractPaymentVO}>
     */
    List<ContractInfoVO> getContractName(String contractName);

    /**
     * 获取项目回款认领信息详情
     * @param id
     * @return
     */
    ProjectPaymentInfoDetailVO getPaymentInfo(Long id);

    /**
     * 批量认领操作(待认领 -> 已认领)
     * @param claimListDTO
     * @return
     */
    Boolean batchClaim(ProjectPaymentClaimListDTO claimListDTO);

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPaymentVO}>
     */
    StatisticsPage<ProjectPaymentPageVO> findPage(Page<ProjectPayment> page, ProjectPaymentDTO projectPaymentDTO);

    /**
     * 回款跟踪表同步
     *
     * @return
     */
    ApiResult synchronizeData();
}
