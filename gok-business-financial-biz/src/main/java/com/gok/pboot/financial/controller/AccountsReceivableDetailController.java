package com.gok.pboot.financial.controller;

import com.gok.components.common.util.R;
import com.gok.pboot.financial.service.IAccountsReceivableDetailService;
import com.gok.pboot.financial.vo.AccountsReceivableDetailVO;
import com.gok.pboot.financial.vo.PayAndInvoicingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 应收账款台账详情
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@RestController
@RequestMapping("/accounts-receivable-detail")
@RequiredArgsConstructor
@Api(tags = "应收账款台账详情")
public class AccountsReceivableDetailController {

    private final IAccountsReceivableDetailService accountsReceivableDetailService;

    /**
     * 应收账款明细
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link R}<{@link AccountsReceivableDetailVO}>
     */
    @GetMapping
    @ApiOperation(value = "应收账款明细", notes = "应收账款明细")
    public R<AccountsReceivableDetailVO> accountsReceivableDetail(@RequestParam("projectId") Long projectId,
                                                                  @RequestParam("contractId") Long contractId) {
        return R.ok(accountsReceivableDetailService.accountsReceivableDetail(projectId, contractId));
    }

    /**
     * 获取回款与发票月份详情
     *
     * @param yearMonthDate 年月
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link R}<{@link PayAndInvoicingVO}>
     */
    @GetMapping("/detailByDate")
    @ApiOperation(value = "回款与发票月份详情", notes = "回款与发票月份详情")
    public R<PayAndInvoicingVO> payBackAndInvoicingDetailByDate(@RequestParam("yearMonthDate") String yearMonthDate,
                                                                @RequestParam("projectId") Long projectId,
                                                                @RequestParam("contractId") Long contractId) {
        return R.ok(accountsReceivableDetailService.payBackAndInvoicingDetailByDate(yearMonthDate, projectId, contractId));
    }

    /**
     * 获取回款与发票详情
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link R}<{@link PayAndInvoicingVO}>
     */
    @GetMapping("/pay-invoicing")
    @ApiOperation(value = "回款与发票明细", notes = "回款与发票明细")
    public R<PayAndInvoicingVO> payBackAndInvoicingDetail(@RequestParam("projectId") Long projectId,
                                                          @RequestParam("contractId") Long contractId) {
        return R.ok(accountsReceivableDetailService.payBackAndInvoicingDetail(projectId, contractId));
    }
}
