package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 汇总状态 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum SummaryStatusEnum implements ValueEnum<Integer> {

    /**
     * 已汇总
     */
    YES(0, "已汇总"),

    /**
     * 待汇总
     */
    NO(1, "待汇总");

    private final Integer value;

    private final String name;

    SummaryStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
