package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 收单状态 Enum
 *
 * author caihf
 * since 2023-09-01
 */
@Getter
public enum ReceiptStatusEnum implements ValueEnum<Integer> {

    // 待收单
    PENDING_RECEIPT(0,"待收单")
    ,

    // 已收单
    RECEIPTED(1,"已收单")

   ;


    private final Integer value;

    private final String name;

    ReceiptStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
