package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @create 2025/06/09
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class OaFileInfoVo {

    /**
     * 流程id
     */
    private String requestId;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 文件图像id
     */
    private String imageFileId;

    /**
     * 文件名
     */
    private String filename;

    /**
     * 文件名下载链接
     */
    private String downloadUrl;

}
