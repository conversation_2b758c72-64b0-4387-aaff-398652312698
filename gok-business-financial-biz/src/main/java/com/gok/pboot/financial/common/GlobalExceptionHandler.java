package com.gok.pboot.financial.common;

import com.gok.components.common.util.R;
import com.gok.pboot.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLException;

/**
 * 异常统一拦截
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务代码异常
     *
     * @param exception {@link BusinessException}
     * @return {@link R}<{@link String}>
     */
    @ExceptionHandler(BusinessException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<String> handleBusinessException(BusinessException exception){
        return R.failed(exception.getMessage());
    }

    /**
     * 处理 SQL 异常
     *
     * @param e {@link SQLException}
     * @return {@link R}<{@link String}>
     */
    @ExceptionHandler(SQLException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<String> handleSqlException(SQLException e){
        log.info("SQLException: {}", e.getMessage());
        return R.failed("服务数据访问异常");
    }

    /**
     * 处理运行时异常
     *
     * @param e {@link Exception}
     * @return {@link R}<{@link String}>
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R<String> handleException(Exception e){
        log.info("未知异常错误: {}", e);
        return R.failed("系统未知异常");
    }
}
