package com.gok.pboot.financial.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 教育回款导入Excel
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EducationPaymentExcelDTO {

    /**
     * 收款平台
     */
    @NotBlank(message = "收款平台不能为空")
    @ExcelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 收款公司
     */
    @ExcelProperty("收款公司")
    private String paymentCompany;

    /**
     * 交易流水号
     */
    @NotBlank(message = "交易流水号不能为空")
    @ExcelProperty("交易流水号")
    private String transactionNumber;

    /**
     * 到账金额
     */
    @NotNull(message = "到账金额不能为空")
    @ExcelProperty("到账金额")
    private BigDecimal receivedAmount;

    /**
     * 收款日期
     */
    @NotBlank(message = "收款日期不能为空")
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 开票状态value（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    @ExcelProperty("开票状态")
    private String invoicingStatusTxt;

    /**
     * 开票日期
     */
    @ExcelProperty("开票日期")
    private String invoicingDate;

    /**
     * 发票号码
     */
    @ExcelProperty("发票号码")
    private String invoicingNumber;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 客户经理
     */
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 回款一级部门
     */
    @ExcelProperty("回款一级部门")
    private String paymentDept;

    /**
     * 回款二级部门
     */
    @ExcelProperty("回款二级部门")
    private String paymentSecondaryDept;

    /**
     * 归属业务线
     */
    @ExcelProperty("归属业务线")
    private String businessLine;

    /**
     * 学校名称
     */
    @ExcelProperty("学校名称")
    private String schoolName;

    /**
     * 产品名称
     */
    @ExcelProperty("产品名称")
    private String productName;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 大客户经理姓名
     */
    @ExcelProperty("大客户经理")
    private String keyAccountManagerName;

    /**
     * 销售主管姓名
     */
    @ExcelProperty("销售主管")
    private String salesExecutiveName;

    /**
     * 客户经理姓名
     */
    @ExcelProperty("客户经理")
    private String accountManagerName;

    /**
     * 咨询顾问姓名
     */
    @ExcelProperty("咨询顾问")
    private String consultantUserName;

    /**
     * 确认收入日期
     */
    @ExcelProperty("确认收入日期")
    private String sureRevenueDate;

    /**
     * 认领状态value（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ExcelProperty("认领状态")
    private String claimStatusTxt;

    /**
     * 锁定状态value（0已锁定，1未锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ExcelProperty("锁定状态")
    private String lockStatusTxt;

    /**
     * 汇总状态value（0已汇总，1未汇总）
     * {@link com.gok.pboot.financial.enums.SummaryStatusEnum}
     */
    @ExcelProperty("汇总状态")
    private String summaryStatusTxt;

    /**
     * 创建人姓名
     */
    @ExcelProperty("创建人")
    private String creatorName;

    /**
     * 认领人姓名
     */
    @ExcelProperty("认领人")
    private String claimantName;

    /**
     * 回款备注
     */
    @ExcelProperty("备注")
    private String paymentNote;
}
