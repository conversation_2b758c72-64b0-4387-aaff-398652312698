package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 收付款状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum PaymentStatusEnum implements ValueEnum<String> {
    NOT_COLLECTED("0","待回款"),
    PARTIAL_COLLECTED("1","部分回款"),
    RECEIVED("2","已回款"),
    BAD_DEBT("3","坏账"),
    NOT_ACHIEVED("4","未达成")
    ;

    private final String value;

    private final String name;

    PaymentStatusEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
