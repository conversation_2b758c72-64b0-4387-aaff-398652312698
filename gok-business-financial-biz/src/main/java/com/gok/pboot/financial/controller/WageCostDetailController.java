package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.service.IWageCostDetailService;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import com.gok.pboot.financial.vo.WageCostDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工资成本
 *
 * <AUTHOR>
 * @menu 成本管理-工资汇总明细
 * @since 2024-01-18
 */
@RestController
@RequestMapping("/wage-cost/detail")
@RequiredArgsConstructor
@Api(tags = "工资成本明细")
public class WageCostDetailController {

    private final IWageCostDetailService wageCostDetailService;

    /**
     * 模糊查询带分页
     *
     * @param wageCostDTO 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link ProjectLaborCostDetailVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('wagesSummary')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<WageCostDetailVO>> findPage(@RequestBody WageCostDTO wageCostDTO) {
        StatisticsPage<WageCostDetailVO> page = new StatisticsPage<>(wageCostDTO.getCurrent(), wageCostDTO.getSize());
        return R.ok(wageCostDetailService.findPage(page, wageCostDTO));
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param wageCostDTO {@link WageCostDTO}
     * @return {@link List}
     */
    @PostMapping("/export")
    @ResponseExcel(async = true, name = "工资汇总明细", functionEnum = FunctionEnum.FINANCIAL_WAGE_COST_DETAIL_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PreAuthorize("@pms.hasPermission('wagesSummary/export')")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List export(@RequestBody WageCostDTO wageCostDTO) {
        return wageCostDetailService.export(wageCostDTO);
    }

    /**
     * 发薪主体下拉框
     *
     * @return R
     */
    @GetMapping("/salaryPaidSubject")
    @ApiOperation(value = "发薪主体下拉框", notes = "发薪主体下拉框")
    public R<List<String>> salaryPaidSubject() {
        return R.ok(wageCostDetailService.getSalaryPaidSubjects());
    }

}
