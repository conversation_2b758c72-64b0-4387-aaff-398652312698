package com.gok.pboot.financial.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
    * 科目类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountTypeDTO{
    /**
    * id
    */
    private Long id;

    /**
    * 类型编码
    */
    private String typeCode;

    /**
    * 类型名称
    */
    private String typeName;

    /**
    * 会计体系id
    */
    private Long accountingSystemId;

}