package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用友id关联表
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("yy_relation")
public class YyRelation{

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 关联ID（部门ID或人员ID）
     */
    @ApiModelProperty("关联ID（部门ID或人员ID）")
    private Long relateId;

    /**
     * 关联类型 1-部门，2-人员
     */
    @ApiModelProperty("关联类型 1-部门，2-人员")
    private Integer relateType;

    /**
     * 用友ID
     */
    @ApiModelProperty("用友ID")
    private String yyId;

    /**
     * 同步时间
     */
    @ApiModelProperty("同步时间")
    private LocalDateTime syncTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}
