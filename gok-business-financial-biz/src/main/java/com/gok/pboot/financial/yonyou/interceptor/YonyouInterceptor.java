package com.gok.pboot.financial.yonyou.interceptor;

import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.ForestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 用友-请求-拦截器
 *
 * <AUTHOR>
 * @date 2025/08/04
 */
@Component
@Slf4j
public class YonyouInterceptor implements ForestInterceptor {
    @Override
    public boolean beforeExecute(ForestRequest request) {
        return true;
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        log.error(
                "接口请求错误: statusCode={}, content={}, result={}",
                response.getStatusCode(),
                response.getContent(),
                response.getResult()
        );
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        log.info("返回结果: {},{}", response.getStatusCode(), response.getResult());
    }
}
