package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 销售收款计划客户付款审批流程
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sales_receipt_payment_process")
public class SalesReceiptPaymentProcess {
    /**
    * id
    */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    /**
    * 销售收款id
    */
    @ApiModelProperty("销售收款id")
    private Long salesReceiptId;

    /**
     * 审批节点名称
     */
    @ApiModelProperty("审批节点名称")
    private String approvalProcess;

    /**
    * 审批状态(0未开始 1进行中 2已完成)
    */
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人")
    private String createBy;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
    * 所属租户
    */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}