package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 更新回款认领
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentClaimInfoDTO {

    /**
     * 回款类型
     */
    private Integer paymentType;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPaymentId;

    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPayment;

    /**
     * 款项金额
     */
    private String paymentMoney;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 客户经理id
     */
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    private String salesmanUserName;

    /**
     * 保证金编号
     */
    private String marginCode;

    /**
     * 保证金类型
     */
    private Integer marginType;

    /**
     * 保证金金额
     */
    private String marginMoney;

    /**
     * 业务板块
     */
    private Integer businessBlock;

    /**
     * 技术类型
     */
    private Integer skillType;

    /**
     * 认领金额
     */
    private BigDecimal claimMoney;

    /**
     * 客户名称
     */
    private String relatedCustomerName;

    /**
     * 认领备注
     */
    private String claimRemark;
}
