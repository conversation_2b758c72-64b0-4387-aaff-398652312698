package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * - oa获取Token -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class OaGetTokenDTO {

    private String msg;

    private Integer code;

    private Boolean status;

    private String token;




}
