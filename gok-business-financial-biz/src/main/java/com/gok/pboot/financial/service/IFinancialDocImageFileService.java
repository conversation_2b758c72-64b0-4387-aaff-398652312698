package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.FinancialDocImageFile;
import com.gok.pboot.financial.vo.OaFileInfoVo;

import java.util.List;

/**
 * OA文件映射 服务类接口
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
public interface IFinancialDocImageFileService extends IService<FinancialDocImageFile> {

    /**
     * 获取OA文件列表
     *
     * @param docId
     * @return
     */
    List<OaFileInfoVo> getOaFileVoList(String docId);

}
