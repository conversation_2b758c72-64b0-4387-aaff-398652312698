package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * - 创建流程 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class OaSubmitRequestDTO {

    /**
     * 流程id
     */
    private Integer requestId;

    /**
     * 主表数据
     */
    private List<OaMainParamDTO> mainData;

    /**
     * 其他参数 {↵  "isnextflow":"0"↵}
     */
    private Map<String,Object> otherParams;





}
