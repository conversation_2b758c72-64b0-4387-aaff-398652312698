package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 开票状态 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum InvoicingStatusEnum implements ValueEnum<Integer> {

    /**
     * 已开票
     */
    YES(0, "已开票"),

    /**
     * 未开票
     */
    NO(1, "未开票");

    private final Integer value;

    private final String name;

    InvoicingStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
