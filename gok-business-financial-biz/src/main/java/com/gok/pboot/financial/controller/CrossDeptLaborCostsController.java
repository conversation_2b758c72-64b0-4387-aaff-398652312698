package com.gok.pboot.financial.controller;

import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.dto.CrossDeptLaborShareCostsDTO;
import com.gok.pboot.financial.dto.DeptArtificialCostDTO;
import com.gok.pboot.financial.service.ICrossDeptLaborCostsService;
import com.gok.pboot.financial.vo.CrossDeptLaborCostsDetailVO;
import com.gok.pboot.financial.vo.CrossDeptLaborCostsVO;
import com.gok.pboot.financial.vo.CrossIncomeDeptVO;
import com.gok.pboot.financial.vo.DeptVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 跨部门人工成本台账Controller
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@RestController
@RequestMapping("/cross-dept-labor-costs")
@RequiredArgsConstructor
public class CrossDeptLaborCostsController {

    private final ICrossDeptLaborCostsService crossDeptLaborCostsService;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    /**
     * 列表展示跨部门人工成本台帐数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link R}<{@link List}<{@link CrossDeptLaborCostsVO}>>
     */
    @PostMapping("/findList")
    @PreAuthorize("@pms.hasPermission('cross-departmental-labor-cost-ledger')")
    @ApiOperation(value = "列表展示跨部门人工成本台账数据", notes = "列表展示跨部门人工成本台账数据")
    public R<List<CrossDeptLaborCostsVO>> findList(@RequestBody DeptArtificialCostDTO deptArtificialCostDTO) {
        return R.ok(crossDeptLaborCostsService.findList(deptArtificialCostDTO));
    }

    /**
     * 列表展示跨部门人工成本台帐明细数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link R}<{@link List}<{@link CrossDeptLaborCostsDetailVO}>>
     */
    @PostMapping("/findDetailList")
    @PreAuthorize("@pms.hasPermission('cross-departmental-labor-cost-ledger-details')")
    @ApiOperation(value = "列表展示跨部门人工成本台账明细数据", notes = "列表展示跨部门人工成本台账明细数据")
    public R<List<CrossDeptLaborCostsDetailVO>> findDetailList(@RequestBody DeptArtificialCostDTO deptArtificialCostDTO) {
        return R.ok(crossDeptLaborCostsService.findDetailList(deptArtificialCostDTO));
    }

    /**
     * 跨部门人工成本台账导出（异步导出）
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    @ResponseExcel(async = true, name = "跨部门人工成本台账", functionEnum = FunctionEnum.FINANCIAL_CRO_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('cross-departmental-labor-cost-ledger/export')")
    @ApiOperation(value = "跨部门人工成本台账导出", notes = "跨部门人工成本台账导出")
    public List<CrossDeptLaborCostsVO> export(@RequestBody DeptArtificialCostDTO deptArtificialCostDTO) {
        return crossDeptLaborCostsService.export(deptArtificialCostDTO);
    }

    /**
     * 跨部门人工成本台帐明细导出（异步导出）
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    @ResponseExcel(async = true, name = "跨部门人工成本台账明细", functionEnum = FunctionEnum.FINANCIAL_CRO_DE_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/detailExport")
    @PreAuthorize("@pms.hasPermission('cross-departmental-labor-cost-ledger-detail/export')")
    @ApiOperation(value = "跨部门人工成本台账明细导出", notes = "跨部门人工成本台账明细导出")
    public List<CrossDeptLaborCostsDetailVO> detailExport(@RequestBody DeptArtificialCostDTO deptArtificialCostDTO) {
        return crossDeptLaborCostsService.detailExport(deptArtificialCostDTO);
    }

    /**
     * 列表展示跨部门人工成本台帐分摊数据
     *
     * @param crossDeptLaborShareCostsDTO {@link CrossDeptLaborShareCostsDTO} 条件查询
     * @return {@link R}<{@link List}<{@link CrossIncomeDeptVO}>>
     */
    @PostMapping("/findShareVoList")
    @ApiOperation(value = "列表展示跨部门人工成本台账分摊数据", notes = "列表展示跨部门人工成本台账分摊数据")
    public R<CrossIncomeDeptVO> findShareVoList(@RequestBody CrossDeptLaborShareCostsDTO crossDeptLaborShareCostsDTO) {
        return R.ok(crossDeptLaborCostsService.findShareVoList(crossDeptLaborShareCostsDTO));
    }

    /**
     * 一级部门下拉框
     *
     * @return {@link R}<{@link List}<{@link CrossDeptLaborCostsVO}>>
     */
    @GetMapping("/firstDeptList")
    @ApiOperation(value = "一级部门下拉框", notes = "一级部门下拉框")
    public R<List<DeptVO>> firstDeptList() {
        List<DeptVO> deptList = new ArrayList<>();
        remoteOutMultiDeptService.getDeptList("行政组织", null, NumberUtils.INTEGER_ONE)
                .getData()
                .forEach(d -> deptList.add(DeptVO.builder().deptId(d.getDeptId()).deptName(d.getName()).build()));
        return R.ok(deptList);
    }

    /**
     * 二级部门下拉框
     *
     * @return {@link R}<{@link List}<{@link CrossDeptLaborCostsDetailVO}>>
     */
    @GetMapping("/secondDeptList")
    @ApiOperation(value = "二级部门下拉框", notes = "二级部门下拉框")
    public R<List<DeptVO>> secondDeptList() {
        List<DeptVO> deptList = new ArrayList<>();
        remoteOutMultiDeptService.getDeptList("行政组织", null, NumberUtils.INTEGER_TWO)
                .getData()
                .forEach(d -> deptList.add(DeptVO.builder().deptId(d.getDeptId()).deptName(d.getName()).build()));
        return R.ok(deptList);
    }
}
