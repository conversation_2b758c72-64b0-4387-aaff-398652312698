package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 应收账款台账 Dto
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountsReceivableDTO {

    /**
     * 当前页
     */
    private Integer current;

    /**
     * 每页条数
     */
    private Integer size;

    /**
     * 导出Excel所需要的id集合
     */
    private List<Long> ids;

    /**
     * 项目名称/编码
     */
    private String project;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同名称/编码
     */
    private String contract;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    private String projectStatus;

    /**
     * 归属主体
     */
    private String attributableSubject;

    /**
     * 归属一级部门
     */
    private List<Long> firstDepartmentId;

    /**
     * 合同金额
     */
    private BigDecimal contractMoneyMin;

    /**
     * 合同金额
     */
    private BigDecimal contractMoneyMax;

    /**
     * 相关人员
     * (原负责人名称（销售salesmanUserName/项目经理managerUserName））
     */
    private String leader;

    /**
     * 账龄区间（30天内/30-60天内/60-120天/120天以上）
     * {@link com.gok.pboot.financial.enums.AgingEnum}
     */
    private Integer[] aging;

    /**
     * 账龄区间（30天内/30-60天内/60-120天/120天以上）
     * {@link com.gok.pboot.financial.enums.AgingEnum}
     */
    private String[] agingTxt;

    /**
     * 开票待汇款金额
     */
    private BigDecimal invoicingCollectedMin;

    /**
     * 开票待汇款金额
     */
    private BigDecimal invoicingCollectedMax;

    /**
     * 排序属性
     * {@link com.gok.pboot.financial.enums.SortEnum}
     */
    private Integer sort;

    /**
     * 排序属性
     * {@link com.gok.pboot.financial.enums.SortEnum}
     */
    private String sortTxt;

    /**
     * 排序规则
     * {@link com.gok.pboot.financial.enums.SortRuleEnum}
     */
    private Integer sortRule;

    /**
     * 排序规则
     * {@link com.gok.pboot.financial.enums.SortRuleEnum}
     */
    private String sortRuleTxt;

    /**
     * 销售人员id
     */
    private List<Long> salesmanUserId;

    /**
     * 项目管理人员id
     */
    private List<Long> managerUserId;

    /**
     * 截止月份
     */
    private String yearMonth;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 动态导出表头
     */
    private List<String> includeExcel;
}
