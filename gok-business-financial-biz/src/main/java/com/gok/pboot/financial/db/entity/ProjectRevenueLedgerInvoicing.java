package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目收入台帐发票
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_revenue_ledger_invoicing")
@ApiModel("项目收入台帐发票")
public class ProjectRevenueLedgerInvoicing extends Model<ProjectRevenueLedgerInvoicing> {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 发票id
     */
    @ApiModelProperty("invoicingId")
    private Long invoicingId;

    /**
     * 项目收入台账id
     */
    @ApiModelProperty("项目收入台账id")
    private Long revenueLedgerId;

    /**
     * 发票日期
     */
    @ApiModelProperty("发票日期")
    private String invoicingDate;

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoicingNumber;

    /**
     * 开票金额（含税）
     */
    @ApiModelProperty("开票金额（含税）")
    private String invoicingAmountIncludingTax;

    /**
     * 税额
     */
    @ApiModelProperty("税额")
    private String taxAmount;

    /**
     * 开票金额（不含税）
     */
    @ApiModelProperty("开票金额（不含税）")
    private String invoicingAmount;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private Integer taxRate;

    /**
     * 开票主体
     */
    @ApiModelProperty("开票主体")
    private String invoicingSubject;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}