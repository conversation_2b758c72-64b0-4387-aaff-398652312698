package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户付款审批流程节点审批状态 Enum
 *
 * author luoyq
 * since 2023-09-01
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PaymentProcessStatusEnum implements ValueEnum<Integer> {

    // 未开始
    WKS(0,"未开始")
    ,

    // 进行中
    JXZ(1,"进行中")
    ,

    // 已完成
    YWC(2,"已完成")
   ;


    private final Integer value;

    private final String name;

}
