package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.enums.YesOrNoEnum;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.db.entity.FundAccount;
import com.gok.pboot.financial.db.mapper.AccountingSetMapper;
import com.gok.pboot.financial.db.mapper.FundAccountMapper;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.enums.EnableStatusEnum;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.AccountingSystemVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 会计账套业务层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Service
@RequiredArgsConstructor
public class AccountingSetServiceImpl extends ServiceImpl<AccountingSetMapper, AccountingSet>
        implements IAccountingSetService {

    private final LogRecordUtils logRecordUtils;

    private static final String CHANGE = "】变更为【";

    private final FundAccountMapper fundAccountMapper;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSetVO}>
     */
    @Override
    public Page<AccountingSetVO> findPage(AccountingSetPageDTO dto) {
        // 1、获取分页信息
        Page<AccountingSet> page = new Page<>(dto.getCurrent(), dto.getSize());
        Page<AccountingSetVO> accountingSetPage = baseMapper.findPage(page,dto);
        // 2、封装分页信息
        if (ObjectUtils.isNotEmpty(accountingSetPage.getRecords())) {
            accountingSetPage.getRecords().forEach(r -> {
                r.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, r.getEnableStatus()));
            });
        }
        return accountingSetPage;
    }

    /**
     * 导出Excel
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSetVO}>
     */
    @Override
    public List<AccountingSetVO> exportExcel(AccountingSetPageDTO dto) {
        List<AccountingSetVO> accountingSetList = new ArrayList<>();
        // 导出模板表
        if (CollUtil.isNotEmpty(dto.getIdList())) {
            if (NumberUtils.LONG_MINUS_ONE.equals(dto.getIdList().get(NumberUtils.INTEGER_ZERO))) {
                AccountingSetVO accountingSetVO = AccountingSetVO.builder()
                        .build();
                accountingSetList.add(accountingSetVO);
                return accountingSetList;
            }
        }

        // 1、查询数据
        accountingSetList = baseMapper.findPage(dto);

        // 2、返回数据
        if (ObjectUtils.isNotEmpty(accountingSetList)) {
            accountingSetList.forEach(r -> {
                r.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, r.getEnableStatus()));
            });
        }
        return accountingSetList;
    }

    /**
     * 新增
     *
     * @param dto {@link AccountingSetDTO}
     * @return {@code true or false>
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean addOrEdit(AccountingSetDTO dto) {
        // 1、获取数据
        AccountingSet as = baseMapper.selectOne(Wrappers.<AccountingSet>lambdaQuery()
                .eq(AccountingSet::getId, dto.getId())
        );
        if (Optional.ofNullable(as).isPresent()) {
            // 2.1、记录日志
            editLog(as, dto);
            // 2.2、更新数据
            AccountingSet accountingSet = BeanUtil.copyProperties(dto, AccountingSet.class);
            accountingSet.setAccountingRemarks(dto.getAccountingRemarks());
            return this.updateById(accountingSet);
        }
        // 3、新增操作
        AccountingSet accountingSet = BeanUtil.copyProperties(dto, AccountingSet.class, "id");
        accountingSet.setAccountingRemarks(dto.getAccountingRemarks());
        // 3.1、记录日志
        final String s = OperationEnum.ADD_ACCOUNTING_SET.getName() + "【"
                + dto.getAccountingCode() + "】【" + dto.getAccountingSubject() + "】";
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SET,
                OperationEnum.ADD_ACCOUNTING_SET, s);
        // 3.2、入库
        return this.save(accountingSet);
    }

    /**
     * 删除
     *
     * @param idList {@link List}<{@link Long}>
     * @return {@code true or false>
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean del(List<Long> idList) {
        // 1、防止空数组删除全数据
        if (ObjectUtils.isEmpty(idList)) {
            throw new BusinessException("删除接口传参有误");
        }
        // 2、查询数据记录日志
        StringBuilder s = new StringBuilder();
        s.append(OperationEnum.DELETE_ACCOUNTING_SET.getName());
        baseMapper.selectList(Wrappers.<AccountingSet>lambdaQuery().in(AccountingSet::getId, idList)
                .eq(AccountingSet::getEnableStatus, NumberUtils.INTEGER_ONE))
                .forEach(a -> s.append("【").append(a.getAccountingCode()).append(a.getAccountingSubject()).append("】"));
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SET,
                OperationEnum.DELETE_ACCOUNTING_SET, s.toString());
        // 3、删除数据
        return baseMapper.delByIds(idList);
    }

    /**
     * 启用
     *
     * @param dto {@link AccountingSetDTO>
     * @return {@code true or false>
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean enable(AccountingSetDTO dto) {
        // 1、获取数据
        List<AccountingSet> accountingSetList = baseMapper.selectList(Wrappers.<AccountingSet>lambdaQuery()
                .in(AccountingSet::getId, dto.getIdList())
                .eq(AccountingSet::getEnableStatus,dto.getEnableStatusFlag()?NumberUtils.INTEGER_ONE:NumberUtils.INTEGER_ZERO));

        // 2、修改数据状态并记录日志
        return enableLog(dto, accountingSetList);
    }

    /**
     * 获取页面数据集合
     *
     * @param accountingCode    {@link String} 账套编码
     * @param accountingSubject {@link String} 归属主体
     * @param enableStatus      {@link String} 启用状态
     * @return {@link List}<{@link AccountingSet}>
     */
    private List<AccountingSet> selectPageList(String accountingCode, String accountingSubject, Integer enableStatus) {
        return baseMapper.selectList(Wrappers.<AccountingSet>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(accountingCode), AccountingSet::getAccountingCode, accountingCode)
                .eq(ObjectUtils.isNotEmpty(accountingSubject), AccountingSet::getAccountingSubject, accountingSubject)
                .eq(ObjectUtils.isNotEmpty(enableStatus), AccountingSet::getEnableStatus, enableStatus)
                .orderByAsc(AccountingSet::getCreateTime)
        );
    }

    /**
     * 编辑/更新行数据时记录日志
     *
     * @param as  {@link AccountingSet}
     * @param dto {@link AccountingSetDTO}
     */
    private void editLog(AccountingSet as, AccountingSetDTO dto) {
        String accountingAccount = as.getAccountingAccount();
        String accountingPassword = as.getAccountingPassword();
        Integer enableStatus = as.getEnableStatus();
        // 1、账号密码变更日志记录
        if (!accountingAccount.equals(dto.getAccountingAccount())
                && !accountingPassword.equals(dto.getAccountingPassword())) {
            final String s = "【" + as.getAccountingCode() + "】【" + as.getAccountingSubject() + "】【账号"
                    + as.getAccountingAccount() + "】【密码" + as.getAccountingPassword() + CHANGE
                    + dto.getAccountingAccount() + "】【" + dto.getAccountingPassword() + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SET,
                    OperationEnum.UPDATE_ACCOUNT_PASSWORD, s);
        } else if (!accountingAccount.equals(dto.getAccountingAccount())) {
            final String s = "【" + as.getAccountingCode() + "】【" + as.getAccountingSubject() + "】【账号"
                    + as.getAccountingAccount() + CHANGE + dto.getAccountingAccount() + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SET,
                    OperationEnum.UPDATE_ACCOUNT_PASSWORD, s);
        } else if (!accountingPassword.equals(dto.getAccountingPassword())) {
            final String s = "【" + as.getAccountingCode() + "】【" + as.getAccountingSubject() + "】【密码"
                    + as.getAccountingPassword() + CHANGE + dto.getAccountingPassword() + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SET,
                    OperationEnum.UPDATE_ACCOUNT_PASSWORD, s);
        }
        // 2、启用状态变更日志记录
        if (!enableStatus.equals(dto.getEnableStatus())) {
            String enable = EnableStatusEnum.YES.getName();
            if (dto.getEnableStatus().equals(EnableStatusEnum.YES.getValue())) {
                enable = EnableStatusEnum.NO.getName();
            }
            final String s = "【" + as.getAccountingCode() + "】【" + as.getAccountingSubject() + "】【停用状态"
                    + enable + CHANGE + EnumUtils.getNameByValue(EnableStatusEnum.class, dto.getEnableStatus()) + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SET,
                    OperationEnum.EDIT_STATUS, s);
        }
    }

    /**
     * 修改启用状态日志
     *
     * @param dto               {@link AccountingSetDTO}
     * @param accountingSetList {@link List}<{@link AccountingSet}>
     * @return {@code true or false}
     */
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean enableLog(AccountingSetDTO dto, List<AccountingSet> accountingSetList) {
        if (!accountingSetList.isEmpty()) {
            // 1、启用状态 前者用来修改信息 后者用来记录日志
            Integer enableStatus = EnableStatusEnum.NO.getValue();
            String enableStatusTxt = EnableStatusEnum.YES.getName();
            if (Boolean.TRUE.equals(dto.getEnableStatusFlag())) {
                enableStatus = EnableStatusEnum.YES.getValue();
                enableStatusTxt = EnableStatusEnum.NO.getName();
            }
            // 2、记录日志
            StringBuilder s = new StringBuilder();
            s.append("【").append(enableStatusTxt).append(CHANGE)
                    .append(EnumUtils.getNameByValue(EnableStatusEnum.class, enableStatus))
                    .append("】: ");
            for (AccountingSet a : accountingSetList) {
                if (!enableStatus.equals(a.getEnableStatus())) {
                    a.setEnableStatus(enableStatus);
                    s.append("【").append(a.getAccountingCode()).append(a.getAccountingSubject()).append("】");
                }
            }
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SET, OperationEnum.EDIT_STATUS, s.toString());
            // 3、更新数据库
            return this.updateBatchById(accountingSetList);
        }
        return true;
    }

    @Override
    public Map<String, AccountingSet> getAccountingSet() {
        // 查询科目指标列表
        List<AccountingSet> accountingSetList = baseMapper
                .selectList(Wrappers.<AccountingSet>lambdaQuery()
                        .eq(AccountingSet::getAccountingType, "0")
                        .eq(AccountingSet::getEnableStatus, YesOrNoEnum.NO.getVal()));

        //key->核算部门编码,value->科目编码
        return accountingSetList.stream().collect(Collectors.toMap(AccountingSet::getAccountingSubject,
                a->a, (a, b) -> a));
    }

    /**
     * 导入Excel
     *
     * @param importDTOList {@link List}<{@link AccountingSetImportDTO}>
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> importExcel(List<AccountingSetImportDTO> importDTOList) {
        // 遍历封装后插入数据库
        List<AccountingSet> accountingSetList = new ArrayList<>();
        Map<String, Long> fundAccountMap = fundAccountMapper.selectList(new QueryWrapper<>())
                .stream().collect(Collectors.toMap(FundAccount::getFundAccount, FundAccount::getId, (a, b) -> a));

        if (CollUtil.isNotEmpty(importDTOList)) {
            StringBuilder error = new StringBuilder();
            AtomicInteger i = new AtomicInteger(2);
            importDTOList.forEach(p -> {
                // 校验
                this.checkImport(p, error, i.getAndIncrement(),fundAccountMap);
                // 1、项目回款
                AccountingSet accountingSet = BeanUtil.copyProperties(p, AccountingSet.class);
                accountingSet.setEnableStatus(EnumUtils.getValueByName(EnableStatusEnum.class, p.getEnableStatusStr()));
                accountingSet.setFundAccountId(fundAccountMap.getOrDefault(p.getFundAccount(),null));
                accountingSetList.add(accountingSet);
            });
            if (error.toString().contains("空")) {
                return R.failed("导入失败,原因是" + error.toString());
            }

            // 2、记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SET,
                    OperationEnum.IMPORT,
                    OperationEnum.IMPORT.getName() + "【" + accountingSetList.size() + "条】");

            return this.saveBatch(accountingSetList) ? R.ok("导入成功") : R.failed("导入失败");
        }

        return R.ok("无数据导入");
    }

    @Override
    public R<AccountingSetVO> info(Long id) {
        AccountingSetVO vo = baseMapper.selById(id);
        if(!Optional.ofNullable(vo).isPresent()){
            return R.failed("查询不到数据");
        }
        vo.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, vo.getEnableStatus()));

        return R.ok(vo);
    }

    /**
     * 校验导入参数
     *
     * @param dto {@link EducationPaymentExcelDTO}
     * @param error {@link StringBuilder}
     * @param row 行数
     */
    private void checkImport(AccountingSetImportDTO dto, StringBuilder error, Integer row,Map<String, Long> fundAccountMap) {
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getAccountingCode())) {
            error.append("账套编码不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountingAccount())) {
            error.append("账套账号不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountingPassword())) {
            error.append("账套密码不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountingSubject())) {
            error.append("归属主体不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getEnableStatusStr())) {
            error.append("停用状态不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountingType())) {
            error.append("账套类型不能为空。");
        }
        if (CharSequenceUtil.isBlank(dto.getFundAccount())) {
            Long fundAccountId = fundAccountMap.getOrDefault(dto.getFundAccount(), null);
            if (!Optional.ofNullable(fundAccountId).isPresent()){
                error.append("资金账户不存在。");
            }
        }
    }
}
