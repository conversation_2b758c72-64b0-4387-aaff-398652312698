package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentClaimDetailVO {
    /**
     * 客户经理id
     */
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    private String salesmanUserName;

    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPaymentId;

    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPayment;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 回款类型
     */
    private Integer paymentType;

    /**
     * 回款类型
     */
    private String paymentTypeText;

    /**
     * 款项金额
     */
    private String paymentMoney;

    /**
     * 保证金编号
     */
    private String marginCode;

    /**
     * 保证金类型
     */
    private Integer marginType;

    /**
     * 保证金类型
     */
    private String marginTypeText;

    /**
     * 保证金金额
     */
    private String marginMoney;

    /**
     * 认领金额
     */
    private String claimMoney;

    /**
     * 客户名称
     */
    private String relatedCustomerName;

    /**
     * 认领备注
     */
    private String claimRemark;

}
