package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 一对多查询封装为同一条行数据
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectRevenueLedger2VO {

    /**
     * 流程id
     */
    private Long id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 流程编码
     */
    private String processCoding;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 合同id
     */
    private String contractId;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 结项启动条件
     * {@link com.gok.pboot.financial.enums.ClosingStartConditionsEnum}
     */
    private Integer closingStartConditions;

    /**
     * 结项启动条件
     * {@link com.gok.pboot.financial.enums.ClosingStartConditionsEnum}
     */
    private String closingStartConditionsTxt;

    /**
     * 收入日期
     */
    private String incomeDate;

    /**
     * 收入金额（含税）
     */
    private String incomeAmountTax;

    /**
     * 收入金额（不含税）
     */
    private String incomeAmount;

    /**
     * 验收报告（0:是，1:否）
     * {@link com.gok.pboot.financial.enums.AcceptanceReportEnum}
     */
    private Integer acceptanceReport;

    /**
     * 验收报告（0:是，1:否）
     * {@link com.gok.pboot.financial.enums.AcceptanceReportEnum}
     */
    private String acceptanceReportTxt;

    /**
     * 验收日期
     */
    private String acceptanceDate;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 核算部门
     */
    private String virtualDeptName;

    /**
     * 科目名称
     */
    private String accountName;

    /**
     * 业务方向
     * {@link com.gok.pboot.financial.enums.BusinessDirectionEnum}
     */
    private Integer businessDirection;

    /**
     * 业务方向
     * {@link com.gok.pboot.financial.enums.BusinessDirectionEnum}
     */
    private String businessDirectionTxt;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同金额
     */
    private String contractMoney;

    /**
     * 业务归属一级部门id
     */
    private Long businessFirstDeptId;

    /**
     * 业务归属一级部门
     */
    private String businessFirstDept;

    /**
     * 业务归属二级部门
     */
    private String businessSecondaryDept;

    /**
     * 客户经理
     */
    private String salesmanUserName;

    /**
     * 申请人
     */
    private String applicantUserName;

    /**
     * 推送状态（0待推送，1已推送，2推送失败）
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private Integer pushStatus;

    /**
     * 推送状态（0待推送，1已推送，2推送失败）
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private String pushStatusTxt;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 发票明细 id v1.2.4隐藏
     */
    private String invoicingId;

    /**
     * 发票明细 发票日期 v1.2.4隐藏
     */
    private String invoicingDate;

    /**
     * 发票明细 发票号码 v1.2.4隐藏
     */
    private String invoicingNumber;

    /**
     * 发票明细 开票金额（含税） v1.2.4隐藏
     */
    private String invoicingAmountIncludingTax;

    /**
     * 发票明细 税额 v1.2.4隐藏
     */
    private String taxAmount;

    /**
     * 发票明细 开票金额（不含税） v1.2.4隐藏
     */
    private String invoicingAmount;

    /**
     * 发票明细 税率 v1.2.4隐藏
     */
    private String taxRate;

    /**
     * 发票明细 开票主体 v1.2.4隐藏
     */
    private String invoicingSubject;

    /**
     * 收入类型 v1.2.4新增
     */
    private String incomeType;

    /**
     * 收入明细金额含税 v1.2.4新增
     */
    private String incomeDetailAmountTax;

    /**
     * 收入明细金额不含税 v1.2.4新增
     */
    private String incomeDetailAmount;

    /**
     * 税率 v1.2.4新增
     */
    private String taxTate;

    /**
     * 合同所属公司 v1.2.4新增
     */
    private String contractCompany;
}
