package com.gok.pboot.financial.invoice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.dto.InvoicePlanDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoicePlanVO;
import com.gok.pboot.financial.invoice.service.IInvoicePlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 发票规划台账前端控制器
 *
 * <AUTHOR>
 * @create 2025/06/03
 * @menu 发票规划台账
 **/
@RestController
@RequestMapping("/invoicePlan")
@RequiredArgsConstructor
@Api(tags = "发票规划台账")
public class InvoicePlanController {

    private final IInvoicePlanService invoicePlanService;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link InvoicePlanVO}
     * @return {@link R}<{@link Page}<{@link InvoicePlanVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<InvoicePlanVO>> findPage(@RequestBody @Valid InvoicePlanDTO dto) {
        return R.ok(invoicePlanService.findPage(dto));
    }

    /**
     * 导出Excel
     *
     * @param dto {@link InvoicePlanVO}
     * @return {@link Page}<{@link InvoicePlanVO}>
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(async = true, name = "发票规划台账", nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<InvoicePlanVO> exportExcel(@RequestBody InvoicePlanDTO dto) {
        return invoicePlanService.exportExcel(dto);
    }

    /**
     * PMS导出Excel
     *
     * @param dto {@link InvoicePlanVO}
     * @return {@link Page}<{@link InvoicePlanVO}>
     */
    @PostMapping("/pms/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(name = "发票规划台账", nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<InvoicePlanVO> pmsExportExcel(@RequestBody InvoicePlanDTO dto) {
        return invoicePlanService.exportExcel(dto);
    }

    /**
     * 申请开票
     * 发起对应OA流程
     *
     * @param id
     * @return 推送成功发票规划ID集合[]
     */
    @ApiOperation(value = "申请开票", notes = "申请开票")
    @PostMapping("/apply/{id}")
    public R<String> applyInvoice(@PathVariable("id") Long id) {
        return invoicePlanService.applyInvoice(id);
    }
}
