package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 发票单据 展示
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InvoicingDocumentVO {

    /**
     * 发票日期
     */
    private String yearMonthDate;

    /**
     * 开票金额
     */
    private String money;

    /**
     * 发票号码
     */
    private String number;

    /**
     * 开票类型
     */
    private String type;

    /**
     * 开票税率
     */
    private String taxRate;

    /**
     * 开票内容
     */
    private String content;

    /**
     * 开票说明
     */
    private String description;

    /**
     * 发票申请人
     */
    private String application;

}
