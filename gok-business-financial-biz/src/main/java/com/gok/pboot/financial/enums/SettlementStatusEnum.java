package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 *  结算状态Enum
 *
 * author caihf
 * since 2023-09-01
 */
@Getter
public enum SettlementStatusEnum implements ValueEnum<Integer> {


        // 待结算
    PENDING_SETTLEMENT(0,"待结算"),
    // 无需结算
    NO_SETTLEMENT_REQUIRED(1,"无需结算"),

    // 已结算
    SETTLED(2,"已结算"),
    // 结算中
    UNDER_SETTLEMENT(3,"结算中"),
    // 部分结算
    PARTIALLY_SETTLEMENT(4,"部分结算"),
    // 结算失败
    SETTLEMENT_FAILED(5,"结算失败")
   ;




    private final Integer value;

    private final String name;

    SettlementStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
