package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 账龄区间 Enum
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Getter
public enum AgingEnum implements ValueEnum<Integer> {

    /**
     * 30天内
     */
    WITHIN_THIRTY(0, "within_thirty"),

    /**
     * 30-60天内
     */
    THIRTY_SIXTY(1, "thirty_sixty"),

    /**
     * 60-120天
     */
    SIXTY_HUNDREDTWENTY(2, "sixty_hundredTwenty"),

    /**
     * 120天以上
     */
    ABOVE_HUNDREDTWENTY(3, "above_hundredTwenty");

    private final Integer value;

    private final String name;

    AgingEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
