package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.EducationPayment;
import com.gok.pboot.financial.dto.EducationDTO;
import com.gok.pboot.financial.dto.EducationPaymentClaimDTO;
import com.gok.pboot.financial.dto.EducationPaymentDTO;
import com.gok.pboot.financial.dto.EducationPaymentExcelDTO;
import com.gok.pboot.financial.vo.EducationPaymentClaimVO;
import com.gok.pboot.financial.vo.EducationVO;

import java.util.List;

/**
 * 教育回款Service
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
public interface IEducationPaymentService extends IService<EducationPayment> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}<{@link EducationPayment}>
     * @param educationDTO {@link EducationDTO}
     * @return {@link Page}<{@link EducationVO}>
     */
    StatisticsPage<EducationVO> findPage(Page<EducationPayment> page, EducationDTO educationDTO);

    /**
     * 导出所选数据
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link List}
     */
    List export(EducationDTO educationDTO);

    /**
     * 认领（已认领 -> 待认领）
     *
     * @param id 教育回款id
     * @return R {@link R}
     */
    R<String> claim(Long id);

    /**
     * 认领（待认领 -> 已认领）
     *
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     * @return {@code true} or {@code false}
     */
    Boolean claim(EducationPaymentClaimDTO educationPaymentClaimDTO);

    /**
     * 新增教育回款
     *
     * @param educationPaymentDTO {@link EducationPaymentDTO}
     * @return {@code 0} or {@code 1}
     */
    Integer save(EducationPaymentDTO educationPaymentDTO);

    /**
     * 汇总
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@code true} or {@code false}
     */
    Boolean summary(PaymentDTO paymentDTO);

    /**
     * 取消汇总
     *
     * @param id 教育回款id
     * @return R {@link R}
     */
    R<String> unSummary(Long id);

    /**
     * 根据id获取教育回款详情
     *
     * @param id 教育回款id
     * @return {@link EducationPaymentClaimVO}
     */
    EducationPaymentClaimVO getOne(Long id);

    /**
     * 根据id更新教育回款详情
     *
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     * @return {@code true} or {@code false}
     */
    Boolean update(EducationPaymentClaimDTO educationPaymentClaimDTO);

    /**
     * 锁定
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@code true} or {@code false}
     */
    Boolean lock(EducationDTO educationDTO);

    /**
     * 删除
     *
     * @param ids {@link List}
     * @return {@link R}
     */
    R<String> delete(List<Long> ids);

    /**
     * 导入
     *
     * @param educationPaymentExcelDTOList {@link List}<{@link EducationPaymentExcelDTO}>
     * @return {@link R}
     */
    R<String> importExcel(List<EducationPaymentExcelDTO> educationPaymentExcelDTOList);

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    R<String> updateLockStatus();
}
