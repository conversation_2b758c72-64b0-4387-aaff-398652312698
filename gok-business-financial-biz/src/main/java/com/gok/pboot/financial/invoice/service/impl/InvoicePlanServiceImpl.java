package com.gok.pboot.financial.invoice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.base.admin.feign.RemoteOaService;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.user.PigxUser;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.exception.ServiceException;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.common.client.OaClient;
import com.gok.pboot.financial.dto.OaMainParamDTO;
import com.gok.pboot.financial.invoice.entity.domain.InvoicePlan;
import com.gok.pboot.financial.invoice.entity.dto.InvoicePlanDTO;
import com.gok.pboot.financial.invoice.entity.dto.OaCreateRequestDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoicePlanVO;
import com.gok.pboot.financial.invoice.mapper.InvoicePlanMapper;
import com.gok.pboot.financial.invoice.service.IInvoicePlanService;
import com.gok.pboot.financial.util.OaUtil;
import com.gok.pboot.financial.vo.OaAccountVO;
import com.gok.pboot.utils.RSAUtils;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/05/29
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoicePlanServiceImpl extends ServiceImpl<InvoicePlanMapper, InvoicePlan> implements IInvoicePlanService {

    private final RemoteBcpDictService remoteBcpDictService;

    private final RemoteOaService remoteOaService;

    private final DBApi dbApi;
    private final OaUtil oaUtil;
    private final OaClient oaClient;

    @Value("${gok-oa.workflowUrl}")
    private String workflowUrl;

    /**
     * oa的url地址
     */
    @Value("${gok-oa.url}")
    private String url;

    /**
     * oa的资源appId
     */
    @Value("${gok-oa.resourcesAppId}")
    private String appId;

    /**
     * oa的spk
     */
    @Value("${gok-oa.spk}")
    private String spk;

    @Override
    public StatisticsPage<InvoicePlanVO> findPage(InvoicePlanDTO dto) {
        Page<InvoicePlanVO> invoicePlanVOPage = baseMapper.findPage(new Page<>(dto.getCurrent(), dto.getSize()), dto);
        StatisticsPage<InvoicePlanVO> statisticsPage =
                new StatisticsPage<>(invoicePlanVOPage.getCurrent(), invoicePlanVOPage.getSize(), invoicePlanVOPage.getTotal());
        List<InvoicePlanVO> records = invoicePlanVOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return statisticsPage;
        }

        Map<String, BigDecimal> pageSum = baseMapper.findPageSum(dto);
        statisticsPage.setStatistics(pageSum);

        handleInvoicePlanVO(records);
        statisticsPage.setRecords(records);
        return statisticsPage;
    }

    @Override
    public List<InvoicePlanVO> exportExcel(InvoicePlanDTO dto) {
        dto.setCurrent(0L);
        dto.setSize(Long.MAX_VALUE);
        return CollUtil.emptyIfNull(findPage(dto).getRecords());
    }

    @Override
    public R<String> applyInvoice(Long id) {
        PigxUser user = SecurityUtils.getUser();
        Long currentUserId = user.getId();
        //获得当前用户免登录token
        String redirectOaToken = getOaTokenByUserId(currentUserId);
        // 查询OA用户信息
        OaAccountVO oaAccountVO = dbApi.getOaAccountInfoVOByUserId(currentUserId);
        if (oaAccountVO == null || oaAccountVO.getOaId() == null) {
            log.error("OA用户信息:{}", oaAccountVO);
            throw new ServiceException("发起OA流程失败，原因:未查询到当前用户的OA信息~");
        }
        try {
            InvoicePlan invoicePlan = baseMapper.selectById(id);
            if (null == invoicePlan) {
                return R.failed("根据id:" + id + ",没有找到对应发票规划台账数据");
            }
            Long requestId = generateRequestId(oaAccountVO, invoicePlan);
            return R.ok(StrUtil.format(workflowUrl, redirectOaToken, requestId), "请求成功");
        } catch (Exception e) {
            log.error("发起OA流程失败:{}", e.getMessage());
            e.printStackTrace();
            throw new ServiceException("发起OA流程失败:" + e.getMessage());
        }
    }

    /**
     * 生成OA流程请求ID
     *
     * @param oaAccountVO OA账户信息
     * @return OA流程请求ID
     * @throws JsonProcessingException JSON处理异常
     */
    private Long generateRequestId(final OaAccountVO oaAccountVO,
                                   InvoicePlan invoicePlan) throws JsonProcessingException {
        // 创建OA请求DTO
        OaCreateRequestDTO oaCreateRequestDTO = new OaCreateRequestDTO();
        String flowTypeName = "XM-09发票开具申请";
        oaCreateRequestDTO.setOaAccountVO(oaAccountVO);
        oaCreateRequestDTO.setWorkflowId(dbApi.getWorkflowIdByName(flowTypeName));

        // 构建主表数据
        List<OaMainParamDTO> mainDataList = new ArrayList<>();
        mainDataList.add(buildMainParam("ApplicantID", String.valueOf(oaAccountVO.getOaId())));
        mainDataList.add(buildMainParam("bh", String.valueOf(oaAccountVO.getWorkcode())));
        mainDataList.add(buildMainParam("ApplicantDeptID", oaAccountVO.getDepartmentid()));
        mainDataList.add(buildMainParam("ApplicantTel", oaAccountVO.getMobile()));
        mainDataList.add(buildMainParam("ApplicantJobID", oaAccountVO.getJobtitle()));
        mainDataList.add(buildMainParam("xmmc", String.valueOf(invoicePlan.getProjectId())));
        mainDataList.add(buildMainParam("kpxmmc", String.valueOf(invoicePlan.getProjectId())));
        mainDataList.add(buildMainParam("xmbm", invoicePlan.getProjectNo()));
        if (null != invoicePlan.getContractId()) {
            mainDataList.add(buildMainParam("sfyht", "0"));
            mainDataList.add(buildMainParam("htmc", invoicePlan.getContractName()));
            mainDataList.add(buildMainParam("htbh", invoicePlan.getContractNo()));
            mainDataList.add(buildMainParam("htjsfs", String.valueOf(invoicePlan.getContractSettlementType())));
            mainDataList.add(buildMainParam("contract", String.valueOf(invoicePlan.getContractId())));
            mainDataList.add(buildMainParam("htqdkhmc", String.valueOf(invoicePlan.getContractCustomerId())));
        }

        List<Map<String, Object>> detailDataList = new ArrayList<>(1);
        Map<String, Object> detailDataMap = new HashMap<>();
        detailDataMap.put("tableDBName", "formtable_main_118_dt1");
        detailDataMap.put("deleteAll", "1");

        List<Map<String, Object>> recordMaps = new ArrayList<>();
        List<OaMainParamDTO> tableFieldList = new ArrayList<>();
        tableFieldList.add(buildMainParam("kpgh", String.valueOf(invoicePlan.getId())));
        Map<String, Object> tableRecordMap = new HashMap<>(2);
        tableRecordMap.put("recordOrder", 0);
        tableRecordMap.put("workflowRequestTableFields", tableFieldList);
        recordMaps.add(tableRecordMap);

        detailDataMap.put("workflowRequestTableRecords", recordMaps);
        detailDataList.add(detailDataMap);

        // 获取OA标题
        String oaTitle = StrUtil.format(flowTypeName + "-{}-{}"
                , SecurityUtils.getUser().getName()
                , LocalDate.now());

        // 设置请求名称和数据
        oaCreateRequestDTO.setRequestName(oaTitle)
                .setMainData(mainDataList)
                .setDetailData(detailDataList)
                .setOtherParams(Collections.singletonMap("isnextflow", "0"));

        // 发起工作流
        Long requestId = initiateWorkflow(oaCreateRequestDTO);

        return requestId;
    }

    /**
     * 批量处理InvoicePlanVO
     *
     * @param voList InvoicePlanVO集合
     */
    private void handleInvoicePlanVO(List<InvoicePlanVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 获取中台字典
        Set<String> dictKeys = new HashSet<>(Arrays.asList("税率", "开票类型", "商品/服务分类", "所属公司", "结算方式"));
        Map<String, List<DictKvVo>> allDictMap = remoteBcpDictService.getDictKvBatchList(dictKeys).getData();
        Map<Integer, String> taxRateMap = allDictMap.getOrDefault("税率", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> invoiceTypeMap = allDictMap.getOrDefault("开票类型", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> goodsServiceNameMap = allDictMap.getOrDefault("商品/服务分类", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> contractSubjectMap = allDictMap.getOrDefault("所属公司", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> settlementMethodMap = allDictMap.getOrDefault("结算方式", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));

        // 数字处理成千分位
        DecimalFormat formatter = new DecimalFormat("##,##0.00");

        voList.forEach(vo -> {
            vo.setInvoiceTypeTxt(null != vo.getInvoiceType() ? invoiceTypeMap.get(vo.getInvoiceType()) : null);
            vo.setContractSubjectTxt(null != vo.getContractSubject() ? contractSubjectMap.get(vo.getContractSubject()) : null);
            vo.setPlanTaxRateTxt(Optional.ofNullable(vo.getPlanTaxRate()).isPresent() ? taxRateMap.get(vo.getPlanTaxRate()) : null);
            vo.setActualTaxRateTxt(Optional.ofNullable(vo.getActualTaxRate()).isPresent() ? taxRateMap.get(vo.getActualTaxRate()) : null);
            vo.setGoodsServiceNameTxt(null != vo.getGoodsServiceName() ? goodsServiceNameMap.get(vo.getGoodsServiceName()) : null);
            vo.setContractSettlementTypeTxt(null != vo.getContractSettlementType() ? settlementMethodMap.get(vo.getContractSettlementType()) : null);
            vo.setPlanAmountIncludedTaxTxt(null != vo.getPlanAmountIncludedTax() ? formatter.format(vo.getPlanAmountIncludedTax()) : null);
            vo.setPlanAmountExcludingTaxTxt(null != vo.getPlanAmountExcludingTax() ? formatter.format(vo.getPlanAmountExcludingTax()) : null);
            vo.setActualAmountIncludedTaxTxt(null != vo.getActualAmountIncludedTax() ? formatter.format(vo.getActualAmountIncludedTax()) : null);
            vo.setActualAmountExcludingTaxTxt(null != vo.getActualAmountExcludingTax() ? formatter.format(vo.getActualAmountExcludingTax()) : null);
            vo.setPendingAmountIncludedTaxTxt(null != vo.getPendingAmountIncludedTax() ? formatter.format(vo.getPendingAmountIncludedTax()) : null);
            vo.setPendingAmountExcludingTaxTxt(null != vo.getPendingAmountExcludingTax() ? formatter.format(vo.getPendingAmountExcludingTax()) : null);
        });
    }

    @NotNull
    private String getOaTokenByUserId(Long currentUserId) {
        String redirectOaToken;
        try {
            com.gok.components.common.util.R<String> res = remoteOaService.getOaSsoTokenByUserId(currentUserId);
            redirectOaToken = res.getData();
            if (StrUtil.isBlank(redirectOaToken)) {
                log.error("获取跳转token为空,{}", res);
                throw new ServiceException("跳转失败，请使用有OA权限的账号~");
            }
        } catch (Exception e) {
            log.error("获取跳转token失败:{}", e.getMessage());
            throw new ServiceException("获取跳转token失败" + e.getMessage());
        }

        return redirectOaToken;
    }

    /**
     * 构建主表参数DTO
     */
    private OaMainParamDTO buildMainParam(String fieldName, String fieldValue) {
        return OaMainParamDTO.builder()
                .fieldName(fieldName)
                .fieldValue(fieldValue)
                .build();
    }

    private Long initiateWorkflow(OaCreateRequestDTO oaCreateRequestDTO) throws JsonProcessingException {
        // 获取token
        final String token = oaUtil.getToken();
        // 对userID进行加密
        final OaAccountVO oaAccountVO = oaCreateRequestDTO.getOaAccountVO();
        final String encryptUserId = RSAUtils.encrypt(String.valueOf(oaAccountVO.getOaId()), spk);
        final String requestName = oaCreateRequestDTO.getRequestName();
        final Map<String, Object> otherParams = null != oaCreateRequestDTO.getOtherParams() ? oaCreateRequestDTO.getOtherParams() : new HashMap<>(0);
        final List<OaMainParamDTO> mainData = oaCreateRequestDTO.getMainData();
        final Integer workflowId = oaCreateRequestDTO.getWorkflowId();
        final List<Map<String, Object>> detailData = CollUtil.emptyIfNull(oaCreateRequestDTO.getDetailData());
        log.info("OA流程请求参数：workflowId:{}\nRequestName:{}\nmainParam:{}\notherParams:{}\ndetailParam:{}", workflowId,
                requestName, mainData.toArray(), otherParams, detailData.toArray());

        String mainParamStr = new ObjectMapper().writeValueAsString(mainData);
        String otherParamsStr = JSONUtil.toJsonStr(otherParams);
        String detailParamStr = new ObjectMapper().writeValueAsString(detailData);
        // 发起OA流程
        JSONObject jsonObject = oaClient.doCreateRequest(token, appId, encryptUserId, url, String.valueOf(workflowId),
                requestName, mainParamStr, otherParamsStr, detailParamStr);
        if (!"SUCCESS".equals(jsonObject.getString("code"))) {
            log.error("原因:{}", jsonObject);
            throw new ServiceException("发起OA流程失败1，原因:" + jsonObject);
        }
        //流程id
        Long requestId = jsonObject.getJSONObject("data").getLong("requestid");
        oaCreateRequestDTO.setRequestId(requestId);
        return requestId;
    }


}
