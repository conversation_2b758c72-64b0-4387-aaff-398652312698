package com.gok.pboot.financial.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.db.entity.SalesReceiptPaymentProcess;
import com.gok.pboot.financial.db.mapper.SalesReceiptPaymentProcessMapper;
import com.gok.pboot.financial.service.ISalesReceiptPaymentProcessService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2025/6/5
 **/

@Service
@RequiredArgsConstructor
public class SalesReceiptPaymentProcessServiceImpl extends ServiceImpl<SalesReceiptPaymentProcessMapper, SalesReceiptPaymentProcess>
        implements ISalesReceiptPaymentProcessService {
}
