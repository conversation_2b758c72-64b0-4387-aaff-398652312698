package com.gok.pboot.financial.controller;

import com.gok.components.common.util.R;
import com.gok.pboot.financial.service.IProjectRevenueLedgerDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目收入台账明细
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@RestController
@RequiredArgsConstructor
@Api(tags = "项目收入台账明细")
@RequestMapping("/project-income-account-detail")
public class ProjectRevenueLedgerDetailController {

    private final IProjectRevenueLedgerDetailService projectRevenueLedgerDetailService;

    /**
     * 归属主体（合同所属公司）查询列表
     *
     * @return {@link R}<{@link List}<{@link String}>>
     */
    @GetMapping("/company")
    @ApiOperation(value = "归属主体（合同所属公司）查询列表", notes = "归属主体（合同所属公司）查询列表")
    public R<List<String>> getContractCompany() {
        return R.ok(projectRevenueLedgerDetailService.getContractCompany());
    }

    /**
     * 收入类型查询列表
     *
     * @return {@link R}<{@link List}<{@link String}>>
     */
    @GetMapping("/income-type")
    @ApiOperation(value = "收入类型查询列表", notes = "收入类型查询列表")
    public R<List<String>> getIncomeType() {
        return R.ok(projectRevenueLedgerDetailService.getIncomeType());
    }
}
