package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 凭证部门信息实体类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TPlusDeptDTO {

    /**
     * 默认部门id
     */
    private String  deptId;

    /**
     * 默认部门
     */
    private String  dept;

    /**
     * T+默认部门编码
     */
    private String  tDeptId;

    /**
     * T+默认部门
     */
    private String  tDeptName;
}
