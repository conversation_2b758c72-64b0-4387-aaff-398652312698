package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.vo.AccountRelationshipVO;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.db.entity.AccountingAccount;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipDTO;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipPageDTO;
import com.gok.pboot.financial.enums.EnableStatusEnum;
import com.gok.pboot.financial.service.IAccountingAccountService;
import com.gok.pboot.financial.service.IIndicatorAccountRelationshipService;
import com.gok.pboot.financial.vo.IndicatorAccountRelationshipVO;
import com.gok.pboot.financial.vo.SubjectVO;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 指标科目关系
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@RestController
@RequestMapping("/indicator-account-relationship")
@RequiredArgsConstructor
public class IndicatorAccountRelationshipController {

    private final IIndicatorAccountRelationshipService indicatorAccountRelationshipService;

    private final IAccountingAccountService accountingAccountService;

    /**
     * 分页查询
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO} 分页条件
     * @return {@link R}<{@link Page}<{@link IndicatorAccountRelationshipVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('IndicatorAccountRelationship')")
    public R<Page<IndicatorAccountRelationshipVO>> findPage(@RequestBody @Valid IndicatorAccountRelationshipPageDTO dto) {
        return R.ok(indicatorAccountRelationshipService.findPage(dto));
    }

    /**
     * 根据id获取详情
     *
     * @param id 科目id
     * @return {@link R}<{@link IndicatorAccountRelationshipVO}>
     */
    @GetMapping("/getOne")
    public R<IndicatorAccountRelationshipVO> getOne(@RequestParam("id") Long id) {
        return R.ok(indicatorAccountRelationshipService.getOne(id));
    }

    /**
     * 根据id批量关联会计科目
     *
     * @param ids  ids
     * @param code 法典
     * @return {@link R }<{@link String }>
     */
    @PostMapping("/relatedSubjects/{code}")
    public R<String> relatedSubjects(@RequestBody Set<Long> ids,
                                     @PathVariable("code") String code) {
        indicatorAccountRelationshipService.relatedAccountingSubjects(ids, code);
        return R.ok("关联成功");
    }


    /**
     * 导出选中数据（异步导出）
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO}
     * @return 导出Excel
     */
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('IndicatorAccountRelationship/export')")
    @ResponseExcel(async = true, name = "指标科目关系", functionEnum = FunctionEnum.FINANCIAL_INDICATOR_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<IndicatorAccountRelationshipVO> export(@RequestBody IndicatorAccountRelationshipPageDTO dto) {
        return indicatorAccountRelationshipService.export(dto);
    }

    /**
     * 导入Excel
     *
     * @param indicatorAccountRelationshipDTOList {@link List}<{@link IndicatorAccountRelationshipDTO}>
     * @return {@link R}
     */
    @PostMapping("/import")
    @PreAuthorize("@pms.hasPermission('IndicatorAccountRelationship/import')")
    public R<String> importExcel(@RequestExcel(async = true) List<IndicatorAccountRelationshipDTO> indicatorAccountRelationshipDTOList) {
        return indicatorAccountRelationshipService.importExcel(indicatorAccountRelationshipDTOList);
    }

    /**
     * 科目编码名称列表
     *
     * @param accountingSystemId 会计体系 ID
     * @return {@link R}<{@link List}<{@link SubjectVO}>>
     */
    @GetMapping("/getSubject/{accountingSystemId}")
    public R<List<SubjectVO>> getSubject(@PathVariable Long accountingSystemId) {
        List<SubjectVO> list = accountingAccountService
                .lambdaQuery()
                .eq(AccountingAccount::getAccountingSystemId, accountingSystemId)
                .eq(AccountingAccount::getEnableStatus, EnableStatusEnum.YES)
                .list().stream()
                .map(e -> SubjectVO.builder()
                        .name(e.getAccountName())
                        .code(e.getAccountCode())
                        .build())
                .collect(Collectors.toList());
        return R.ok(list);
    }


    /**
     * 同步更新指标科目关系
     *
     * @return {@link R }<{@link String }>
     */
    @GetMapping("/sync")
    @Inner(false)
    public R<String> syncUpdateRelationship() {
        indicatorAccountRelationshipService.syncUpdateRelationship();
        return R.ok("同步成功");
    }

    /**
     * 通过会计体系编码查询指标科目关系列表
     *
     * @param systemCode 会计体系编码
     * @return {@link R}<{@link List}<{@link AccountRelationshipVO}>>
     */
    @GetMapping("/getBySystemCode")
    @Inner(false)
    public R<List<AccountRelationshipVO>> getBySystemCode(@RequestParam("systemCode") String systemCode) {
        List<AccountRelationshipVO> result = indicatorAccountRelationshipService.getBySystemCode(systemCode);
        return R.ok(result);
    }


//    /**
//     * 根据id更新科目信息
//     *
//     * @param indicatorAccountRelationshipDTO {@link IndicatorAccountRelationshipDTO}
//     * @return {@link R}
//     */
//    @PutMapping("/update")
//    @PreAuthorize("@pms.hasPermission('IndicatorAccountRelationship/edit')")
//    public R<String> update(@Valid @RequestBody IndicatorAccountRelationshipDTO indicatorAccountRelationshipDTO) {
//        return indicatorAccountRelationshipService.update(indicatorAccountRelationshipDTO) > 0 ? R.ok("更新数据成功") : R.failed("更新数据失败");
//    }
//
//    /**
//     * 插入指定科目关系
//     *
//     * @param indicatorAccountRelationshipDTO {@link IndicatorAccountRelationshipDTO}
//     * @return {@link R}
//     */
//    @PostMapping("/save")
//    @PreAuthorize("@pms.hasPermission('IndicatorAccountRelationship/add')")
//    public R<String> save(@Valid @RequestBody IndicatorAccountRelationshipDTO indicatorAccountRelationshipDTO) {
//        return indicatorAccountRelationshipService.save(indicatorAccountRelationshipDTO) > 0 ? R.ok("新增数据成功") : R.failed("新增数据失败");
//    }
//
//    /**
//     * 根据id删除科目信息
//     *
//     * @param indicatorAccountRelationshipDTO {@link IndicatorAccountRelationshipDTO}
//     * @return {@link R}
//     */
//    @DeleteMapping("/delete")
//    @PreAuthorize("@pms.hasPermission('IndicatorAccountRelationship/del')")
//    public R<String> delete(@RequestBody IndicatorAccountRelationshipDTO indicatorAccountRelationshipDTO) {
//        return indicatorAccountRelationshipService.delete(indicatorAccountRelationshipDTO.getIds());
//    }

}
