package com.gok.pboot.financial.service.impl;


import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gok.bcp.flowable.task.feign.RemoteWorkflowService;
import com.gok.bcp.flowable.task.vo.OaWorkflowRequestbaseVo;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.util.R;
import com.gok.pboot.base.PageRequest;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.common.client.OaClient;
import com.gok.pboot.financial.common.client.OaProperties;
import com.gok.pboot.financial.db.entity.FinanceProcessForm;
import com.gok.pboot.financial.db.mapper.FinanceProcessFormMapper;
import com.gok.pboot.financial.dto.ConfirmSettlementDTO;
import com.gok.pboot.financial.dto.FinancialProcessFormPageDTO;
import com.gok.pboot.financial.dto.OaMainParamDTO;
import com.gok.pboot.financial.dto.OaSubmitRequestDTO;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAuthorityService;
import com.gok.pboot.financial.service.IFinanceProcessFormService;
import com.gok.pboot.financial.util.DecimalFormatUtil;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.vo.ConfirmSettlementBatchVO;
import com.gok.pboot.financial.vo.FinanceProcessFormBaseVO;
import com.gok.pboot.financial.vo.FinanceProcessFormVO;
import com.gok.pboot.utils.RSAUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 财务处理单业务层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FinanceProcessFormServiceImpl extends ServiceImpl<FinanceProcessFormMapper, FinanceProcessForm>
        implements IFinanceProcessFormService {

    private final DBApi dbApi;
    private final OaClient oaClient;

    private final OaProperties properties;

    private final RemoteWorkflowService workflowService;

    private final IAuthorityService authorityService;


//    public StatisticsPage<FinanceProcessFormVO> findPage(FinancialProcessFormPageDTO dto) {
//        StopWatch stopWatch = StopWatch.createStarted();
//
//        statusFilter(dto);
//
//        Set<Integer> approvalStatusList;
//        if(CollUtil.isNotEmpty(dto.getApprovalStatus())){
//            approvalStatusList = new HashSet<>(dto.getApprovalStatus());
//        }else {
//            approvalStatusList = new HashSet<>();
//        }
//         //出纳节点可能已结算，查询实时流程状态
//        if(CollUtil.isNotEmpty(approvalStatusList)&&approvalStatusList.contains(ApprovalStatusEnum.CASHIER_APPROVAL.getValue())){
//            dto.setApprovalStatus(null);
//        }
//        StopWatch stopWatch3 = StopWatch.createStarted();
//        List<FinanceProcessFormVO> financeProcessFormVOList = baseMapper.findList(dto);
//        financeProcessFormVOList = this.getAuthority(dto,financeProcessFormVOList);
//        PageRequest pageRequest;
//        if(dto.getIfExport()){
//             pageRequest = new PageRequest(1,Integer.MAX_VALUE);
//        }else{
//             pageRequest = new PageRequest(dto.getCurrent().intValue(),dto.getSize().intValue());
//        }
//
//        if (CollUtil.isEmpty(financeProcessFormVOList)) {
//            return new StatisticsPage<>(pageRequest.getPageNumber(),  pageRequest.getPageSize());
//        }
//        stopWatch3.stop();
//        log.info("全部查询 took {} milliseconds.", stopWatch3.getTime());
//        //流程实时归档
//        Map<Long, String> requestidStatusMap;
//        if (CollUtil.isEmpty(approvalStatusList) || approvalStatusList.contains(ApprovalStatusEnum.CASHIER_APPROVAL.getValue())) {
//            requestidStatusMap = getFlowStatus(financeProcessFormVOList);
//        } else {
//            requestidStatusMap = Collections.emptyMap();
//        }
//
//        //结算状态，审批状态过滤，排序
//        StopWatch stopWatch4 = StopWatch.createStarted();
//        Set<Integer> finalSettlementStatus = new HashSet<>(CollUtil.emptyIfNull(dto.getSettlementStatus()));
//        financeProcessFormVOList = financeProcessFormVOList
//                        .stream()
//                        .peek(f -> {
//                            if (StrUtil.isNotBlank(requestidStatusMap.get(f.getRequestid()))) {
//                                f.setApprovalStatus(ApprovalStatusEnum.FILE.getValue());
//                            }
//                            if (ApprovalStatusEnum.FILE.getValue().equals(f.getApprovalStatus())
//                                    && SettlementStatusEnum.PENDING_SETTLEMENT.getValue().equals(f.getSettlementStatus())) {
//                                f.setSettlementStatus(SettlementStatusEnum.NO_SETTLEMENT_REQUIRED.getValue());
//                            }
//                        })
//                        .filter(f -> (CollUtil.isEmpty(approvalStatusList)
//                                || (CollUtil.isNotEmpty(approvalStatusList)
//                                && approvalStatusList.contains(f.getApprovalStatus())))
//                                && ((CollUtil.isEmpty(finalSettlementStatus)
//                                || (CollUtil.isNotEmpty(finalSettlementStatus)
//                                && finalSettlementStatus.contains(f.getSettlementStatus())))))
//                        .sorted(Comparator.comparing(FinanceProcessFormVO::getApplicationDate).reversed())
//                        .collect(Collectors.toList());
//        stopWatch4.stop();
//        log.info("结算状态，审批状态过滤，排序 took {} milliseconds.", stopWatch4.getTime());
//
//        StopWatch stopWatch2 = StopWatch.createStarted();
//        StatisticsPage<FinanceProcessFormVO> page = new StatisticsPage<>(pageRequest.getPageNumber(),pageRequest.getPageSize());
//        int size = financeProcessFormVOList.size();
//        if(dto.getIfExport()){
//            page.setRecords(financeProcessFormVOList);
//        }else{
//            financeProcessFormVOList=CollUtil.page(Math.toIntExact(dto.getCurrent() - 1),
//                    Math.toIntExact(dto.getSize()),financeProcessFormVOList);
//            page.setRecords(financeProcessFormVOList);
//            page.setSize(dto.getSize());
//            page.setCurrent(dto.getCurrent());
//            page.setTotal(size);
//        }
//        List<FinanceProcessFormVO> records = page.getRecords();
//        // 统计数据
//        statistics(page,financeProcessFormVOList);
//        records.forEach(r->{
//            r.setOffsetAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(StrUtil.isNotBlank(r.getOffsetAmount())?
//                    new BigDecimal(r.getOffsetAmount()):BigDecimal.ZERO,DecimalFormatUtil.ZERO));
//            r.setBillingAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(StrUtil.isNotBlank(r.getBillingAmount())?
//                    new BigDecimal(r.getBillingAmount()):BigDecimal.ZERO,DecimalFormatUtil.ZERO));
//            r.setPayableAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(StrUtil.isNotBlank(r.getPayableAmount())?
//                        new BigDecimal(r.getPayableAmount()):BigDecimal.ZERO,DecimalFormatUtil.ZERO));
//            r.setApprovalStatusTxt(EnumUtils.getNameByValue(ApprovalStatusEnum.class, r.getApprovalStatus()));
//            r.setBillTypeTxt(EnumUtils.getNameByValue(BillTypeEnum.class, r.getBillType()));
//            r.setReceiptStatusTxt(EnumUtils.getNameByValue(ReceiptStatusEnum.class, r.getReceiptStatus()));
//            r.setVoucherStatusTxt(EnumUtils.getNameByValue(VoucherStatusEnum.class, r.getVoucherStatus()));
//            r.setSettlementStatusTxt(EnumUtils.getNameByValue(SettlementStatusEnum.class, r.getSettlementStatus()));
//        });
//
//        stopWatch2.stop();
//        log.info("分页 took {} milliseconds.", stopWatch2.getTime());
//
//        stopWatch.stop();
//        log.info("列表主要信息查询分页 took {} milliseconds.", stopWatch.getTime());
//
//        return page;
//    }
    @Override
    public StatisticsPage<FinanceProcessFormVO> findPage(FinancialProcessFormPageDTO dto) {

        statusFilter(dto);

        Set<Integer> approvalStatusSet = dto.getApprovalStatus();

        //出纳节点可能已结算，查询实时流程状态
        if (CollUtil.isNotEmpty(approvalStatusSet) && approvalStatusSet.contains(ApprovalStatusEnum.CASHIER_APPROVAL.getValue())) {
            dto.setApprovalStatus(null);
        }

        List<FinanceProcessFormBaseVO> baseList = baseMapper.findBaseList(dto);
        // 权限过滤
        baseList = this.getAuthority(dto, baseList);

        PageRequest pageRequest;
        if (dto.getIfExport()) {
            pageRequest = new PageRequest(1, Integer.MAX_VALUE);
        } else {
            pageRequest = new PageRequest(dto.getCurrent().intValue(), dto.getSize().intValue());
        }

        if (CollUtil.isEmpty(baseList)) {
            return new StatisticsPage<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        }

        //流程实时归档
        Map<Long, String> requestidStatusMap;
        if (CollUtil.isEmpty(approvalStatusSet) || approvalStatusSet.contains(ApprovalStatusEnum.CASHIER_APPROVAL.getValue())) {
            requestidStatusMap = getFlowStatus(baseList);
        } else {
            requestidStatusMap = Collections.emptyMap();
        }

        //结算状态，审批状态过滤，排序
        Set<Integer> finalSettlementStatus = new HashSet<>(dto.getSettlementStatus());
        baseList = baseList
                .stream()
                .peek(f -> {
                    if (StrUtil.isNotBlank(requestidStatusMap.get(f.getRequestid()))) {
                        f.setApprovalStatus(ApprovalStatusEnum.FILE.getValue());
                    }
                    if (EnumUtils.valueEquals(f.getApprovalStatus(),(ApprovalStatusEnum.FILE))
                            && EnumUtils.valueEquals(f.getSettlementStatus(), SettlementStatusEnum.PENDING_SETTLEMENT)) {
                        f.setSettlementStatus(SettlementStatusEnum.NO_SETTLEMENT_REQUIRED.getValue());
                    }
                })
                .filter(f -> (CollUtil.isEmpty(approvalStatusSet)
                        || (CollUtil.isNotEmpty(approvalStatusSet)
                        && approvalStatusSet.contains(f.getApprovalStatus())))
                        && ((CollUtil.isEmpty(finalSettlementStatus)
                        || (CollUtil.isNotEmpty(finalSettlementStatus)
                        && finalSettlementStatus.contains(f.getSettlementStatus())))))
                .sorted(Comparator.comparing(FinanceProcessFormBaseVO::getApplicationDate).reversed())
                .collect(Collectors.toList());

        StatisticsPage<FinanceProcessFormVO> page = new StatisticsPage<>(pageRequest.getPageNumber(), pageRequest.getPageSize());
        if (CollUtil.isEmpty(baseList)) {
            return page;
        }
        int size = baseList.size();
        FinancialProcessFormPageDTO formPageDTO = new FinancialProcessFormPageDTO();
        List<Long> ids = CollStreamUtil.toList(baseList, FinanceProcessFormBaseVO::getId);
        List<FinanceProcessFormVO> financeProcessFormVOList;
        if (dto.getIfExport()) {
            formPageDTO.setIds(ids);
        } else {
            List<Long> pageIds = CollUtil.page(pageRequest.getPageNumber()-1, pageRequest.getPageSize(), ids);
            formPageDTO.setIds(pageIds);
        }
        if (CollUtil.isEmpty(formPageDTO.getIds())) {
            return page;
        }
        financeProcessFormVOList = baseMapper.findPage(formPageDTO);
        page.setTotal(size);
        page.setRecords(financeProcessFormVOList);
        List<FinanceProcessFormVO> records = page.getRecords();
        // 统计数据
        statistics(page,baseList);
        records.forEach(r -> {
            r.setOffsetAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(StrUtil.isNotBlank(r.getOffsetAmount()) ?
                    new BigDecimal(r.getOffsetAmount()) : BigDecimal.ZERO, DecimalFormatUtil.ZERO));
            r.setBillingAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(StrUtil.isNotBlank(r.getBillingAmount()) ?
                    new BigDecimal(r.getBillingAmount()) : BigDecimal.ZERO, DecimalFormatUtil.ZERO));
            r.setPayableAmount(DecimalFormatUtil.setThousandthAndTwoDecimal(StrUtil.isNotBlank(r.getPayableAmount()) ?
                    new BigDecimal(r.getPayableAmount()) : BigDecimal.ZERO, DecimalFormatUtil.ZERO));
            r.setApprovalStatusTxt(EnumUtils.getNameByValue(ApprovalStatusEnum.class, r.getApprovalStatus()));
            r.setBillTypeTxt(EnumUtils.getNameByValue(BillTypeEnum.class, r.getBillType()));
            r.setReceiptStatusTxt(EnumUtils.getNameByValue(ReceiptStatusEnum.class, r.getReceiptStatus()));
            r.setVoucherStatusTxt(EnumUtils.getNameByValue(VoucherStatusEnum.class, r.getVoucherStatus()));
            r.setSettlementStatusTxt(EnumUtils.getNameByValue(SettlementStatusEnum.class, r.getSettlementStatus()));
        });

        return page;
    }

    private Map<Long, String> getFlowStatus(List<FinanceProcessFormBaseVO> financeProcessFormVOList) {

        Set<Long> requestIds = financeProcessFormVOList.stream()
                .filter(f -> !ApprovalStatusEnum.FILE.getValue().equals(f.getApprovalStatus()))
                .map(FinanceProcessFormBaseVO::getRequestid).collect(Collectors.toSet());
        Map<Long, String> resultMap = new HashMap<>(0);
        if (CollUtil.isEmpty(requestIds)) {
          return resultMap;
        }

        R<List<OaWorkflowRequestbaseVo>> byRequestIds = workflowService.findByRequestIds(requestIds);
        List<OaWorkflowRequestbaseVo> data = byRequestIds.getData();
        return CollUtil.emptyIfNull(data).stream()
                .filter(r -> StrUtil.isNotBlank(r.getStatus()) && r.getRequestid() != null)
                .collect(Collectors.toMap(OaWorkflowRequestbaseVo::getRequestid, OaWorkflowRequestbaseVo::getStatus));
    }


    private static void statusFilter(FinancialProcessFormPageDTO dto) {
        TabTypeEnum tabTypeEnum = EnumUtils.getEnumByValue(TabTypeEnum.class, dto.getTabType());

        if (tabTypeEnum == null) {
            return;
        }
        switch (tabTypeEnum) {
            case PENDING_APPROVAL:
                dto.setApprovalStatus(CollUtil.newHashSet(ApprovalStatusEnum.ACCOUNTING_APPROVAL.getValue()));
                dto.setSettlementStatus(CollUtil.newHashSet(SettlementStatusEnum.PENDING_SETTLEMENT.getValue()));
                break;
            case PENDING_REVIEW:
                dto.setApprovalStatus(CollUtil.newHashSet(ApprovalStatusEnum.FINANCIAL_APPROVAL.getValue()
                        , ApprovalStatusEnum.BUSINESS_APPROVAL.getValue()));
                dto.setSettlementStatus(CollUtil.newHashSet(SettlementStatusEnum.PENDING_SETTLEMENT.getValue()));
                break;
            case PENDING_SETTLEMENT:
                dto.setApprovalStatus(CollUtil.newHashSet(ApprovalStatusEnum.CASHIER_APPROVAL.getValue()));
                dto.setSettlementStatus(CollUtil.newHashSet(SettlementStatusEnum.PENDING_SETTLEMENT.getValue()));
                break;
            case PARTIAL_SETTLEMENT:
                dto.setApprovalStatus(CollUtil.newHashSet(ApprovalStatusEnum.FILE.getValue()));
                dto.setSettlementStatus(CollUtil.newHashSet(SettlementStatusEnum.UNDER_SETTLEMENT.getValue(),
                        SettlementStatusEnum.PARTIALLY_SETTLEMENT.getValue(),
                        SettlementStatusEnum.SETTLEMENT_FAILED.getValue()));
                break;
            case ALREADY_SETTLED:
                dto.setApprovalStatus(CollUtil.newHashSet(ApprovalStatusEnum.FILE.getValue()));
                dto.setSettlementStatus(CollUtil.newHashSet(SettlementStatusEnum.SETTLED.getValue()));
                break;
            case NO_SETTLEMENT_REQUIRED:
                dto.setApprovalStatus(CollUtil.newHashSet(ApprovalStatusEnum.FILE.getValue()));
                dto.setSettlementStatus(CollUtil.newHashSet(SettlementStatusEnum.NO_SETTLEMENT_REQUIRED.getValue()));
                break;
            default:
        }
    }

    private void statistics(StatisticsPage<FinanceProcessFormVO> page, List<FinanceProcessFormBaseVO> list) {
        Map<String, BigDecimal> resultMap = new HashMap<>(3);
        resultMap.put("billingAmountTotal", BigDecimal.ZERO);
        resultMap.put("payableAmountTotal", BigDecimal.ZERO);
        resultMap.put("offsetAmountTotal", BigDecimal.ZERO);

        list.forEach(f -> {
            BigDecimal billingAmount = f.getBillingAmount() == null ? BigDecimal.ZERO : f.getBillingAmount();
            resultMap.merge("billingAmountTotal", billingAmount, BigDecimal::add);

            BigDecimal payableAmount = f.getPayableAmount() == null ? BigDecimal.ZERO : f.getPayableAmount();
            resultMap.merge("payableAmountTotal", payableAmount, BigDecimal::add);

            BigDecimal offsetAmount = f.getOffsetAmount() == null ? BigDecimal.ZERO : f.getOffsetAmount();
            resultMap.merge("offsetAmountTotal", offsetAmount, BigDecimal::add);
        });
        page.setStatistics(resultMap);
    }


    @Override
    public String confirmSettlement(Long id) throws  Exception {
        Long userid = SecurityUtils.getUser().getId();
        FinanceProcessForm f = this.getById(id);
        OaSubmitRequestDTO oaCreateRequestDTO = new OaSubmitRequestDTO();
        if(!ApprovalStatusEnum.CASHIER_APPROVAL.getValue().equals(f.getApprovalStatus())
                ||!SettlementStatusEnum.PENDING_SETTLEMENT.getValue().equals(f.getSettlementStatus())){
            throw new BusinessException("流程" + f.getRequestid() + "提交失败，原因:流程状态不符合结算条件");
        }else if(StrUtil.isBlank(f.getApproverIds())||(StrUtil.isNotBlank(f.getApproverIds())
                &&!f.getApproverIds().contains(userid.toString()))){
            throw new BusinessException("流程" + f.getRequestid() + "提交失败，原因:当前用户无权限");
        }

        try {
        //请求参数
        List<OaMainParamDTO> mainDataList = new ArrayList<>();
        String jsrq;
            if (BillTypeEnum.INTERNAL_LOAN.getValue().equals(f.getBillType())||
                   BillTypeEnum.IN_PROGRESS_PROJECT_REIMBURSEMENT.getValue().equals(f.getBillType())||
                   BillTypeEnum.BUSINESS_OPPORTUNITY_PROJECT_REIMBURSEMENT.getValue().equals(f.getBillType())) {
                jsrq = "fksj";
            }else{
                jsrq = "fkrq";

            }
        if (StrUtil.isBlank(jsrq)) {
            log.error("流程" + f.getRequestid() + "提交失败，原因:结算日期为空");
            return"结算日期为空";
        }
        String format = LocalDate.now().format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN));
            if (!BillTypeEnum.SALARY_AND_BENEFITS_PAYMENT.getValue().equals(f.getBillType())){
                OaMainParamDTO oaMainParamDTO1 =
                        OaMainParamDTO.builder().fieldName(jsrq)
                                .fieldValue(format).build();
                mainDataList.add(oaMainParamDTO1);
            }

        oaCreateRequestDTO.setMainData(mainDataList);

        Map<String, Object> otherParamsMap = new HashMap<>();
        String OtherParamsStr = JSONUtil.toJsonStr(otherParamsMap);
        String mianParamStr = new ObjectMapper().writeValueAsString(mainDataList);
            String encryptUserId=null;
            String oaUserId=null;
            try {
                oaUserId = dbApi.getOaAccountInfoByUserId(userid);
            }catch (Exception e){
                throw new BusinessException("dbApi调用失败");
            }
            try {
                encryptUserId = RSAUtils.encrypt(oaUserId, properties.getSpk());
            }catch (Exception e){
                throw new BusinessException("加密失败");
            }
            Map<String, Object> map = oaClient.submitRequest(encryptUserId, f.getRequestid().toString(),
                    mianParamStr,
                    OtherParamsStr);
            String code = (String) map.get("code");
        if ("NO_PERMISSION".equals(code)) {
            throw new BusinessException("流程已结算，无需再结算");
        }else if(!"SUCCESS".equals(code)){
            throw new BusinessException(map.toString());
        }
    } catch(Exception e) {
        log.error("流程" + f.getRequestid() + "提交失败，原因:" + e.getMessage());
            throw new Exception("流程" + f.getRequestid() + "提交失败，原因:" + e.getMessage());
        }

        return"请求成功";
    }

    @Override
    public R<ConfirmSettlementBatchVO> confirmSettlementBatch(ConfirmSettlementDTO dto) {
        ConfirmSettlementBatchVO confirmSettlementBatchVO = new ConfirmSettlementBatchVO();
        Integer successNum=0;
       Integer failNum=0;

        List<FinanceProcessForm> financeProcessForms = this.listByIds(dto.getIds());
        String msg=StrUtil.EMPTY;
        for (FinanceProcessForm f:financeProcessForms){
            try {
                successNum++;
                confirmSettlement(f.getId());
            } catch (Exception e) {
                msg=e.getMessage();
                failNum++;
                successNum--;
            }
        }
        confirmSettlementBatchVO.setFailNum(failNum);
        confirmSettlementBatchVO.setSuccessNum(successNum);
        if(financeProcessForms.size()==1&&StrUtil.isNotBlank(msg)){
            return R.failed(msg);
        }
        return R.ok(confirmSettlementBatchVO);
    }

    @Override
    public  List<FinanceProcessFormVO> export(FinancialProcessFormPageDTO dto){
        return findPage(dto).getRecords();
    }

    private List<FinanceProcessFormBaseVO> getAuthority(FinancialProcessFormPageDTO dto,
                                                    List<FinanceProcessFormBaseVO> baseList){
        Long userid = SecurityUtils.getUser().getId();
        String menuCode;
        if(dto.getTabType()==null){
            menuCode = FinanceProcessTabAuthEnum.ALL.getName();
        }else{
            menuCode = EnumUtils.getNameByValue(FinanceProcessTabAuthEnum.class, dto.getTabType());
            menuCode = menuCode == null ? FinanceProcessTabAuthEnum.ALL.getName() : menuCode;
        }


        SysUserRoleDataVo sysUserRoleDataVo = authorityService.getByClientIdAndRoleId(userid, menuCode);
        List<Long> userIdList = sysUserRoleDataVo.getUserIdList();
        Boolean isAll = sysUserRoleDataVo.getIsAll();


        if (Boolean.TRUE.equals(isAll)) {
            return baseList;
        }
        return baseList
                .stream()
                .filter(f -> isRelatedUserPresent(f.getRelatedIds(), userIdList))
                .collect(Collectors.toList());

    }

    private boolean isRelatedUserPresent(String relatedIds, List<Long> userIdList) {
        if (StrUtil.isBlank(relatedIds) || userIdList == null || userIdList.isEmpty()) {
            return false;
        }

        Set<Long> userIdSet = new HashSet<>(userIdList);
        String[] idArray = relatedIds.split(",");

        for (String id : idArray) {
            try {
                Long userId = Long.valueOf(id.trim());
                if (userIdSet.contains(userId)) {
                    return true;
                }
            } catch (NumberFormatException e) {
                log.error("非法ID: {}", id);
            }
        }
        return false;
    }
}
