package com.gok.pboot.financial.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售收款明细
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptInDetailVO {

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;


    /**
     * 合同签订日期
     */
    @ApiModelProperty(value = "合同签订日期")
    private String signingDate;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private String contractMoney;

    /**
     * 归属主体/主体名称
     */
    @ApiModelProperty(value = "归属主体/主体名称")
    private String attributableSubject;



    /**
     * 一级部门
     */
    @ApiModelProperty(value = "一级部门")
    private String firstDepartment;

    /**
     * 项目销售人员
     */
    @ApiModelProperty(value = "项目销售人员")
    private String salesmanUserName;


    /**
     * 项目经理人员姓名
     */
    @ApiModelProperty(value = "项目经理人员姓名")
    private String managerUserName;
}
