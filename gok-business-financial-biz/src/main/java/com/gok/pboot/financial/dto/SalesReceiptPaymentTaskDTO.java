package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 销售收款计划客户付款审批流程
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPaymentTaskDTO {

    /**
    * 销售收款id
    */
    @NotNull(message = "销售收款id不能为空")
    private Long id;

    /**
    * 审批节点名称
    */
    private List<String> customerPaymentProcessList;

    /**
     * 当前审批节点
     */
    private String currentApprovalProcess;

    /**
     * 审批进度
     */
    private String approvalProgress;

    /**
     * 更新日期
     */
    private String updateDate;

    /**
     * 所需材料
     */
    private String requiredMaterials;

    /**
     * 回款风险(0有 1无)
     */
    private Integer paymentRisk;

    /**
     * 进度说明
     */
    private String progressDescription;

    /**
     * 下一步工作
     */
    private String nextStepWork;

    /**
     * 回款详情
     */
    private SalesReceiptPaymentDetailDTO paymentDetailDto;

    /**
     * 销售收款计划客户付款审批流程
     */
    private List<SalesReceiptPaymentProcessDTO> paymentProcessDtoList;
}