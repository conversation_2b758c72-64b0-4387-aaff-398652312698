package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 凭证帐套信息实体类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TPlusUserDTO {

    /**
     * 用户名
     */
    private String  user;

    /**
     * 密码
     */
    private String  pwd;

    /**
     * 账套
     */
    private String  account;

    /**
     * 所属公司字典id
     */
    private String salaryPaidSubjectId;

    /**
     * 所属公司字典值
     */
    private String salaryPaidSubject;
}
