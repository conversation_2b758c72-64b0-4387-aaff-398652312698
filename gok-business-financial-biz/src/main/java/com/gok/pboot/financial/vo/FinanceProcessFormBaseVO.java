package com.gok.pboot.financial.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 财务流程表单 Base Vo
 *
 * <AUTHOR>
 * @date 2025/06/10
 */
@Data
public class FinanceProcessFormBaseVO {

    /**
     * id
     */
    private Long id;
    /**
     * 流程id
     */
    private Long requestid;

    /**
     * 流程关联人id
     */
    private String relatedIds;


    /**
     * 审批状态
     */
    private Integer approvalStatus;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 申请日期
     *
     */
    private LocalDate applicationDate;
    /**
     * 报账金额
     */
    private BigDecimal billingAmount;
    /**
     * 应付金额
     */
    private BigDecimal payableAmount;
    /**
     * 冲销金额
     */
    private BigDecimal offsetAmount;
}
