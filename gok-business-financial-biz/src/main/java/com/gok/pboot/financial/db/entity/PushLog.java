package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 推送记录表
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushLog  extends Model<PushLog> {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 推送内容id
    */
    private Long contentId;

    /**
    * 推送类型（0销售收款任务超期提醒；1销售收款任务跟进提醒，2销售收款任务更新提醒）
    */
    private Integer contentType;

    /**
    * 创建人
    */
    private String createBy;

    /**
    * 修改人
    */
    private String updateBy;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 修改时间
    */
    private Date updateTime;

    private String delFlag;

    /**
    * 所属租户
    */
    private Long tenantId;
}