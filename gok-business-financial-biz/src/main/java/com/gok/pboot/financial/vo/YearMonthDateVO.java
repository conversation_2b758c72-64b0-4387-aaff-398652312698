package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 年月
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class YearMonthDateVO {

    /**
     * 年
     */
    private String year;

    /**
     * 月
     */
    private MonthAndCostSumVO monthAndCostSum;

    /**
     * 月
     */
    private List<MonthAndCostSumVO> monthAndCostSumList;
}
