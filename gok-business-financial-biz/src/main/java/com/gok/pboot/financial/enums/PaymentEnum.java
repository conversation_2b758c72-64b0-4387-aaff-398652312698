package com.gok.pboot.financial.enums;

import com.gok.pboot.enumeration.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 款项名称 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
@AllArgsConstructor
public enum PaymentEnum implements ValueEnum<String> {

    /**
     * 首付款
     */
    SFK("首付款", "首付款"),

    /**
     * 到货款
     */
    DHK("到货款", "到货款"),

    /**
     * 进度款
     */
    JDK("进度款", "进度款"),

    /**
     * 初验款
     */
    CYK("初验款", "初验款"),

    /**
     * 终验款
     */
    ZYK("终验款", "终验款"),

    /**
     * 全款
     */
    QK("全款", "全款"),
    /**
     * 质保金
     */
    ZBJ("质保金", "质保金"),
    /**
     * 结算款
     */
    JSK("结算款","结算款")
    ;

    private final String value;

    private final String name;
}
