package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 回款单据 展示
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayBackDocumentVO {

    /**
     * 收款日期
     */
    private String yearMonthDate;

    /**
     * 收款金额
     */
    private String money;

    /**
     * 客户名称
     */
    private String customName;

    /**
     * 企业名称
     */
    private String officeName;

    /**
     * 回款归属一级部门
     */
    private String firstDeptName;

    /**
     * 回款归属二级部门
     */
    private String secondDeptName;

    /**
     * 业务员姓名
     */
    private String salesman;

    /**
     * 备注
     */
    private String remarks;

}
