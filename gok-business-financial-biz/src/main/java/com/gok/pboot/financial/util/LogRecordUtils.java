package com.gok.pboot.financial.util;

import cn.hutool.core.thread.ThreadUtil;
import com.gok.bcp.log.common.enums.EventTypeEnum;
import com.gok.bcp.log.dto.LogsSaveDto;
import com.gok.bcp.log.fegin.RemoteLogService;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.common.core.util.WebUtils;
import com.gok.pboot.financial.enums.ClientSourceEnum;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * 记录日志
 *
 * <AUTHOR>
 * @since 2023-12-05
 */
@Slf4j
@Component
public class LogRecordUtils {

    @Resource
    private RemoteLogService remoteLogService;

    /**
     * 日志内容
     *
     * @param operationType {@link com.gok.pboot.financial.enums.TypeEnum} 操作类型
     * @param moduleName    {@link com.gok.pboot.financial.enums.FunctionEnum} 功能模块
     * @param logName       {@link OperationEnum} 日志名称
     * @param content       操作内容
     */
    public void logRecord(TypeEnum operationType, FunctionEnum moduleName,
                          OperationEnum logName, String content) {
        // 1、基础日志信息
        LogsSaveDto.LogsSaveDtoBuilder logDTO = LogsSaveDto.builder();
        if (content.length() > 100) {
            content = content.substring(0, 100) + "...";
        }

        String application = WebUtils.getRequest().getHeader("Application");
        log.info("application: {}", application);
        Long applicationId;
        SourceEnum sourceEnum;
        try {
            applicationId = Long.valueOf(application);
            sourceEnum = ClientSourceEnum.getNameByVal(application);
        } catch (Exception e) {
            throw new BusinessException(application, "应用ID非法");
        }

        logDTO.clientId(applicationId)
                .clientName(moduleName.getName())
                .sourceEnum(sourceEnum)
                .level(3)
                .levelName("财务日志等级名称")
                .logName(logName.getName())
                .featuresPage(moduleName.getName())
                .content(content)
                .operatorId(UserUtils.getUser().getId())
                .operator(UserUtils.getUser().getName());
        // 2、操作类型
        for (EventTypeEnum e : EventTypeEnum.values()) {
            if (e.getLabel().equals(operationType.getName())) {
                logDTO.eventTypeEnum(e);
            }
        }
        // 3、操作ip
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (Optional.ofNullable(attributes).isPresent()) {
            logDTO.ip(getClientIp(attributes.getRequest()));
        } else {
            // 异步导入获取不到HTTP请求
            logDTO.ip("127.0.0.1");
        }
        // 4、记录日志
        ThreadUtil.execAsync(()->{
            try {
                remoteLogService.syncSave(SecurityConstants.FROM_IN, logDTO.build());
            } catch (Exception e) {
                log.info("中台记录日志失败, 日志对象: {}, 原因: {}", logDTO, e.getMessage());
            }
        });

    }

    /**
     * 获取客户端IP地址
     *
     * @param request {@link HttpServletRequest}
     * @return ipaddress
     */
    private String getClientIp(HttpServletRequest request) {
        final String s = "unknown";
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || s.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.isEmpty() || s.equalsIgnoreCase(ip)) {
            ip = request.getHeader("http_client_ip");
        }
        if (ip == null || ip.isEmpty() || s.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip == null || ip.isEmpty() || s.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || s.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || s.equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        // 如果是多级代理，那么取第一个ip为客户ip
        if (ip != null && ip.contains(",")) {
            ip = ip.substring(ip.lastIndexOf(",") + 1).trim();
        }
        return ip;
    }
}
