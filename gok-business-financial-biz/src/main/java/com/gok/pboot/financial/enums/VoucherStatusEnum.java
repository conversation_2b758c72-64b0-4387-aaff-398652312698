package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 凭证状态 Enum
 *
 * author caihf
 * since 2023-09-01
 */
@Getter
public enum VoucherStatusEnum implements ValueEnum<Integer> {


    // 待生成
    TO_BE_GENERATED(0,"待生成"),

    // 生成中
    IN_PROGRESS(1,"生成中"),
    // 生成成功
    SUCCESSFULLY_GENERATED(2,"生成成功"),
    // 生成失败
    FAILED_TO_GENERATE(3,"生成失败")
   ;


    private final Integer value;

    private final String name;

    VoucherStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
