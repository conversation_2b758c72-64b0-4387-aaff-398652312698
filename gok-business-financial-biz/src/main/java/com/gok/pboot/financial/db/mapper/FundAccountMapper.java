package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.FundAccount;
import com.gok.pboot.financial.dto.FundAccountFindDTO;
import com.gok.pboot.financial.vo.FundAccountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 资金账户数据访问层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface FundAccountMapper extends BaseMapper<FundAccount> {


    Page<FundAccountVO> queryPage(@Param("page") Page<FundAccount> page, @Param("dto") FundAccountFindDTO dto);

    List<FundAccount> selectByCode(@Param("code") String code);

    List<FundAccountVO> findList();
}