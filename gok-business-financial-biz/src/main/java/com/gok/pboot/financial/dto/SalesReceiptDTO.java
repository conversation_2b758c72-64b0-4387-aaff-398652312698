package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 销售收款计划DTO
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptDTO {
    /**
     * 当前页
     */
    private Integer current;

    /**
     * 每页条数
     */
    private Integer size;

    /**
     * 导出Excel所需要id集合
     */
    private List<Long> ids;

    /**
     * 项目名称/编码
     */
    private String projectNameOrNo;

    /**
     * 合同名称/编码
     */
    private String contractNameOrNo;

    /**
     * 项目状态
     */
    private String projectStatus;

    /**
     * 最低合同金额
     */
    private BigDecimal contractMoneyLow;

    /**
     * 最高合同金额
     */
    private BigDecimal contractMoneyUp;

    /**
     * 款项名称
     */
    private String currentPaymentName;

    /**
     * 款项名称
     */
    private List<String> currentPaymentNameList;

    /**
     * tab页款项状态
     */
    private String paymentStatus;

    /**
     * 款项状态
     */
    private List<String> paymentStatusList;

    /**
     * 预计开始日期
     * （原预计收款日期）
     */
    private String expectedStartDate;

    /**
     * 预计截止日期
     * （原预计收款日期）
     */
    private String expectedEndDate;

    /**
     * 预警等级
     */
    private Integer warningLevel;

    /**
     * 逾期状态
     */
    private Integer overdueStatus;

    /**
     * 目标回款日期
     */
    private String targetPaymentStartDate;

    private String targetPaymentEndDate;

    /**
     * 相关人员
     * (原负责人名称）
     */
    private String salesmanOrManageUserName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 归属主体
     */
    private String attributableSubject;

    /**
     * 业务归属部门
     */
    private List<Long> deptIdList;

    /**
     * 任务状态(0未维护、1未更新、2已更新)
     */
    private String taskStatus;

    /**
     * 预算标识
     */
    private String budgetMarking;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 有权限的一级部门id（前端不需要传入）
     */
    private List<Long> authDeptIdList;

    /**
     * 有权限的一级部门id（前端不需要传入）
     */
    private List<Long> authUserIdList;

    /**
     * 权限设置用的用户Id（前端不需要传入）
     */
    private Long userId;

    /**
     * 数据权限标识
     */
    private Boolean authority;

    /**
     * 数据是否有权限
     */
    private Boolean isAll;

    /**
     * 按更新日期排序 0升序 1降序
     */
    private Integer updateDateSort;

    /**
     * 动态导出表头
     */
    private List<String> includeExcel;
}
