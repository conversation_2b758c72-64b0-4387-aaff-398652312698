package com.gok.pboot.financial.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.module.excel.api.handler.SelectedSheetWriteHandler;
import com.gok.pboot.financial.dto.AccountingSetDTO;
import com.gok.pboot.financial.dto.AccountingSetImportDTO;
import com.gok.pboot.financial.dto.AccountingSetPageDTO;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.FundAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.CharEncoding;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 会计账套控制层
 *
 * <AUTHOR>
 * @since 2024-03-25
 * @menu 会计账套
 */
@RestController
@Api(tags = "会计账套")
@RequiredArgsConstructor
@RequestMapping("/accounting-set")
public class AccountingSetController {

    private final IAccountingSetService accountingSetService;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link R}<{@link Page}<{@link AccountingSetVO}>>
     */
    @PostMapping("/vo")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<AccountingSetVO>> findPage(@RequestBody @Valid AccountingSetPageDTO dto) {
        return R.ok(accountingSetService.findPage(dto));
    }

    /**
     * 导出Excel
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSetVO}>
     */
    @PostMapping("/export-excel")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(async = true, name = "会计账套", functionEnum = FunctionEnum.FINANCIAL_ACCOUNTING_SET_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<AccountingSetVO> exportExcel(@RequestBody AccountingSetPageDTO dto) {
        return accountingSetService.exportExcel(dto);
    }

    @GetMapping("/excel-model")
    @ApiOperation(value = "导入模板", notes = "导入模板")
    public void excelModel(HttpServletResponse response) throws IOException {
        final String fileName = "会计账套导入模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharEncoding.UTF_8) + ExcelTypeEnum.XLSX.getValue());
        int columnSize = 8;
        List<String> emptyList = new ArrayList<>();
        for (int i = 0; i < columnSize; i++) {
            emptyList.add(StringPool.EMPTY);
        }
        EasyExcelFactory.write(response.getOutputStream())
                .head(AccountingSetVO.class)
                .autoCloseStream(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SelectedSheetWriteHandler(AccountingSetVO.class))
                .sheet("导入数据").doWrite(Collections.singletonList(emptyList));
    }

    /**
     * 查看
     *
     * @return {@link FundAccountVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<AccountingSetVO> info(@PathVariable Long id) {
        return accountingSetService.info(id);
    }

    /**
     * 新增
     *
     * @param dto {@link AccountingSetDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/add-edit")
    @ApiOperation(value = "新增", notes = "新增")
    public R<Void> addOrEdit(@RequestBody @Valid AccountingSetDTO dto) {
        return accountingSetService.addOrEdit(dto) ? R.ok() : R.failed();
    }

    /**
     * 删除
     *
     * @param dto {@link AccountingSetDTO}
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R<Void> del(@RequestBody AccountingSetDTO dto) {
        return accountingSetService.del(dto.getIdList()) ? R.ok() : R.failed();
    }

    /**
     * 启用
     *
     * @param dto {@link AccountingSetDTO}
     * @return {@link R}<{@link Void>
     */
    @PutMapping("/enable")
    @ApiOperation(value = "启用", notes = "启用")
    public R<Void> enable(@RequestBody AccountingSetDTO dto) {
        return accountingSetService.enable(dto) ? R.ok() : R.failed();
    }

    /**
     * 导入
     * @param importDTOList {@link List}<{@link AccountingSetImportDTO}>
     * @return {@link R}<{@link String>
     */
    @PostMapping("/import-excel")
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R<String> importExcel(@RequestExcel List<AccountingSetImportDTO> importDTOList) {
        return accountingSetService.importExcel(importDTOList);
    }
}
