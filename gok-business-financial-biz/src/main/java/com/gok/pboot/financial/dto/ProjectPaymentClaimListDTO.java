package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目回款认领信息
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentClaimListDTO {
    /**
     * 项目回款id
     */
    private Long id;

    /**
     * 认领信息列表
     */
    private List<ProjectPaymentClaimInfoDTO> claimDTOList;
}
