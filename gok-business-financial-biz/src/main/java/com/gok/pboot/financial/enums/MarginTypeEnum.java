package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 保证金回款类型 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum MarginTypeEnum implements ValueEnum<Integer> {

    /**
     * 投标保证金
     */
    TENDER_MARGIN(0, "投标保证金"),

    /**
     * 履约保证金
     */
    PERFORMANCE_MARGIN(1, "履约保证金"),

    /**
     * 其他保证金
     */
    OTHER_MARGIN(2, "其他保证金"),

    /**
     * 退回现金保证金
     */
    REFUND_CASH_MARGIN(3, "退回现金保证金");

    private final Integer value;

    private final String name;

    MarginTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
