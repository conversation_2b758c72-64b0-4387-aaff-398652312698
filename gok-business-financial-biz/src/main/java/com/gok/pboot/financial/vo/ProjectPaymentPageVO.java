package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目回款追踪页面展示
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ProjectPaymentPageVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 单据编号
     */
    @ColumnWidth(20)
    @ExcelProperty("单据编号")
    private String documentNumber;

    /**
     * 收款公司code
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ExcelIgnore
    private String paymentCompany;

    /**
     * 收款公司value
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ColumnWidth(40)
    @ExcelProperty("收款公司")
    private String paymentCompanyTxt;

    /**
     * 客户id
     */
    @ExcelIgnore
    private String customerId;

    /**
     * 客户名称
     */
    @ColumnWidth(25)
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 企业名称
     */
    @ColumnWidth(25)
    @ExcelProperty("企业名称")
    private String enterpriseName;

    /**
     * 收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 收款金额
     */
    @ColumnWidth(20)
    @ExcelProperty("收款金额")
    private String paymentAmount;

    /**
     * 收款平台
     */
    @ColumnWidth(20)
    @ExcelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    @ExcelIgnore
    private String voucherNumber;

    /**
     * 认领状态code（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ExcelIgnore
    private Integer claimStatus;

    /**
     * 认领状态value（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("认领状态")
    private String claimStatusTxt;

    /**
     * 锁定状态code（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ExcelIgnore
    private Integer lockStatus;

    /**
     * 锁定状态value（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("锁定状态")
    private String lockStatusTxt;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ExcelIgnore
    private Integer pushStatus;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("推送状态")
    private String pushStatusTxt;

    /**
     * 创建人名称
     */
    @ColumnWidth(20)
    @ExcelProperty("创建人")
    private String creatorName;

    /**
     * 资金账户
     * （原银行账户）
     */
    @ExcelProperty("资金账户")
    private String bankAccount;

    /**
     * 备注
     */
    @ColumnWidth(60)
    @ExcelProperty("备注")
    private String paymentNote;

    /**
     * 认领信息
     */
    private List<ProjectPaymentClaimPageVO> claimPageVOList;
}
