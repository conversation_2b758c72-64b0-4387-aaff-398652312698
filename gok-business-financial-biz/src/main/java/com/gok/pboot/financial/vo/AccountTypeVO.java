package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 科目类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountTypeVO {
    /**
    * id
    */
    private Long id;

    /**
    * 类型编码
    */
    private String typeCode;

    /**
    * 类型名称
    */
    private String typeName;

    /**
    * 会计体系id
    */
    private Long accountingSystemId;

    /**
     * 会计体系
     */
    private String accountingSystem;
}