package com.gok.pboot.financial.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedger;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail;
import com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerDetailMapper;
import com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerMapper;
import com.gok.pboot.financial.service.IProjectRevenueLedgerDetailService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目收入台账明细业务层
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@Service
@RequiredArgsConstructor
public class ProjectRevenueLedgerDetailServiceImpl
        extends ServiceImpl<ProjectRevenueLedgerDetailMapper, ProjectRevenueLedgerDetail>
        implements IProjectRevenueLedgerDetailService {

    private final ProjectRevenueLedgerMapper projectRevenueLedgerMapper;

    /**
     * 归属主体（合同所属公司）查询列表
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getContractCompany() {
        return projectRevenueLedgerMapper.selectList(Wrappers.lambdaQuery())
                .stream()
                .map(ProjectRevenueLedger::getContractCompany)
                .filter(ObjectUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 收入类型查询列表
     *
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getIncomeType() {
        return baseMapper.selectList(Wrappers.lambdaQuery())
                .stream()
                .map(ProjectRevenueLedgerDetail::getIncomeType)
                .filter(ObjectUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
    }
}
