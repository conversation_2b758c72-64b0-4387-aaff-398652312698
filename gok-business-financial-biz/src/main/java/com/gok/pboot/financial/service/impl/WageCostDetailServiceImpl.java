package com.gok.pboot.financial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.WageCostDetail;
import com.gok.pboot.financial.db.mapper.WageCostDetailMapper;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IWageCostDetailService;
import com.gok.pboot.financial.util.DateUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.WageCostDetailVO;
import com.gok.pboot.financial.vo.WageCostVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 工资成本Service实现
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WageCostDetailServiceImpl extends ServiceImpl<WageCostDetailMapper, WageCostDetail>
        implements IWageCostDetailService {

    private final WageCostDetailMapper wageCostDetailMapper;
    private final RemoteRoleService remoteRoleService;
    private final LogRecordUtils logRecordUtils;
    private final RemoteOutMultiDeptService remoteOutMultiDeptService;
    @Value("${middlePlatform.adminDeptCatName}")
    private String adminDeptCatName;

    @Override
    public StatisticsPage<WageCostDetailVO> findPage(StatisticsPage<WageCostDetailVO> page, WageCostDTO wageCostDTO) {
        //分页查询
        wageCostDetailMapper.queryDetailPage(page, limitDto(wageCostDTO));

        List<MultiDimensionDeptDto> adminDeptList = remoteOutMultiDeptService.getDeptList(adminDeptCatName, null, null).getData();
        Map<Long, MultiDimensionDeptDto> adminDeptMap = adminDeptList.stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId, a -> a, (a, b) -> a));

        List<WageCostDetailVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(r -> {
                WageCostDetailVO pageVo = Convert.convert(WageCostDetailVO.class, r);
                //配置数据解密
                r = configurationVo(pageVo);

                MultiDimensionDeptDto primaryDeptDto = adminDeptMap.get(r.getPrimaryDeptId());
                r.setPrimaryDeptSort(Optional.ofNullable(primaryDeptDto).isPresent()?primaryDeptDto.getSort():NumberUtils.INTEGER_ZERO);
                MultiDimensionDeptDto secondaryDeptDto = adminDeptMap.get(r.getSecondaryDeptId());
                r.setSecondaryDeptSort(Optional.ofNullable(secondaryDeptDto).isPresent()?secondaryDeptDto.getSort():NumberUtils.INTEGER_ZERO);
                MultiDimensionDeptDto tertiaryDeptDto = adminDeptMap.get(r.getTertiaryDeptId());
                r.setTertiaryDeptSort(Optional.ofNullable(tertiaryDeptDto).isPresent()?tertiaryDeptDto.getSort():NumberUtils.INTEGER_ZERO);
            });
        }
        List<WageCostDetailVO> sortList = records.stream().sorted(Comparator.comparing(WageCostDetailVO::getPrimaryDeptSort)
                .thenComparing(WageCostDetailVO::getSecondaryDeptSort)
                .thenComparing(WageCostDetailVO::getTertiaryDeptSort))
                .collect(Collectors.toList());
        page.setRecords(sortList);
        statistics(page,wageCostDTO);
        return page;
    }

    private void statistics(StatisticsPage<WageCostDetailVO> page, WageCostDTO wageCostDTO) {
        if (page.getTotal() == 0) {
            return;
        }

        // 设置每批处理的数据量
        final int batchSize = 3000;
        // 计算总批次数
        int totalBatches = (int) Math.ceil((double) page.getTotal() / batchSize);
        
        Map<String, BigDecimal> result = new ConcurrentHashMap<>(17);
        result.put("salaryTotal", BigDecimal.ZERO);
        result.put("individualIncomeTaxTotal", BigDecimal.ZERO);
        result.put("individualSocialSecurityTotal", BigDecimal.ZERO);
        result.put("individualProvidentFundTotal", BigDecimal.ZERO);
        result.put("backPayTotal", BigDecimal.ZERO);
        result.put("incomeTaxDifferenceFromLastMonthTotal", BigDecimal.ZERO);
        result.put("actualPayTotal", BigDecimal.ZERO);
        result.put("agreementCompensationTotal", BigDecimal.ZERO);
        result.put("actualAgreementCompensationTotal", BigDecimal.ZERO);
        result.put("performanceTotal", BigDecimal.ZERO);
        result.put("actualPerformanceTotal", BigDecimal.ZERO);
        result.put("actualPaySumTotal", BigDecimal.ZERO);
        result.put("unitSocialSecurityTotal", BigDecimal.ZERO);
        result.put("unitProvidentFundTotal", BigDecimal.ZERO);
        result.put("disabledSecurityFundTotal", BigDecimal.ZERO);
        result.put("peopleNumTotal", BigDecimal.ZERO);
        result.put("payrollCostTotal", BigDecimal.ZERO);

        // 分批处理数据
        for (int i = 0; i < totalBatches; i++) {
            // 设置分页参数
            Page<WageCostDetailVO> query = new Page<>(i + 1,  batchSize);
            query.setSearchCount(false);
            // 查询当前批次数据
            wageCostDetailMapper.queryDetailPage(query,wageCostDTO);
            List<WageCostDetailVO> batchList = query.getRecords();
            
            // 处理当前批次数据
            batchList.parallelStream().forEach(item -> {
                BigDecimal salary = MoneyUtils.decryptToBigDecimal(item.getSalary(), true);
                result.computeIfPresent("salaryTotal", (key, oldValue) -> oldValue.add(salary));

                BigDecimal individualIncomeTax = MoneyUtils.decryptToBigDecimal(item.getIndividualIncomeTax(), true);
                result.computeIfPresent("individualIncomeTaxTotal", (key, oldValue) -> oldValue.add(individualIncomeTax));

                BigDecimal individualSocialSecurity = MoneyUtils.decryptToBigDecimal(item.getIndividualSocialSecurity(), true);
                result.computeIfPresent("individualSocialSecurityTotal", (key, oldValue) -> oldValue.add(individualSocialSecurity));

                BigDecimal individualProvidentFund = MoneyUtils.decryptToBigDecimal(item.getIndividualProvidentFund(), true);
                result.computeIfPresent("individualProvidentFundTotal", (key, oldValue) -> oldValue.add(individualProvidentFund));

                BigDecimal backPay = MoneyUtils.decryptToBigDecimal(item.getBackPay(), true);
                result.computeIfPresent("backPayTotal", (key, oldValue) -> oldValue.add(backPay));

                BigDecimal incomeTaxDifferenceFromLastMonth = MoneyUtils.decryptToBigDecimal(item.getIncomeTaxDifferenceFromLastMonth(), true);
                result.computeIfPresent("incomeTaxDifferenceFromLastMonthTotal", (key, oldValue) -> oldValue.add(incomeTaxDifferenceFromLastMonth));

                BigDecimal actualPay = MoneyUtils.decryptToBigDecimal(item.getActualPay(), true);
                result.computeIfPresent("actualPayTotal", (key, oldValue) -> oldValue.add(actualPay));

                BigDecimal agreementCompensation = MoneyUtils.decryptToBigDecimal(item.getAgreementCompensation(), true);
                result.computeIfPresent("agreementCompensationTotal", (key, oldValue) -> oldValue.add(agreementCompensation));

                BigDecimal actualAgreementCompensation = MoneyUtils.decryptToBigDecimal(item.getActualAgreementCompensation(), true);
                result.computeIfPresent("actualAgreementCompensationTotal", (key, oldValue) -> oldValue.add(actualAgreementCompensation));

                BigDecimal performance = MoneyUtils.decryptToBigDecimal(item.getPerformance(), true);
                result.computeIfPresent("performanceTotal", (key, oldValue) -> oldValue.add(performance));

                BigDecimal actualPerformance = MoneyUtils.decryptToBigDecimal(item.getActualPerformance(), true);
                result.computeIfPresent("actualPerformanceTotal", (key, oldValue) -> oldValue.add(actualPerformance));

                BigDecimal actualPaySum = MoneyUtils.decryptToBigDecimal(item.getActualPaySum(), true);
                result.computeIfPresent("actualPaySumTotal", (key, oldValue) -> oldValue.add(actualPaySum));

                BigDecimal unitSocialSecurity = MoneyUtils.decryptToBigDecimal(item.getUnitSocialSecurity(), true);
                result.computeIfPresent("unitSocialSecurityTotal", (key, oldValue) -> oldValue.add(unitSocialSecurity));

                BigDecimal unitProvidentFund = MoneyUtils.decryptToBigDecimal(item.getUnitProvidentFund(), true);
                result.computeIfPresent("unitProvidentFundTotal", (key, oldValue) -> oldValue.add(unitProvidentFund));

                BigDecimal disabledSecurityFund = MoneyUtils.decryptToBigDecimal(item.getDisabledSecurityFund(), true);
                result.computeIfPresent("disabledSecurityFundTotal", (key, oldValue) -> oldValue.add(disabledSecurityFund));

                BigDecimal peopleNum = item.getPeopleNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(item.getPeopleNum());
                result.computeIfPresent("peopleNumTotal", (key, oldValue) -> oldValue.add(peopleNum));

                BigDecimal payrollCost = MoneyUtils.decryptToBigDecimal(item.getPayrollCost(), true);
                result.computeIfPresent("payrollCostTotal", (key, oldValue) -> oldValue.add(payrollCost));
            });
        }

        page.setStatistics(result);
    }

    @Override
    public List<WageCostDetailVO> export(WageCostDTO dto) {
        List<WageCostDetailVO> detailVOS = wageCostDetailMapper.exportList(limitDto(dto));
        List<MultiDimensionDeptDto> adminDeptList = remoteOutMultiDeptService.getDeptList(adminDeptCatName, null, null).getData();
        Map<Long, MultiDimensionDeptDto> adminDeptMap = adminDeptList.stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId, a -> a, (a, b) -> a));

        if (CollUtil.isNotEmpty(detailVOS)) {

            List<WageCostDetailVO> sortList = detailVOS.stream().map(r -> {
                WageCostDetailVO wageCostDetail = Convert.convert(WageCostDetailVO.class, r);
                wageCostDetail = configurationVo(r);

                MultiDimensionDeptDto primaryDeptDto = adminDeptMap.get(r.getPrimaryDeptId());
                wageCostDetail.setPrimaryDeptSort(Optional.ofNullable(primaryDeptDto).isPresent() ? primaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                MultiDimensionDeptDto secondaryDeptDto = adminDeptMap.get(r.getSecondaryDeptId());
                wageCostDetail.setSecondaryDeptSort(Optional.ofNullable(secondaryDeptDto).isPresent() ? secondaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                MultiDimensionDeptDto tertiaryDeptDto = adminDeptMap.get(r.getTertiaryDeptId());
                wageCostDetail.setTertiaryDeptSort(Optional.ofNullable(tertiaryDeptDto).isPresent() ? tertiaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                return wageCostDetail;
            }).collect(Collectors.toList())
                    .stream().sorted(Comparator.comparing(WageCostDetailVO::getPrimaryDeptSort)
                            .thenComparing(WageCostDetailVO::getSecondaryDeptSort)
                            .thenComparing(WageCostDetailVO::getTertiaryDeptSort))
                    .collect(Collectors.toList());

            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.WAGE_COST,
                    OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + detailVOS.size() + "条】");
            return sortList;
        }
        detailVOS.add(new WageCostDetailVO());
        return detailVOS;
    }

    @Override
    public List<String> getSalaryPaidSubjects() {
        return baseMapper.getSalaryPaidSubjects();
    }

    /**
     * 给vo类解密配置信息
     *
     * @param vo 未配置的vo类
     * @return {@link WageCostVO}
     */
    private WageCostDetailVO configurationVo(WageCostDetailVO vo) {
        vo.setYearMonthDate(DateUtils.dateTrans(vo.getYearMonthDate()));
        vo.setSalary(MoneyUtils.getInstance().transType(vo.getSalary(), true));
        vo.setIndividualIncomeTax(MoneyUtils.getInstance().transType(vo.getIndividualIncomeTax(), true));
        vo.setIndividualSocialSecurity(MoneyUtils.getInstance().transType(vo.getIndividualSocialSecurity(), true));
        vo.setIndividualProvidentFund(MoneyUtils.getInstance().transType(vo.getIndividualProvidentFund(), true));
        vo.setBackPay(MoneyUtils.getInstance().transType(vo.getBackPay(), true));
        vo.setIncomeTaxDifferenceFromLastMonth(MoneyUtils.getInstance().transType(vo.getIncomeTaxDifferenceFromLastMonth(), true));
        vo.setActualPay(MoneyUtils.getInstance().transType(vo.getActualPay(), true));
        vo.setUnitSocialSecurity(MoneyUtils.getInstance().transType(vo.getUnitSocialSecurity(), true));
        vo.setUnitProvidentFund(MoneyUtils.getInstance().transType(vo.getUnitProvidentFund(), true));
        vo.setDisabledSecurityFund(MoneyUtils.getInstance().transType(vo.getDisabledSecurityFund(), true));
        vo.setPayrollCost(MoneyUtils.getInstance().transType(vo.getPayrollCost(), true));
        vo.setAgreementCompensation(MoneyUtils.getInstance().transType(vo.getAgreementCompensation(), true));
        vo.setActualAgreementCompensation(MoneyUtils.getInstance().transType(vo.getActualAgreementCompensation(), true));
        vo.setPerformance(MoneyUtils.getInstance().transType(vo.getPerformance(), true));
        vo.setActualPerformance(MoneyUtils.getInstance().transType(vo.getActualPerformance(), true));
        vo.setActualPaySum(MoneyUtils.getInstance().transType(vo.getActualPaySum(), true));
        return vo;
    }

    /**
     * 给Dto类限制权限
     *
     * @param dto 需要添加限制的dto
     * @return {@link WageCostDTO}
     */
    private WageCostDTO limitDto(WageCostDTO dto) {
        SysUserRoleDataVo userDataScope = remoteRoleService
                .getRoleDataDetailByUserId(dto.getClientId(), UserUtils.getUser().getId(), dto.getMenuCode()).getData();
        log.error("中台返回数据情况：{}", userDataScope);

        // 1、创建总部门id列表
        List<Long> deptIdList = new ArrayList<>();
        // 2、条件查询部门id列表
        if (CollUtil.isNotEmpty(dto.getDeptIds())) {
            dto.setConditionDeptIdList(dto.getDeptIds());
        }
        // 3、数据权限处理
        if (Boolean.FALSE.equals(userDataScope.getIsAll())) {
            // 3.1、赋初始值-1防止为空跳过权限问题
            deptIdList.add(NumberUtils.LONG_MINUS_ONE);
            // 3.2、数据权限部门id列表 有则行政部门对应虚拟部门
            List<Long> innerDeptIdList = userDataScope.getDeptIdList();
            if (CollUtil.isNotEmpty(innerDeptIdList)) {
                for (Long id : innerDeptIdList) {
                    deptIdList.add(id);
                }
            }
        }
        // 4、部门id列表去重
        deptIdList = deptIdList.stream().distinct().collect(Collectors.toList());
        dto.setDeptIds(deptIdList);

        return dto;
    }

}
