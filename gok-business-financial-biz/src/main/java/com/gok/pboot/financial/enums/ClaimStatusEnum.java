package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 认领状态 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum ClaimStatusEnum implements ValueEnum<Integer> {

    /**
     * 已认领
     */
    YES(0, "已认领"),

    /**
     * 待认领
     */
    NO(1, "待认领");

    private final Integer value;

    private final String name;

    ClaimStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
