package com.gok.pboot.financial.common.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.dtflys.forest.exceptions.ForestRuntimeException;
import com.dtflys.forest.http.ForestQueryMap;
import com.dtflys.forest.http.ForestRequest;
import com.dtflys.forest.http.ForestResponse;
import com.dtflys.forest.interceptor.Interceptor;
import com.dtflys.forest.reflection.ForestMethod;
import com.gok.bcp.common.util.JsonUtils;
import com.gok.bcp.upms.dto.OaGetTokenDTO;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.wx.utils.SpringContextUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * - OA请求-响应拦截器 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OaRequestInterceptor implements Interceptor<Object> {

    /**
     * token过期标记
     */
    private final static Long[] EXPIRE_FLAG_ARRAY = {-1L, -2L};
    /**
     * OA请求响应错误码
     */
    private final static String ERROR_CODE = "-1";



    @Override
    public void onInvokeMethod(ForestRequest request, ForestMethod method, Object[] args) {
        log.info("Request invoke: {}", request);
    }

    @Override
    public boolean beforeExecute(ForestRequest request) {
        OaProperties properties = SpringContextUtils.getBean(OaProperties.class);
        RedisTemplate redisTemplate = SpringContextUtils.getBean(RedisTemplate.class);
        //过期时间
        String tokenKey = "OA_TOKEN:" + properties.getAppid();
        Long expire = redisTemplate.getExpire(tokenKey);
        String token = (String) redisTemplate.opsForValue().get(tokenKey);
        OaClient oaClient = SpringContextUtils.getBean(OaClient.class);
        if (ArrayUtil.contains(EXPIRE_FLAG_ARRAY, expire) || StrUtil.isBlank(token)) {
            OaGetTokenDTO oaGetTokenDTO =
                    oaClient.getToken(properties.getUrl(), properties.getAppid(), properties.getSecret());
            if (oaGetTokenDTO.getCode() == -1) {
                throw new BusinessException(oaGetTokenDTO.getMsg());
            }
            token = oaGetTokenDTO.getToken();
            //30分钟有效
            redisTemplate.opsForValue().set(tokenKey, token);
            redisTemplate.expire(tokenKey, 30, TimeUnit.MINUTES);
        }

        request.addHeader("token", token);
        request.addHeader("appid", properties.getAppid());
        String url = request.getUrl();
        if (!StrUtil.startWith(url, properties.getUrl())) {
            request.setUrl(properties.getUrl() + request.getUrl());
        }

        return true;
    }

    @Override
    public void onError(ForestRuntimeException ex, ForestRequest request, ForestResponse response) {
        log.warn(
                "An error occurred when sending request: statusCode={}, content={}, result={}",
                response.getStatusCode(),
                response.getContent(),
                response.getResult()
        );
    }

    @Override
    public void afterExecute(ForestRequest request, ForestResponse response) {
        Map map = JsonUtils.toObject(response.getContent(), Map.class);
        if (CollUtil.isNotEmpty(map)) {
            String code = map.getOrDefault("code", StrUtil.EMPTY).toString();
            String sign = map.getOrDefault("sign", StrUtil.EMPTY).toString();
            if (ERROR_CODE.equals(code)) {
                throw new BusinessException(map.getOrDefault("msg", StrUtil.EMPTY).toString());
            }
            if (ERROR_CODE.equals(sign)) {
                throw new BusinessException(map.getOrDefault("message", StrUtil.EMPTY).toString());
            }
            Map<String, Map<String, String>> mapMap = (HashMap) map.getOrDefault("result", new HashMap<>(0));
            if (CollUtil.isEmpty(mapMap)) {
                return;
            }
            mapMap.forEach((key, value) -> {
                Map<String, String> stringStringMap = mapMap.get(key);
                if (ERROR_CODE.equals(code)) {
                    throw new BusinessException(String.format("[%s]OA后台请求业务异常", stringStringMap.get("msg")));
                } else {
                    String secCode = stringStringMap.getOrDefault("code", StrUtil.EMPTY).toString();
                    if (ERROR_CODE.equals(secCode)) {
                        throw new BusinessException(String.format("[%s]OA后台请求业务异常", stringStringMap.get("msg")));
                    }
                }

            });
        }
    }




}

