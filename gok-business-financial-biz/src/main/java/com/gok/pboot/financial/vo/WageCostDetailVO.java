package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工资汇总明细VO
 *
 * <AUTHOR>
 * @since 2024-01-18
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class WageCostDetailVO {

    /**
     * id
     */
    @ExcelIgnore
    private String id;

    /**
     * 归属月份
     */
    @ExcelProperty("所属月份")
    @ColumnWidth(15)
    private String yearMonthDate;

    /**
     * 发薪主体
     */
    @ExcelProperty("发薪主体")
    @ColumnWidth(20)
    private String salaryPaidSubject;

    /**
     * 当前行政部门id
     */
    @ExcelIgnore
    private Long currentDeptId;

    /**
     * 当前行政部门
     */
    @ExcelIgnore
    private String currentDept;

    /**
     * 一级部门id
     */
    @ExcelIgnore
    private Long primaryDeptId;

    /**
     * 一级部门
     */
    @ExcelProperty("人员归属一级部门")
    @ColumnWidth(20)
    private String primaryDept;

    /**
     * 二级部门id
     */
    @ExcelIgnore
    private Long secondaryDeptId;

    /**
     * 二级部门
     */
    @ExcelProperty("人员归属二级部门")
    @ColumnWidth(20)
    private String secondaryDept;

    /**
     * 三级部门id
     */
    @ExcelIgnore
    private Long tertiaryDeptId;

    /**
     * 三级部门
     */
    @ExcelProperty("人员归属三级部门")
    @ColumnWidth(20)
    private String tertiaryDept;

    /**
     * 应发合计
     */
    @ExcelProperty("应发合计")
    @ColumnWidth(20)
    private String salary;

    /**
     * 个人所得税
     */
    @ExcelProperty("个人所得税")
    @ColumnWidth(20)
    private String individualIncomeTax;

    /**
     * 个人社保
     */
    @ExcelProperty("个人社保")
    @ColumnWidth(20)
    private String individualSocialSecurity;


    /**
     * 个人公积金
     */
    @ExcelProperty("个人公积金")
    @ColumnWidth(20)
    private String individualProvidentFund;


    /**
     * 补发/补扣
     */
    @ExcelProperty("补发/补扣")
    @ColumnWidth(20)
    private String backPay;

    /**
     * 个税差异
     */
    @ExcelProperty("个税差异")
    @ColumnWidth(20)
    private String incomeTaxDifferenceFromLastMonth;

    /**
     * 实发合计
     */
    @ExcelProperty("实发合计")
    @ColumnWidth(20)
    private String actualPay;

    /**
     * 协议补偿
     */
    @ExcelProperty("协议补偿")
    @ColumnWidth(20)
    private String agreementCompensation;

    /**
     * 实发协议补偿
     */
    @ExcelProperty("实发协议补偿")
    @ColumnWidth(20)
    private String actualAgreementCompensation;

    /**
     * 绩效工资
     */
    @ExcelProperty("绩效工资")
    @ColumnWidth(20)
    private String performance;

    /**
     * 实发绩效工资
     */
    @ExcelProperty("实发绩效工资")
    @ColumnWidth(20)
    private String actualPerformance;

    /**
     * 实付工资总计
     */
    @ExcelProperty("实付工资总计")
    @ColumnWidth(20)
    private String actualPaySum;

    /**
     * 单位社保
     */
    @ExcelProperty("单位社保")
    @ColumnWidth(20)
    private String unitSocialSecurity;

    /**
     * 单位公积金
     */
    @ExcelProperty("单位公积金")
    @ColumnWidth(20)
    private String  unitProvidentFund;

    /**
     * 残保金
     */
    @ExcelProperty("残保金")
    @ColumnWidth(20)
    private String disabledSecurityFund;

    /**
     * 人数
     */
    @ExcelProperty("人数")
    @ColumnWidth(15)
    private Integer peopleNum;

    /**
     * 工资成本
     */
    @ExcelProperty("工资成本")
    @ColumnWidth(20)
    private String payrollCost;

    /**
     * 一级部门排序值
     */
    @ExcelIgnore
    private Integer primaryDeptSort;

    /**
     * 二级部门排序值
     */
    @ExcelIgnore
    private Integer secondaryDeptSort;

    /**
     * 三级部门排序值
     */
    @ExcelIgnore
    private Integer tertiaryDeptSort;
}
