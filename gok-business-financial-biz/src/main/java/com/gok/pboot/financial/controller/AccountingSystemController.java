package com.gok.pboot.financial.controller;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.handler.SelectedSheetWriteHandler;
import com.gok.pboot.financial.dto.AccountingSystemDTO;
import com.gok.pboot.financial.dto.AccountingSystemImportDTO;
import com.gok.pboot.financial.dto.AccountingSystemPageDTO;
import com.gok.pboot.financial.service.IAccountingSystemService;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.AccountingSystemVO;
import com.gok.pboot.financial.vo.FundAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.CharEncoding;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 会计体系控制层
 *
 * <AUTHOR>
 * @since 2024-12-18
 * @menu 会计体系
 */
@RestController
@Api(tags = "会计体系")

@RequiredArgsConstructor
@RequestMapping("/accounting-system")
public class AccountingSystemController {

    private final IAccountingSystemService service;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link AccountingSystemPageDTO}
     * @return {@link R}<{@link Page}<{@link AccountingSetVO}>>
     */
    @PostMapping("/findPage")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<AccountingSystemVO>> findPage(@RequestBody @Valid AccountingSystemPageDTO dto) {
        return R.ok(service.findPage(dto));
    }
    /**
     * 导出Excel
     * @return {@link Page}<{@link AccountingSystemPageDTO}>
     */
    @PostMapping("/export-excel")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(async = true, name = "会计体系",
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<AccountingSystemVO> exportExcel(@RequestBody AccountingSystemPageDTO dto) {
        return service.exportExcel(dto);
    }

    @GetMapping("/excel-model")
    @ApiOperation(value = "导入模板", notes = "导入模板")
    public void excelModel(HttpServletResponse response) throws IOException {
        final String fileName = "会计体系导入模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharEncoding.UTF_8) + ExcelTypeEnum.XLSX.getValue());
        int columnSize = 8;
        List<String> emptyList = new ArrayList<>();
        for (int i = 0; i < columnSize; i++) {
            emptyList.add(StringPool.EMPTY);
        }
        EasyExcelFactory.write(response.getOutputStream())
                .head(AccountingSystemVO.class)
                .autoCloseStream(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SelectedSheetWriteHandler(AccountingSystemVO.class))
                .sheet("导入数据").doWrite(Collections.singletonList(emptyList));
    }
    /**
     * 查看
     *
     * @return {@link FundAccountVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<AccountingSystemVO> info(@PathVariable Long id) {
        return service.info(id);
    }

    /**
     * 新增
     *
     * @param dto {@link AccountingSystemDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/add-edit")
    @ApiOperation(value = "新增", notes = "新增")
    public R<String> addOrEdit(@RequestBody @Valid AccountingSystemDTO dto) {
        return service.addOrEdit(dto);
    }

    /**
     * 删除
     *
     * @param dto {@link AccountingSystemDTO}
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R<String> del(@RequestBody AccountingSystemDTO dto) {
        return service.del(dto.getIdList());
    }

    /**
     * 启用
     *
     * @param dto {@link AccountingSystemDTO}
     * @return {@link R}<{@link Void>
     */
    @PutMapping("/enable")
    @ApiOperation(value = "启用", notes = "启用")
    public R<Void> enable(@RequestBody AccountingSystemDTO dto) {
        return service.enable(dto) ? R.ok() : R.failed();
    }

    /**
     * 导入
     * @param importDTOList {@link List}<{@link AccountingSystemImportDTO}>
     * @return {@link R}<{@link String>
     */
    @PostMapping("/import-excel")
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R<String> importExcel(@RequestExcel List<AccountingSystemImportDTO> importDTOList) {
        return service.importExcel(importDTOList);
    }
}
