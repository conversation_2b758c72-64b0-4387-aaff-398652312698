package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 应收账款台账
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("accounts_receivable")
@ApiModel("应收账款台账")
public class AccountsReceivable extends Model<AccountsReceivable> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ApiModelProperty("项目状态")
    private String projectStatus;

    /**
     * 30天内
     */
    @ApiModelProperty("30天内")
    private BigDecimal withinThirty;

    /**
     * 30-60天内
     */
    @ApiModelProperty("30-60天内")
    private BigDecimal thirtySixty;

    /**
     * 60-120天
     */
    @ApiModelProperty("60-120天")
    private BigDecimal sixtyHundredTwenty;

    /**
     * 120天以上
     */
    @ApiModelProperty("120天以上")
    private BigDecimal aboveHundredTwenty;

    /**
     * 回款
     */
    @ApiModelProperty("回款")
    private BigDecimal payBack;

    /**
     * 开票
     */
    @ApiModelProperty("开票")
    private BigDecimal invoicing;

    /**
     * 验收
     */
    @ApiModelProperty("本次结项确认收入金额(含税)合计")
    private BigDecimal revenue;

    /**
     * 收入确认待回款金额
     */
    @ApiModelProperty("收入确认待回款金额")
    private BigDecimal revenueCollected;

    /**
     * 开票待回款金额
     */
    @ApiModelProperty("开票待回款金额")
    private BigDecimal invoicingCollected;

    /**
     * 合同待回款金额
     */
    @ApiModelProperty("合同待回款金额")
    private BigDecimal customerCollected;

    /**
     * 合同待开票金额
     */
    @ApiModelProperty("合同待开票金额")
    private BigDecimal customerInvoicing;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 合同id
     */
    @ApiModelProperty("合同id")
    private Long contractId;

    /**
     * 合同名称
     */
    @ApiModelProperty("合同名称")
    private String contractName;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    private String contractCode;

    /**
     * 合同签订日期
     */
    @ApiModelProperty("合同签订日期")
    private String signingDate;

    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractMoney;

    /**
     * 归属主体/主体名称
     */
    @ApiModelProperty("归属主体")
    private String attributableSubject;

    /**
     * 一级部门
     */
    @ApiModelProperty("一级部门")
    private Long firstDepartmentId;

    /**
     * 一级部门名称
     */
    @ApiModelProperty("一级部门名称")
    private String firstDepartment;

    /**
     * 二级部门
     */
    @ApiModelProperty("二级部门")
    private Long secondDepartmentId;

    /**
     * 二级部门名称
     */
    @ApiModelProperty("二级部门名称")
    private String secondDepartment;

    /**
     * 项目销售人员id
     */
    @ApiModelProperty("项目销售人员id")
    private Long salesmanUserId;

    /**
     * 项目销售人员姓名
     */
    @ApiModelProperty("项目销售人员姓名")
    private String salesmanUserName;

    /**
     * 项目经理人员id
     */
    @ApiModelProperty("项目经理人员id")
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    @ApiModelProperty("项目经理人员姓名")
    private String managerUserName;

    /**
     * 主管人员id
     */
    @ApiModelProperty("主管人员id")
    private Long headUserId;

    /**
     * 主管人员姓名
     */
    @ApiModelProperty("主管人员姓名")
    private String headUserName;

    /**
     * 项目市场总监人员id
     */
    @ApiModelProperty("项目市场总监人员id")
    private Long commissionerUserId;

    /**
     * 项目市场总监人员姓名
     */
    @ApiModelProperty("项目市场总监人员姓名")
    private String commissionerUserName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}
