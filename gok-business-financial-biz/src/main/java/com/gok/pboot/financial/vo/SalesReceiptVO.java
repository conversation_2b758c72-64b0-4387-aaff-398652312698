package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.gok.module.excel.api.annotation.CustomMerge;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 销售收款计划VO
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class SalesReceiptVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 客户id
     */
    @ExcelIgnore
    private Long customerId;

    /**
     * 客户名称
     */
    @ColumnWidth(20)
    @ExcelProperty("签约客户")
    private String customerName;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目名称
     */
    @ColumnWidth(20)
    @ExcelProperty("项目名称")
    @CustomMerge()
    private String projectName;

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同名称
     */
    @ColumnWidth(30)
    @ExcelProperty("合同名称")
    @CustomMerge()
    private String contractName;

    /**
     * 客户经理
     */
    @ColumnWidth(20)
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 项目经理人员姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("项目经理")
    private String managerUserName;

    /**
     * 项目状态
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态
     */
    @ColumnWidth(20)
    @ExcelProperty("项目状态")
    private String projectStatusTxt;

    /**
     * 合同金额（含税）
     */
    @ExcelProperty("合同金额")
    private String contractMoney;

    /**
     * 款项ID
     */
    @ColumnWidth(20)
    @ExcelProperty("款项ID")
    private Long paymentId;

    /**
     * 款项名称
     */
    @ColumnWidth(20)
    @ExcelProperty("款项名称")
    private String currentPaymentName;

    /**
     * 款项条件
     */
    @ColumnWidth(20)
    @ExcelProperty("款项条件")
    private String paymentCondition;

    /**
     * 款项状态
     */
    @ColumnWidth(20)
    @ExcelProperty("款项状态")
    private String paymentStatus;

    /**
     * 款项比例
     */
    @ColumnWidth(20)
    @ExcelProperty("款项比例")
    private String paymentProportion;

    /**
     * 预估款项金额_含税
     */
    @ColumnWidth(20)
    @ExcelProperty("预估款项金额_含税")
    private String estimateCurrentPaymentMoney;

    /**
     * 计划款项金额_含税
     * （原款项金额）
     */
    @ColumnWidth(20)
    @ExcelProperty("计划款项金额_含税")
    private String currentPaymentMoney;

    /**
     * 实际回款金额
     * 原（实际款项金额）
     */
    @ColumnWidth(20)
    @ExcelProperty("实际回款金额")
    private String actualPaymentAmount;

    /**
     * 款项差额
     */
    @ColumnWidth(20)
    @ExcelProperty("款项差额")
    private String paymentDifference;

    /**
     * 里程碑
     */
    @ColumnWidth(20)
    @ExcelProperty("里程碑")
    private String milestones;

    /**
     * 预计完成日期
     * 原（里程碑计划日期）
     */
    @ColumnWidth(20)
    @ExcelProperty("预计完成日期")
    private LocalDate expectedCompleteDate;

    /**
     * 实际完成日期
     * 原（里程碑达成日期）
     */
    @ColumnWidth(20)
    @ExcelProperty("实际完成日期")
    private LocalDate actualCompleteDate;

    /**
     * 回款账期（天）
     */
    @ColumnWidth(20)
    @ExcelProperty("回款账期（天）")
    private Integer collectDays;

    /**
     * 目标回款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("目标回款日期")
    private LocalDate targetPaymentDate;

    /**
     * 预计日期
     */
    @ExcelIgnore
    private String expectedDate;

    /**
     * 验收日期
     */
    @ExcelIgnore
    private String acceptanceDate;

    /**
     * 逾期天数
     */
    @ColumnWidth(20)
    @ExcelProperty("逾期天数")
    private String collectionDelayDays;

    /**
    * 客户付款窗口期
    */
    @ColumnWidth(20)
    @ExcelProperty("客户付款窗口期")
    private String customerPaymentWindowPeriod;

    /**
    * 预计回款日期
    */
    @ColumnWidth(20)
    @ExcelProperty("预计回款日期")
    private LocalDate expectedPaymentDate;

    /**
     * 实际回款日期
     * 原（实际款项日期）
     */
    @ColumnWidth(20)
    @ExcelProperty("最近一笔回款日期")
    private String paymentDate;

    /**
     * 实际追款时长（天）
     */
    @ColumnWidth(20)
    @ExcelProperty("实际追款时长（天）")
    private Long actualCollectionTime;

    /**
     * 更新日期
     */
    @ColumnWidth(20)
    @ExcelProperty("更新日期")
    private String updateDate;

    /**
     * 客户付款审批流程
     */
    @ColumnWidth(20)
    @ExcelProperty("客户付款审批流程")
    private String customerPaymentProcess;

    /**
     * 当前审批环节
     */
    @ColumnWidth(20)
    @ExcelProperty("当前审批环节")
    private String currentApprovalProcess;

    /**
     * 审批进度
     */
    @ColumnWidth(20)
    @ExcelProperty("审批进度")
    private String approvalProgress;

    /**
     * 回款风险(0有 1无)
     */
    @ExcelIgnore
    private Integer paymentRisk;

    /**
     * 回款风险(0有 1无)
     */
    @ColumnWidth(20)
    @ExcelProperty("回款风险")
    private String paymentRiskStr;

    /**
     * 所需材料
     */
    @ColumnWidth(20)
    @ExcelProperty("所需材料")
    private String requiredMaterials;

    /**
     * 进度说明
     */
    @ColumnWidth(20)
    @ExcelProperty("进度说明")
    private String progressDescription;

    /**
     * 下一步工作
     */
    @ColumnWidth(20)
    @ExcelProperty("下一步工作")
    private String nextStepWork;

    /**
     * 交付方式
     */
    @ColumnWidth(20)
    @ExcelProperty("交付方式")
    private String deliveryMethod;

    /**
     * 结算方式
     */
    @ColumnWidth(20)
    @ExcelProperty("结算方式")
    private String paymentMethod;

    /**
     * 业务归属一级部门
     */
    @ColumnWidth(20)
    @ExcelProperty("业务归属一级部门")
    private String firstDepartment;

    /**
     * 业务归属二级部门
     */
    @ColumnWidth(20)
    @ExcelProperty("业务归属二级部门")
    private String secondDepartment;

    /**
     * 项目编码
     */
    @ColumnWidth(20)
    @ExcelProperty("项目编码")
    private String projectNo;

    /**
     * 合同编码
     */
    @ColumnWidth(20)
    @ExcelProperty("合同编码")
    private String contractCode;

    /**
     * 客户集
     */
    @ColumnWidth(20)
    @ExcelProperty("最终客户")
    private String customerMarket;

    /**
     * 归属主体
     */
    @ExcelIgnore
    private String attributableSubject;

    /**
     * 归属主体
     */
    @ColumnWidth(20)
    @ExcelProperty("归属主体")
    private String attributableSubjectTxt;

    /**
     * 坏账流程编号
     */
    @ColumnWidth(20)
    @ExcelProperty("坏账流程编号")
    private String badDebtProcessNumber;

    /**
     * 坏账金额
     */
    @ColumnWidth(20)
    @ExcelProperty("坏账金额")
    private BigDecimal badDebtAmount;

    /**
     * 坏账归档时间
     */
    @ColumnWidth(20)
    @ExcelProperty("坏账归档时间")
    private LocalDate badDebtFilingTime;

    /**
     * 累计收款金额（含税）
     */
    @ExcelIgnore
    private String accumulatedAmount;

    /**
     * 合同签订日期
     */
    @ExcelIgnore
    private String signingDate;

    /**
     * 累计收款比例
     */
    @ExcelIgnore
   private String collectionRatio;

    /**
     * 任务状态
     */
    @ColumnWidth(20)
    @ExcelProperty("任务状态")
    private String taskStatus;

    /**
     * 预算标记
     */
    @ColumnWidth(20)
    @ExcelProperty("预算标记")
    private String budgetMarking;
}
