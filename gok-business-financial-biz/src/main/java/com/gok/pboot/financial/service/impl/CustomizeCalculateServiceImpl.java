package com.gok.pboot.financial.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.CustomizeCalculate;
import com.gok.pboot.financial.db.mapper.CustomizeCalculateMapper;
import com.gok.pboot.financial.dto.CustomizeCalculateDTO;
import com.gok.pboot.financial.dto.CustomizeCalculateFindDTO;
import com.gok.pboot.financial.service.ICustomizeCalculateService;
import com.gok.pboot.financial.vo.CustomizeCalculateVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 自定义辅助核算项业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomizeCalculateServiceImpl extends ServiceImpl<CustomizeCalculateMapper, CustomizeCalculate>
        implements ICustomizeCalculateService {

    private final CustomizeCalculateMapper customizeCalculateMapper;

    @Override
    public Page<CustomizeCalculateVO> findPage(CustomizeCalculateFindDTO dto) {
        Page<CustomizeCalculate> page = new Page<>(dto.getCurrent(), dto.getSize());
        return customizeCalculateMapper.queryPage(page,dto);
    }

    @Override
    public R addOrEdit(CustomizeCalculateDTO dto) {
        List<CustomizeCalculate> fundAccounts = customizeCalculateMapper.selectByCode(dto.getCode());
        //插入或更新
        if(Optional.ofNullable(dto.getId()).isPresent() && dto.getId()> NumberUtils.LONG_ZERO){
            //更新
            if(CollectionUtils.isNotEmpty(fundAccounts) && !dto.getId().equals(fundAccounts.get(NumberUtils.INTEGER_ZERO).getId())){
                return R.failed("编号已存在");
            }
            CustomizeCalculate customizeCalculate = customizeCalculateMapper.selectById(dto.getId());
            if(!Optional.ofNullable(customizeCalculate).isPresent()){
                return R.failed("当前数据不存在");
            }
            customizeCalculate.setCode(dto.getCode());
            customizeCalculate.setCustomizeCalculate(dto.getCustomizeCalculate());
            customizeCalculateMapper.updateById(customizeCalculate);
            return info(customizeCalculate.getId());
        }
        //新增
        if(CollectionUtils.isNotEmpty(fundAccounts)){
            return R.failed("编号已存在");
        }
        //插入
        CustomizeCalculate customizeCalculate = new CustomizeCalculate();
        customizeCalculate.setCode(dto.getCode());
        customizeCalculate.setCustomizeCalculate(dto.getCustomizeCalculate());
        customizeCalculateMapper.insert(customizeCalculate);
        return info(customizeCalculate.getId());
    }

    @Override
    public R<CustomizeCalculateVO> info(Long id) {
        CustomizeCalculate customizeCalculate = customizeCalculateMapper.selectById(id);
        if(!Optional.ofNullable(customizeCalculate).isPresent()){
            return R.failed("查询不到数据");
        }
        return R.ok(CustomizeCalculateVO.builder()
                .id(customizeCalculate.getId())
                .code(customizeCalculate.getCode())
                .customizeCalculate(customizeCalculate.getCustomizeCalculate())
                .build());
    }

    @Override
    public boolean del(List<Long> idList) {
        return customizeCalculateMapper.deleteBatchIds(idList)>0?true:false;
    }

    @Override
    public List<CustomizeCalculateVO> findList() {
        return customizeCalculateMapper.findList();
    }

    @Override
    public void insertOrUpdateByCode(List<CustomizeCalculateDTO> list) {
        List<CustomizeCalculate> insertList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(list)) {
            List<String> codes = list.stream().map(CustomizeCalculateDTO::getCode).collect(Collectors.toList());
            //根据code查询数据
            List<CustomizeCalculate>  selList = customizeCalculateMapper.selectByCodes(codes);
            if(CollectionUtils.isNotEmpty(selList)){
                List<String> collect = selList.stream().map(CustomizeCalculate::getCode).collect(Collectors.toList());
                 insertList =list.stream().filter(e->!collect.contains(e.getCode()))
                        .map(x->CustomizeCalculate.builder()
                                .code(x.getCode())
                                .customizeCalculate(x.getCustomizeCalculate())
                                .build()).collect(Collectors.toList());
                List<CustomizeCalculate> updateList =list.stream().filter(e->collect.contains(e.getCode()))
                        .map(x->{
                            Long id = selList.stream().filter(y->y.getCode().equals(x.getCode())).findFirst().get().getId();
                            return CustomizeCalculate.builder()
                                    .id(id)
                                    .code(x.getCode())
                                    .customizeCalculate(x.getCustomizeCalculate())
                                    .build();
                        }).collect(Collectors.toList());
                insertList.addAll(updateList);
            }else{
                insertList =list.stream().map(x->CustomizeCalculate.builder()
                                                    .code(x.getCode())
                                                    .customizeCalculate(x.getCustomizeCalculate())
                                                    .build()).collect(Collectors.toList());
            }

            if(CollectionUtils.isNotEmpty(insertList)){
                customizeCalculateMapper.insertOrUpdateBatch(insertList);
            }
        }
    }
}
