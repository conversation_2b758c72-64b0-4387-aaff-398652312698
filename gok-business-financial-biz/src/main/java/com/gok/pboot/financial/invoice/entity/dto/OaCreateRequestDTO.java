package com.gok.pboot.financial.invoice.entity.dto;

import com.gok.pboot.financial.dto.OaMainParamDTO;
import com.gok.pboot.financial.vo.OaAccountVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * - 创建流程 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class OaCreateRequestDTO {

    private Long requestId;
    /**
     * 流程id
     */
    private Integer workflowId;
    /**
     * ZP-03新员工录用申请-系统管理员-2023-12-11（拟录用人姓名:w测试14）
     */
    private String requestName;
    /**
     * 主表数据
     */
    private List<OaMainParamDTO> mainData;

    /**
     * 其他参数 {↵  "isnextflow":"0"↵}
     */
    private Map<String, Object> otherParams;


    private List<Map<String, Object>> detailData;

    private OaAccountVO oaAccountVO;
}
