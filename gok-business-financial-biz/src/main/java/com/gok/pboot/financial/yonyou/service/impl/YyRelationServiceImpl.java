package com.gok.pboot.financial.yonyou.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.db.entity.YyRelation;
import com.gok.pboot.financial.db.mapper.YyRelationMapper;
import com.gok.pboot.financial.yonyou.service.IYyRelationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用友关联表Service实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
public class YyRelationServiceImpl extends ServiceImpl<YyRelationMapper, YyRelation> implements IYyRelationService {

    @Override
    public YyRelation getByRelateIdAndType(Long relateId, Integer relateType) {
        return baseMapper.selectByRelateIdAndType(relateId, relateType);
    }

    @Override
    public List<YyRelation> listByRelateIdsAndType(List<Long> relateIds, Integer relateType) {
        if (CollUtil.isEmpty(relateIds)) {
            return CollUtil.newArrayList();
        }
        return baseMapper.selectByRelateIdsAndType(relateIds, relateType);
    }

    @Override
    public YyRelation getByYyId(String yyId) {
        return baseMapper.selectByYyId(yyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public YyRelation saveOrUpdateRelation(Long relateId, Integer relateType, String yyId) {
        // 查询是否已存在关联关系
        YyRelation existingRelation = getByRelateIdAndType(relateId, relateType);
        
        LocalDateTime now = LocalDateTime.now();
        
        if (existingRelation != null) {
            // 更新现有关联关系
            existingRelation.setYyId(yyId);
            existingRelation.setSyncTime(now);
            updateById(existingRelation);
            log.info("更新用友关联关系，关联ID：{}，关联类型：{}，用友ID：{}", relateId, relateType, yyId);
            return existingRelation;
        } else {
            // 创建新的关联关系
            YyRelation newRelation = YyRelation.builder()
                    .relateId(relateId)
                    .relateType(relateType)
                    .yyId(yyId)
                    .syncTime(now)
                    .build();
            save(newRelation);
            log.info("创建用友关联关系，关联ID：{}，关联类型：{}，用友ID：{}", relateId, relateType, yyId);
            return newRelation;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSaveOrUpdate(List<YyRelation> relations) {
        if (CollUtil.isEmpty(relations)) {
            return true;
        }
        
        try {
            for (YyRelation relation : relations) {
                saveOrUpdateRelation(relation.getRelateId(), relation.getRelateType(), relation.getYyId());
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存或更新用友关联关系失败", e);
            return false;
        }
    }

    @Override
    public boolean updateSyncTime(Long id, LocalDateTime syncTime) {
        LambdaUpdateWrapper<YyRelation> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(YyRelation::getId, id)
                    .set(YyRelation::getSyncTime, syncTime);
        return update(updateWrapper);
    }
}
