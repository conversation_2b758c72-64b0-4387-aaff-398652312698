package com.gok.pboot.financial.controller;

import cn.hutool.core.collection.CollUtil;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.enums.EnumModel;
import com.gok.pboot.financial.vo.OaDictVO;
import com.gok.pboot.financial.vo.ProjectDictVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 前端枚举查询
 * @menu 枚举查询
 * @author: wengc
 * @create: 2023-11-30 15:57
 **/
@Inner(value = false)
@RestController
@RequestMapping("/enum")
@Validated
@Slf4j
public class EnumController {

    /**
     * 枚举路径
     */
    private final static String EnumBaseUrl="com.gok.pboot.financial.enums.";

    private final static String GetName="getName";
    private final static String GetValue="getValue";
    @Resource
    private  DBApi dbApi;

    @GetMapping("/getByEnumPath")
    public R getByEnumPath(@NotBlank(message = "path不能为空") String path) {
        String classNameUrl=EnumBaseUrl+path;
        Class<Enum> clazz = null;
        try{
            clazz=(Class<Enum>) Class.forName(classNameUrl);
        }catch (Exception e){
            log.error("没有找到对应的枚举类:"+classNameUrl);
            return R.failed("没有找到对应的枚举类:"+classNameUrl);
        }
        //获取枚举实例
        String enumName = clazz.getName();
        Enum[] enumConstants = clazz.getEnumConstants();
        List<EnumModel> enumList = new ArrayList<>();
        //根据方法名获取方法
        Method getValue;
        Method getName;
        try {
            getValue = clazz.getMethod(GetValue);
            getName = clazz.getMethod(GetName);
        }catch (Exception e){
            log.error("枚举："+enumName+",没有getValue或者getName方法，不能通过该接口查询枚举");
            return R.failed("枚举："+enumName+"没有getValue或者getName方法，不能通过该接口查询枚举");
        }
        for (Enum e : enumConstants) {
            EnumModel dict = new EnumModel();
            try {
                dict.setName(getName.invoke(e));
                dict.setValue(getValue.invoke(e));
            } catch (Exception ex) {
                log.error("查询枚举:"+classNameUrl+",失败，原因:"+ex.getMessage());
                return R.failed("查询枚举:"+classNameUrl+",失败，原因:"+ex.getMessage());
            }
            enumList.add(dict);
        }
        return R.ok(enumList,"枚举查询成功");
    }

    @GetMapping("/getByEnumPathBatch")
    public R getByEnumPathBatch(@RequestParam("enumPathList") List<String> enumPathList) {
        Map<String, Object> enumModelMap = new HashMap<>();
        for (String path:enumPathList) {
            String classNameUrl=EnumBaseUrl+path;
            Class<Enum> clazz;
            try{
                clazz=(Class<Enum>) Class.forName(classNameUrl);
            }catch (Exception e){
                log.error("没有找到对应的枚举类:"+classNameUrl);
                enumModelMap.put(path,"没有找到对应的枚举类");
                continue;
            }
            //获取枚举实例
            String enumName = clazz.getName();
            Enum[] enumConstants = clazz.getEnumConstants();
            List<EnumModel> enumList = new ArrayList<>();
            //根据方法名获取方法
            Method getValue;
            Method getName;
            try {
                getValue = clazz.getMethod(GetValue);
                getName = clazz.getMethod(GetName);
            }catch (Exception e){
                log.error("枚举："+enumName+",没有getValue或者getName方法，不能通过该接口查询枚举");
                enumModelMap.put(path,"没有getValue或者getName方法，不能通过该接口查询枚举");
                continue;
            }
            for (Enum e : enumConstants) {
                EnumModel dict = new EnumModel();
                try {
                    dict.setName(getName.invoke(e));
                    dict.setValue(getValue.invoke(e));
                } catch (Exception ex) {
                    log.error("查询枚举:"+classNameUrl+",失败，原因:"+ex.getMessage());
                }
                enumList.add(dict);
            }
            enumModelMap.put(path,enumList);
        }
        return R.ok(enumModelMap,"枚举查询成功");
    }


    @GetMapping("/getOaEnumByFieldId/{fieldId}")
    public R getOaEnumByFieldId(@PathVariable("fieldId") String fieldId) {
        Map<Integer, String> oaEnumMap = dbApi.projectDictNew(fieldId).stream()
                .collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName));
        return R.ok(oaEnumMap);
    }

}
