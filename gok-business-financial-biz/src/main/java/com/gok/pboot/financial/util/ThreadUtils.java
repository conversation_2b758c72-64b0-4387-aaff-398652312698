package com.gok.pboot.financial.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程工具类
 *
 * <AUTHOR>
 * @since 2023-11-30
 */
@Slf4j
public class ThreadUtils {

    private ThreadUtils() {}

    private static class ThreadUtilsInstance {
        public static final ThreadUtils INSTANCE = new ThreadUtils();
    }

    public static ThreadUtils getInstance() {
        return ThreadUtilsInstance.INSTANCE;
    }

    /**
     * 获取自定义线程池
     *
     * @param size 阻塞队列长度
     * @return {@link ThreadPoolExecutor} 线程池
     */
    public ThreadPoolExecutor getThreadPool(int size) {
        return new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(size), (r, e) -> {
                    try {
                        e.getQueue().put(r);
                    } catch (InterruptedException ex) {
                        Thread.currentThread().interrupt();
                        log.info("Thread interrupt");
                    }
                });
    }

    /**
     * 获取执行线程数
     *
     * @param listSize 集合长度
     * @param size 每个线程执行的任务数
     * @return 线程数
     */
    public int getThreadCount(int listSize, int size) {
        return listSize % size == 0 ? listSize / size : listSize / size + 1;
    }
}
