package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 会计科目
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingAccountDTO{

    /**
     * id
     */
    private Long id;

    /**
     * 启用
     */
    private Boolean enableStatusFlag;

    /**
    * 科目编码
    */
    private String accountCode;

    /**
    * 科目名称
    */
    private String accountName;

    /**
    * 助记词
    */
    private String mnemonicWords;

    /**
     * 级次
     */
    private Integer level;

    /**
    * 上级科目id
    */
    private Long parentId;

    /**
    * 余额方向
    */
    private Integer balanceDirection;

    /**
    * 会计体系id
    */
    private Long accountingSystemId;

    /**
    * 科目类型id
    */
    private Long accountTypeId;

    /**
    * 现金分类
    */
    private Integer cashClassification;

    /**
    * 账页格式
    */
    private Integer accountPageFormat;

    /**
    * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
    */
    private Integer enableStatus;

    /**
    * 停用日期
    */
    private String suspensionDate;

    /**
    * 辅助核算项集合
    */
    private List<String> auxiliaryCalculateItems;

    /**
    * 自定义核算项集合
    */
    private List<Long> customizeCalculateItems;

    /**
     * ids
     */
    private List<Long> idList;
}