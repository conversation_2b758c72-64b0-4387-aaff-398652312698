package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.vo.AccountRelationshipVO;
import com.gok.pboot.financial.db.entity.IndicatorAccountRelationship;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipPageDTO;
import com.gok.pboot.financial.vo.IndicatorAccountRelationshipVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 指定科目关系Mapper
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface IndicatorAccountRelationshipMapper extends BaseMapper<IndicatorAccountRelationship> {

    /**
     * 分页查询
     *
     * @param page {@link Page}<{@link IndicatorAccountRelationship}>
     * @param dto {@link IndicatorAccountRelationshipPageDTO}
     * @return {@link Page}<{@link IndicatorAccountRelationship}>
     */
    Page<IndicatorAccountRelationshipVO> findPage(Page<IndicatorAccountRelationshipVO> page,
                                                @Param("query") IndicatorAccountRelationshipPageDTO dto);

    /**
     * 获取集合数据
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO}
     * @return {@link List}<{@link IndicatorAccountRelationship}>
     */
    List<IndicatorAccountRelationshipVO> findPage(@Param("query") IndicatorAccountRelationshipPageDTO dto);

    /**
     * 通过会计体系编码查询指标科目关系列表
     *
     * @param systemCode 会计体系编码
     * @return {@link List}<{@link IndicatorAccountRelationshipVO}>
     */
    List<AccountRelationshipVO> getBySystemCode(@Param("systemCode") String systemCode);
}