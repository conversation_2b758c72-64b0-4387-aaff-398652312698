package com.gok.pboot.financial.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.FundAccount;
import com.gok.pboot.financial.db.mapper.FundAccountMapper;
import com.gok.pboot.financial.dto.FundAccountDTO;
import com.gok.pboot.financial.dto.FundAccountFindDTO;
import com.gok.pboot.financial.service.IFundAccountService;
import com.gok.pboot.financial.vo.FundAccountVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 资金账户业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FundAccountServiceImpl extends ServiceImpl<FundAccountMapper, FundAccount>
        implements IFundAccountService {

    private final FundAccountMapper fundAccountMapper;

    @Override
    public Page<FundAccountVO> findPage(FundAccountFindDTO dto) {
        Page<FundAccount> page = new Page<>(dto.getCurrent(), dto.getSize());
        return fundAccountMapper.queryPage(page,dto);
    }

    @Override
    public R addOrEdit(FundAccountDTO dto) {
        List<FundAccount> fundAccounts = fundAccountMapper.selectByCode(dto.getCode());
        //插入或更新
        if(Optional.ofNullable(dto.getId()).isPresent() && dto.getId()> NumberUtils.LONG_ZERO){
            //更新
            if(CollectionUtils.isNotEmpty(fundAccounts) && !dto.getId().equals(fundAccounts.get(NumberUtils.INTEGER_ZERO).getId())){
                return R.failed("编号已存在");
            }
            FundAccount fundAccount = fundAccountMapper.selectById(dto.getId());
            if(!Optional.ofNullable(fundAccount).isPresent()){
                return R.failed("当前数据不存在");
            }
            fundAccount.setCode(dto.getCode());
            fundAccount.setFundAccount(dto.getFundAccount());
            fundAccountMapper.updateById(fundAccount);
            return info(fundAccount.getId());
        }
        //新增
        if(CollectionUtils.isNotEmpty(fundAccounts)){
            return R.failed("编号已存在");
        }
        //插入
        FundAccount fundAccount = new FundAccount();
        fundAccount.setCode(dto.getCode());
        fundAccount.setFundAccount(dto.getFundAccount());
        fundAccountMapper.insert(fundAccount);
        return info(fundAccount.getId());
    }

    @Override
    public R<FundAccountVO> info(Long id) {
        FundAccount fundAccount = fundAccountMapper.selectById(id);
        if(!Optional.ofNullable(fundAccount).isPresent()){
            return R.failed("查询不到数据");
        }
        return R.ok(FundAccountVO.builder()
                        .id(fundAccount.getId())
                        .code(fundAccount.getCode())
                        .fundAccount(fundAccount.getFundAccount())
                        .build());
    }

    @Override
    public boolean del(List<Long> idList) {
        return fundAccountMapper.deleteBatchIds(idList)>0?true:false;
    }

    @Override
    public List<FundAccountVO> findList() {
        return fundAccountMapper.findList();
    }
}
