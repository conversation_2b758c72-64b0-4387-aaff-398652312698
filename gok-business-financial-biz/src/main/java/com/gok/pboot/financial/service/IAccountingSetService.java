package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.dto.AccountingSetDTO;
import com.gok.pboot.financial.dto.AccountingSetImportDTO;
import com.gok.pboot.financial.dto.AccountingSetPageDTO;
import com.gok.pboot.financial.vo.AccountingSetVO;

import java.util.List;
import java.util.Map;

/**
 * 会计账套业务层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IAccountingSetService extends IService<AccountingSet> {

    /**
     * 模糊查询带分页
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSetVO}>
     */
    Page<AccountingSetVO> findPage(AccountingSetPageDTO dto);

    /**
     * 导出Excel
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSetVO}>
     */
    List<AccountingSetVO> exportExcel(AccountingSetPageDTO dto);

    /**
     * 新增
     *
     * @param dto {@link AccountingSetDTO}
     * @return {@code true or false>
     */
    boolean addOrEdit(AccountingSetDTO dto);

    /**
     * 删除
     *
     * @param idList {@link List}<{@link Long}>
     * @return {@code true or false>
     */
    Boolean del(List<Long> idList);

    /**
     * 启用
     *
     * @param dto {@link AccountingSetDTO>
     * @return {@code true or false>
     */
    boolean enable(AccountingSetDTO dto);

    /**
     * 获取会计帐套信息
     * @return {@link Map}
     */
    Map<String,AccountingSet> getAccountingSet();

    /**
     * 导入
     * @param importDTOList
     * @return
     */
    R<String> importExcel(List<AccountingSetImportDTO> importDTOList);

    /**
     * 查看
     * @param id
     * @return
     */
    R<AccountingSetVO> info(Long id);
}
