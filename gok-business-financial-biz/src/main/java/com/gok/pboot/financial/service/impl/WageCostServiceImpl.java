package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.common.enums.DeptAssociationEnum;
import com.gok.bcp.upms.dto.InnerDeptMapDto;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.OpenApi;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.WageCost;
import com.gok.pboot.financial.db.mapper.WageCostMapper;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.service.IIndicatorAccountRelationshipService;
import com.gok.pboot.financial.service.IWageCostService;
import com.gok.pboot.financial.util.*;
import com.gok.pboot.financial.vo.PushErrorReasonVO;
import com.gok.pboot.financial.vo.PushResultVO;
import com.gok.pboot.financial.vo.WageCostPushVO;
import com.gok.pboot.financial.vo.WageCostVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工资成本Service实现
 *
 * <AUTHOR>
 * @since 2023-09-04 10:24:25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class WageCostServiceImpl extends ServiceImpl<WageCostMapper, WageCost> implements IWageCostService {

    private final WageCostMapper wageCostMapper;

    private final RemoteRoleService remoteRoleService;

    private final IIndicatorAccountRelationshipService indicatorAccountRelationshipService;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final IAccountingSetService accountingSetService;

    private final LogRecordUtils logRecordUtils;

    private final OpenApi openApi;

    private final DBApi dbApi;

    @Value("${middlePlatform.deptCatName}")
    private String deptCatName;

    @Value("${middlePlatform.adminDeptCatName}")
    private String adminDeptCatName;

    @Override
    public StatisticsPage<WageCostVO> findPage(StatisticsPage<WageCostVO> page, WageCostDTO wageCostDTO) {
        //分页查询
        wageCostMapper.queryWageCostPage(page, limitDto(wageCostDTO));

        List<MultiDimensionDeptDto> adminDeptList = remoteOutMultiDeptService.getDeptList(deptCatName, null, null).getData();
        Map<Long, MultiDimensionDeptDto> adminDeptMap = adminDeptList.stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId, a -> a, (a, b) -> a));

        List<WageCostVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(r -> {
                WageCostVO pageVo = Convert.convert(WageCostVO.class, r);
                //配置数据解密
                r = configurationVo(pageVo);

                MultiDimensionDeptDto primaryDeptDto = adminDeptMap.get(r.getPrimaryDeptId());
                r.setPrimaryDeptSort(Optional.ofNullable(primaryDeptDto).isPresent() ? primaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                MultiDimensionDeptDto secondaryDeptDto = adminDeptMap.get(r.getSecondaryDeptId());
                r.setSecondaryDeptSort(Optional.ofNullable(secondaryDeptDto).isPresent() ? secondaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                MultiDimensionDeptDto tertiaryDeptDto = adminDeptMap.get(r.getTertiaryDeptId());
                r.setTertiaryDeptSort(Optional.ofNullable(tertiaryDeptDto).isPresent() ? tertiaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
            });
        }
        List<WageCostVO> sortList = records.stream().sorted(Comparator.comparing(WageCostVO::getPrimaryDeptSort)
                        .thenComparing(WageCostVO::getSecondaryDeptSort)
                        .thenComparing(WageCostVO::getTertiaryDeptSort))
                .collect(Collectors.toList());
        page.setRecords(sortList);
        statistics(page,wageCostDTO);
        return page;
    }

    private void statistics(StatisticsPage<WageCostVO> page, WageCostDTO wageCostDTO) {
        if (page.getTotal() == 0) {
            return;
        }

        // 设置每批处理的数据量
        final int batchSize = 3000;
        // 计算总批次数
        int totalBatches = (int) Math.ceil((double) page.getTotal() / batchSize);

        Map<String, BigDecimal> result = new ConcurrentHashMap<>(17);
        result.put("salaryTotal", BigDecimal.ZERO);
        result.put("individualIncomeTaxTotal", BigDecimal.ZERO);
        result.put("individualSocialSecurityTotal", BigDecimal.ZERO);
        result.put("individualProvidentFundTotal", BigDecimal.ZERO);
        result.put("backPayTotal", BigDecimal.ZERO);
        result.put("incomeTaxDifferenceFromLastMonthTotal", BigDecimal.ZERO);
        result.put("actualPayTotal", BigDecimal.ZERO);
        result.put("agreementCompensationTotal", BigDecimal.ZERO);
        result.put("actualAgreementCompensationTotal", BigDecimal.ZERO);
        result.put("performanceTotal", BigDecimal.ZERO);
        result.put("actualPerformanceTotal", BigDecimal.ZERO);
        result.put("actualPaySumTotal", BigDecimal.ZERO);
        result.put("unitSocialSecurityTotal", BigDecimal.ZERO);
        result.put("unitProvidentFundTotal", BigDecimal.ZERO);
        result.put("disabledSecurityFundTotal", BigDecimal.ZERO);
        result.put("peopleNumTotal", BigDecimal.ZERO);
        result.put("payrollCostTotal", BigDecimal.ZERO);

        // 分批处理数据
        for (int i = 0; i < totalBatches; i++) {
            // 设置分页参数
            Page<WageCostVO> query = new Page<>(i + 1, batchSize);
            query.setSearchCount(false);
            // 查询当前批次数据
            wageCostMapper.queryWageCostPage(query, wageCostDTO);
            List<WageCostVO> wageCostVOList = query.getRecords();

            wageCostVOList.parallelStream().forEach(item -> {
                BigDecimal salary = MoneyUtils.decryptToBigDecimal(item.getSalary(), true);
                result.computeIfPresent("salaryTotal", (key, oldValue) -> oldValue.add(salary));

                BigDecimal individualIncomeTax = MoneyUtils.decryptToBigDecimal(item.getIndividualIncomeTax(), true);
                result.computeIfPresent("individualIncomeTaxTotal", (key, oldValue) -> oldValue.add(individualIncomeTax));

                BigDecimal individualSocialSecurity = MoneyUtils.decryptToBigDecimal(item.getIndividualSocialSecurity(), true);
                result.computeIfPresent("individualSocialSecurityTotal", (key, oldValue) -> oldValue.add(individualSocialSecurity));

                BigDecimal individualProvidentFund = MoneyUtils.decryptToBigDecimal(item.getIndividualProvidentFund(), true);
                result.computeIfPresent("individualProvidentFundTotal", (key, oldValue) -> oldValue.add(individualProvidentFund));

                BigDecimal backPay = MoneyUtils.decryptToBigDecimal(item.getBackPay(), true);
                result.computeIfPresent("backPayTotal", (key, oldValue) -> oldValue.add(backPay));

                BigDecimal incomeTaxDifferenceFromLastMonth = MoneyUtils.decryptToBigDecimal(item.getIncomeTaxDifferenceFromLastMonth(), true);
                result.computeIfPresent("incomeTaxDifferenceFromLastMonthTotal", (key, oldValue) -> oldValue.add(incomeTaxDifferenceFromLastMonth));

                BigDecimal actualPay = MoneyUtils.decryptToBigDecimal(item.getActualPay(), true);
                result.computeIfPresent("actualPayTotal", (key, oldValue) -> oldValue.add(actualPay));

                BigDecimal agreementCompensation = MoneyUtils.decryptToBigDecimal(item.getAgreementCompensation(), true);
                result.computeIfPresent("agreementCompensationTotal", (key, oldValue) -> oldValue.add(agreementCompensation));

                BigDecimal actualAgreementCompensation = MoneyUtils.decryptToBigDecimal(item.getActualAgreementCompensation(), true);
                result.computeIfPresent("actualAgreementCompensationTotal", (key, oldValue) -> oldValue.add(actualAgreementCompensation));

                BigDecimal performance = MoneyUtils.decryptToBigDecimal(item.getPerformance(), true);
                result.computeIfPresent("performanceTotal", (key, oldValue) -> oldValue.add(performance));

                BigDecimal actualPerformance = MoneyUtils.decryptToBigDecimal(item.getActualPerformance(), true);

                result.computeIfPresent("actualPerformanceTotal", (key, oldValue) -> oldValue.add(actualPerformance));

                BigDecimal actualPaySum = MoneyUtils.decryptToBigDecimal(item.getActualPaySum(), true);
                result.computeIfPresent("actualPaySumTotal", (key, oldValue) -> oldValue.add(actualPaySum));

                BigDecimal unitSocialSecurity = MoneyUtils.decryptToBigDecimal(item.getUnitSocialSecurity(), true);
                result.computeIfPresent("unitSocialSecurityTotal", (key, oldValue) -> oldValue.add(unitSocialSecurity));

                BigDecimal unitProvidentFund = MoneyUtils.decryptToBigDecimal(item.getUnitProvidentFund(), true);
                result.computeIfPresent("unitProvidentFundTotal", (key, oldValue) -> oldValue.add(unitProvidentFund));

                BigDecimal disabledSecurityFund = MoneyUtils.decryptToBigDecimal(item.getDisabledSecurityFund(), true);
                result.computeIfPresent("disabledSecurityFundTotal", (key, oldValue) -> oldValue.add(disabledSecurityFund));

                BigDecimal peopleNum = item.getPeopleNum() == null ? BigDecimal.ZERO : BigDecimal.valueOf(item.getPeopleNum());
                result.computeIfPresent("peopleNumTotal", (key, oldValue) -> oldValue.add(peopleNum));

                BigDecimal payrollCost = MoneyUtils.decryptToBigDecimal(item.getPayrollCost(), true);
                result.computeIfPresent("payrollCostTotal", (key, oldValue) -> oldValue.add(payrollCost));
            });
        }
        page.setStatistics(result);
    }


    @Override
    public R<List<String>> salaryPaidSubject() {
        List<String> list = wageCostMapper.salaryPaidSubject().stream()
                .filter(s -> Optional.ofNullable(s).isPresent())
                .collect(Collectors.toList());
        return R.ok(list);
    }

    @Override
    public List<WageCostVO> export(WageCostDTO wageCostDTO) {
        List<WageCostVO> wageCostVOList = wageCostMapper.exportList(limitDto(wageCostDTO));
        List<MultiDimensionDeptDto> adminDeptList = remoteOutMultiDeptService.getDeptList(deptCatName, null, null).getData();
        Map<Long, MultiDimensionDeptDto> adminDeptMap = adminDeptList.stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId, a -> a, (a, b) -> a));

        if (CollUtil.isNotEmpty(wageCostVOList)) {


            List<WageCostVO> sortList = wageCostVOList.stream().map(r -> {
                        WageCostVO wageCost = Convert.convert(WageCostVO.class, r);
                        wageCost = configurationVo(r);
                        MultiDimensionDeptDto primaryDeptDto = adminDeptMap.get(r.getPrimaryDeptId());
                        wageCost.setPrimaryDeptSort(Optional.ofNullable(primaryDeptDto).isPresent() ? primaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                        MultiDimensionDeptDto secondaryDeptDto = adminDeptMap.get(r.getSecondaryDeptId());
                        wageCost.setSecondaryDeptSort(Optional.ofNullable(secondaryDeptDto).isPresent() ? secondaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                        MultiDimensionDeptDto tertiaryDeptDto = adminDeptMap.get(r.getTertiaryDeptId());
                        wageCost.setTertiaryDeptSort(Optional.ofNullable(tertiaryDeptDto).isPresent() ? tertiaryDeptDto.getSort() : NumberUtils.INTEGER_ZERO);
                        return wageCost;
                    }).collect(Collectors.toList())
                    .stream().sorted(Comparator.comparing(WageCostVO::getPrimaryDeptSort)
                            .thenComparing(WageCostVO::getSecondaryDeptSort)
                            .thenComparing(WageCostVO::getTertiaryDeptSort))
                    .collect(Collectors.toList());
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.WAGE_COST,
                    OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + wageCostVOList.size() + "条】");

            return sortList;
        }
        wageCostVOList.add(new WageCostVO());
        return wageCostVOList;
    }

    @Override
    public PushResultVO push(List<String> ids) {
        AtomicInteger succeed = new AtomicInteger();
        AtomicInteger error = new AtomicInteger();

        // 1、获取待推送工资汇总详情
        List<WageCost> wageCostList = wageCostMapper.selList(ids);
        if (CollUtil.isEmpty(wageCostList)) {
            return PushResultVO.builder()
                    .success(NumberUtils.INTEGER_ZERO)
                    .error(ids.size()).build();
        }
        StringBuilder sb = new StringBuilder();
        List<PushErrorReasonVO> errorReasonList = new ArrayList<>();

        //获取核算部门和科目编码的对应关系
        Map<String, String> indicatorMap = indicatorAccountRelationshipService.getIndicatorAccountRelationship();
        List<WageCostPushVO> wageCostPushVOList = wageCostList.stream().map(w -> {
            WageCostPushVO pushVO = BeanUtil.copyProperties(w, WageCostPushVO.class);
            pushVO.setAccountCode(indicatorMap.getOrDefault(String.valueOf(w.getCurrentDeptId()), StrUtil.EMPTY));
            //工资成本
            pushVO.setPayrollCost(MoneyUtils.getInstance().decrypt(w.getPayrollCost()));
            //单位社保
            pushVO.setUnitSocialSecurity(MoneyUtils.getInstance().decrypt(w.getUnitSocialSecurity()));
            //单位公积金
            pushVO.setUnitProvidentFund(MoneyUtils.getInstance().decrypt(w.getUnitProvidentFund()));
            //协议补偿金
            pushVO.setAgreementCompensation(MoneyUtils.getInstance().decrypt(w.getAgreementCompensation()));
            //绩效工资
            pushVO.setPerformance(MoneyUtils.getInstance().decrypt(w.getPerformance()));
            return pushVO;
        }).collect(Collectors.toList());

        List<WageCostPushVO> pushVOList = wageCostPushVOList.stream()
                .filter(v -> !StrUtil.EMPTY.equals(v.getAccountCode()))
                .collect(Collectors.toList());

        //传入的ids和获取到的项目数据个数是否一致
        Integer notPushNum = NumberUtils.INTEGER_ZERO;
        if (ids.size() != pushVOList.size()) {
            notPushNum = ids.size() - pushVOList.size();
            List<WageCostPushVO> notPushVOList = wageCostPushVOList.stream().filter(v -> StrUtil.EMPTY.equals(v.getAccountCode())).collect(Collectors.toList());
            notPushVOList.stream().forEach(n -> {
                errorReasonList.add(PushErrorReasonVO.builder()
                        .id(n.getYearMonthDate() + n.getSecondaryDept() + n.getSalaryPaidSubject())
                        .errorReason(VoucherRecordEnum.ERROR_STR.getName())
                        .build());
                WageCost wageCost = BeanUtil.copyProperties(n, WageCost.class);
                wageCost.setUpdateBy(UserUtils.getUser().getUsername());
                wageCost.setUpdateTime(LocalDateTime.now());
                wageCost.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                wageCostMapper.updatePushStatus(wageCost);
                // 记录日志
                sb.append("【").append(wageCost.getYearMonthDate()).append("】【").append(wageCost.getSalaryPaidSubject())
                        .append("】【").append(wageCost.getSecondaryDept()).append("】【")
                        .append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
            });

        }

        //根据发薪主体+归属月份+所属科目分组
        Map<String, List<WageCostPushVO>> pushVOMap = pushVOList.stream().collect(Collectors.groupingBy(a ->
                a.getSalaryPaidSubject() + a.getYearMonthDate() + a.getAccountCode()));

        //获取帐套信息,key:发薪主体，value:帐套信息
        Map<String, AccountingSet> accountingSetMap = accountingSetService.getAccountingSet();
        //获取默认部门信息,key：T+部门id
        Map<Long, String> tPlusMap = remoteOutMultiDeptService.getThirdDeptRelation(deptCatName, DeptAssociationEnum.T_PLUS).getData();


        //获取帐套信息
        pushVOMap.values().forEach(pushList -> {
            WageCostPushVO wageCostPushVO = pushList.get(NumberUtils.INTEGER_ZERO);
            AccountingSet accountingSet = accountingSetMap.get(wageCostPushVO.getSalaryPaidSubject());
            if (Optional.ofNullable(accountingSet).isPresent()) {
                //获取登录T+token
                String token = openApi.login(accountingSet.getAccountingAccount(), accountingSet.getAccountingPassword(), accountingSet.getAccountingCode());
//                //获取所有科目编号
//                List<String> accountCodeList = pushList.stream().map(WageCostPushVO::getAccountCode).distinct().collect(Collectors.toList());
                //根据科目编号分组
                Map<String, List<WageCostPushVO>> pushMap = pushList.stream().collect(Collectors.groupingBy(WageCostPushVO::getAccountCode));

                for (Map.Entry<String, List<WageCostPushVO>> entry : pushMap.entrySet()) {
                    // 科目编号
                    String num = entry.getKey();
                    List<WageCostPushVO> pushVOS = entry.getValue();
                    // 获取凭证列表
                    VoucherListDTO voucherList = getVoucherList(tPlusMap, num, pushVOS);

                    //凭证创建
                    String result = openApi.createBatchVoucher(token, voucherList);

                    if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                        List<WageCost> wageCosts = pushVOS.stream().map(e -> {
                            WageCost wageCost = BeanUtil.copyProperties(e, WageCost.class);
                            wageCost.setUpdateBy(UserUtils.getUser().getUsername());
                            wageCost.setUpdateTime(LocalDateTime.now());
                            wageCost.setPushStatus(PushStatusEnum.SUCCESS_PUSH.getValue());

                            succeed.incrementAndGet();
                            // 记录日志
                            sb.append("【").append(wageCost.getYearMonthDate()).append("】【").append(wageCost.getSalaryPaidSubject())
                                    .append("】【").append(wageCost.getSecondaryDept()).append("】【")
                                    .append(PushStatusEnum.SUCCESS_PUSH.getName()).append("】");

                            return wageCost;
                        }).collect(Collectors.toList());

                        wageCostMapper.batchUpdatePushStatus(wageCosts);
                    } else {
                        List<WageCost> wageCosts = pushVOS.stream().map(e -> {
                            WageCost wageCost = BeanUtil.copyProperties(e, WageCost.class);
                            wageCost.setUpdateBy(UserUtils.getUser().getUsername());
                            wageCost.setUpdateTime(LocalDateTime.now());
                            wageCost.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());

                            error.incrementAndGet();
                            errorReasonList.add(PushErrorReasonVO.builder()
                                    .id(wageCost.getYearMonthDate() + wageCost.getSecondaryDept() + wageCost.getSalaryPaidSubject())
                                    .errorReason(result)
                                    .build());
                            // 记录日志
                            sb.append("【").append(wageCost.getYearMonthDate()).append("】【").append(wageCost.getSalaryPaidSubject())
                                    .append("】【").append(wageCost.getSecondaryDept()).append("】【")
                                    .append(PushStatusEnum.FAIL_PUSH.getName()).append("】");

                            return wageCost;
                        }).collect(Collectors.toList());
                        wageCostMapper.batchUpdatePushStatus(wageCosts);
                    }
                }
            } else {
                List<WageCost> wageCosts = pushList.stream().map(e -> {
                    WageCost wageCost = BeanUtil.copyProperties(e, WageCost.class);
                    wageCost.setUpdateBy(UserUtils.getUser().getUsername());
                    wageCost.setUpdateTime(LocalDateTime.now());
                    wageCost.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    errorReasonList.add(PushErrorReasonVO.builder()
                            .id(wageCost.getYearMonthDate() + wageCost.getSecondaryDept() + wageCost.getSalaryPaidSubject())
                            .errorReason(VoucherRecordEnum.ACCOUNTING_ERROR.getName())
                            .build());
                    error.incrementAndGet();
                    // 记录日志
                    sb.append("【").append(wageCost.getYearMonthDate()).append("】【").append(wageCost.getSalaryPaidSubject())
                            .append("】【").append(wageCost.getSecondaryDept()).append("】【")
                            .append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                    return wageCost;
                }).collect(Collectors.toList());
                wageCostMapper.batchUpdatePushStatus(wageCosts);
            }
        });
        // 日志记录
        if (sb.length() > NumberUtils.INTEGER_ZERO) {
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.WAGE_COST,
                    OperationEnum.PUSH, OperationEnum.PUSH.getName() + sb.toString());
        }

        return PushResultVO.builder()
                .success(succeed.get())
                .error(error.get() + notPushNum)
                .errorReasonList(errorReasonList).build();
    }

    /**
     * 获取凭证列表
     *
     * @param tPlusMap 默认部门信息,key：T+部门id
     * @param num      科目编号
     * @param pushVOS  编号对应的组
     * @return 凭证列表
     */
    private VoucherListDTO getVoucherList(Map<Long, String> tPlusMap, String num, List<WageCostPushVO> pushVOS) {
        //工资成本合计
        String payrollCost = pushVOS.stream()
                .filter(c -> Optional.ofNullable(c.getPayrollCost()).isPresent())
                .map(e -> new BigDecimal(e.getPayrollCost()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
        //单位社保合计
        String unitSocialSecurity = pushVOS.stream()
                .filter(c -> Optional.ofNullable(c.getUnitSocialSecurity()).isPresent())
                .map(e -> new BigDecimal(e.getUnitSocialSecurity()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
        //单位公积金合计
        String unitProvidentFund = pushVOS.stream()
                .filter(c -> Optional.ofNullable(c.getUnitProvidentFund()).isPresent())
                .map(e -> new BigDecimal(e.getUnitProvidentFund()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
        // 协议补偿
        String agreementCompensation = pushVOS.stream()
                .filter(c -> Optional.ofNullable(c.getAgreementCompensation()).isPresent())
                .map(e -> new BigDecimal(e.getAgreementCompensation()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toString();
        // 绩效工资
        String performance = pushVOS.stream()
                .filter(c -> Optional.ofNullable(c.getPerformance()).isPresent())
                .map(e -> new BigDecimal(e.getPerformance()))
                .reduce(BigDecimal.ZERO, BigDecimal::add).toString();

        List<VoucherDTO> dtos = new ArrayList<>();

        if (IndicatorSubjectsEnum.MANAGE.getName().equals(num)) {
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    payrollCost,
                    VoucherRecordEnum.PAYROLL_COST.getName(),
                    VoucherSubjectIdEnum.MANAGEMENT_SALARY.getName(),
                    VoucherSubjectIdEnum.PAYROLL_PAYABLE.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    unitSocialSecurity,
                    VoucherRecordEnum.UNIT_SOCIAL_SECURITY.getName(),
                    VoucherSubjectIdEnum.MANAGEMENT_SOCIAL_SECURITY.getName(),
                    VoucherSubjectIdEnum.PAYABLE_SOCIAL_INSURANCE.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    unitProvidentFund,
                    VoucherRecordEnum.VOUCHER_RECORD_ENUM.getName(),
                    VoucherSubjectIdEnum.MANAGEMENT_PROVIDENT_FUND.getName(),
                    VoucherSubjectIdEnum.PAYABLE_HOUSING_PROVIDENT_FUND.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    agreementCompensation,
                    VoucherRecordEnum.AGREEMENT_COMPENSATION.getName(),
                    VoucherSubjectIdEnum.MANAGEMENT_AGREEMENT_COMPENSATION.getName(),
                    VoucherSubjectIdEnum.PAYABLE_AGREEMENT_COMPENSATION.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    performance,
                    VoucherRecordEnum.PERFORMANCE.getName(),
                    VoucherSubjectIdEnum.MANAGEMENT_PERFORMANCE.getName(),
                    VoucherSubjectIdEnum.PAYABLE_PERFORMANCE.getName()
            ));
        } else if (IndicatorSubjectsEnum.SALE.getName().equals(num)) {
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    payrollCost,
                    VoucherRecordEnum.PAYROLL_COST.getName(),
                    VoucherSubjectIdEnum.SALE_SALARY.getName(),
                    VoucherSubjectIdEnum.PAYROLL_PAYABLE.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    unitSocialSecurity,
                    VoucherRecordEnum.UNIT_SOCIAL_SECURITY.getName(),
                    VoucherSubjectIdEnum.SALE_SOCIAL_SECURITY.getName(),
                    VoucherSubjectIdEnum.PAYABLE_SOCIAL_INSURANCE.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    unitProvidentFund,
                    VoucherRecordEnum.VOUCHER_RECORD_ENUM.getName(),
                    VoucherSubjectIdEnum.SALE_PROVIDENT_FUND.getName(),
                    VoucherSubjectIdEnum.PAYABLE_HOUSING_PROVIDENT_FUND.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    agreementCompensation,
                    VoucherRecordEnum.AGREEMENT_COMPENSATION.getName(),
                    VoucherSubjectIdEnum.SALE_AGREEMENT_COMPENSATION.getName(),
                    VoucherSubjectIdEnum.PAYABLE_AGREEMENT_COMPENSATION.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    performance,
                    VoucherRecordEnum.PERFORMANCE.getName(),
                    VoucherSubjectIdEnum.SALE_PERFORMANCE.getName(),
                    VoucherSubjectIdEnum.PAYABLE_PERFORMANCE.getName()
            ));

        } else if (IndicatorSubjectsEnum.RESEARCH.getName().equals(num)) {
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    payrollCost,
                    VoucherRecordEnum.PAYROLL_COST.getName(),
                    VoucherSubjectIdEnum.PAYROLL.getName(),
                    VoucherSubjectIdEnum.PAYROLL_PAYABLE.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    unitSocialSecurity,
                    VoucherRecordEnum.UNIT_SOCIAL_SECURITY.getName(),
                    VoucherSubjectIdEnum.SOCIAL_SECURITY.getName(),
                    VoucherSubjectIdEnum.PAYABLE_SOCIAL_INSURANCE.getName()
            ));
            dtos.add(voucher(
                    pushVOS,
                    tPlusMap,
                    unitProvidentFund,
                    VoucherRecordEnum.VOUCHER_RECORD_ENUM.getName(),
                    VoucherSubjectIdEnum.PROVIDENT_FUND.getName(),
                    VoucherSubjectIdEnum.PAYABLE_HOUSING_PROVIDENT_FUND.getName()
            ));
        }
        // 过滤null值(金额为0不推送该条凭证)
        dtos = dtos.stream().filter(Objects::nonNull).collect(Collectors.toList());
        return VoucherListDTO.builder().dtos(dtos)
                .param(JSONUtil.parse(new Object())).build();
    }

    /**
     * 凭证 (crCost为"0.00"时返回null)
     *
     * @param pushVoList    {@link List}
     * @param tPlusMap      {@link Map}
     * @param crCost        {@link String}
     * @param recordName    {@link String}
     * @param drSubjectName {@link String}
     * @param crSubjectName {@link String}
     * @return {@link VoucherDTO}
     */
    public VoucherDTO voucher(List<WageCostPushVO> pushVoList,
                              Map<Long, String> tPlusMap,
                              String crCost,
                              String recordName,
                              String drSubjectName,
                              String crSubjectName) {
        if (DecimalFormatUtil.ZERO.equals(crCost)) {
            return null;
        }
        WageCostPushVO wageCostPushVO = pushVoList.get(NumberUtils.INTEGER_ZERO);
        //制单日期为归属月份的最后一日天
        LocalDate date = LocalDate.parse(wageCostPushVO.getYearMonthDate().replace("-", "") + "01", DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
        String yearMonthDate = wageCostPushVO.getYearMonthDate();
        //凭证实体数据组装
        VoucherDTO.VoucherDTOBuilder voucherDtoBuilder = VoucherDTO.builder()
                .ExternalCode(wageCostPushVO.getCurrentDeptId() + yearMonthDate.replace("-", "") + drSubjectName)
                .VoucherDate(lastDay.toString())
                .DocType(VoucherDocTypeDTO.builder().Code(VoucherRecordEnum.DOCTYPE.getName()).build());

        //凭证明细
        ArrayList<VoucherDetailDTO> arrayList = new ArrayList<>();
        pushVoList.forEach(p -> {
            //凭证创建辅佐项集合
            List<AuxiliaryDTO> auxiliaryList = new ArrayList<>();
            AuxiliaryDTO crAuxiliary = AuxiliaryDTO.builder()
                    .AuxAccDepartment(AuxAccDepartmentDTO.builder().Code(tPlusMap.getOrDefault(p.getCurrentDeptId(), StrUtil.EMPTY)).build())
                    .AuxAccProject(AuxAccProjectDTO.builder().Code("030001").build())
                    .build();
            auxiliaryList.add(crAuxiliary);

//            String drCost = VoucherRecordEnum.PAYROLL_COST.getName().equals(recordName) ? p.getPayrollCost() :
//                    VoucherRecordEnum.UNIT_SOCIAL_SECURITY.getName().equals(recordName) ? p.getUnitSocialSecurity() :
//                            p.getUnitProvidentFund();
            String drCost = getDrCost(p, recordName);
            if (Optional.ofNullable(drCost).isPresent() && !MoneyUtils.ZERO_THREE.equals(drCost)) {
                VoucherDetailDTO payrollDr = VoucherDetailDTO.builder()
                        .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                        .Account(EntryAccountDTO.builder().Code(drSubjectName).build())
                        .AmountDr(drCost)
                        .Summary(VoucherRecordEnum.WAGE_COST_SUMMARY.getName() + recordName + p.getYearMonthDate())
                        .AuxInfos(auxiliaryList)
                        .build();
                arrayList.add(payrollDr);
            }
        });
        VoucherDetailDTO payrollPayableCr = VoucherDetailDTO.builder()
                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                .Account(EntryAccountDTO.builder().Code(crSubjectName).build())
                .AmountCr(crCost)
                .Summary(VoucherRecordEnum.WAGE_COST_SUMMARY.getName() + recordName + yearMonthDate)
                .build();
        arrayList.add(payrollPayableCr);

        return voucherDtoBuilder.Entrys(arrayList).build();
    }

    private String getDrCost(WageCostPushVO p, String recordName) {
        String drCost;
        if (VoucherRecordEnum.PAYROLL_COST.getName().equals(recordName)) {
            drCost = p.getPayrollCost();
        } else if (VoucherRecordEnum.UNIT_SOCIAL_SECURITY.getName().equals(recordName)) {
            drCost = p.getUnitSocialSecurity();
        } else if (VoucherRecordEnum.VOUCHER_RECORD_ENUM.getName().equals(recordName)) {
            drCost = p.getUnitProvidentFund();
        } else if (VoucherRecordEnum.AGREEMENT_COMPENSATION.getName().equals(recordName)) {
            drCost = p.getAgreementCompensation();
        } else {
            drCost = p.getPerformance();
        }
        return drCost;
    }

    @Override
    public List<Tree<Long>> accountDeptBox() {
        // 一级部门
        List<MultiDimensionDeptDto> data = remoteOutMultiDeptService.getDeptList(deptCatName, "", 1).getData();
        // 二级部门
        data.addAll(remoteOutMultiDeptService.getDeptList(deptCatName, "", 2).getData());
        // 三级部门
        data.addAll(remoteOutMultiDeptService.getDeptList(deptCatName, "", 3).getData());
        List<TreeNode<Long>> deptTree = data.stream().map(getUserNodeFunction()).collect(Collectors.toList());
        return TreeUtil.build(deptTree, NumberUtils.LONG_MINUS_ONE);
    }

    @NotNull
    private Function<MultiDimensionDeptDto, TreeNode<Long>> getUserNodeFunction() {
        return deptUser -> {
            TreeNode<Long> node = new TreeNode<>();
            node.setId(deptUser.getDeptId());
            node.setName(deptUser.getName());
            node.setParentId(deptUser.getParentId());
            node.setWeight(0);
            // 扩展属性
            Map<String, Object> extra = new HashMap<>(16);
            extra.put("sortOrder", 0);
            extra.put("isDept", true);
            extra.put("uid", deptUser.getDeptId());
            node.setExtra(extra);
            return node;
        };
    }

    /**
     * 给vo类解密配置信息
     *
     * @param vo 未配置的vo类
     * @return {@link WageCostVO}
     */
    private WageCostVO configurationVo(WageCostVO vo) {
        vo.setYearMonthDate(DateUtils.dateTrans(vo.getYearMonthDate()));
        vo.setPushStatusStr(EnumUtils.getNameByValue(PushStatusEnum.class, vo.getPushStatus()));
        vo.setSalary(MoneyUtils.getInstance().transType(vo.getSalary(), true));
        vo.setIndividualIncomeTax(MoneyUtils.getInstance().transType(vo.getIndividualIncomeTax(), true));
        vo.setIndividualSocialSecurity(MoneyUtils.getInstance().transType(vo.getIndividualSocialSecurity(), true));
        vo.setIndividualProvidentFund(MoneyUtils.getInstance().transType(vo.getIndividualProvidentFund(), true));
        vo.setBackPay(MoneyUtils.getInstance().transType(vo.getBackPay(), true));
        vo.setIncomeTaxDifferenceFromLastMonth(MoneyUtils.getInstance().transType(vo.getIncomeTaxDifferenceFromLastMonth(), true));
        vo.setActualPay(MoneyUtils.getInstance().transType(vo.getActualPay(), true));
        vo.setUnitSocialSecurity(MoneyUtils.getInstance().transType(vo.getUnitSocialSecurity(), true));
        vo.setUnitProvidentFund(MoneyUtils.getInstance().transType(vo.getUnitProvidentFund(), true));
        vo.setDisabledSecurityFund(MoneyUtils.getInstance().transType(vo.getDisabledSecurityFund(), true));
        vo.setPayrollCost(MoneyUtils.getInstance().transType(vo.getPayrollCost(), true));
        vo.setAgreementCompensation(MoneyUtils.getInstance().transType(vo.getAgreementCompensation(), true));
        vo.setActualAgreementCompensation(MoneyUtils.getInstance().transType(vo.getActualAgreementCompensation(), true));
        vo.setPerformance(MoneyUtils.getInstance().transType(vo.getPerformance(), true));
        vo.setActualPerformance(MoneyUtils.getInstance().transType(vo.getActualPerformance(), true));
        vo.setActualPaySum(MoneyUtils.getInstance().transType(vo.getActualPaySum(), true));
        return vo;
    }

    /**
     * 给Dto类限制权限
     *
     * @param dto 需要添加限制的dto
     * @return {@link WageCostDTO}
     */
    private WageCostDTO limitDto(WageCostDTO dto) {
        SysUserRoleDataVo userDataScope = remoteRoleService
                .getRoleDataDetailByUserId(dto.getClientId(), UserUtils.getUser().getId(), dto.getMenuCode()).getData();
        log.error("中台返回数据情况：{}", userDataScope);
        // 获取中台的部门架构树id集合
        Set<Long> deptTreeIds = new HashSet<>();
        remoteOutMultiDeptService.getDeptList(adminDeptCatName, null, null)
                .getData().forEach(dept -> {
                    if (dept.getDeptId() != null) {
                        deptTreeIds.add(dept.getDeptId());
                    }
                    if (dept.getParentId() != null) {
                        deptTreeIds.add(dept.getParentId());
                    }
                });
        // 获取中台的行政部门与虚拟部门对应关系
        List<InnerDeptMapDto> innerDeptMapDtoList = remoteOutMultiDeptService.getInnerDeptMapDto(deptCatName).getData();
        // 行政部门id作为key 虚拟部门id作为value
        if (CollUtil.isEmpty(innerDeptMapDtoList)) {
            innerDeptMapDtoList = new ArrayList<>();
        }
        Map<Long, Long> innerDeptMap = innerDeptMapDtoList.stream()
                .collect(Collectors.toMap(InnerDeptMapDto::getInnerDeptId, InnerDeptMapDto::getVirtualDeptId, (a, b) -> a));

        // 1、创建总部门id列表
        List<Long> deptIdList = new ArrayList<>();
        // 2、条件查询部门id列表
        if (CollUtil.isNotEmpty(dto.getDeptIds())) {
            dto.setConditionDeptIdList(dto.getDeptIds());
        }
        // 3、数据权限处理
        if (Boolean.FALSE.equals(userDataScope.getIsAll())) {
            // 3.1、赋初始值-2防止为空跳过权限问题
            deptIdList.add(-2L);
            // 3.2、数据权限部门id列表 有则行政部门对应虚拟部门
            List<Long> innerDeptIdList = userDataScope.getDeptIdList();
            if (CollUtil.isNotEmpty(innerDeptIdList)) {
                // 3.3、行政组织父子级重新封装id集合
                for (Long id : innerDeptIdList) {
                    if (deptTreeIds.contains(id)) {
                        // 3.4、虚拟部门对应行政部门id
                        deptIdList.add(innerDeptMap.get(id));
                    }
                }
            }
        }
        // 4、部门id列表去重
        deptIdList = deptIdList.stream().distinct().collect(Collectors.toList());
        dto.setDeptIds(deptIdList);
        return dto;
    }

}




