package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 会计体系
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("会计体系")
@TableName("accounting_system")
@EqualsAndHashCode(callSuper = true)
public class AccountingSystem extends Model<AccountingSystem> {
    /**
    * id
    */
    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 体系编码
    */
    @ApiModelProperty("体系编码")
    private String systemCode;

    /**
    * 体系名称
    */
    @ApiModelProperty("体系名称")
    private String systemName;

    /**
    * 账套类型
    */
    @ApiModelProperty("账套类型")
    private String accountingType;

    /**
    * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
    */
    @ApiModelProperty("停用状态")
    private Integer enableStatus;

    /**
    * 体系备注
    */
    @ApiModelProperty("体系备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String systemRemarks;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}