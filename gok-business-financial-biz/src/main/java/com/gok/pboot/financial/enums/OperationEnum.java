package com.gok.pboot.financial.enums;

import lombok.Getter;

/**
 * 操作内容 Enum
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
public enum OperationEnum {

    /**
     * 导出数据
     */
    EXPORT("导出数据"),

    /**
     * 同步数据
     */
    REFRESH("同步数据"),

    /**
     * 导入数据
     */
    IMPORT("导入数据"),

    /**
     * 新增回款
     */
    ADD_PAYMENT("新增回款"),

    /**
     * 编辑回款
     */
    EDIT_PAYMENT("编辑回款"),

    /**
     * 锁定回款跟踪表
     */
    LOCK_PAYMENT("锁定回款跟踪表"),

    /**
     * 取消锁定回款跟踪表
     */
    UNLOCK_PAYMENT("取消锁定回款跟踪表"),

    /**
     * 认领回款
     */
    CLAIM_PAYMENT("认领回款"),

    /**
     * 重新认领回款
     */
    AGAIN_CLAIM_PAYMENT("重新认领回款"),

    /**
     * 取消认领回款
     */
    UN_CLAIM_PAYMENT("取消认领回款"),

    /**
     * 删除回款
     */
    DELETE_PAYMENT("删除回款"),

    /**
     * 推送凭证
     */
    PUSH("推送凭证"),

    /**
     * 汇总回款
     */
    SUMMARY_PAYMENT("汇总回款"),

    /**
     * 取消汇总回款
     */
    UN_SUMMARY_PAYMENT("取消汇总回款"),

    /**
     * 关联发票
     */
    RELATION_INVOICE("关联发票"),

    /**
     * 修改备注
     */
    EDIT_RECORDS("修改备注"),

    /**
     * 新增回款
     */
    ADD_PAYMENT_METHOD("新增回款"),

    /**
     * 删除回款
     */
    DELETE_PAYMENT_METHOD("删除回款"),

    /**
     * 新增收款方式
     */
    ADD_PAYMENT2_METHOD("新增收款方式"),

    /**
     * 修改收款方式
     */
    EDIT_PAYMENT2_METHOD("修改收款方式"),

    /**
     * 删除收款方式
     */
    DELETE_PAYMENT2_METHOD("删除收款方式"),

    /**
     * 费率变更
     */
    EDIT_EXCHANGE_RATE("费率变更"),

    /**
     * 状态变更
     */
    EDIT_STATUS("状态变更"),

    /**
     * 关系变更
     */
    EDIT_RELATION("关系变更"),

    /**
     * 新增指标科目关系
     */
    ADD_INDICATOR_ACCOUNT_RELATIONSHIP("新增指标科目关系"),

    /**
     * 修改指标科目关系
     */
    EDIT_INDICATOR_ACCOUNT_RELATIONSHIP("修改指标科目关系"),

    /**
     * 删除指标科目关系
     */
    DELETE_INDICATOR_ACCOUNT_RELATIONSHIP("删除指标科目关系"),

    /**
     * 新增会计账套
     */
    ADD_ACCOUNTING_SET("新增会计账套"),

    /**
     * 删除会计账套
     */
    DELETE_ACCOUNTING_SET("删除会计账套"),

    /**
     * 更新账号密码
     */
    UPDATE_ACCOUNT_PASSWORD("更新账号密码"),

    /**
     * 新增会计体系
     */
    ADD_ACCOUNTING_SYSTEM("新增会计体系"),

    /**
     * 删除会计体系
     */
    DELETE_ACCOUNTING_SYSTEM("删除会计体系"),
    /**
     * 新增科目类型
     */
    ADD_ACCOUNT_TYPE("新增科目类型"),

    /**
     * 编辑科目类型
     */
    EDIT_ACCOUNT_TYPE("编辑科目类型"),

    /**
     * 删除科目类型
     */
    DELETE_ACCOUNT_TYPE("删除科目类型"),
    /**
     * 新增会计科目
     */
    ADD_ACCOUNTING_ACCOUNT("新增会计科目"),

    /**
     * 编辑会计科目
     */
    EDIT_ACCOUNTING_ACCOUNT("编辑会计科目"),

    /**
     * 删除会计科目
     */
    DELETE_ACCOUNTING_ACCOUNT("删除会计科目")
    ;

    private final String name;

    OperationEnum(String name) {
        this.name = name;
    }
}
