package com.gok.pboot.financial.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.VoucherWord;
import com.gok.pboot.financial.db.mapper.VoucherWordMapper;
import com.gok.pboot.financial.dto.VoucherWordDTO;
import com.gok.pboot.financial.dto.VoucherWordFindDTO;
import com.gok.pboot.financial.service.IVoucherWordService;
import com.gok.pboot.financial.vo.VoucherWordVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 凭证字业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VoucherWordServiceImpl extends ServiceImpl<VoucherWordMapper, VoucherWord>
        implements IVoucherWordService {

    private final VoucherWordMapper voucherWordMapper;

    @Override
    public Page<VoucherWordVO> findPage(VoucherWordFindDTO dto) {
        Page<VoucherWord> page = new Page<>(dto.getCurrent(), dto.getSize());
        return voucherWordMapper.queryPage(page,dto);
    }

    @Override
    public R addOrEdit(VoucherWordDTO dto) {
        List<VoucherWord> voucherWords = voucherWordMapper.selectByWord(dto.getVoucherWord());
        //插入或更新
        if(Optional.ofNullable(dto.getId()).isPresent() && dto.getId()> NumberUtils.LONG_ZERO){
            //更新
            if(CollectionUtils.isNotEmpty(voucherWords) && !dto.getId().equals(voucherWords.get(NumberUtils.INTEGER_ZERO).getId())){
                return R.failed("编号已存在");
            }
            VoucherWord voucherWord = voucherWordMapper.selectById(dto.getId());
            if(!Optional.ofNullable(voucherWord).isPresent()){
                return R.failed("当前数据不存在");
            }
            voucherWord.setVoucherWord(dto.getVoucherWord());
            voucherWord.setPrintTemplate(dto.getPrintTemplate());
            voucherWord.setSetDefault(dto.getSetDefault());
            voucherWordMapper.updateById(voucherWord);
            //当前设置为默认时需要处理只有一条默认
            if(NumberUtils.INTEGER_ONE.equals(dto.getSetDefault())){
                setDefault(voucherWord.getId());
            }
            return info(voucherWord.getId());
        }
        //新增
        if(CollectionUtils.isNotEmpty(voucherWords)){
            return R.failed("凭证字已存在");
        }
        //插入
        VoucherWord voucherWord = new VoucherWord();
        voucherWord.setVoucherWord(dto.getVoucherWord());
        voucherWord.setPrintTemplate(dto.getPrintTemplate());
        voucherWord.setSetDefault(dto.getSetDefault());
        voucherWordMapper.insert(voucherWord);
        //当前设置为默认时需要处理只有一条默认
        if(NumberUtils.INTEGER_ONE.equals(dto.getSetDefault())){
            setDefault(voucherWord.getId());
        }
        return info(voucherWord.getId());
    }

    @Override
    public R<VoucherWordVO> info(Long id) {
        VoucherWord voucherWord = voucherWordMapper.selectById(id);
        if(!Optional.ofNullable(voucherWord).isPresent()){
            return R.failed("查询不到数据");
        }
        return R.ok(VoucherWordVO.builder()
                .id(voucherWord.getId())
                .voucherWord(voucherWord.getVoucherWord())
                .printTemplate(voucherWord.getPrintTemplate())
                        .setDefault(voucherWord.getSetDefault())
                .build());
    }

    @Override
    public R del(List<Long> idList) {
        List<VoucherWord> list = voucherWordMapper.selectBatchIds(idList);
        if(CollectionUtils.isEmpty(list)){
            return R.failed("id集合查询不到凭证字信息");
        }else{
            VoucherWord voucherWord = list.stream().filter(e -> NumberUtils.INTEGER_ONE.equals(e.getSetDefault())).findFirst().orElse(null);
            if(Optional.ofNullable(voucherWord).isPresent()){
                return R.failed("默认凭证字不允许删除，请重新选择！");
            }
        }
        voucherWordMapper.deleteBatchIds(idList);
        return R.ok("删除成功!");
    }


    @Override
    public R setDefault(Long id) {
        //全部设置为0
        voucherWordMapper.noDefaultAll();
        //当前数据查询出来设置为1
        VoucherWord voucherWord = voucherWordMapper.selectById(id);
        voucherWord.setSetDefault(NumberUtils.INTEGER_ONE);
        voucherWordMapper.updateById(voucherWord);
        return R.ok("设置默认完成");
    }
}
