package com.gok.pboot.financial.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.module.excel.api.handler.SelectedSheetWriteHandler;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.config.BudgetImportInfoEventListener;
import com.gok.pboot.financial.dto.SakesReceiptImportBudgetMarkingDTO;
import com.gok.pboot.financial.dto.SakesReceiptImportExcelDTO;
import com.gok.pboot.financial.dto.SalesReceiptDTO;
import com.gok.pboot.financial.dto.SalesReceiptPaymentTaskDTO;
import com.gok.pboot.financial.enums.PaymentStatusEnum;
import com.gok.pboot.financial.enums.ProjectStatusEnum;
import com.gok.pboot.financial.enums.WarningLevelEnum;
import com.gok.pboot.financial.service.ISalesReceiptCollectionRecordsService;
import com.gok.pboot.financial.service.ISalesReceiptService;
import com.gok.pboot.financial.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.CharEncoding;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售收款计划
 *
 * <AUTHOR>
 * @menu 销售收款计划
 * @since 2023-09-27
 */
@RestController
@RequestMapping("/sales-receipt")
@RequiredArgsConstructor
@Api(tags = "销售收款计划")
public class SalesReceiptController {

    private final ISalesReceiptService service;

    private final ISalesReceiptCollectionRecordsService recordsService;

    private final DBApi dbApi;
    @Resource
    private RemoteBcpDictService remoteBcpDictService;

    /**
     * 分页条件查询
     *
     * @param dto 查询条件
     * @return R
     */
    @PostMapping("/page")
    @ApiOperation(value = "分页条件查询", notes = "分页条件查询")
    public R<StatisticsPage<SalesReceiptVO>> findPage(@RequestBody SalesReceiptDTO dto) {
        return R.ok(service.findPage(dto));
    }

    /**
     * 项款名称下拉框
     *
     * @param clientId 应用id
     * @param menuCode 菜单code
     * @return R
     */
    @GetMapping("/currentPaymentNameBox")
    @ApiOperation(value = "项款名称的下拉框")
    public R<List<String>> currentPaymentNameBox(@RequestParam("clientId") Long clientId,
                                                 @RequestParam("menuCode") String menuCode) {
        return R.ok(service.currentPaymentNameBox(clientId, menuCode));
    }

    /**
     * 项目状态下拉框
     *
     * @return R
     */
    @GetMapping("/projectStatusBox")
    @ApiOperation(value = "项目状态的下拉框")
    public R<List<String>> projectStatusBox() {
        return R.ok(service.projectStatusBox());
    }

    /**
     * 导出excel（异步导出）
     *
     * @param dto dto
     * @return {@link List}
     */
    @PostMapping("/export")
    @ResponseExcel(async = true, name = "销售回款跟踪", functionEnum = FunctionEnum.FINANCIAL_SALES_RECEIPT_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @ApiOperation(value = "导出选中的数据", notes = "导出选中数据")
    public List<SalesReceiptVO> export(@RequestBody SalesReceiptDTO dto) {
        return service.export(dto);
    }

    @PostMapping("pms/export")
    @ResponseExcel(name = "销售回款跟踪", dynamicHeader = true, mergeSameColumn = true)
    @ApiOperation(value = "导出选中的数据", notes = "导出选中数据")
    public List<SalesReceiptVO> pmsExport(@RequestBody SalesReceiptDTO dto) {
        return service.export(dto);
    }

    /**
     * 销售收款任务超期提醒
     *
     * @return {@link R<Boolean>}
     */
    @Inner(false)
    @GetMapping("pushMessage")
    @ApiOperation(value = "销售收款任务超期提醒", notes = "销售收款任务超期提醒")
    public R<Boolean> pushMessage() {
        return R.ok(service.pushMessage());
    }

    /**
     * 催款记录
     *
     * @param salesReceiptId 销售收款id
     * @return {@link R}<{@link List}<{@link SalesReceiptCollectionRecordsVO}>>
     */
    @GetMapping("/recordsList/{salesReceiptId}")
    @ApiOperation(value = "催款记录", notes = "催款记录")
    public R<List<SalesReceiptCollectionRecordsVO>> recordsList(@PathVariable("salesReceiptId") Long salesReceiptId) {
        return R.ok(recordsService.recordsList(salesReceiptId));
    }

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@link R}
     */
    @PostMapping("/saveRecords")
    @ApiOperation(value = "新增记录", notes = "新增记录")
    public R<String> saveRecords(@RequestBody @Valid SalesReceiptCollectionRecordsDTO dto) {
        return recordsService.saveRecords(dto)
                ? R.ok("新增记录成功") : R.failed("新增记录失败");
    }
    /**
     * 页面列表查询字段
     *
     * @return {@link R}<{@link SalesQueryFormVO}>
     */
    @GetMapping("/dictItemList")
    @ApiOperation(value = "页面列表查询字段", notes = "页面列表查询字段")
    public R<SalesQueryFormVO> dictItemList() {
        SalesQueryFormVO.SalesQueryFormVOBuilder builder = SalesQueryFormVO.builder();
        // 1、项目状态
        List<ProjectStatusVO> projectStatusVoList = new ArrayList<>();
        for (ProjectStatusEnum p: ProjectStatusEnum.values()) {
            projectStatusVoList.add(new ProjectStatusVO(p.getValue(), p.getName()));
        }
        builder.projectStatusVoList(projectStatusVoList);
        // 2、预警等级
        List<WarningLevelVO> warningLevelVoList = new ArrayList<>();
        for (WarningLevelEnum w: WarningLevelEnum.values()) {
            warningLevelVoList.add(new WarningLevelVO(w.getValue(), w.getName()));
        }
        builder.warningLevelVoList(warningLevelVoList);
        // 3、账款类型
        List<PaymentVO> paymentVoList = new ArrayList<>();
        List<DictKvVo> dictKvVoList = remoteBcpDictService.getDictKvList("账款类型(A表)").getData();
        for (DictKvVo p : dictKvVoList) {
            paymentVoList.add(new PaymentVO(p.getValue(), p.getName()));
        }
        builder.paymentVoList(paymentVoList);
        // 4、归属主体
        List<AttributableSubjectVO> attributableSubjectVoList = new ArrayList<>();
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        if (CollectionUtil.isNotEmpty(oaDictVOS)){
            attributableSubjectVoList = oaDictVOS.stream().sorted(Comparator.comparing(OaDictVO::getDisorder))
                    .map(o -> new AttributableSubjectVO(String.valueOf(o.getDisorder()),o.getName()))
                    .collect(Collectors.toList());
        }
        builder.attributableSubjectVoList(attributableSubjectVoList);
        // 5、款项状态
        List<PaymentVO> paymentStatusVoList = new ArrayList<>();
        for (PaymentStatusEnum a: PaymentStatusEnum.values()) {
            paymentStatusVoList.add(new PaymentVO(a.getValue(), a.getName()));
        }
        builder.paymentStatusVoList(paymentStatusVoList);
        return R.ok(builder.build());
    }

    /**
     * 保存回款任务详情
     *
     * @param dto {@link SalesReceiptPaymentTaskDTO}
     * @return {@link R}
     */
    @PostMapping("/savePaymentTask")
    @ApiOperation(value = "保存回款任务详情", notes = "保存回款任务详情")
    public R<String> savePaymentTask(@RequestBody @Valid SalesReceiptPaymentTaskDTO dto) {
        return service.savePaymentTask(dto);
    }

    /**
     * 回款任务详情
     *
     * @param salesReceiptId 销售收款id
     * @return {@link R}<{@link List}<{@link SalesReceiptCollectionRecordsVO}>>
     */
    @GetMapping("/getPaymentTask/{salesReceiptId}")
    @ApiOperation(value = "回款任务详情", notes = "回款任务详情")
    public R<SalesReceiptPaymentTaskVO> getPaymentTask(@PathVariable("salesReceiptId") Long salesReceiptId) {
        return R.ok(service.getPaymentTask(salesReceiptId));
    }

    /**
     * 销售收款任务跟进提醒
     * @return
     */
    @Inner(value = false)
    @GetMapping("/task/reminder")
    public R<String> deadLineNotify() {
        service.taskReminder();
        return R.ok("任务推送成功");
    }

    /**
     * 上下页ids列表
     *
     * @param dto 查询条件
     * @return R
     */
    @PostMapping("/findIds")
    @ApiOperation(value = "上下页ids列表", notes = "上下页ids列表")
    public R<List<Long>> findIds(@RequestBody SalesReceiptDTO dto) {
        return R.ok(service.findIds(dto));
    }


    /**
     * 销售收款任务更新提醒
     * 【系统时间】-【里程碑达成时间】大于等于7天，且【款项状态】等于待回款/部分和回款【任务状态】等于未更新；
     * @return {@link R<Boolean>}
     */
    @Inner(false)
    @GetMapping("task/update/reminder")
    @ApiOperation(value = "销售收款任务更新提醒", notes = "销售收款任务更新提醒")
    public R<Boolean> taskUpdateReminder() {
        return R.ok(service.taskUpdateReminder());
    }

    /**
     * 销售收款任务未更新提醒
     * 【系统时间】-【里程碑达成时间】等于3天，且【款项状态】等于待回款/部分回款【任务状态】等于未维护；
     * @return {@link R<Boolean>}
     */
    @Inner(false)
    @GetMapping("task/not/updated/reminder")
    @ApiOperation(value = "销售收款任务未更新提醒", notes = "销售收款任务未更新提醒")
    public R<Boolean> TaskNotUpdatedReminder() {
        return R.ok(service.TaskNotUpdatedReminder());
    }

    /**
     * 销售计划任务导入
     * @param sakesReceiptImportExcelDTOList
     * @return
     */
    @Inner(value = false)
    @PostMapping("/import")
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R<Boolean> importExcel(@RequestExcel List<SakesReceiptImportExcelDTO> sakesReceiptImportExcelDTOList) {
        return R.ok(service.importExcel(sakesReceiptImportExcelDTOList));
    }

    /**
     * 导入预算标记
     * @param list {@link List}<{@link SakesReceiptImportBudgetMarkingDTO}>
     * @return {@link R}<{@link String>
     */
    @PostMapping("/importBudgetMarking")
    @ApiOperation(value = "销售收款计划导入预算标记Excel", notes = "销售收款计划导入预算标记Excel")
    public R<String> importBudgetMarking(@RequestExcel(readListener = BudgetImportInfoEventListener.class) List<SakesReceiptImportBudgetMarkingDTO> list) {
        return service.importBudgetMarking(list);
    }

    /**
     * 销售收款计划导入预算标记模板
     * @param response
     * @throws IOException
     */
    @GetMapping("/importBudgetMarking-model")
    @ApiOperation(value = "销售收款计划导入预算标记模板", notes = "销售收款计划导入预算标记模板")
    public void excelModel(HttpServletResponse response) throws IOException {
        final String fileName = "销售收款计划导入预算标记导入模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharEncoding.UTF_8) + ExcelTypeEnum.XLSX.getValue());
        int columnSize = 8;
        List<String> emptyList = new ArrayList<>();
        for (int i = 0; i < columnSize; i++) {
            emptyList.add(StringPool.EMPTY);
        }
        EasyExcelFactory.write(response.getOutputStream())
                .head(SakesReceiptImportBudgetMarkingDTO.class)
                .autoCloseStream(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SelectedSheetWriteHandler(SakesReceiptImportBudgetMarkingDTO.class))
                .sheet("导入数据").doWrite(Collections.singletonList(emptyList));
    }

    /**
     * 初始化客户付款审批流程
     */
    @Inner(value = false)
    @GetMapping("/init/payment/process")
    public void initPaymentProcess(){
        service.initPaymentProcess();

    }
}
