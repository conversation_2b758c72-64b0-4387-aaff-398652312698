package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.AccountType;
import com.gok.pboot.financial.dto.AccountTypePageDTO;
import com.gok.pboot.financial.vo.AccountTypeVO;
import com.gok.pboot.financial.vo.AccountTypePageVO;
import com.gok.pboot.financial.vo.AccountingSystemCorrelationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AccountTypeMapper extends BaseMapper<AccountType> {

    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    Page<AccountTypePageVO> findPage(Page<AccountType> page, @Param("query")  AccountTypePageDTO dto);

    /**
     * 查看详情
     * @param id
     * @return
     */
    AccountTypeVO selById(@Param("id") Long id);

    /**
     * 删除
     * @param idList
     * @return
     */
    Boolean delByIds(@Param("idList") List<Long> idList);

    /**
     * 科目类型列表
     * @param idList
     * @return
     */
    List<AccountTypeVO> findList(@Param("idList") List<Long> idList);

    /**
     * 查询关联数量
     * @param idList
     * @return
     */
    List<AccountingSystemCorrelationVO> selCorrelationNum(@Param("idList") List<Long> idList);
}