package com.gok.pboot.financial.common;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.exceptions.ForestNetworkException;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.client.ForestClient;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.db.entity.ProjectPaymentClaim;
import com.gok.pboot.financial.dto.TPlusDeptDTO;
import com.gok.pboot.financial.dto.TPlusUserDTO;
import com.gok.pboot.financial.vo.*;
import com.google.protobuf.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * DBApi接入
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Slf4j
@Component
public class DBApi {

    private static final String DBAPI_TOKEN = "DBAPI:FINANCIAL:TOKEN";

    private static final String EXCEPTION_DESC = "DBAPI异常,获取不到token信息";

    private static final String getOaAccountInfoByUserId = "/api/getOaAccountInfoByUserId";
    private static final String getOaAccountByIdUrl = "/api/getOaAccountById";


    /**
     * DbApi获取token的url地址
     */
    @Value("${dbapi.url}")
    private String url;

    /**
     * DbApi经营分析应用的appId
     */
    @Value("${dbapi.appid}")
    private String appId;

    /**
     * DbApi经营分析应用的secret
     */
    @Value("${dbapi.secret}")
    private String secret;

    /**
     * 是否获取成功的字段
     */
    public static final String SUCCESS = "success";
    private static final String DPAPI_EXCEPTION_MSG = "DBAPI异常,获取不到明细内容";
    private static final String DPAPI_EXCEPTION = "请求DBAPI接口异常";
    /**
     * 获取到的字段
     */
    public static final String DATA = "data";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ForestClient forestClient;

    @PostConstruct
    private void removeToken() {
        redisTemplate.delete(DBAPI_TOKEN);
    }

    /**
     * 同步获取缓存token
     *
     * @return token
     */
    public synchronized String readToken() {
        // 预发布环境url判断 获取token需变换
        final String tokenStr = "token";
        String preUrl = url;
        if (url.contains("/api")) {
            preUrl = url.replace("/api", "");
        }
        Object token = redisTemplate.opsForValue().get(DBAPI_TOKEN);
        if (!Optional.ofNullable(token).isPresent()) {
            // 促发forest进行获取token并写入
            JSONObject jsonObject = forestClient.getDbApiToken(preUrl, appId, secret);
            token = jsonObject.get(tokenStr);
            if (!Optional.ofNullable(token).isPresent()) {
                throw new BusinessException("DBApi异常,获取不到token信息");
            }
            redisTemplate.opsForValue().set(DBAPI_TOKEN, token, NumberUtils.INTEGER_TWO, TimeUnit.HOURS);

            return (String) jsonObject.get(tokenStr);
        }
        try {
            forestClient.projectDict(url, (String) token, "11930");
        } catch (Exception e) {
            log.info("token失效");
            JSONObject jsonObject = forestClient.getDbApiToken(preUrl, appId, secret);
            token = jsonObject.get(tokenStr);
            redisTemplate.opsForValue().set(DBAPI_TOKEN, token, NumberUtils.INTEGER_TWO, TimeUnit.HOURS);
        }

        return (String) token;
    }

    /**
     * 获取项目字典
     *
     * @param fieldId 字典值
     * @return {@link List}<{@link ProjectDictVO}>
     */
    public List<ProjectDictVO> projectDict(String fieldId) {
        JSONObject jsonObject = forestClient.projectDict(url, readToken(), fieldId);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectDictVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取OA字典
     *
     * @param id 字典值
     * @return {@link List}<{@link ProjectDictVO}>
     */
    public List<OaDictVO> projectDictNew(String id) {
        JSONObject jsonObject = forestClient.projectDictNew(url, readToken(), id);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(OaDictVO.class);
        }
        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取回款详情
     *
     * @param projectId  项目id
     * @param contractId 合同id
     * @return {@link List}<{@link PayBackDocumentVO}>
     */
    public List<PayBackDocumentVO> payBackDetails(String projectId, String contractId) {
        JSONObject jsonObject = forestClient.payBackDetails(url, readToken(), projectId, contractId);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(PayBackDocumentVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取发票详情
     *
     * @param projectId  项目id
     * @param contractId 合同id
     * @return {@link List}<{@link InvoicingDocumentVO}>
     */
    public List<InvoicingDocumentVO> invoicingDetails(String projectId, String contractId) {
        JSONObject jsonObject = forestClient.invoicingDetails(url, readToken(), projectId, contractId);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(InvoicingDocumentVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 凭证创建-获取帐套信息
     *
     * @return TPlusUserDto
     */
    public List<TPlusUserDTO> getTPlusUser() {
        JSONObject jsonObject = forestClient.getTPlusUser(url, readToken());
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(TPlusUserDTO.class);
        }
        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 凭证创建-获取默认部门
     *
     * @return TPlusUserDto
     */
    public List<TPlusDeptDTO> getTPlusDept() {
        JSONObject jsonObject = forestClient.getTPlusDept(url, readToken());
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(TPlusDeptDTO.class);
        }
        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取科目编码名称
     *
     * @return {@link List}<{@link SubjectVO}>
     */
    public List<SubjectVO> getSubject() {
        JSONObject jsonObject = forestClient.getSubject(url, readToken());
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(SubjectVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 客户台帐
     *
     * @param customerName 客户名称
     * @return {@link List}<{@link CustomerAccountVO}>
     */
    public List<CustomerAccountVO> getCustomerAccount(String customerName) {
        JSONObject jsonObject = forestClient.customerAccount(url, readToken(), customerName);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(CustomerAccountVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 银行台帐
     *
     * @param name 银行名称
     * @return {@link List}<{@link BackAccountVO}>
     */
    public List<BackAccountVO> getBankAccount(String name) {
        JSONObject jsonObject = forestClient.bankAccount(url, readToken(), name);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(BackAccountVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param id id
     * @return {@link List}<{@link ContractPaymentVO}>
     */
    public List<ContractPaymentVO> getContractPayment(String id) {
        JSONObject jsonObject = forestClient.contractPayment(url, readToken(), id);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ContractPaymentVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 根据合同名称搜索归属合同
     *
     * @param contractName 合同名称
     * @return {@link List}<{@link ContractPaymentVO}>
     */
    public List<ContractInfoVO> getContractName(String contractName) {
        JSONObject jsonObject = forestClient.contractName(url, readToken(), contractName);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ContractInfoVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 归属合同收款明细
     *
     * @param contractNumber 合同编码列表
     * @return {@link List}<{@link ContractPaymentVO}>
     */
    public List<ContractPaymentVO> getContractPaymentByNumber(List<String> contractNumber) {
        JSONObject jsonObject = forestClient.contractPaymentByNumber(url, readToken(), contractNumber);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ContractPaymentVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 项目台帐
     *
     * @param projectName 项目名称
     * @return {@link List}<{@link ProjectAccountVO}>
     */
    public List<ProjectAccountVO> getProjectAccount(String projectName) {
        JSONObject jsonObject = forestClient.projectAccount(url, readToken(), projectName);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectAccountVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 项目台帐
     *
     * @param projectName 项目名称集合
     * @return {@link List}<{@link ProjectAccountVO}>
     */
    public List<ProjectAccountVO> getProjectAccountByName(List<String> projectName) {
        JSONObject jsonObject = forestClient.projectAccountByName(url, readToken(), projectName);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectAccountVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 本月预算回款
     *
     * @param customerName 合同名称
     * @param yearMonth    预算年月
     * @return {@link List}<{@link ProjectAccountVO}>
     */
    public List<PaymentBudgetVO> getPaymentBudget(String customerName, String yearMonth) {
        JSONObject jsonObject = forestClient.paymentBudget(url, readToken(), customerName, yearMonth);
        if ((boolean) jsonObject.get(SUCCESS)) {
            List list = (List) jsonObject.get(DATA);
            if (list.isEmpty()) {
                return list;
            }

            return jsonObject.getJSONArray(DATA).toJavaList(PaymentBudgetVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 本月预算回款
     *
     * @param customerName 合同名称
     * @param yearMonth    预算年月
     * @return {@link List}<{@link ProjectAccountVO}>
     */
    public List<PaymentBudgetVO> getPaymentBudgetList(List<String> customerName, List<String> yearMonth) {
        JSONObject jsonObject = forestClient.paymentBudgetList(url, readToken(), customerName, yearMonth);
        if ((boolean) jsonObject.get(SUCCESS)) {
            // 这里做泛型处理可能会导致转型报错 因此不做泛型处理
            List list = (List) jsonObject.get(DATA);
            if (list.isEmpty()) {
                return list;
            }

            return jsonObject.getJSONArray(DATA).toJavaList(PaymentBudgetVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 关联发票信息
     *
     * @param contractCode 合同编码
     * @return {@link List}<{@link ProjectRevenueLedgerInvoicingVO}>
     */
    public List<ProjectRevenueLedgerInvoicingVO> getRelationInvoicing(String contractCode) {
        JSONObject jsonObject = forestClient.getRelationInvoicing(url, readToken(), contractCode);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectRevenueLedgerInvoicingVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 凭证创建公司-拼接字典查询
     */
    public List<PaymentCompanyVO> getPaymentCompany() {
        JSONObject jsonObject = forestClient.paymentCompany(url, readToken());
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(PaymentCompanyVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取保证金台账
     */
    public List<MarginVO> getMarginVo(String flowNo) {
        JSONObject jsonObject = forestClient.getMarginVo(url, readToken(), flowNo);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(MarginVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取OA用户id
     */
    public String getOaAccountInfoByUserId(Long id) {
        JSONObject jsonObject = forestClient.getOaAccountById(url, getOaAccountInfoByUserId, readToken(), id);
        if ((Boolean) jsonObject.get(SUCCESS)) {
            List<OaAccountVO> result = jsonObject.getJSONArray("data").toJavaList(OaAccountVO.class);
            if (CollUtil.isNotEmpty(result)) {
                return result.get(0).getOaId().toString();
            }
        }
        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 获取OA用户信息
     */
    public OaAccountVO getOaAccountInfoVOByUserId(Long id) {
        OaAccountVO oaAccountVO = new OaAccountVO();
        try {
            JSONObject jsonObject = forestClient.getOaAccountById(url, getOaAccountByIdUrl, readToken(), id);
            if ((boolean) jsonObject.get(SUCCESS)) {
                List<OaAccountVO> data = jsonObject.getJSONArray("data").toJavaList(OaAccountVO.class);
                if (CollUtil.isNotEmpty(data)) {
                    oaAccountVO = data.get(0);
                }
            } else {
                throw new BusinessException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            log.error(DPAPI_EXCEPTION + e);
        }
        return oaAccountVO;
    }


    /**
     * 凭证创建-获取帐套信息
     *
     * @return TPlusUserDto
     */
    public List<ProjectPayment> selPayment() {
        JSONObject jsonObject = forestClient.selPayment(url, readToken());
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectPayment.class);
        }
        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 凭证创建-获取帐套信息
     *
     * @return TPlusUserDto
     */
    public List<ProjectPaymentClaim> selPaymentClaim() {
        JSONObject jsonObject = forestClient.selPaymentClaim(url, readToken());
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectPaymentClaim.class);
        }
        throw new BusinessException(EXCEPTION_DESC);
    }

    public List<ProjectCustomerVO> selCustomerId(Long userId) {
        JSONObject jsonObject = forestClient.selCustomerId(url, readToken(), userId);
        if ((boolean) jsonObject.get(SUCCESS)) {
            return jsonObject.getJSONArray(DATA).toJavaList(ProjectCustomerVO.class);
        }

        throw new BusinessException(EXCEPTION_DESC);
    }

    /**
     * 通过OA流程名称查询流程workflowId
     */
    public Integer getWorkflowIdByName(String workflowName) {
        Integer result = null;
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.getWorkflowIdByName(url, token, workflowName);
            if ((Boolean) jsonObject.get("success")) {
                JSONArray jsonArray = jsonObject.getJSONArray("data");
                result = CollUtil.isEmpty(jsonArray) ? null : ((JSONObject) jsonArray.get(0)).getInteger("ID");
            } else {
                throw new BusinessException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            removeToken();
            log.error(DPAPI_EXCEPTION + e);
        }
        if (result == null) {
            throw new BusinessException("OA未找到相关流程!");
        }

        return result;
    }

    /**
     * 更新销售收款计划
     */
    public void updateSalesReceipt(List<Long> ids) {
        String token = readToken();
        try {
            JSONObject jsonObject = forestClient.updateSalesReceipt(url, token, ids);
            if (!Boolean.TRUE.equals(jsonObject.get(SUCCESS))) {
                throw new BusinessException(DPAPI_EXCEPTION_MSG);
            }
        } catch (ForestNetworkException e) {
            log.error(DPAPI_EXCEPTION + e);
        }
    }
}
