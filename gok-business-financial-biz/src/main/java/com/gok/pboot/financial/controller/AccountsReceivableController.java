package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.AccountsReceivable;
import com.gok.pboot.financial.dto.AccountsReceivableDTO;
import com.gok.pboot.financial.service.IAccountsReceivableService;
import com.gok.pboot.financial.vo.AccountsReceivableVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 应收账款台账
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@RestController
@RequestMapping("/accounts-receivable")
@RequiredArgsConstructor
@Api(tags = "应收账款台账")
public class AccountsReceivableController {

    private final IAccountsReceivableService accountsReceivableService;

    /**
     * 模糊查询带分页
     *
     * @param accountsReceivableDto 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link AccountsReceivableVO}>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('standingBook')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<AccountsReceivableVO>> findPage(@RequestBody AccountsReceivableDTO accountsReceivableDto) {
        Page<AccountsReceivable> page = new Page<>(accountsReceivableDto.getCurrent(), accountsReceivableDto.getSize());
        return R.ok(accountsReceivableService.findPage(page, accountsReceivableDto));
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param accountsReceivableDTO {@link AccountsReceivableDTO}
     * @return {@link List}<{@link AccountsReceivableVO}>
     */
    @ResponseExcel(async = true, name = "应收账款台账", functionEnum = FunctionEnum.FINANCIAL_ACC_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('standingBook/export')")
    public List<AccountsReceivableVO> export(@RequestBody AccountsReceivableDTO accountsReceivableDTO) {
        return accountsReceivableService.export(accountsReceivableDTO);
    }

    /**
     * 手动同步刷新数据
     *
     * @return {@link R}
     */
    @GetMapping("/refresh")
    @PreAuthorize("@pms.hasPermission('standingBook/sync','projectLaborCostDetailed/sync','wagesSummary/sync','project-revenue/sync')")
    @ApiOperation(value = "手动同步刷新数据", notes = "手动同步刷新数据")
    public R<String> refresh() {
        return accountsReceivableService.refresh();
    }

}
