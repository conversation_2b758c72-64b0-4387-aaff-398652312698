package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目回款跟踪
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_payment")
@ApiModel("项目回款")
public class ProjectPayment extends Model<ProjectPayment> {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 单据编号
     */
    @ApiModelProperty("单据编号")
    private String documentNumber;

    /**
     * 收款公司
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ApiModelProperty("收款公司")
    private Integer paymentCompany;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerName;

    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String enterpriseName;

    /**
     * 收款日期
     */
    @ApiModelProperty("收款日期")
    private String paymentDate;

    /**
     * 收款金额
     */
    @ApiModelProperty("收款金额")
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    @ApiModelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    @ApiModelProperty("凭证编号")
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    @ApiModelProperty("预算回款金额")
    private BigDecimal budgetCollectionAmount;

    /**
     * 回款备注
     */
    @ApiModelProperty("回款备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String paymentNote;

    /**
     * 银行账号
     */
    @ApiModelProperty("银行账号")
    private String bankAccount;

    /**
     * 认领状态（0已认领，1待认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ApiModelProperty("认领状态")
    private Integer claimStatus;

    /**
     * 锁定状态（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ApiModelProperty("锁定状态")
    private Integer lockStatus;

    /**
     * 自动锁定（0自动，1取消）
     * {@link com.gok.pboot.financial.enums.AutoLockEnum}
     */
    @ApiModelProperty("自动锁定")
    private Integer autoLock;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ApiModelProperty("推送状态(0已推送,1待推送,2推送失败)")
    private Integer pushStatus;

    /**
     * 备案人id
     */
    @ApiModelProperty("备案人id")
    private Long recordManId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long creatorId;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人")
    private String creatorName;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

    /**
     * 项目回款认领
     */
    @TableField(exist = false)
    private ProjectPaymentClaim projectPaymentClaim;

    /**
     * 教育回款id列表
     */
    @ApiModelProperty("教育回款id列表")
    private String educationIds;

    /**
     * 数据是否来源OA
     */
    @ApiModelProperty("数据是否来源OA")
    private Integer sourceOa;
}