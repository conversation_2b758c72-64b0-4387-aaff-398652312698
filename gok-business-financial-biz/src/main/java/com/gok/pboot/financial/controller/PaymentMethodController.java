package com.gok.pboot.financial.controller;

import cn.hutool.core.bean.BeanUtil;
import com.gok.base.admin.vo.PaymentPlatformVO;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.enums.YesOrNoEnum;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.dto.PaymentMethodDTO;
import com.gok.pboot.financial.enums.EnableStatusEnum;
import com.gok.pboot.financial.service.IPaymentMethodService;
import com.gok.pboot.financial.vo.PaymentMethodVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;

/**
 * 收款方式
 *
 * <AUTHOR>
 * @menu 收款方式
 * @since 2023-09-27
 */
@RestController
@RequestMapping("/payment-method")
@RequiredArgsConstructor
@Api(tags = "收款方式")
public class PaymentMethodController {

    private final IPaymentMethodService service;

    /**
     * 分页查询
     *
     * @param size    每页个数
     * @param current 页码
     * @return {@link R}<{@link Page}<{@link PaymentPlatformVO}>>
     */
    @GetMapping("/page")
    @PreAuthorize("@pms.hasPermission('paymentMethod')")
    @ApiOperation(value = "分页查询", notes = "分页查询")
    public R<Page<PaymentMethodVO>> findPage(Long size, Long current) {
        Page<PaymentMethodVO> page = new Page<>(current, size);
        return R.ok(service.findPage(page));
    }

    /**
     * 新增编辑收款方式
     *
     * @param dto 收款方式Dto
     * @return R
     */
    @PostMapping("/addOrEdit")
    @PreAuthorize("@pms.hasPermission('paymentMethod/add')")
    @ApiOperation(value = "新增编辑收款方式", notes = "新增编辑收款方式")
    public R<String> addOrEdit(@RequestBody PaymentMethodDTO dto) {
        return service.addOrEdit(dto);
    }

    /**
     * 根据ids删除收款方式
     *
     * @param dto id列表
     * @return R
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@pms.hasPermission('paymentMethod/del')")
    @ApiOperation(value = "根据id删除", notes = "单个id删单个，多个id删多个")
    public R<String> delete(@RequestBody PaymentMethodDTO dto) {
        return service.delete(dto.getIds());
    }

    /**
     * 收款平台列表
     *
     * @return {@link R}<{@link List}<{@link PaymentPlatformVO}>>
     */
    @Inner(value = false)
    @GetMapping("/list")
    @ApiOperation(value = "收款平台列表", notes = "收款平台列表")
    public R<List<PaymentPlatformVO>> list() {
        List<PaymentPlatformVO> paymentPlatformVOList = new ArrayList<>();
        service.list().forEach(p -> {
            if (YesOrNoEnum.NO.getVal().toString().equals(p.getDelFlag())
                    && EnableStatusEnum.YES.getValue().equals(p.getEnableStatus())){
                paymentPlatformVOList.add(BeanUtil.copyProperties(p, PaymentPlatformVO.class));
            }
        });
        return R.ok(paymentPlatformVOList);
    }

}
