package com.gok.pboot.financial.db.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销售收款计划
 * @TableName sales_receipt
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value ="sales_receipt")
@ApiModel("销售收款计划")
public class SalesReceipt extends Model<SalesReceipt> {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty(value = "项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    /**
     * 合同id
     */
    @ApiModelProperty(value = "合同id")
    private Long contractId;

    /**
     * 合同名称
     */
    @ApiModelProperty(value = "合同名称")
    private String contractName;

    /**
     * 合同编码
     */
    @ApiModelProperty(value = "合同编码")
    private String contractCode;

    /**
     * 合同签订日期
     */
    @ApiModelProperty(value = "合同签订日期")
    private String signingDate;

    /**
     * 预计收款日期
     */
    @ApiModelProperty(value = "预计收款日期")
    private String expectedDate;

    /**
     * 实际回款金额
     * 原（实际款项金额）
     */
    @ApiModelProperty(value = "实际款项金额")
    private String actualPaymentAmount;

    /**
     * 款项差额
     */
    @ApiModelProperty(value = "款项差额")
    private String paymentDifference;


    /**
     * 实际回款日期
     * 原（实际款项日期）
     */
    @ApiModelProperty(value = "实际回款日期")
    private String paymentDate;

    /**
     * 验收日期
     */
    @ApiModelProperty(value = "验收日期")
    private String acceptanceDate;

    /**
     * 合同金额
     */
    @ApiModelProperty(value = "合同金额")
    private BigDecimal contractMoney;

    /**
     * 累计收款金额（含税）
     */
    @ApiModelProperty(value = "累计收款金额（含税）")
    private BigDecimal accumulatedAmount;

    /**
     * 累计收款比例
     */
    @ApiModelProperty(value = "累计收款比例")
    private String collectionRatio;

    /**
     * 预估款项金额_含税
     */
    @ApiModelProperty("预估款项金额_含税")
    private String estimateCurrentPaymentMoney;

    /**
     * 计划款项金额_含税
     * （原款项金额）
     */
    @ApiModelProperty(value = "计划款项金额_含税")
    private BigDecimal currentPaymentMoney;

    /**
     * 款项状态
     */
    @ApiModelProperty(value = "款项状态")
    private String paymentStatus;

    /**
     * 款项ID
     */
    @ApiModelProperty("款项ID")
    private Long paymentId;

    /**
     * 款项名称
     */
    @ApiModelProperty(value = "款项名称")
    private String currentPaymentName;

    /**
     * 款项比例
     */
    @ApiModelProperty(value = "款项比例")
    private String paymentProportion;

    /**
     * 逾期天数
     * （原收款滞后天数）
     */
    @ApiModelProperty(value = "逾期天数")
    private String collectionDelayDays;

    /**
     * 预警等级
     */
    @ApiModelProperty(value = "预警等级")
    private String warningLevel;

    /**
     * 款项条件
     */
    @ApiModelProperty(value = "款项条件")
    private String paymentCondition;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 客户经理
     * （原销售负责）
     */
    @ApiModelProperty(value = "客户经理ID")
    private Long salesmanUserId;

    /**
     * 客户经理
     * （原销售负责）
     */
    @ApiModelProperty(value = "客户经理")
    private String salesmanUserName;

    /**
     * 项目经理人员ID
     */
    @ApiModelProperty(value = "项目经理人员ID")
    private Long managerUserId;

    /**
     * 项目经理人员姓名
     */
    @ApiModelProperty(value = "项目经理人员姓名")
    private String managerUserName;

    /**
     * 项目区域主管ID
     */
    @ApiModelProperty(value = "项目区域主管ID")
    private Long headUserId;

    /**
     * 项目区域主管姓名
     */
    @ApiModelProperty(value = "项目区域主管姓名")
    private String headUserName;

    /**
     * 项目市场总监ID
     */
    @ApiModelProperty(value = "项目市场总监ID")
    private Long commissionerUserId;

    /**
     * 项目市场总监姓名
     */
    @ApiModelProperty(value = "项目市场总监姓名")
    private String commissionerUserName;

    /**
     * 归属主体/主体名称
     */
    @ApiModelProperty(value = "归属主体/主体名称")
    private String attributableSubject;

    /**
     * 一级部门
     */
    @ApiModelProperty(value = "一级部门")
    private Long firstDepartmentId;

    /**
     * 一级部门
     */
    @ApiModelProperty(value = "一级部门")
    private String firstDepartment;

    /**
     * 二级部门
     */
    @ApiModelProperty(value = "二级部门")
    private Long secondDepartmentId;

    /**
     * 二级部门
     */
    @ApiModelProperty(value = "二级部门")
    private String secondDepartment;

    /**
     * 里程碑
     */
    @ApiModelProperty("里程碑")
    private String milestones;

    /**
     * 预计完成日期
     * 原（里程碑计划日期）
     */
    @ApiModelProperty("预计完成日期")
    private LocalDate expectedCompleteDate;

    /**
     * 实际完成日期
     * 原（里程碑达成日期）
     */
    @ApiModelProperty("实际完成日期")
    private LocalDate actualCompleteDate;

    /**
     * 回款账期（天）
     */
    @ApiModelProperty("回款账期（天）")
    private Integer collectDays;

    /**
     * 目标回款日期
     */
    @ApiModelProperty("目标回款日期")
    private LocalDate targetPaymentDate;

    /**
     * 客户集
     */
    @ApiModelProperty("客户集")
    private String customerMarket;

    /**
     * 预计回款日期
     */
    @ApiModelProperty("预计回款日期")
    private LocalDate expectedPaymentDate;

    /**
     * 实际追款时长
     */
    @ApiModelProperty("实际追款时长")
    private Integer actualCollectionTime;

    /**
     * 客户付款窗口期
     */
    @ApiModelProperty("客户付款窗口期")
    private String customerPaymentWindowPeriod;

    /**
     * 交付方式
     */
    @ApiModelProperty(value = "交付方式")
    private String deliveryMethod;

    /**
     * 结算方式
     */
    @ApiModelProperty(value = "结算方式")
    private String paymentMethod;

    /**
     * 预算标识
     */
    @ApiModelProperty(value = "预算标识")
    private String budgetMarking;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * delFlag
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "delFlag")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty(value = "所属租户", hidden = true)
    private Long tenantId;
}