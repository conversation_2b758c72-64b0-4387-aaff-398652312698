package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 凭证字
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("凭证字")
@TableName("voucher_word")
@EqualsAndHashCode(callSuper = true)
public class VoucherWord extends Model<VoucherWord> {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 凭证字
     */
    @ApiModelProperty("凭证字")
    private String voucherWord;

    /**
     * 设为默认
     */
    @ApiModelProperty("设为默认")
    private Integer setDefault;

    /**
     * 打印模板
     */
    @ApiModelProperty("打印模板")
    private String printTemplate;


    /**
     * 账套账号
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}