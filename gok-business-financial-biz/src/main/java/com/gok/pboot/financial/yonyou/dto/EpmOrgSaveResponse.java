/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 企业绩效组织体系成员新增响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 企业绩效组织体系成员新增响应DTO
 * 用于接收企业绩效组织体系成员新增操作的返回结果
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EpmOrgSaveResponse {

    /**
     * 成员编码
     */
    private String code;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 成员名称，支持多语
     */
    private MultiLangName name;

    /**
     * 汇总属性
     */
    private String aggr;

    /**
     * 创建时间
     */
    private Date creationtime;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 创建者用户名
     */
    private String creator_userName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种编码
     */
    private String currency_code;

    /**
     * 币种名称
     */
    private String currency_name;

    /**
     * 删除标识
     */
    private String dr;

    /**
     * 启用状态
     */
    private String enable;

    /**
     * 组编码
     */
    private String groupcode;

    /**
     * 是否差额单位
     */
    private String is_banlance_unit;

    /**
     * emp组织体系id
     */
    private String org_epm_systems_id;

    /**
     * 部门性质
     */
    private String org_form;

    /**
     * 部门性质名称
     */
    private String org_form_name;

    /**
     * 组织类型
     */
    private String org_source;

    /**
     * 父节点
     */
    private String parent;

    /**
     * 父节点名称
     */
    private String parent_name;

    /**
     * 对应实体组织
     */
    private String physical_org;

    /**
     * 对应实体组织名称
     */
    private String physical_org_name;

    /**
     * 插入日期
     */
    private Date selectDate;

    /**
     * 简码
     */
    private String shortcode;

    /**
     * 简称
     */
    private String shortname;

    /**
     * 系统标识
     */
    private String sysid;

    /**
     * 多语言名称内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {
        
        /**
         * 中文名称
         */
        private String zh_CN;
        
        /**
         * 英文名称
         */
        private String en_US;
        
        /**
         * 繁体中文名称
         */
        private String zh_TW;
    }
}