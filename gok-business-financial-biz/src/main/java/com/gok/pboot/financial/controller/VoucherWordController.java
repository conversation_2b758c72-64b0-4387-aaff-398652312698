package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.service.IVoucherWordService;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.VoucherWordVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 凭证字控制层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@RestController
@Api(tags = "凭证字")
@RequiredArgsConstructor
@RequestMapping("/voucher_word")
public class VoucherWordController {

    private final IVoucherWordService voucherWordService;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link VoucherWordFindDTO}
     * @return {@link R}<{@link Page}<{@link VoucherWordVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<VoucherWordVO>> findPage(@RequestBody @Valid VoucherWordFindDTO dto) {
        return R.ok(voucherWordService.findPage(dto));
    }

    /**
     * 新增
     *
     * @param dto {@link VoucherWordDTO}
     * @return {@link R}
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    public R addOrEdit(@RequestBody @Valid VoucherWordDTO dto) {
        return voucherWordService.addOrEdit(dto);
    }

    /**
     * 查看
     *
     * @return {@link VoucherWordVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<VoucherWordVO> info(@PathVariable Long id) {
        return voucherWordService.info(id);
    }

    /**
     * 编辑
     *
     * @param dto {@link VoucherWordDTO}
     * @return {@link R}
     */
    @PostMapping("/edit/{id}")
    @ApiOperation(value = "编辑", notes = "编辑")
    public R addOrEdit(@PathVariable Long id,@RequestBody @Valid VoucherWordDTO dto) {
        dto.setId(id);
        return voucherWordService.addOrEdit(dto) ;
    }

    /**
     * 删除
     *
     * @param dto {@link VoucherWordIdsDTO}
     * @return {@link R}
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R del(@RequestBody VoucherWordIdsDTO dto) {
        return voucherWordService.del(dto.getIdList());
    }

    /**
     * 设置默认
     *
     * @return {@link R}
     */
    @PutMapping("/default/{id}")
    @ApiOperation(value = "设置默认", notes = "设置默认")
    public R setDefault(@PathVariable Long id) {
        return voucherWordService.setDefault(id);
    }


}
