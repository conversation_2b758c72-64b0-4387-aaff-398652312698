package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 跨部门二级人工成本台账
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("cross_dept_labor_costs")
@ApiModel("跨部门二级人工成本台账")
public class CrossDeptLaborCosts extends Model<CrossDeptLaborCosts> {

    /**
     * 归属月份
     */
    @ApiModelProperty("归属月份")
    private String yearMonthDate;

    /**
     * 一级部门排序
     */
    @ApiModelProperty("一级部门排序")
    private Integer sortOrder;

    /**
     * 二级部门排序
     */
    @ApiModelProperty("二级部门排序")
    private Integer secondaryDeptSortOrder;

    /**
     * 人员归属二级部门id
     */
    @ApiModelProperty("人员归属二级部门id")
    private Long personnelSecondaryDeptId;

    /**
     * 人员归属二级部门
     */
    @ApiModelProperty("人员归属二级部门")
    private String personnelSecondaryDept;

    /**
     * 人员归属一级部门id
     */
    @ApiModelProperty("人员归属一级部门id")
    private Long personnelDeptId;

    /**
     * 人员归属一级部门
     */
    @ApiModelProperty("人员归属一级部门")
    private String personnelDept;

    /**
     * 人数
     */
    @ApiModelProperty("人数")
    private Integer peopleNum;

    /**
     * 人工成本
     */
    @ApiModelProperty("人工成本")
    private String laborCosts;

    /**
     * 项目成本
     */
    @ApiModelProperty("项目成本")
    private String projectCost;

    /**
     * 项目成本占比
     */
    @ApiModelProperty("项目成本占比")
    private String projectCostProportion;

    /**
     * 战略性项目成本
     */
    @ApiModelProperty("战略性项目成本")
    private String strategicProjectsCost;

    /**
     * 战略性项目占比
     */
    @ApiModelProperty("战略性项目占比")
    private String strategicProjectsCostProportion;

    /**
     * 管理性项目成本
     */
    @ApiModelProperty("管理性项目成本")
    private String manageCostProjectsCost;

    /**
     * 管理性项目占比
     */
    @ApiModelProperty("管理性项目占比")
    private String manageCostProjectsCostProportion;

    /**
     * 未分摊成本
     */
    @ApiModelProperty("未分摊成本")
    private String unallocatedCost;

    /**
     * 未分摊成本占比
     */
    @ApiModelProperty("未分摊成本占比")
    private String unallocatedCostProportion;

    /**
     * 学员复用（实习生）人数
     */
    @ApiModelProperty("学员复用（实习生）人数")
    private Integer studentInternPeopleNum;

    /**
     * 学员复用（实习生）人工成本
     */
    @ApiModelProperty("学员复用（实习生）人工成本")
    private String studentInternCost;

    /**
     * 学员复用（实习生）项目成本
     */
    @ApiModelProperty("学员复用（实习生）项目成本")
    private String studentInternProjectsCost;

    /**
     * 学员复用（实习生）内部项目
     */
    @ApiModelProperty("学员复用（实习生）内部项目")
    private String studentInternInternalProjectsCost;

    /**
     * 学员复用（实习生）未分摊成本
     */
    @ApiModelProperty("学员复用（实习生）未分摊成本")
    private String studentInternUnallocatedCost;

    /**
     * 学员复用（实习生）复用占比
     */
    @ApiModelProperty("学员复用（实习生）复用占比")
    private String studentInternCostProportion;

    /**
     * 学员复用（外部）人数
     */
    @ApiModelProperty("学员复用（外部）人数")
    private Integer studentOutsidePeopleNum;

    /**
     * 学员复用（外部）人工成本
     */
    @ApiModelProperty("学员复用（外部）人工成本")
    private String studentOutsideCost;

    /**
     * 人均月成本自有人工
     */
    @ApiModelProperty("人均月成本自有人工")
    private String perCapitaMonthlyCostOwn;

    /**
     * 人均月成本学员复用
     */
    @ApiModelProperty("人均月成本学员复用")
    private String perCapitaMonthlyCostStusen;

    /**
     * 项目耗用均价（全部人员）项目成本
     */
    @ApiModelProperty("项目耗用均价（全部人员）项目成本")
    private String totalPersonnelProjectCost;

    /**
     * 项目耗用均价（全部人员）项目工时
     */
    @ApiModelProperty("项目耗用均价（全部人员）项目工时")
    private String totalPersonnelProjectHours;

    /**
     * 项目耗用均价（全部人员）项目单价
     */
    @ApiModelProperty("项目耗用均价（全部人员）项目单价")
    private String totalPersonnelProjectPrice;

    /**
     * 项目耗用均价（不含实习生）项目成本
     */
    @ApiModelProperty("项目耗用均价（不含实习生）项目成本")
    private String excludingInternsProjectCost;

    /**
     * 项目耗用均价（不含实习生）项目工时
     */
    @ApiModelProperty("项目耗用均价（不含实习生）项目工时")
    private String excludingInternsProjectHours;

    /**
     * 项目耗用均价（不含实习生）项目单价
     */
    @ApiModelProperty("项目耗用均价（不含实习生）项目单价")
    private String excludingInternsProjectPrice;

    /**
     * 项目耗用均价（实习生）项目成本
     */
    @ApiModelProperty("项目耗用均价（实习生）项目成本")
    private String internsProjectCost;

    /**
     * 项目耗用均价（实习生）项目工时
     */
    @ApiModelProperty("项目耗用均价（实习生）项目工时")
    private String internsProjectHours;

    /**
     * 项目耗用均价（实习生）项目单价
     */
    @ApiModelProperty("项目耗用均价（实习生）项目单价")
    private String internsProjectPrice;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}