package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.service.IProjectLaborCostDetailService;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目人工成本分摊明细
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@RestController
@RequestMapping("/project-labor-cost-detail")
@RequiredArgsConstructor
@Api(tags = "项目人工成本分摊明细")
public class ProjectLaborCostDetailController {

    private final IProjectLaborCostDetailService projectLaborCostDetailService;

    /**
     * 模糊查询带分页
     *
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link ProjectLaborCostDetailVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('projectLaborCostDetailed')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<ProjectLaborCostDetailVO>> findPage(@RequestBody ProjectLaborCostDTO projectLaborCostDto) {
        return R.ok(projectLaborCostDetailService.findPage( projectLaborCostDto));
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostDetailVO}>
     */
    @ResponseExcel(async = true, name = "项目人工成本分摊明细", functionEnum = FunctionEnum.FINANCIAL_PROJECT_DE_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('projectLaborCostDetailed/export')")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List<ProjectLaborCostDetailVO> export(@RequestBody ProjectLaborCostDTO projectLaborCostDTO) {
        return projectLaborCostDetailService.export(projectLaborCostDTO);
    }

}
