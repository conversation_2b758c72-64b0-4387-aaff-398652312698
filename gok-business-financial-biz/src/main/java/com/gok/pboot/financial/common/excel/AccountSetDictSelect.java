package com.gok.pboot.financial.common.excel;

import com.gok.module.excel.api.service.ExcelDynamicSelect;
import com.gok.pboot.common.core.util.SpringContextHolder;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.service.IFundAccountService;
import com.gok.pboot.financial.vo.FundAccountVO;
import com.gok.pboot.financial.vo.OaDictVO;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * ehr dict
 *
 * <AUTHOR>
 * @date 2024/12/04
 */
public class AccountSetDictSelect implements ExcelDynamicSelect {


    @Override
    public String[] getSource(String code) {
        String[] result = null;
        if (StringUtils.isBlank(code)) {
            return new String[0];
        }
        if (DictConstant.BELONGING_SUBJECT_MAIN_ID.equals(code)) {
            DBApi dbApi = SpringContextHolder.getBean(DBApi.class);
            List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
            result = new String[oaDictVOS.size()];
            for (int i = 0; i < oaDictVOS.size(); i++) {
                result[i] = oaDictVOS.get(i).getName();
            }
        } else if ("fundAccount".equals(code)) {
            IFundAccountService fundAccountService = SpringContextHolder.getBean(IFundAccountService.class);
            List<FundAccountVO> list = fundAccountService.findList();
            result = new String[list.size()];
            for (int i = 0; i < list.size(); i++) {
                result[i] = list.get(i).getFundAccount();
            }
        }
        return result;
    }
}
