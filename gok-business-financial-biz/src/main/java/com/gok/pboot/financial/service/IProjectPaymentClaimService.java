package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.ProjectPaymentClaim;
import com.gok.pboot.financial.dto.IdsDTO;
import com.gok.pboot.financial.vo.BusinessBlockVO;
import com.gok.pboot.financial.vo.ProjectPaymentClaimVO;

import java.util.List;

/**
 * 项目回款认领 Service
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface IProjectPaymentClaimService extends IService<ProjectPaymentClaim> {

    /**
     * 获取列表中的业务板块
     *
     * @return {@link List}<{@link BusinessBlockVO}>
     */
    List<BusinessBlockVO> queryBusinessBlock();

    /**
     * 获取认领信息
     * @param idsDTO
     * @return
     */
    List<ProjectPaymentClaimVO> getClaim(IdsDTO idsDTO);
}
