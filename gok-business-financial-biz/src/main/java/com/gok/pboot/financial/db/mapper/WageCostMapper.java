package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.WageCost;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.vo.WageCostVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 工资成本Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface WageCostMapper extends BaseMapper<WageCost> {

    /**
     * 查询全部种类的发薪主体
     *
     * @return 发薪主体列表
     */
    List<String> salaryPaidSubject();


    /**
     * 模糊分页查询
     *
     * @param page        分页条件
     * @param wageCostDTO 显示内容
     * @return {@link Page}<{@link WageCostVO}>
     */
    Page<WageCostVO> queryWageCostPage(@Param("page") Page<WageCostVO> page, @Param("dto") WageCostDTO wageCostDTO);

    /**
     * 模糊分页查询
     *
     * @param wageCostDTO 显示内容
     * @return {@link Page}<{@link WageCostVO}>
     */
    List<WageCostVO> queryWageCostPage( @Param("dto") WageCostDTO wageCostDTO);

    /**
     * 导出表
     *
     * @param wageCostDTO {@link WageCostDTO}
     * @return {@link List}<{@link WageCostVO}>
     */
    List<WageCostVO> exportList(@Param("dto") WageCostDTO wageCostDTO);

    /**
     * ..
     *
     * @param ids {@link List} id集合
     * @return {@link List}<{@link WageCost}>
     */
    List<WageCost> selList(@Param("ids") List<String> ids);

    /**
     * 修改推送状态
     *
     * @param wageCost {@link WageCost}
     */
    void updatePushStatus(@Param("wageCost") WageCost wageCost);

    /**
     * 批量修改推送状态
     *
     * @param wageCostList {@link WageCost}
     */
    void batchUpdatePushStatus(@Param("wageCostList") List<WageCost> wageCostList);
}




