package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.WageCostDetail;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import com.gok.pboot.financial.vo.WageCostDetailVO;
import com.gok.pboot.financial.vo.WageCostVO;

import java.util.List;

/**
 * 工资成本明细Service
 *
 * <AUTHOR>
 * @since 2024-01-18
 */
public interface IWageCostDetailService extends IService<WageCostDetail> {

    /**
     * 模糊查询带分页
     *
     * @param page        分页请求
     * @param wageCostDTO 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link ProjectLaborCostDetailVO}>>
     */
    StatisticsPage<WageCostDetailVO> findPage(StatisticsPage<WageCostDetailVO> page, WageCostDTO wageCostDTO);


    /**
     * 导出Excel选中的数据
     *
     * @param dto 包含导入id
     * @return {@link List}<{@link WageCostVO}>
     */
    List<WageCostDetailVO> export(WageCostDTO dto);

    List<String> getSalaryPaidSubjects();
}
