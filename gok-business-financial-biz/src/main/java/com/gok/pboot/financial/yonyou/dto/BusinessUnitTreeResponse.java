/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 业务单元树查询响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务单元树查询响应DTO
 * 用于接收业务单元树查询操作的返回结果
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
public class BusinessUnitTreeResponse {

    /**
     * 业务单元ID
     */
    private String id;

    /**
     * 业务单元编码
     */
    private String code;

    /**
     * 业务单元名称
     */
    private String name;

    /**
     * 父业务单元ID
     */
    @JsonProperty("parent_id")
    private String parentId;

    /**
     * 父业务单元编码
     */
    @JsonProperty("parent_code")
    private String parentCode;

    /**
     * 启用状态（1:启用，0:停用）
     */
    private Integer enable;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序号
     */
    private Integer sortno;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 是否叶子节点
     */
    @JsonProperty("is_leaf")
    private Boolean isLeaf;
}