package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.VoucherWord;
import com.gok.pboot.financial.dto.VoucherWordDTO;
import com.gok.pboot.financial.dto.VoucherWordFindDTO;
import com.gok.pboot.financial.vo.VoucherWordVO;

import java.util.List;

/**
 * 凭证字业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IVoucherWordService extends IService<VoucherWord> {


    Page<VoucherWordVO> findPage(VoucherWordFindDTO dto);

    R addOrEdit(VoucherWordDTO dto);

    R<VoucherWordVO> info(Long id);

    R del(List<Long> idList);

    R setDefault(Long id);
}
