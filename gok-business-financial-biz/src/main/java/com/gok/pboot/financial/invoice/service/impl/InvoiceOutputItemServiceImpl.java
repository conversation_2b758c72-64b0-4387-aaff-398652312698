package com.gok.pboot.financial.invoice.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.domain.InvoiceOutputItem;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputItemDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputItemVO;
import com.gok.pboot.financial.invoice.enums.InvoiceStatusEnum;
import com.gok.pboot.financial.invoice.mapper.InvoiceOutputItemMapper;
import com.gok.pboot.financial.invoice.service.IInvoiceOutputItemService;
import com.gok.pboot.financial.util.EnumUtils;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/06/10
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceOutputItemServiceImpl extends ServiceImpl<InvoiceOutputItemMapper, InvoiceOutputItem> implements IInvoiceOutputItemService {

    private final RemoteBcpDictService remoteBcpDictService;

    @Override
    public StatisticsPage<InvoiceOutputItemVO> findPage(InvoiceOutputItemDTO dto) {
        Page<InvoiceOutputItemVO> outputItemVOPage = baseMapper.findPage(new Page<>(dto.getCurrent(), dto.getSize()), dto);
        StatisticsPage<InvoiceOutputItemVO> statisticsPage =
                new StatisticsPage<>(outputItemVOPage.getCurrent(), outputItemVOPage.getSize(), outputItemVOPage.getTotal());
        List<InvoiceOutputItemVO> records = outputItemVOPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return statisticsPage;
        }
        Map<String, BigDecimal> pageSum = baseMapper.findPageSum(dto);
        statisticsPage.setStatistics(pageSum);

        handleInvoiceOutputItemVO(records);
        statisticsPage.setRecords(records);

        return statisticsPage;
    }

    @Override
    public List<InvoiceOutputItemVO> exportExcel(InvoiceOutputItemDTO dto) {
        dto.setCurrent(0L);
        dto.setSize(Long.MAX_VALUE);
        return CollUtil.emptyIfNull(findPage(dto).getRecords());
    }

    private void handleInvoiceOutputItemVO(List<InvoiceOutputItemVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }
        // 获取中台字典
        Set<String> dictKeys = new HashSet<>(Arrays.asList("税率", "开票类型", "商品/服务分类", "所属公司", "结算方式", "红蓝字", "收款平台"));
        Map<String, List<DictKvVo>> allDictMap = remoteBcpDictService.getDictKvBatchList(dictKeys).getData();
        Map<Integer, String> taxRateMap = allDictMap.getOrDefault("税率", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> goodsServiceNameMap = allDictMap.getOrDefault("商品/服务分类", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> contractSubjectMap = allDictMap.getOrDefault("所属公司", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> settlementMethodMap = allDictMap.getOrDefault("结算方式", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> redAndBlueMap = allDictMap.getOrDefault("红蓝字", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> receiptPlatformMap = allDictMap.getOrDefault("收款平台", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));

        // 数字处理成千分位
        DecimalFormat formatter = new DecimalFormat("##,##0.00");

        voList.forEach(vo -> {
            vo.setRedAndBlueTxt(null != vo.getRedAndBlue() ? redAndBlueMap.get(vo.getRedAndBlue()) : null);
            vo.setReceiptPlatformTxt(null != vo.getReceiptPlatform() ? receiptPlatformMap.get(vo.getReceiptPlatform()) : null);
            vo.setContractSubjectTxt(null != vo.getContractSubject() ? contractSubjectMap.get(vo.getContractSubject()) : null);
            vo.setGoodsServiceNameTxt(null != vo.getGoodsServiceName() ? goodsServiceNameMap.get(vo.getGoodsServiceName()) : null);
            vo.setActualTaxRateTxt(Optional.ofNullable(vo.getActualTaxRate()).isPresent() ? taxRateMap.get(vo.getActualTaxRate()) : null);
            vo.setContractSettlementTypeTxt(null != vo.getContractSettlementType() ? settlementMethodMap.get(vo.getContractSettlementType()) : null);
            vo.setActualAmountIncludedTaxTxt(null != vo.getActualAmountIncludedTax() ? formatter.format(vo.getActualAmountIncludedTax()) : null);
            vo.setActualAmountExcludingTaxTxt(null != vo.getActualAmountExcludingTax() ? formatter.format(vo.getActualAmountExcludingTax()) : null);
            vo.setInvoiceStatusTxt(EnumUtils.getNameByValue(InvoiceStatusEnum.class, vo.getInvoiceStatus()));
        });
    }

} 