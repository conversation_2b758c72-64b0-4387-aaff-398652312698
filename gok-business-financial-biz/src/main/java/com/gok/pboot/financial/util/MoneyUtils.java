package com.gok.pboot.financial.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.gok.pboot.common.secret.AESEncryptor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 金额转换工具
 *
 * <AUTHOR>
 * @since 2023-09-14
 */
public class MoneyUtils {

    public static final String ZERO = "0";

    public static final String ZERO_TWO = "0.0";

    public static final String ZERO_THREE = "0.00";

    public static final String TYPE = "---";

    private MoneyUtils() {}

    private static class MoneyUtilsInstance {
        private static final MoneyUtils INSTANCE = new MoneyUtils();
    }

    public static MoneyUtils getInstance() {
        return MoneyUtilsInstance.INSTANCE;
    }

    /**
     * 保留两位小数返回
     *
     * @param s 金额
     * @return 金额
     */
    public String transType(BigDecimal s) {
        if (!Optional.ofNullable(s).isPresent()) {
            return null;
        }

        return s.compareTo(BigDecimal.ZERO) == NumberUtils.INTEGER_ZERO
                ? TYPE
                : this.addSeparator(s.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
    }

    /**
     * 保留两位小数返回
     *
     * @param s 金额
     * @param decrypt 是否需要解密
     * @return 金额
     */
    public String transType(String s, boolean decrypt) {
        if (!Optional.ofNullable(s).isPresent() || StrUtil.EMPTY.equals(s)) {
            return null;
        }
        if (decrypt) {
            String val = AESEncryptor.justDecrypt(s);
            if (StrUtil.EMPTY.equals(val)) {
                return null;
            }
            BigDecimal d = new BigDecimal(val);
            return d.compareTo(BigDecimal.ZERO) == NumberUtils.INTEGER_ZERO
                    ? TYPE
                    : this.addSeparator(d.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
        }

        return this.addSeparator(new BigDecimal(s).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
    }

    public static BigDecimal decryptToBigDecimal(String str, boolean decrypt){
        if (StringUtils.isBlank(str)) {
            return BigDecimal.ZERO;
        }
        if (decrypt) {
            String val = AESEncryptor.justDecrypt(str);
            if (StrUtil.EMPTY.equals(val)) {
                return BigDecimal.ZERO;
            }
            return new BigDecimal(val);
        }

        return new BigDecimal(str).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
    }


    /**
     * 保留两位小数并返回相反数
     *
     * @param s 金额
     * @return 金额
     */
    public String oppositeNumber(String s) {
        // 无需解密情况
        if (!Optional.ofNullable(s).isPresent() || ZERO_THREE.equals(s)) {
            return ZERO_THREE;
        }
        String justDecrypt = AESEncryptor.justDecrypt(s);

        return ZERO.equals(justDecrypt) || ZERO_TWO.equals(justDecrypt) || ZERO_THREE.equals(justDecrypt) || StrUtil.EMPTY.equals(justDecrypt)
                ? ZERO_THREE
                : new BigDecimal(justDecrypt).negate().setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString();
    }

    /**
     * 保留两位小数返回
     *
     * @param s 金额
     * @return 金额
     */
    public String decrypt(BigDecimal s) {
        return !Optional.ofNullable(s).isPresent() || s.compareTo(BigDecimal.ZERO) < NumberUtils.INTEGER_ONE
                ? ZERO_THREE
                : s.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString();
    }

    /**
     * 保留两位小数返回
     *
     * @param s 金额
     * @return 金额
     */
    public String decrypt(String s) {
        // 无需解密情况
        if (!Optional.ofNullable(s).isPresent() || ZERO_THREE.equals(s)) {
            return ZERO_THREE;
        }
        
        try {
            String justDecrypt = AESEncryptor.justDecrypt(s);
            
            // 解密结果为null或空字符串时返回默认值
            if (justDecrypt == null || justDecrypt.trim().isEmpty()) {
                return ZERO_THREE;
            }
            
            return ZERO.equals(justDecrypt) || ZERO_TWO.equals(justDecrypt) || ZERO_THREE.equals(justDecrypt) || StrUtil.EMPTY.equals(justDecrypt)
                    ? ZERO_THREE
                    : new BigDecimal(justDecrypt).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString();
        } catch (Exception e) {
            // 解密失败时记录日志并返回默认值
            // 这里不能使用log，因为MoneyUtils是工具类，避免引入日志依赖
            System.err.println("解密金额数据失败，数据: " + s + ", 错误: " + e.getMessage());
            return ZERO_THREE;
        }
    }

    /**
     * 返回占比
     *
     * @param m1 除数字符串金额
     * @param m2 被除数字符串金额
     * @return 商字符串占比
     */
    public String proportion(String m1, String m2) {
        // 为空直接返回即可
        if (!Optional.ofNullable(m1).isPresent() || !Optional.ofNullable(m2).isPresent()) {
            return null;
        }
        // 除数或被除数为0直接返回
        BigDecimal b1 = new BigDecimal(m1);
        BigDecimal b2 = new BigDecimal(m2);
        if (b1.compareTo(BigDecimal.ZERO) == 0 || b2.compareTo(BigDecimal.ZERO) == 0) {
            return TYPE;
        }
        return b1.multiply(new BigDecimal("100"))
                .divide(b2, NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 返回占比
     *
     * @param b1 除数数值金额
     * @param b2 被除数数值金额
     * @return 商字符串占比
     */
    public String proportion(BigDecimal b1, BigDecimal b2) {
        // 为空直接返回即可
        if (!Optional.ofNullable(b1).isPresent() || !Optional.ofNullable(b2).isPresent()) {
            return null;
        }
        // 除数或被除数为0直接返回
        if (b1.compareTo(BigDecimal.ZERO) == 0 || b2.compareTo(BigDecimal.ZERO) == 0) {
            return TYPE;
        }
        return b1.multiply(new BigDecimal("100"))
                .divide(b2, NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 返回占比
     *
     * @param s 除数字符串占比
     * @return 商字符串占比
     */
    public String proportion(String s) {
        // 为空直接返回即可
        if (StringUtils.isBlank(s)) {
            return null;
        }
        // 为0直接返回
        BigDecimal b = new BigDecimal(s);
        if (b.compareTo(BigDecimal.ZERO) == 0) {
            return TYPE;
        }
        return b.multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 返回占比
     *
     * @param s 除数字符串占比
     * @return 商字符串占比
     */
    public String proportion(BigDecimal s) {
        // 为空直接返回即可
        if (!Optional.ofNullable(s).isPresent()) {
            return null;
        }

        return s.multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 除法计算
     *
     * @param b 除数数值金额
     * @param i 被除数数值人数
     * @return 商字符串占比
     */
    public String divide(BigDecimal b, Integer i) {
        // 为空直接返回即可
        if (!Optional.ofNullable(b).isPresent() || !Optional.ofNullable(i).isPresent()) {
            return null;
        }
        // 除数或被除数为0直接返回
        if (b.compareTo(BigDecimal.ZERO) == 0 || NumberUtils.INTEGER_ZERO.equals(i)) {
            return TYPE;
        }
        return this.addSeparator(b.divide(new BigDecimal(i), NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
    }

    /**
     * V1.1.1 优化显示：采用千位分隔符（每三位添加一个分割符号）
     *
     * @param num 金额
     * @return 金额
     */
    private String addSeparator(String num) {
        final int three = 3;
        if (ZERO_THREE.equals(num)) {
            return TYPE;
        }
        // 切割金额 前者为整数 后者为小数
        String[] numArr = num.split(StrPool.BACKSLASH + StrPool.DOT);
        num = numArr[NumberUtils.INTEGER_ZERO];

        int length = num.length();
        List<String> list = new ArrayList<>();
        while (length > three) {
            list.add(num.substring(length - three, length));
            length -= three;
        }
        // 将前面小于三位的数字添加到ArrayList中
        list.add(num.substring(0, length));
        StringBuilder stringBuilder = new StringBuilder();
        // 倒序拼接
        for (int i = list.size() - 1; i > 0; --i) {
            stringBuilder.append(list.get(i)).append(StrPool.COMMA);
        }
        String s = stringBuilder.append(list.get(NumberUtils.INTEGER_ZERO))
                + StrPool.DOT
                + numArr[NumberUtils.INTEGER_ONE];
        if (StrPool.DASHED.equals(s.substring(NumberUtils.INTEGER_ZERO, NumberUtils.INTEGER_ONE))
                && StrPool.COMMA.equals(s.substring(NumberUtils.INTEGER_ONE, NumberUtils.INTEGER_TWO))) {
            s = s.replaceFirst(StrPool.COMMA, CharSequenceUtil.EMPTY);
        }
        return s;
    }

    /**
     * 转换成String保留返回
     *
     * @param s 金额
     * @return 金额
     */
    public String transString(BigDecimal s) {
        if (!Optional.ofNullable(s).isPresent()) {
            return null;
        }

        return s.compareTo(BigDecimal.ZERO) == NumberUtils.INTEGER_ZERO
                ? ZERO_THREE
                : this.addSeparator(s.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
    }

    /**
     * 保存两位小数返回
     * @param s
     * @return
     */
    public String strTransType(String s) {
        if (!Optional.ofNullable(s).isPresent()) {
            return null;
        }
        BigDecimal b = new BigDecimal(s);
        return b.compareTo(BigDecimal.ZERO) == NumberUtils.INTEGER_ZERO
                ? TYPE
                : this.addSeparator(b.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString());
    }


    /**
     * 保留两位小数返回
     *
     * @param s 金额
     * @return 金额
     */
    public String keepTwoDecimal(BigDecimal s) {
        return !Optional.ofNullable(s).isPresent()
                ? ZERO_THREE
                : s.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString();
    }
}
