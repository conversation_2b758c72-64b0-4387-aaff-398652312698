package com.gok.pboot.financial.yonyou.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

public class AccessTokenResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 获取的访问令牌 access_token
     */
    @JsonProperty(value = "access_token")
    private String accessToken;

    /**
     * 访问令牌的过期时间，单位秒
     */
    private long expire;

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public long getExpire() {
        return expire;
    }

    public void setExpire(long expire) {
        this.expire = expire;
    }
}