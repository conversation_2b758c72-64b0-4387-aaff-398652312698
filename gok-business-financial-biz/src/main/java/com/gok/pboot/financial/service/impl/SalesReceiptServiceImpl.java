package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.flowable.task.common.enums.task.*;
import com.gok.bcp.flowable.task.dto.task.LeaderDto;
import com.gok.bcp.flowable.task.dto.task.TaskSaveDto;
import com.gok.bcp.flowable.task.feign.RemoteTaskService;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.entity.model.WeComModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.message.feign.RemoteSendMsgService;
import com.gok.bcp.upms.dto.DeptDetailDto;
import com.gok.bcp.upms.feign.RemoteDeptService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.PushLog;
import com.gok.pboot.financial.db.entity.SalesReceipt;
import com.gok.pboot.financial.db.entity.SalesReceiptPaymentProcess;
import com.gok.pboot.financial.db.entity.SalesReceiptPaymentTask;
import com.gok.pboot.financial.db.mapper.PushLogMapper;
import com.gok.pboot.financial.db.mapper.SalesReceiptMapper;
import com.gok.pboot.financial.db.mapper.SalesReceiptPaymentProcessMapper;
import com.gok.pboot.financial.db.mapper.SalesReceiptPaymentTaskMapper;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.ISalesReceiptPaymentProcessService;
import com.gok.pboot.financial.service.ISalesReceiptService;
import com.gok.pboot.financial.util.*;
import com.gok.pboot.financial.vo.*;
import com.google.common.base.Charsets;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 销售收款计划ServiceImpl
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt(销售收款计划)】的数据库操作Service实现
 * @createDate 2023-09-27 16:08:24
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SalesReceiptServiceImpl extends ServiceImpl<SalesReceiptMapper, SalesReceipt>
        implements ISalesReceiptService {

    private final RemoteRoleService remoteRoleService;

    private final RemoteMailService remoteMailService;

    private final RemoteDeptService remoteDeptService;

    private final SalesReceiptPaymentTaskMapper paymentTaskMapper;

    private final SalesReceiptPaymentProcessMapper paymentProcessMapper;

    private final ISalesReceiptPaymentProcessService processService;

    private final RemoteTaskService remoteTaskService;

    private final RemoteSendMsgService remoteSendMsgService;

    @Value("${pushMessage.portalAppId}")
    private Long portalAppId;

    @Value("${pushMessage.salesReceiptUrl}")
    private String salesReceiptUrl;

    @Value("${pushMessage.salesReceiptUrlRedirect}")
    private String redirect;

    private final LogRecordUtils logRecordUtils;

    private final PushLogMapper pushLogMapper;

    private final DBApi dbApi;

    public Page<SalesReceiptVO> findPageV1(SalesReceiptDTO dto) {
        // 分页
        Page<SalesReceipt> pageInfo = new Page<>(dto.getCurrent(), dto.getSize());
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<String,Integer> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap( OaDictVO::getName,OaDictVO::getDisorder,(b, c)->b));

        // 主体名称转为code
        String attributableSubject = dto.getAttributableSubject();
        if (CharSequenceUtil.isNotBlank(attributableSubject)) {
            dto.setAttributableSubject(String.valueOf(attributableSubjectMap.getOrDefault(attributableSubject,null)));
        }
        baseMapper.querySalesReceiptPage(pageInfo, limitDto(dto));
        List<SalesReceipt> records = pageInfo.getRecords();
        List<SalesReceiptVO> voRecords = new ArrayList<>();
        if (CollUtil.isNotEmpty(records)) {
            //枚举类型转换
            records.forEach(r -> voRecords.add(entityToVo(r)));
        }
        Page<SalesReceiptVO> page = new Page<>();
        BeanUtils.copyProperties(pageInfo, page);
        page.setRecords(voRecords);
        return page;
    }

    @Override
    public List<String> currentPaymentNameBox(Long clientId, String menuCode) {
        SalesReceiptDTO dto = new SalesReceiptDTO();
        dto.setClientId(clientId);
        dto.setMenuCode(menuCode);
        if (clientId == null || menuCode == null) {
            return new ArrayList<>();
        }
        dto.setClientId(clientId);
        dto.setMenuCode(menuCode);

        return baseMapper.currentPaymentNameBox(limitDto(dto));
    }

    @Override
    public List<String> projectStatusBox() {
        return baseMapper.projectStatusBox();
    }

    @Override
    public List<SalesReceiptVO> export(SalesReceiptDTO dto) {
        dto = limitDto(dto);
        List<SalesReceiptVO> salesReceiptList = new ArrayList<>();
        if ((Optional.ofNullable(dto.getAuthority()).isPresent() && CollUtil.isNotEmpty(dto.getAuthUserIdList()))
                || Boolean.TRUE.equals(dto.getIsAll())){
            salesReceiptList = baseMapper.queryPage(limitDto(dto));
        }

        // 记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.SALES_RECEIPT,
                OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + salesReceiptList.size() + "条】");

        if (CollUtil.isNotEmpty(salesReceiptList)) {
            List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);

            Map<Integer,String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

            List<SalesReceiptPaymentProcess> processList = paymentProcessMapper.selectList(Wrappers.<SalesReceiptPaymentProcess>lambdaQuery());
            Map<Long, List<SalesReceiptPaymentProcess>> processListMap = processList.stream().collect(Collectors.groupingBy(SalesReceiptPaymentProcess::getSalesReceiptId));

            //枚举类型转换
            salesReceiptList.forEach(r -> {
                // 数值类型转换
                dateVO(r, attributableSubjectMap,processListMap);

            });
        }
        return salesReceiptList;
    }

    public Boolean pushMessageV1() {
        SimpleDateFormat dateFormatter = new SimpleDateFormat(DateUtils.SIMPLE_DATE_FORMAT);
        try {
            //获取全部的未收款内容
            List<SalesReceiptPushVO> salesReceiptPushVoS = baseMapper.queryPushVoV1();
            for (SalesReceiptPushVO vo : salesReceiptPushVoS) {
                Date expectedDate = dateFormatter.parse(vo.getExpectedDate());
                //当前天数的差距
                Long chaJu = (dateFormatter.parse(dateFormatter.format(new Date())).getTime()
                        - expectedDate.getTime()) / (24 * 60 * 60 * 1000);
                //按时间推送消息到门户
                pushMessageByTime(chaJu, vo);
            }
            return true;
        } catch (ParseException e) {
            throw new BusinessException("时间格式化有误");
        }
    }

    /**
     * 初始化实体类
     *
     * @param salesReceipt 实体类
     * @return {@link SalesReceiptVO}
     */
    private SalesReceiptVO entityToVo(SalesReceipt salesReceipt) {
        SalesReceiptVO vo = new SalesReceiptVO();
        // 基础信息拷贝
        BeanUtils.copyProperties(salesReceipt, vo);
        // 数值类型转换
        MoneyUtils moneyUtils = MoneyUtils.getInstance();
        vo.setContractMoney(moneyUtils.transType(salesReceipt.getContractMoney()));
        vo.setAccumulatedAmount(moneyUtils.transType(salesReceipt.getAccumulatedAmount()));
        vo.setCurrentPaymentMoney(moneyUtils.transType(salesReceipt.getCurrentPaymentMoney()));

        //累计收款比例 *100%
        if (salesReceipt.getCollectionRatio() == null) {
            vo.setCollectionRatio(null);
        } else {
            if (NumberUtils.INTEGER_ZERO != new BigDecimal(salesReceipt.getCollectionRatio()).signum()) {
                BigDecimal ratio = new BigDecimal(salesReceipt.getCollectionRatio())
                        .multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                vo.setCollectionRatio(ratio + "%");
            } else {
                vo.setCollectionRatio(MoneyUtils.TYPE);
            }
        }
         List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer,String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        vo.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, salesReceipt.getProjectStatus()));
        vo.setAttributableSubjectTxt(attributableSubjectMap.getOrDefault(Integer.valueOf(salesReceipt.getAttributableSubject()),StrUtil.EMPTY));
        vo.setPaymentStatus(EnumUtils.getNameByValue(PaymentStatusEnum.class, salesReceipt.getPaymentStatus()));
        return vo;
    }

    /**
     * 给传入的参数添加中台权限
     *
     * @param dto {@link SalesReceiptDTO}
     * @return {@link SalesReceiptDTO}
     */
    private SalesReceiptDTO limitDto(SalesReceiptDTO dto) {
        SysUserRoleDataVo userDataScope = remoteRoleService
                .getRoleDataDetailByUserId(dto.getClientId(), UserUtils.getUser().getId(), dto.getMenuCode()).getData();
        dto.setIsAll(userDataScope.getIsAll());
        if (Boolean.FALSE.equals(userDataScope.getIsAll())) {
            //设置权限标识
            dto.setAuthority(Boolean.TRUE);
            //当前登入人
            dto.setUserId(UserUtils.getUser().getId());
            dto.setAuthUserIdList(userDataScope.getUserIdList());
            //设置中台部门权限
            //dto.setAuthDeptIdList(userDataScope.getDeptIdList());
        }
        return dto;
    }

    private void pushMessageByTime(Long time, SalesReceiptPushVO vo) {
        if (!Optional.ofNullable(vo).isPresent() || !Optional.ofNullable(vo.getSalesmanUserId()).isPresent()
                || !Optional.ofNullable(vo.getSalesmanUserName()).isPresent()) {
            return;
        }
        MailModel model = new MailModel();
        model.setSource(SourceEnum.PROJECT.getValue());
        model.setType(MsgTypeEnum.TEXT_MSG.getValue());
        model.setTitle("业务一体化销售合同待收款");
        model.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
        model.setSender(SourceEnum.PROJECT.getName());
        model.setTargetType(TargetTypeEnum.USERS.getValue());
        model.setRedirectUrl(
                FinancePropertiesUtils.SALES_RECEIPT_URL + "?contractCode=" + vo.getContractCode()
        );

        WeComModel msgModel;
        List<BcpMessageTargetDTO> list = new ArrayList<>();
        BcpMessageTargetDTO salesmanDto = new BcpMessageTargetDTO();
        salesmanDto.setTargetId(vo.getSalesmanUserId().toString());
        salesmanDto.setTargetName(vo.getSalesmanUserName());
        list.add(salesmanDto);

        // 2、推送条件为 -7天提醒
        // 3、推送条件为 0天提醒
        // 4、推送条件为 >=7天且每七天提醒
        if (time == -7 || time >= 7 && time % 7 == 0) {
            model.setTargetList(list);
            model.setContent("你有一笔销售合同"
                    + vo.getContractCode() + "的"
                    + vo.getPaymentName() + "质保金预期"
                    + vo.getExpectedDate() + "收款尚未收款，请及时处理~");
            try {
                remoteMailService.sendMsg(model);
                msgModel = WeComModel.from(MailModel.to(model));
                msgModel.setContent(
                        model.getContent() + "\n<a href=\"" + model.getRedirectUrl() + "\">" + "查看详情</a>"
                );
            } catch (Exception e) {
                log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", vo.getSalesmanUserId(), vo.getSalesmanUserName());
            }
        } else if (time == 0) {
            //获取上级领导的id和名称
            DeptDetailDto deptDetailDto = remoteDeptService.getDeptDetail(vo.getFirstDepartmentId()).getData();
            if (deptDetailDto != null && deptDetailDto.getDeptLeaderMap() != null) {
                Map<Long, String> map = deptDetailDto.getDeptLeaderMap();
                for (Map.Entry<Long, String> m : map.entrySet()) {
                    BcpMessageTargetDTO deptManDto = new BcpMessageTargetDTO();
                    deptManDto.setTargetId(String.valueOf(m.getKey()));
                    deptManDto.setTargetName(map.get(m.getKey()));
                    list.add(deptManDto);
                }
            }
            model.setTargetList(list);
            model.setContent("你有一笔销售合同"
                    + vo.getContractCode() + "的"
                    + vo.getPaymentName() + "质保金预期"
                    + vo.getExpectedDate() + "收款尚未收款，请及时处理~");
            try {
                remoteMailService.sendMsg(model);
                msgModel = WeComModel.from(MailModel.to(model));
                msgModel.setContent(
                        model.getContent() + "\n<a href=\"" + model.getRedirectUrl() + "\">" + "查看详情</a>"
                );
            } catch (Exception e) {
                log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", vo.getSalesmanUserId(), vo.getSalesmanUserName());
            }
        }
    }

    @Override
    public StatisticsPage<SalesReceiptVO> findPage(SalesReceiptDTO dto) {
        // 分页
        StatisticsPage<SalesReceiptVO> page = new StatisticsPage<>(dto.getCurrent(), dto.getSize());
        limitDto(dto);

        if ((Optional.ofNullable(dto.getAuthority()).isPresent() && CollUtil.isNotEmpty(dto.getAuthUserIdList()))
                || Boolean.TRUE.equals(dto.getIsAll())){
            baseMapper.queryPage(page, dto);
        }
        List<SalesReceiptVO> records = page.getRecords();

        if (CollUtil.isNotEmpty(records)) {
            List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
            Map<Integer,String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

            List<SalesReceiptPaymentProcess> processList = paymentProcessMapper.selectList(Wrappers.<SalesReceiptPaymentProcess>lambdaQuery());
            Map<Long, List<SalesReceiptPaymentProcess>> processListMap = processList.stream().collect(Collectors.groupingBy(SalesReceiptPaymentProcess::getSalesReceiptId));

            //枚举类型转换
            records.forEach(r -> {
                // 数值类型转换
                dateVO(r, attributableSubjectMap,processListMap);
            });
        }
        statistics(page,dto);
        return page;
    }

    private void statistics(StatisticsPage<SalesReceiptVO> page, SalesReceiptDTO dto) {
        if (page.getTotal() == 0) {
            return;
        }
        Map<String, BigDecimal> resultMap = baseMapper.statistics(dto);
        page.setStatistics(resultMap);
    }


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> savePaymentTask(SalesReceiptPaymentTaskDTO dto) {
        SalesReceipt salesReceipt = baseMapper.selectOne(Wrappers.<SalesReceipt>lambdaQuery()
                .eq(SalesReceipt::getId, dto.getId()));

        SalesReceiptPaymentDetailDTO paymentDetailDto = dto.getPaymentDetailDto();
        BeanUtil.copyProperties(paymentDetailDto,salesReceipt);
        baseMapper.updateById(salesReceipt);
        List<String> customerPaymentProcessList = dto.getCustomerPaymentProcessList();
        String customerPaymentProcess = StrUtil.EMPTY;
        if (CollectionUtil.isNotEmpty(customerPaymentProcessList)){
            customerPaymentProcess = String.join(">", customerPaymentProcessList);
        }
        //保存销售收款计划客户付款审批流程
        paymentProcessMapper.deleteBySalesReceiptId(dto.getId());
        if (CollectionUtil.isNotEmpty(dto.getPaymentProcessDtoList())){
            List<SalesReceiptPaymentProcess> processList = dto.getPaymentProcessDtoList().stream().map(paymentProcessDto -> {
                SalesReceiptPaymentProcess paymentProcess = BeanUtil.copyProperties(paymentProcessDto, SalesReceiptPaymentProcess.class);
                paymentProcess.setSalesReceiptId(dto.getId());
                return paymentProcess;
            }).collect(Collectors.toList());
            processService.saveBatch(processList);
        }
        SalesReceiptPaymentTask paymentTask = paymentTaskMapper.selectOne(Wrappers.<SalesReceiptPaymentTask>lambdaQuery()
                .eq(SalesReceiptPaymentTask::getSalesReceiptId, dto.getId())
        );
        if (Optional.ofNullable(paymentTask).isPresent()) {
            BeanUtil.copyProperties(dto, paymentTask);
            paymentTask.setCustomerPaymentProcess(customerPaymentProcess);
            paymentTaskMapper.updateById(paymentTask);
            return R.ok("保存成功");
        }
        SalesReceiptPaymentTask addPaymentTask = BeanUtil.copyProperties(dto, SalesReceiptPaymentTask.class);
        addPaymentTask.setCustomerPaymentProcess(customerPaymentProcess);
        addPaymentTask.setSalesReceiptId(dto.getId());
        paymentTaskMapper.insert(addPaymentTask);
        return R.ok("保存成功");
    }

    @Override
    public SalesReceiptPaymentTaskVO getPaymentTask(Long salesReceiptId) {
        SalesReceiptPaymentTaskVO salesReceiptPaymentTaskVO = new SalesReceiptPaymentTaskVO();
        salesReceiptPaymentTaskVO.setId(salesReceiptId);
        salesReceiptPaymentTaskVO.setTaskFlag(Boolean.FALSE);
        SalesReceipt salesReceipt = baseMapper.selectById(salesReceiptId);
        SalesReceiptPaymentDetailVO paymentDetailVO = BeanUtil.copyProperties(salesReceipt, SalesReceiptPaymentDetailVO.class);
        salesReceiptPaymentTaskVO.setPaymentDetailVO(paymentDetailVO);
        SalesReceiptPaymentTask paymentTask = paymentTaskMapper.selectOne(Wrappers.<SalesReceiptPaymentTask>lambdaQuery()
                .eq(SalesReceiptPaymentTask::getSalesReceiptId, salesReceiptId));
        if (Optional.ofNullable(paymentTask).isPresent()){
            SalesReceiptTaskDetailVO taskDetailVO = BeanUtil.copyProperties(paymentTask, SalesReceiptTaskDetailVO.class);
            taskDetailVO.setPaymentRiskStr(EnumUtils.getNameByValue(PaymentRiskEnum.class, paymentTask.getPaymentRisk()));
            String customerPaymentProcess = paymentTask.getCustomerPaymentProcess();
            if (StrUtil.isNotEmpty(customerPaymentProcess)){
                List<String> list = Arrays.asList(customerPaymentProcess.split(">"));
                taskDetailVO.setCustomerPaymentProcessList(list);
            }
            salesReceiptPaymentTaskVO.setTaskDetailVO(taskDetailVO);
            salesReceiptPaymentTaskVO.setTaskFlag(Boolean.TRUE);
        }
        List<SalesReceiptPaymentProcess> processList = paymentProcessMapper.selectList(Wrappers.<SalesReceiptPaymentProcess>lambdaQuery()
                .eq(SalesReceiptPaymentProcess::getSalesReceiptId, salesReceiptId));
        if (CollectionUtil.isNotEmpty(processList)){
            salesReceiptPaymentTaskVO.setPaymentProcessVOList(processList.stream().map(p ->{
                SalesReceiptPaymentProcessVO paymentProcessVO = BeanUtil.copyProperties(p, SalesReceiptPaymentProcessVO.class);
                paymentProcessVO.setApprovalStatusTxt(EnumUtils.getNameByValue(PaymentProcessStatusEnum.class, p.getApprovalStatus()));
                return paymentProcessVO;
            }).collect(Collectors.toList()));
        }
        return salesReceiptPaymentTaskVO;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public Boolean taskReminder() {
        List<SalesReceiptTaskReminderVO> reminderVOS = baseMapper.selTaskReminder();
        if (CollectionUtil.isNotEmpty(reminderVOS)) {
            for (SalesReceiptTaskReminderVO reminderVO : reminderVOS) {
                TaskSaveDto taskSaveDto = new TaskSaveDto();
                taskSaveDto.setThirdTaskId(String.valueOf(reminderVO.getId()));
                taskSaveDto.setSourceEnum(SourceEnum.PROJECT);
                taskSaveDto.setTypeEnum(TaskTypeEnum.PAYMENT_TASK);
                taskSaveDto.setJobTypeEnum(JobTypeEnum.TASK);
                taskSaveDto.setRelateTypeEnum(RelateTypeEnum.PROJECT);
                taskSaveDto.setPriorityEnum(PriorityEnum.MEDIUM);
                taskSaveDto.setStatusEnum(TaskStatusEnum.IN_PROGRESS);
                taskSaveDto.setName("销售收款任务跟进提醒");
                String description = Strings.nullToEmpty("你有一笔销售合同"
                        + reminderVO.getContractName() +
                        "的"
                        + reminderVO.getCurrentPaymentName()
                        + "里程碑已于" + reminderVO.getActualCompleteDate()
                        + "达成，请及时更新回款任务及跟进客户回款~");
                taskSaveDto.setDescription(description);
                String url = StrUtil.EMPTY;
                try {
                    String contractName = URLEncoder.encode(reminderVO.getContractName(), "UTF-8");
                    String paymentName = StrUtil.isEmpty(reminderVO.getCurrentPaymentName()) ? "" : URLEncoder.encode(reminderVO.getCurrentPaymentName(), "UTF-8");
                    url = salesReceiptUrl +
                            Base64.encode(CharSequenceUtil.format(redirect, contractName,paymentName, ""), Charsets.UTF_8);
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                taskSaveDto.setJumpLink(url);

                taskSaveDto.setReminderEnumList(Lists.newArrayList(ReminderTypeEnum.TODO));
                taskSaveDto.setStartDate(reminderVO.getActualCompleteDate().atStartOfDay());
                taskSaveDto.setEndDate(reminderVO.getTargetPaymentDate().plusDays(1).atStartOfDay());
                taskSaveDto.setRelateId(String.valueOf(reminderVO.getContractId()));
                taskSaveDto.setRelateName(reminderVO.getContractName());
                // 负责人信息
                List<LeaderDto> leaderDtos = new ArrayList<>();
                LeaderDto leaderDto = new LeaderDto();
                leaderDto.setLeaderId(reminderVO.getSalesmanUserId());
                leaderDto.setLeader(reminderVO.getSalesmanUserName());
                leaderDtos.add(leaderDto);
                taskSaveDto.setLeaderList(leaderDtos);

                //发送企微消息
                WeComModel weComModel = new WeComModel();
                weComModel.setSource(SourceEnum.PROJECT.getValue());
                weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
                weComModel.setTitle("销售收款任务跟进提醒");
                weComModel.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
                weComModel.setSender(SourceEnum.PROJECT.getName());
                weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
                weComModel.setRedirectUrl(url);
                List<BcpMessageTargetDTO> list = new ArrayList<>();
                BcpMessageTargetDTO salesmanDto = new BcpMessageTargetDTO();
                salesmanDto.setTargetId(reminderVO.getSalesmanUserId().toString());
                salesmanDto.setTargetName(reminderVO.getSalesmanUserName());
                list.add(salesmanDto);

                weComModel.setTargetList(list);
                weComModel.setContent(description +
                        "<a href=\""+url+"\">查看详情</a>");
                weComModel.setSenderId(10000L);
                weComModel.setSender("admin");

                try {
                    remoteTaskService.saveOrUpdate(taskSaveDto);
                    remoteSendMsgService.sendMsg(weComModel);
                    PushLog pushLog = new PushLog();
                    pushLog.setId(IdWorker.getId());
                    pushLog.setContentId(reminderVO.getId());
                    pushLog.setContentType(NumberUtils.INTEGER_ONE);
                    pushLogMapper.insert(pushLog);
                } catch (Exception e) {
                    log.info("任务推送失败: {}", e.getMessage());
                    e.printStackTrace();
                }

            }
        }
        return true;
    }

    @Override
    public List<Long> findIds(SalesReceiptDTO dto) {
        dto = limitDto(dto);
        List<SalesReceiptVO> salesReceiptList = new ArrayList<>();
        if ((Optional.ofNullable(dto.getAuthority()).isPresent() && CollUtil.isNotEmpty(dto.getAuthUserIdList()))
                || Boolean.TRUE.equals(dto.getIsAll())){
            salesReceiptList = baseMapper.queryPage(limitDto(dto));
        }
        List<Long> idList = salesReceiptList.stream().map(SalesReceiptVO::getId).collect(Collectors.toList());
        return idList;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean pushMessage() {
        //获取全部的未收款内容
        List<SalesReceiptPushVO> salesReceiptPushVoS = baseMapper.queryPushVo();
        for (SalesReceiptPushVO vo : salesReceiptPushVoS) {
            WeComModel weComModel = new WeComModel();
            MailModel model;
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle("销售收款任务超期提醒");
            weComModel.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
            String url = StrUtil.EMPTY;
            try {
                String contractName = URLEncoder.encode(vo.getContractName(), "UTF-8");
                String paymentName = URLEncoder.encode(vo.getPaymentName(), "UTF-8");
                String paymentStatus = URLEncoder.encode(vo.getPaymentStatus(), "UTF-8");
                url = salesReceiptUrl +
                        Base64.encode(CharSequenceUtil.format(redirect, contractName,paymentName,paymentStatus), Charsets.UTF_8);
                weComModel.setRedirectUrl(url);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            List<BcpMessageTargetDTO> list = new ArrayList<>();
            BcpMessageTargetDTO salesmanDto = new BcpMessageTargetDTO();
            salesmanDto.setTargetId(vo.getSalesmanUserId().toString());
            salesmanDto.setTargetName(vo.getSalesmanUserName());
            list.add(salesmanDto);

            weComModel.setTargetList(list);
            weComModel.setContent("你有一笔销售合同"
                    + vo.getContractName() + "的"
                    + vo.getPaymentName() + "距离目标回款日期"
                    + vo.getTargetPaymentDate() + "已超期，请及时跟进客户回款~");
            weComModel.setSenderId(10000L);
            weComModel.setSender("admin");
            model = MailModel.from(WeComModel.to(weComModel));
            weComModel.setContent("你有一笔销售合同"
                    + vo.getContractName() + "的"
                    + vo.getPaymentName() + "距离目标回款日期"
                    + vo.getTargetPaymentDate() + "已超期，请及时跟进客户回款~" +
                    "<a href=\""+url+"\">查看详情</a>");
            // 发送企微消息和门户消息
            try {
                remoteSendMsgService.sendMsg(weComModel);
                remoteMailService.sendMsg(model);
                PushLog pushLog = new PushLog();
                pushLog.setId(IdWorker.getId());
                pushLog.setContentId(vo.getId());
                pushLog.setContentType(NumberUtils.INTEGER_ZERO);
                pushLogMapper.insert(pushLog);
            } catch (Exception e) {
                log.info("中台企业微信/门户消息推送失败: {}", e.getMessage());
                e.printStackTrace();
            }
        }
        return true;

    }


    private SalesReceiptVO dateVO(SalesReceiptVO r, Map<Integer,String> attributableSubjectMap ,Map<Long, List<SalesReceiptPaymentProcess>> processListMap) {
        MoneyUtils moneyUtils = MoneyUtils.getInstance();
        r.setContractMoney(moneyUtils.strTransType(r.getContractMoney()));
        r.setAccumulatedAmount(moneyUtils.strTransType(r.getAccumulatedAmount()));
        r.setCurrentPaymentMoney(moneyUtils.strTransType(r.getCurrentPaymentMoney()));
        r.setActualPaymentAmount(moneyUtils.strTransType(r.getActualPaymentAmount()));
        r.setPaymentDifference(moneyUtils.strTransType(r.getPaymentDifference()));
        r.setEstimateCurrentPaymentMoney(moneyUtils.strTransType(r.getEstimateCurrentPaymentMoney()));
        //累计收款比例 *100%
        r.setCollectionRatio(moneyUtils.proportion(r.getCollectionRatio()));
        r.setPaymentProportion(moneyUtils.proportion(r.getPaymentProportion()));

        r.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, r.getProjectStatus()));
        r.setAttributableSubjectTxt(Optional.ofNullable(r.getAttributableSubject()).isPresent()?
                attributableSubjectMap.getOrDefault(Integer.valueOf(r.getAttributableSubject()),StrUtil.EMPTY):StrUtil.EMPTY);
        r.setPaymentStatus(EnumUtils.getNameByValue(PaymentStatusEnum.class, r.getPaymentStatus()));
        r.setPaymentRiskStr(EnumUtils.getNameByValue(PaymentRiskEnum.class, r.getPaymentRisk()));

        List<SalesReceiptPaymentProcess> processList = processListMap.getOrDefault(r.getId(), ListUtil.empty());
        if (CollectionUtil.isNotEmpty(processList)){
            String customerPaymentProcess = processList.stream().map(SalesReceiptPaymentProcess::getApprovalProcess).collect(Collectors.joining(">"));
            r.setCustomerPaymentProcess(customerPaymentProcess);
            List<SalesReceiptPaymentProcess> JxcProcessList = processList.stream().filter(p -> PaymentProcessStatusEnum.JXZ.getValue().equals(p.getApprovalStatus())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(JxcProcessList)){
                r.setCurrentApprovalProcess(JxcProcessList.get(0).getApprovalProcess());
            }
            List<SalesReceiptPaymentProcess> ywcProcessList = processList.stream().filter(p -> PaymentProcessStatusEnum.YWC.getValue().equals(p.getApprovalStatus())).collect(Collectors.toList());
            BigDecimal divide = new BigDecimal(ywcProcessList.size()).divide(new BigDecimal(processList.size()), 4, RoundingMode.HALF_UP);
            r.setApprovalProgress(moneyUtils.proportion(divide));
        }
        return r;
    }

    /**
     * 【系统时间】-【里程碑达成时间】大于等于7天，且【款项状态】等于待回款/部分回款，【任务状态】等于未更新；
     *  每七天推送一次
     * @return
     */
    @Override
    public Boolean taskUpdateReminder() {
        List<SalesReceiptPushVO> pushVOList = baseMapper.queryTaskUpdateReminderVo();
        for (SalesReceiptPushVO vo : pushVOList) {
            if (vo.getDateDifference() % 7 != NumberUtils.INTEGER_ZERO){
                continue;
            }
            WeComModel weComModel = new WeComModel();
            MailModel model;
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle("销售收款任务更新提醒");
            weComModel.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
            String url = StrUtil.EMPTY;
            try {
                String contractName = URLEncoder.encode(vo.getContractName(), "UTF-8");
                String paymentName = URLEncoder.encode(vo.getPaymentName(), "UTF-8");
                String paymentStatus = URLEncoder.encode(vo.getPaymentStatus(), "UTF-8");
                url = salesReceiptUrl +
                        Base64.encode(CharSequenceUtil.format(redirect, contractName,paymentName,paymentStatus), Charsets.UTF_8);
                weComModel.setRedirectUrl(url);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            List<BcpMessageTargetDTO> list = new ArrayList<>();
            BcpMessageTargetDTO salesmanDto = new BcpMessageTargetDTO();
            salesmanDto.setTargetId(vo.getSalesmanUserId().toString());
            salesmanDto.setTargetName(vo.getSalesmanUserName());
            list.add(salesmanDto);

            weComModel.setTargetList(list);
            weComModel.setContent("你有一笔销售合同"
                    + vo.getContractName() + "的"
                    + vo.getPaymentName() + "已"
                    + vo.getDateDifference() + "天未更新，请及时更新回款任务及跟进客户回款~");
            weComModel.setSenderId(10000L);
            weComModel.setSender("admin");
            model = MailModel.from(WeComModel.to(weComModel));
            weComModel.setContent("你有一笔销售合同"
                    + vo.getContractName() + "的"
                    + vo.getPaymentName() + "已"
                    + vo.getDateDifference() + "天未更新，请及时更新回款任务及跟进客户回款~" +
                    "<a href=\""+url+"\">查看详情</a>");
            // 发送企微消息和门户消息
            try {
                remoteSendMsgService.sendMsg(weComModel);
                remoteMailService.sendMsg(model);
            } catch (Exception e) {
                log.info("中台企业微信/门户消息推送失败: {}", e.getMessage());
                e.printStackTrace();
            }
        }
        return true;
    }

    /**
     * 【系统时间】-【里程碑达成时间】等于3天，且【款项状态】等于待回款/部分回款，【任务状态】等于未维护；
     * 仅推送一次
     * @return
     */
    @Override
    public Boolean TaskNotUpdatedReminder() {
        List<SalesReceiptPushVO> pushVOList = baseMapper.queryTaskNotUpdateReminderVo();
        for (SalesReceiptPushVO vo : pushVOList) {
            WeComModel weComModel = new WeComModel();
            MailModel model;
            weComModel.setSource(SourceEnum.PROJECT.getValue());
            weComModel.setType(MsgTypeEnum.TEXT_MSG.getValue());
            weComModel.setTitle("销售收款任务更新提醒");
            weComModel.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
            weComModel.setSender(SourceEnum.PROJECT.getName());
            weComModel.setTargetType(TargetTypeEnum.USERS.getValue());
            String url = StrUtil.EMPTY;
            try {
                String contractName = URLEncoder.encode(vo.getContractName(), "UTF-8");
                String paymentName = URLEncoder.encode(vo.getPaymentName(), "UTF-8");
                String paymentStatus = URLEncoder.encode(vo.getPaymentStatus(), "UTF-8");
                url = salesReceiptUrl +
                        Base64.encode(CharSequenceUtil.format(redirect, contractName,paymentName,paymentStatus), Charsets.UTF_8);
                weComModel.setRedirectUrl(url);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            List<BcpMessageTargetDTO> list = new ArrayList<>();
            BcpMessageTargetDTO salesmanDto = new BcpMessageTargetDTO();
            salesmanDto.setTargetId(vo.getSalesmanUserId().toString());
            salesmanDto.setTargetName(vo.getSalesmanUserName());
            list.add(salesmanDto);

            weComModel.setTargetList(list);
            weComModel.setContent("你有一笔销售合同"
                    + vo.getContractName() + "的"
                    + vo.getPaymentName() + "里程碑已于"
                    + vo.getActualCompleteDate() + "达成3天，请及时更新回款任务及跟进客户回款~");
            weComModel.setSenderId(10000L);
            weComModel.setSender("admin");
            model = MailModel.from(WeComModel.to(weComModel));
            weComModel.setContent("你有一笔销售合同"
                    + vo.getContractName() + "的"
                    + vo.getPaymentName() + "里程碑已于"
                    + vo.getActualCompleteDate() + "达成3天，请及时更新回款任务及跟进客户回款~" +
                    "<a href=\""+url+"\">查看详情</a>");
            // 发送企微消息和门户消息
            try {
                remoteSendMsgService.sendMsg(weComModel);
                remoteMailService.sendMsg(model);
                PushLog pushLog = new PushLog();
                pushLog.setId(IdWorker.getId());
                pushLog.setContentId(vo.getId());
                pushLog.setContentType(NumberUtils.INTEGER_TWO);
                pushLogMapper.insert(pushLog);
            } catch (Exception e) {
                log.info("中台企业微信/门户消息推送失败: {}", e.getMessage());
                e.printStackTrace();
            }
        }
        return true;
    }

    @Override
    public Boolean importExcel(List<SakesReceiptImportExcelDTO> sakesReceiptImportExcelDTOList) {
        List<Long> ids = sakesReceiptImportExcelDTOList.stream().map(SakesReceiptImportExcelDTO::getId).collect(Collectors.toList());
        List<SalesReceipt> salesReceiptList = baseMapper.selectList(Wrappers.<SalesReceipt>lambdaQuery()
                .in(SalesReceipt::getId, ids));
        if (CollectionUtil.isEmpty(salesReceiptList)){
            return false;
        }
        List<Long> existIds = salesReceiptList.stream().map(SalesReceipt::getId).collect(Collectors.toList());
        List<SakesReceiptImportExcelDTO> existSakesReceipt = sakesReceiptImportExcelDTOList.stream().filter(s -> existIds.contains(s.getId())).collect(Collectors.toList());
        Map<Long, SalesReceipt> salesReceiptMap = salesReceiptList.stream().collect(Collectors.toMap(SalesReceipt::getId, a -> a, (a, b) -> a));
        List<SalesReceiptPaymentTask> paymentTaskList = paymentTaskMapper.selectList(Wrappers.<SalesReceiptPaymentTask>lambdaQuery()
                .in(SalesReceiptPaymentTask::getSalesReceiptId, existIds));
        Map<Long, SalesReceiptPaymentTask> paymentTaskMap = paymentTaskList.stream().collect(Collectors.toMap(SalesReceiptPaymentTask::getSalesReceiptId, a -> a, (a, b) -> a));

        for (SakesReceiptImportExcelDTO dto : existSakesReceipt) {
            SalesReceipt salesReceipt = salesReceiptMap.getOrDefault(dto.getId(),null);

            if (!Optional.ofNullable(salesReceipt).isPresent()){
                continue;
            }
            salesReceipt.setExpectedPaymentDate(Optional.ofNullable(dto.getExpectedPaymentDate()).isPresent() ?
                    LocalDate.parse(DateUtils.formatTime(dto.getExpectedPaymentDate()).replaceAll(StrPool.SLASH, StrPool.DASHED)):null);
            salesReceipt.setCustomerPaymentWindowPeriod(dto.getCustomerPaymentWindowPeriod());
            SalesReceiptPaymentTask paymentTask = paymentTaskMap.getOrDefault(salesReceipt.getId(),null);
            if (Optional.ofNullable(paymentTask).isPresent()) {
                BeanUtil.copyProperties(dto, paymentTask);
                paymentTask.setCustomerPaymentProcess(Optional.ofNullable(dto.getCustomerPaymentProcessList()).isPresent() ?
                        dto.getCustomerPaymentProcessList().replace("-",">"):null);
                paymentTask.setUpdateDate(Optional.ofNullable(dto.getUpdateDate()).isPresent() ?
                        DateUtils.formatTime(dto.getUpdateDate()).replaceAll(StrPool.SLASH, StrPool.DASHED):null);
                paymentTask.setPaymentRisk(EnumUtils.getValueByName(PaymentRiskEnum.class, dto.getPaymentRiskStr()));
                paymentTaskMapper.updateById(paymentTask);
                baseMapper.updateById(salesReceipt);
            }else {
                SalesReceiptPaymentTask addPaymentTask = BeanUtil.copyProperties(dto, SalesReceiptPaymentTask.class);
                addPaymentTask.setCustomerPaymentProcess(Optional.ofNullable(dto.getCustomerPaymentProcessList()).isPresent() ?
                        dto.getCustomerPaymentProcessList().replace("-",">"):null);
                addPaymentTask.setUpdateDate(Optional.ofNullable(dto.getUpdateDate()).isPresent() ?
                        DateUtils.formatTime(dto.getUpdateDate()).replaceAll(StrPool.SLASH, StrPool.DASHED):null);
                addPaymentTask.setSalesReceiptId(salesReceipt.getId());
                addPaymentTask.setPaymentRisk(EnumUtils.getValueByName(PaymentRiskEnum.class, dto.getPaymentRiskStr()));

                paymentTaskMapper.insert(addPaymentTask);
                baseMapper.updateById(salesReceipt);
            }

        }
        return true;
    }

    @Override
    public R<String> importBudgetMarking(List<SakesReceiptImportBudgetMarkingDTO> list) {
        List<SalesReceipt> updateList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            List<SalesReceipt> salesReceiptList = baseMapper.selectList(Wrappers.<SalesReceipt>lambdaQuery());
            Map<Long, SalesReceipt> salesReceiptByPaymentId = salesReceiptList.stream().collect(Collectors.toMap(SalesReceipt::getPaymentId, a -> a, (a, b) -> a));

            // 校验
            StringBuilder errorAll = new StringBuilder();
            this.checkImport(list,salesReceiptByPaymentId,errorAll);
            if (errorAll.toString().contains("不")) {
                return R.failed("导入失败,原因是:[" + errorAll.toString()+"]");
            }
            list.stream().forEach(dto ->{
                SalesReceipt salesReceipt = salesReceiptByPaymentId.getOrDefault(Long.valueOf(dto.getPaymentId()), new SalesReceipt());
                if (Optional.ofNullable(salesReceipt).isPresent()) {
                    salesReceipt.setBudgetMarking(dto.getBudgetMarking());
                    updateList.add(salesReceipt);
                }
            });

            return this.updateBatchById(updateList) ? R.ok("导入成功") : R.failed("导入失败");
        }

        return R.ok("无数据导入");
    }

    private void checkImport(List<SakesReceiptImportBudgetMarkingDTO> list,Map<Long, SalesReceipt> salesReceiptByPaymentId,StringBuilder errorAll) {
        AtomicInteger i = new AtomicInteger(3);
        list.stream().forEach(dto ->{
            StringBuilder error = new StringBuilder();
            error.append("第").append(i.getAndIncrement()).append("行: ");
            try {
                if (null == dto.getPaymentId()) {
                    error.append("款项id不能为空。");
                }else if (!dto.getPaymentId().matches("\\d+") || Long.parseLong(dto.getPaymentId()) > Long.MAX_VALUE || Long.parseLong(dto.getPaymentId()) < Long.MIN_VALUE){
                    error.append("款项id不是正确的ID值");
                }else if (!salesReceiptByPaymentId.containsKey(Long.parseLong(dto.getPaymentId()))){
                    error.append("款项id不存在。");
                }
            }catch (Exception e){
                error.append("款项id不是正确的ID值");
            }

            if (null == dto.getBudgetMarking()) {
                error.append("预算标注不能为空。");
            }else if (100<dto.getBudgetMarking().length()){
                error.append("预算标注字符数不能超过100。");
            }
            if (error.toString().contains("不")) {
                errorAll.append(error.append("<br/>"));
            }
        });
    }

    @Override
    public void initPaymentProcess() {
        List<SalesReceiptPaymentProcess> processList = new ArrayList<>();
        List<SalesReceiptPaymentTask> paymentTaskList = paymentTaskMapper.selectList(Wrappers.<SalesReceiptPaymentTask>lambdaQuery());
        paymentTaskList.stream().forEach(paymentTask -> {
            String customerPaymentProcess = paymentTask.getCustomerPaymentProcess();
            if (StrUtil.isNotEmpty(customerPaymentProcess)){
                List<String> customerPaymentProcessList = Arrays.asList(customerPaymentProcess.split(">"));
                List<SalesReceiptPaymentProcess> collect = customerPaymentProcessList.stream().map(process -> {
                    SalesReceiptPaymentProcess paymentProcess = new SalesReceiptPaymentProcess();
                    paymentProcess.setApprovalProcess(process);
                    paymentProcess.setApprovalStatus(PaymentProcessStatusEnum.WKS.getValue());
                    paymentProcess.setSalesReceiptId(paymentTask.getSalesReceiptId());
                    return paymentProcess;
                }).collect(Collectors.toList());
                processList.addAll(collect);
            }
        });
        processService.saveBatch(processList);
    }
}




