package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 *  结算状态Enum
 *
 * author caihf
 * since 2023-09-01
 */
@Getter
public enum TabTypeEnum implements ValueEnum<Integer> {

    /**
     * 待审批
     */
    PENDING_APPROVAL(1,"待审批"),
    /**
     * 待审核
     */
    PENDING_REVIEW(2,"待审核"),
    /**
     * 待结算
     */
    PENDING_SETTLEMENT(3,"待结算"),
    // 部分结算
    PARTIAL_SETTLEMENT(4,"部分结算"),
    /**
     * 已结算
     */
    ALREADY_SETTLED(5,"已结算"),

    /**
     * 无需结算
     */
    NO_SETTLEMENT_REQUIRED(6,"无需结算")
   ;




    private final Integer value;

    private final String name;

    TabTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
