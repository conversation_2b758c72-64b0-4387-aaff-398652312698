package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/06/09
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("financial_docimagefile")
public class FinancialDocImageFile extends Model<FinancialDocImageFile> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件id
     */
    private Long docId;
    /**
     * imagefileid
     */
    private Long imageFileId;

    /**
     * 文件名
     */
    private String imageFileName;
    /**
     * '文件上传人id'
     */
    private Long operateUserId;

    /**
     * 流程id
     */
    private Long requestId;


}
