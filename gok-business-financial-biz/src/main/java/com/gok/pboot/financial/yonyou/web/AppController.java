/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 用友应用控制器
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.web;

import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.yonyou.model.AccessTokenResponse;
import com.gok.pboot.financial.yonyou.model.RefreshTokenResponse;
import com.gok.pboot.financial.yonyou.model.UserTokenResponse;
import com.gok.pboot.financial.yonyou.service.IYyAuthService;
import com.gok.pboot.financial.yonyou.service.IYySyncService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * 用友应用控制器
 * 提供用友开放平台认证相关的REST接口
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Inner(false)
@RestController
public class AppController {


    @Autowired
    private IYyAuthService yyAuthService;

    @Autowired
    private IYySyncService yySyncService;


    /**
     * 多数据中心适配之前通过appKey获取accessToken
     *
     * @return {@link AccessTokenResponse} 访问令牌响应
     */
    @GetMapping("/getAccessToken")
    public String getAccessToken() {
        return yyAuthService.getAccessToken();
    }

    /**
     * 多数据中心适配之后通过tenantId和appKey获取accessToken
     *
     * @return {@link AccessTokenResponse} 访问令牌响应
     * @throws IOException              IO异常
     * @throws InvalidKeyException      无效键异常
     * @throws NoSuchAlgorithmException 算法不存在异常
     */
    @GetMapping("/getAccessTokenV2")
    public AccessTokenResponse getAccessTokenV2() throws IOException, InvalidKeyException, NoSuchAlgorithmException {
        return yyAuthService.getAccessTokenV2();
    }

    /**
     * 通过appkey获取租户id
     *
     * @return {@link String} 租户ID
     * @throws IOException              IO异常
     * @throws InvalidKeyException      无效键异常
     * @throws NoSuchAlgorithmException 算法不存在异常
     */
    @GetMapping("/getTenantId")
    public String getTenantId() throws IOException, InvalidKeyException, NoSuchAlgorithmException {
        return yyAuthService.getTenantId();
    }

    /**
     * 通过accessToken和友空间免登code获取userToken
     *
     * @param accessToken 开放平台accessToken
     * @param code        友空间免登code
     * @return {@link UserTokenResponse} 用户令牌响应
     * @throws Exception 异常
     */
    @GetMapping("/getUserToken")
    public UserTokenResponse getUserToken(@RequestParam("access_token") String accessToken, @RequestParam String code) throws Exception {
        return yyAuthService.getUserToken(accessToken, code);
    }

    /**
     * 使用refreshToken刷新userToken
     *
     * @param refreshToken 刷新令牌
     * @return {@link RefreshTokenResponse} 刷新令牌响应
     * @throws Exception 异常
     */
    @GetMapping("/refreshUserToken")
    public RefreshTokenResponse refreshUserToken(@RequestParam String refreshToken) throws Exception {
        return yyAuthService.refreshUserToken(refreshToken);
    }

    /**
     * 同步部门到用友
     *
     * @return {@link String }
     */
    @GetMapping("/syncDept")
    public String syncDept() {
        yySyncService.syncDept();
        return "同步成功";
    }

    /**
     * 同步主体
     *
     * @return {@link String }
     */
    @GetMapping("/syncCompany")
    public String syncCompany() {
        yySyncService.syncCompany();
        return "同步成功";
    }

}
