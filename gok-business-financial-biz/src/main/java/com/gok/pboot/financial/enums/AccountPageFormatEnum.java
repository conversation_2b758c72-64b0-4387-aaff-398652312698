package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 账页格式 Enum
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum AccountPageFormatEnum implements ValueEnum<Integer> {

    /**
     * 金额式
     */
    AMOUNT_FORMULA(0, "金额式"),

    /**
     * 外币金额式
     */
    FOREIGN_CURRENCY_AMOUNT(1, "外币金额式");

    private final Integer value;

    private final String name;

    AccountPageFormatEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
