package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.AccountsReceivableDetail;
import com.gok.pboot.financial.vo.AccountsReceivableDetailVO;
import com.gok.pboot.financial.vo.PayAndInvoicingVO;

import java.util.List;

/**
 * 应收账款详情 Service
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface IAccountsReceivableDetailService extends IService<AccountsReceivableDetail> {

    /**
     * 应收账款明细
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link AccountsReceivableDetailVO}
     */
    AccountsReceivableDetailVO accountsReceivableDetail(Long projectId, Long contractId);

    /**
     * 获取回款与发票月份详情
     *
     * @param yearMonthDate 年月
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link List}<{@link PayAndInvoicingVO}>
     */
    PayAndInvoicingVO payBackAndInvoicingDetailByDate(String yearMonthDate, Long projectId, Long contractId);

    /**
     * 获取回款与发票详情
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link PayAndInvoicingVO}
     */
    PayAndInvoicingVO payBackAndInvoicingDetail(Long projectId, Long contractId);
}
