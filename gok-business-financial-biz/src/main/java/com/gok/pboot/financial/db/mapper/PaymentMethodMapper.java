package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.PaymentMethod;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.vo.PaymentMethodVO;
import org.apache.ibatis.annotations.Param;

/**
 * 收款方式
 *
 * <AUTHOR>
 * @description 针对表【payment_method(收款方式)】的数据库操作Mapper
 * @createDate 2023-09-27 10:46:49
 * @Entity com.gok.pboot.financial.db.entity.PaymentMethod
 */
public interface PaymentMethodMapper extends BaseMapper<PaymentMethod> {

    /**
     * 分页查询
     *
     * @param page 查询条件
     * @return {@link Page}<{@link PaymentMethodVO}>
     */
    Page<PaymentMethodVO> queryPaymentMethodPage(@Param("page") Page<PaymentMethodVO> page);
}




