package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.db.entity.AccountType;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.db.mapper.AccountTypeMapper;
import com.gok.pboot.financial.dto.AccountTypeDTO;
import com.gok.pboot.financial.dto.AccountTypePageDTO;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IAccountTypeService;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.vo.AccountTypeVO;
import com.gok.pboot.financial.vo.AccountTypePageVO;
import com.gok.pboot.financial.vo.AccountingSystemCorrelationVO;
import com.google.errorprone.annotations.Var;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 科目类型
 * <AUTHOR>
 */

@Service
@RequiredArgsConstructor
public class AccountTypeServiceImpl extends ServiceImpl<AccountTypeMapper, AccountType>
        implements IAccountTypeService {

    private final LogRecordUtils logRecordUtils;

    @Override
    public Page<AccountTypePageVO> findPage(AccountTypePageDTO dto) {
        // 1、获取分页信息
        Page<AccountType> page = new Page<>(dto.getCurrent(), dto.getSize());
        Page<AccountTypePageVO> accountingSetPage = baseMapper.findPage(page,dto);

        return accountingSetPage;
    }

    @Override
    public R<AccountTypeVO> info(Long id) {
        AccountTypeVO vo = baseMapper.selById(id);
        if(!Optional.ofNullable(vo).isPresent()){
            return R.failed("查询不到数据");
        }

        return R.ok(vo);
    }

    @Override
    public R<String> addOrEdit(AccountTypeDTO dto) {
        String s;
        // 1、获取数据
        AccountType as = baseMapper.selectOne(Wrappers.<AccountType>lambdaQuery()
                .eq(AccountType::getId, dto.getId())
        );
        List<String> codeList = baseMapper.selectList(new QueryWrapper<>()).stream().map(AccountType::getTypeCode).collect(Collectors.toList());

        if (Optional.ofNullable(as).isPresent()) {
            if (!as.getTypeCode().equals(dto.getTypeCode())){
                if (codeList.contains(dto.getTypeCode())) {
                    return R.failed("类型编码不可重复");
                }
            }
            // 2.1、记录日志
            s = OperationEnum.EDIT_ACCOUNT_TYPE.getName() + "【"
                    + dto.getTypeCode() + "】【" + dto.getTypeName() + "】";
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNT_TYPE,
                    OperationEnum.EDIT_ACCOUNT_TYPE, s);
            // 2.2、更新数据
            AccountType accountType = BeanUtil.copyProperties(dto, AccountType.class);
            this.updateById(accountType);
            return R.ok("编辑成功");
        }
        if (codeList.contains(dto.getTypeCode())) {
            return R.failed("类型编码不可重复");
        }
        // 3、新增操作
        AccountType accountType = BeanUtil.copyProperties(dto, AccountType.class, "id");
        // 3.1、记录日志
        s = OperationEnum.ADD_ACCOUNT_TYPE.getName() + "【"
                + dto.getTypeCode() + "】【" + dto.getTypeName() + "】";
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNT_TYPE,
                OperationEnum.ADD_ACCOUNT_TYPE, s);
        // 3.2、入库
        this.save(accountType);
        return R.ok("新增成功");
    }

    @Override
    public R<String> del(List<Long> idList) {
        // 1、防止空数组删除全数据
        if (ObjectUtils.isEmpty(idList)) {
            throw new BusinessException("删除接口传参有误");
        }
        List<AccountingSystemCorrelationVO> correlationVOList = baseMapper.selCorrelationNum(idList);
        List<Long> filterIds = correlationVOList.stream()
                .filter(c -> NumberUtils.INTEGER_ZERO.equals(c.getAccountNum()))
                .collect(Collectors.toList())
                .stream().map(AccountingSystemCorrelationVO::getId)
                .collect(Collectors.toList());

        if (ObjectUtils.isEmpty(filterIds)) {
            return R.ok("删除成功");
        }
        // 2、查询数据记录日志
        StringBuilder s = new StringBuilder();
        s.append(OperationEnum.DELETE_ACCOUNT_TYPE.getName());
        baseMapper.selectList(Wrappers.<AccountType>lambdaQuery().in(AccountType::getId, filterIds))
                .forEach(a -> s.append("【").append(a.getTypeCode()).append(a.getTypeCode()).append("】"));
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNT_TYPE,
                OperationEnum.DELETE_ACCOUNT_TYPE, s.toString());
        // 3、删除数据
        baseMapper.delByIds(filterIds);
        return R.ok("删除成功");
    }

    @Override
    public List<AccountTypeVO> findList(List<Long> idList) {
        List<AccountTypeVO> typeList = baseMapper.findList(idList);
        return typeList;
    }
}
