package com.gok.pboot.financial.common.client;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Get;
import com.dtflys.forest.annotation.Post;
import com.dtflys.forest.annotation.Var;

import java.util.List;

/**
 * forest 请求第三方接口
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
public interface ForestClient {

    /**
     * 获取DBApi token
     *
     * @param url    路径
     * @param appId  appId
     * @param secret 密钥
     * @return token
     */
    @Get("${url}/token/generate?appid=${appId}&secret=${secret}")
    JSONObject getDbApiToken(@Var("url") String url, @Var("appId") String appId, @Var("secret") String secret);

    /**
     * 项目管理-字典查询
     *
     * @param url     路径
     * @param token   token
     * @param fieldId 字典id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/project/dict", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject projectDict(@Var("url") String url, @Var("token") String token,
                           @Body("fieldid") String fieldId);

    /**
     * 项目管理-字典查询
     *
     * @param url   路径
     * @param token token
     * @param id    字典id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/project/new/dict", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject projectDictNew(@Var("url") String url, @Var("token") String token,
                              @Body("id") String id);

    /**
     * 获取回款明细
     *
     * @param url        路径
     * @param token      token
     * @param projectId  项目id
     * @param contractId 合同id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/payBackDetails", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject payBackDetails(@Var("url") String url, @Var("token") String token,
                              @Body("projectId") String projectId,
                              @Body("contractId") String contractId);

    /**
     * 获取发票数据
     *
     * @param url        路径
     * @param token      token
     * @param projectId  项目id
     * @param contractId 合同id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/invoiceDetails", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject invoicingDetails(@Var("url") String url, @Var("token") String token,
                                @Body("projectId") String projectId,
                                @Body("contractId") String contractId);

    /**
     * 凭证创建-获取帐套信息
     *
     * @param url       路径
     * @param readToken token
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/getTPlusUser", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getTPlusUser(@Var("url") String url, @Var("token") String readToken);

    /**
     * 凭证创建-获取默认部门
     *
     * @param url       路径
     * @param readToken token
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/getDept", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getTPlusDept(@Var("url") String url, @Var("token") String readToken);

    /**
     * 获取科目编码名称
     *
     * @param url   路径
     * @param token token
     * @return {@link JSONObject}
     */
    @Get(url = "${url}/api/getSubject", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getSubject(@Var("url") String url, @Var("token") String token);

    /**
     * 客户台帐
     *
     * @param url          路径
     * @param token        token
     * @param customerName 客户名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/customerAccount", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject customerAccount(@Var("url") String url, @Var("token") String token,
                               @Body("customerName") String customerName);

    /**
     * 银行台帐
     *
     * @param url   路径
     * @param token token
     * @param name  银行名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/bankAccount", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject bankAccount(@Var("url") String url, @Var("token") String token,
                           @Body("name") String name);

    /**
     * 根据合同编码搜索归属合同收款明细
     *
     * @param url   路径
     * @param token token
     * @param id    id
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/contractPayment", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject contractPayment(@Var("url") String url, @Var("token") String token,
                               @Body("id") String id);

    /**
     * 根据合同名称搜索归属合同收款明细
     *
     * @param url          路径
     * @param token        token
     * @param contractName 合同名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/contractName", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject contractName(@Var("url") String url, @Var("token") String token,
                            @Body("contractName") String contractName);

    /**
     * 归属合同收款明细
     *
     * @param url            路径
     * @param token          token
     * @param contractNumber 合同单号
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/contractPaymentByNumber", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject contractPaymentByNumber(@Var("url") String url, @Var("token") String token,
                                       @Body("contractNumber") List<String> contractNumber);

    /**
     * 项目台帐
     *
     * @param url         路径
     * @param token       token
     * @param projectName 项目名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/projectAccount", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject projectAccount(@Var("url") String url, @Var("token") String token,
                              @Body("projectName") String projectName);

    /**
     * 项目台帐
     *
     * @param url         路径
     * @param token       token
     * @param projectName 项目名称
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/projectAccountByName", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject projectAccountByName(@Var("url") String url, @Var("token") String token,
                                    @Body("projectName") List<String> projectName);

    /**
     * 本月预算回款
     *
     * @param url          路径
     * @param token        token
     * @param customerName 客户名称
     * @param yearMonth    年月
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/paymentBudget", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject paymentBudget(@Var("url") String url, @Var("token") String token,
                             @Body("customerName") String customerName,
                             @Body("yearMonth") String yearMonth);

    /**
     * 本月预算回款
     *
     * @param url          路径
     * @param token        token
     * @param customerName 客户名称
     * @param yearMonth    年月
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/paymentBudgetList", headers = {"Content-Type:application/json", "Authorization:${token}"})
    JSONObject paymentBudgetList(@Var("url") String url, @Var("token") String token,
                                 @Body("customerName") List<String> customerName,
                                 @Body("yearMonth") List<String> yearMonth);

    /**
     * 查询关联发票信息
     *
     * @param url          路径
     * @param token        token
     * @param contractCode 合同code
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/getRelationInvoicing", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getRelationInvoicing(@Var("url") String url, @Var("token") String token,
                                    @Body("contractCode") String contractCode);

    /**
     * 凭证创建公司-拼接字典查询
     *
     * @param url   路径
     * @param token token
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/paymentCompany", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject paymentCompany(@Var("url") String url, @Var("token") String token);

    /**
     * 获取保证金台账
     *
     * @param url    路径
     * @param token  token
     * @param flowNo 流程编号
     * @return {@link JSONObject}
     */
    @Post(url = "${url}/api/getMarginVo", headers = {"Content-Type:application/x-www-form-urlencoded", "Authorization:${token}"})
    JSONObject getMarginVo(@Var("url") String url, @Var("token") String token, @Body("flowNo") String flowNo);

    /**
     * 获取OA账号
     */
    @Post(url = "${url}${api}", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject getOaAccountById(@Var("url") String url, @Var("api") String api, @Var("token") String token
            , @Body("id") Long id);

    /**
     * 查询回款跟踪表数据
     */
    @Post(url = "${url}/api/selPayment", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject selPayment(@Var("url") String url, @Var("token") String token);

    /**
     * 查询回款跟踪表认领数据
     */
    @Post(url = "${url}/api/selPaymentClaim", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject selPaymentClaim(@Var("url") String url, @Var("token") String token);

    /**
     * 根据客户经理查询客户id
     *
     * @param url
     * @param readToken
     * @param userId
     * @return
     */
    @Post(url = "${url}/api/selCustomerId", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject selCustomerId(@Var("url") String url, @Var("token") String readToken, @Body("userId") Long userId);

    /**
     * 通过OA流程名称查询流程workflowId
     *
     * @param url
     * @param token
     * @param workflowName
     * @return
     */
    @Post(url = "${url}/api/getWorkflowIdByName", headers = {"Content-Type:application/json",
            "Authorization:${token}"})
    JSONObject getWorkflowIdByName(@Var("url") String url, @Var("token") String token, @Body("workflowName") String workflowName);


    /**
     * 更新销售收款计划款项状态
     * @param url
     * @param token
     * @param ids
     * @return
     */
    @Post(url = "${url}/api/updateSalesReceipt", headers = {"Content-Type:application/x-www-form-urlencoded",
            "Authorization:${token}"})
    JSONObject updateSalesReceipt(@Var("url") String url, @Var("token") String token, @Body("ids") List<Long> ids);
}
