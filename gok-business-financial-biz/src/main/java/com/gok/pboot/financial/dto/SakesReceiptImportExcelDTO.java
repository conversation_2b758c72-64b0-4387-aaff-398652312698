package com.gok.pboot.financial.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 销售计划任务导入Excel
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SakesReceiptImportExcelDTO {

    /**
     * id
     */
    @ExcelProperty("id")
    private Long id;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 合同名称
     */
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 款项名称
     */
    @ExcelProperty("款项名称")
    private String currentPaymentName;

    /**
     * 预计回款日期
     */
    @ExcelProperty("预计回款日期")
    private String expectedPaymentDate;

    /**
     * 客户付款窗口期
     */
    @ExcelProperty("客户付款窗口期")
    private String customerPaymentWindowPeriod;

    /**
     * 当前客户付款审批节点
     */
    @ExcelProperty("当前客户付款审批节点")
    private String currentApprovalProcess;

    /**
     * 审批进度
     */
    @ExcelProperty("审批进度")
    private String approvalProgress;

    /**
     * 回款风险(0有 1无)
     */
    @ExcelProperty("回款风险")
    private String paymentRiskStr;

    /**
     * 所需材料
     */
    @ExcelProperty("所需材料")
    private String requiredMaterials;

    /**
     * 进度说明
     */
    @ExcelProperty("进度说明")
    private String progressDescription;

    /**
     * 下一步工作
     */
    @ExcelProperty("下一步工作")
    private String nextStepWork;

    /**
     * 更新日期
     */
    @ExcelProperty("更新日期")
    private String updateDate;

    /**
     * 审批节点名称
     */
    @ExcelProperty("审批节点名称")
    private String customerPaymentProcessList;

}
