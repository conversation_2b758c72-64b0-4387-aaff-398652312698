package com.gok.pboot.financial.dto;

import com.gok.pboot.financial.vo.ProjectRevenueLedgerInvoicingVO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 关联发票
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RelationInvoiceDTO {

    /**
     * 项目收入台帐id
     */
    private Long id;

    /**
     * 发票id
     */
    private List<ProjectRevenueLedgerInvoicingVO> invoicingList;

}
