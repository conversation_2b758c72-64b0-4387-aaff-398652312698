package com.gok.pboot.financial.invoice.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 销项发票台账表实体类
 *
 * <AUTHOR>
 * @create 2025/06/04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("invoice_output")
public class InvoiceOutput extends Model<InvoiceOutput> {

    /**
     * 销项发票ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("销项发票ID")
    private Long id;

    /**
     * 项目ID
     */
    @ApiModelProperty("项目ID")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 开票客户ID
     */
    @ApiModelProperty("开票客户ID")
    private Long invoiceCustomerId;

    /**
     * 开票客户名称
     */
    @ApiModelProperty("开票客户名称")
    private String invoiceCustomerName;

    /**
     * 商品和服务名称
     */
    @ApiModelProperty("商品和服务名称")
    private Integer goodsServiceName;

    /**
     * 计划发票金额_含税
     */
    @ApiModelProperty("计划发票金额_含税")
    private BigDecimal planAmountIncludedTax;

    /**
     * 计划发票金额_不含税
     */
    @ApiModelProperty("计划发票金额_不含税")
    private BigDecimal planAmountExcludingTax;

    /**
     * 实际开票金额_含税
     */
    @ApiModelProperty("实际开票金额_含税")
    private BigDecimal actualAmountIncludedTax;

    /**
     * 实际开票金额_不含税
     */
    @ApiModelProperty("实际开票金额_不含税")
    private BigDecimal actualAmountExcludingTax;

    /**
     * 计划发票税率
     */
    @ApiModelProperty("计划发票税率")
    private Integer planTaxRate;

    /**
     * 实际开票税率
     */
    @ApiModelProperty("实际开票税率")
    private Integer actualTaxRate;

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNo;

    /**
     * 原蓝字发票号码
     */
    @ApiModelProperty("原蓝字发票号码")
    private String blueInvoiceNo;

    /**
     * 红蓝字 0=蓝字 1=红字
     */
    @ApiModelProperty("红蓝字 0=蓝字 1=红字")
    private Integer redAndBlue;

    /**
     * 发票类型
     */
    @ApiModelProperty("发票类型")
    private Integer invoiceType;

    /**
     * 发票状态
     */
    @ApiModelProperty("发票状态")
    private Integer invoiceStatus;

    /**
     * 发票日期
     */
    @ApiModelProperty("发票日期")
    private LocalDate invoiceDate;

    /**
     * 发票备注
     */
    @ApiModelProperty("发票备注")
    private String invoiceRemark;

    /**
     * 开票申请人ID
     */
    @ApiModelProperty("开票申请人ID")
    private Long invoiceApplicationId;

    /**
     * 开票申请人
     */
    @ApiModelProperty("开票申请人")
    private String invoiceApplication;

    /**
     * 发票申请流程
     */
    @ApiModelProperty("发票申请流程")
    private Long applyRequestId;

    /**
     * 发票作废流程
     */
    @ApiModelProperty("发票作废流程")
    private Long cancelRequestId;

    /**
     * 发票规划ID
     */
    @ApiModelProperty("发票规划ID")
    private Long invoicePlanId;

    /**
     * 合同id
     */
    @ApiModelProperty("合同id")
    private Long contractId;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    private String contractNo;

    /**
     * 合同名称
     */
    @ApiModelProperty("合同名称")
    private String contractName;

    /**
     * 合同签约客户id
     */
    @ApiModelProperty("合同签约客户id")
    private Long contractCustomerId;

    /**
     * 合同签约客户名称
     */
    @ApiModelProperty("合同签约客户名称")
    private String contractCustomerName;

    /**
     * 合同签约主体
     */
    @ApiModelProperty("合同签约主体")
    private Integer contractSubject;

    /**
     * 合同结算方式
     */
    @ApiModelProperty("合同结算方式")
    private Integer contractSettlementType;

    /**
     * 合同起始日期
     */
    @ApiModelProperty("合同起始日期")
    private LocalDate contractStartDate;

    /**
     * 合同截止日期
     */
    @ApiModelProperty("合同截止日期")
    private LocalDate contractEndDate;

    /**
     * 业务归属部门id
     */
    @ApiModelProperty("业务归属部门id")
    private Long businessDeptId;

    /**
     * 业务归属部门
     */
    @ApiModelProperty("业务归属部门")
    private String businessDept;

    /**
     * 客户经理id
     */
    @ApiModelProperty("客户经理id")
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ApiModelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 项目经理id
     */
    @ApiModelProperty("项目经理id")
    private Long managerUserId;

    /**
     * 项目经理
     */
    @ApiModelProperty("项目经理")
    private String managerUserName;

    /**
     * 收款日期
     */
    @ApiModelProperty("收款日期")
    private LocalDate receiptDate;

    /**
     * 付款客户ID
     */
    @ApiModelProperty("收款客户ID")
    private Long receiptCustomerId;

    /**
     * 付款客户名称
     */
    @ApiModelProperty("付款客户名称")
    private String receiptCustomerName;

    /**
     * 收款平台
     */
    @ApiModelProperty("收款平台")
    private Integer receiptPlatform;

    /**
     * 附件
     */
    @ApiModelProperty("附件")
    private String attachment;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
} 