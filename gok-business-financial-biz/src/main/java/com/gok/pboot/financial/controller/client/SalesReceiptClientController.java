package com.gok.pboot.financial.controller.client;

import com.gok.components.common.util.R;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.pboot.financial.service.ISalesReceiptCollectionRecordsService;
import com.gok.pboot.financial.vo.SalesReceiptCollectionRecordsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 销售回款跟踪外部调用
 *
 * <AUTHOR>
 * @since 2024-03-15
 * @menu 销售回款跟踪外部调用
 */
@Inner(false)
@RestController
@RequiredArgsConstructor
@Api(tags = "销售回款跟踪外部调用")
@RequestMapping("/inner/sales-receipt")
public class SalesReceiptClientController {

    private final ISalesReceiptCollectionRecordsService iSalesReceiptCollectionRecordsService;

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/save-records")
    @ApiOperation(value = "新增记录", notes = "新增记录")
    public R<Boolean> saveRecords(@RequestBody @Valid SalesReceiptCollectionRecordsDTO dto) {
        return iSalesReceiptCollectionRecordsService.saveRecords(dto)
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 催款记录
     *
     * @param contractId 合同id
     * @return {@link R}<{@link List}<{@link SalesReceiptCollectionRecordsVO}>>
     */
    @GetMapping("/salesReceiptRecordsList")
    @ApiOperation(value = "催款记录", notes = "催款记录")
    public R<List<SalesReceiptCollectionRecordsVO>> salesReceiptRecordsList(@RequestParam("contractId") Long contractId) {
        return R.ok(iSalesReceiptCollectionRecordsService.getContractCollectionVoList(contractId));
    }
}
