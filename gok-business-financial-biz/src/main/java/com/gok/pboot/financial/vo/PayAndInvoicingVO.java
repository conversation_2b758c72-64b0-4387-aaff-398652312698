package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 回款票据与开票票据统一展示
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayAndInvoicingVO {

    /**
     * 回款票据集合
     */
    private List<PayBackDocumentVO> payPackDocumentVOList;

    /**
     * 开票票据集合
     */
    private List<InvoicingDocumentVO> invoicingDocumentVOList;
}
