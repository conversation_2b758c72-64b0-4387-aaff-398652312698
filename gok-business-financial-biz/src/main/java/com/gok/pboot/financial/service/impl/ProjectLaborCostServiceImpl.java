package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.common.enums.DeptAssociationEnum;
import com.gok.bcp.upms.dto.InnerDeptMapDto;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.utils.Threads;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.OpenApi;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.ProjectLaborCost;
import com.gok.pboot.financial.db.mapper.ProjectLaborCostMapper;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.service.IProjectLaborCostService;
import com.gok.pboot.financial.util.*;
import com.gok.pboot.financial.vo.ProjectLaborCostVO;
import com.gok.pboot.financial.vo.PushErrorReasonVO;
import com.gok.pboot.financial.vo.PushResultVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 项目人工成本分摊汇总 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectLaborCostServiceImpl extends ServiceImpl<ProjectLaborCostMapper, ProjectLaborCost> implements IProjectLaborCostService {

    private final ProjectLaborCostMapper projectLaborCostMapper;

    private final OpenApi openApi;


    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final IAccountingSetService accountingSetService;

    private final LogRecordUtils logRecordUtils;

    @Value("${middlePlatform.deptCatName}")
    private String deptCatName;

    /**
     * 模糊查询带分页
     *
     * @param page                分页请求
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}<{@link ProjectLaborCostVO}>
     */
    @Override
    public StatisticsPage<ProjectLaborCostVO> findPage(Page<ProjectLaborCost> page, ProjectLaborCostDTO projectLaborCostDto) {
        StatisticsPage<ProjectLaborCostVO> projectLaborCostVoPage = new StatisticsPage<>();
        // 分页
        // V1.2.0 试用期展示为正式
        this.condition(projectLaborCostDto);
        Page<ProjectLaborCost> projectLaborCostPage = baseMapper.findPage(page, projectLaborCostDto);
        BeanUtil.copyProperties(projectLaborCostPage, projectLaborCostVoPage, "records");

        List<ProjectLaborCost> projectLaborCostList = projectLaborCostPage.getRecords();
        if (CollUtil.isEmpty(projectLaborCostList)) {
            return projectLaborCostVoPage;
        }
        // 根据项目人工分摊汇总数据封装为展示数据集合
        List<ProjectLaborCostVO> projectLaborCostVOList = this.addVOToList(projectLaborCostList, true);
        projectLaborCostVoPage.setRecords(projectLaborCostVOList);
        //  统计
        statistics(projectLaborCostVoPage,projectLaborCostDto);
        return projectLaborCostVoPage;
    }

    /**
     * 统计
     *
     * @param page                页
     * @param projectLaborCostDto 项目人工成本 DTO
     */
    public void statistics(StatisticsPage<ProjectLaborCostVO> page, ProjectLaborCostDTO projectLaborCostDto) {
        List<ProjectLaborCost> projectLaborCosts = baseMapper.findPage(projectLaborCostDto);

        Map<String, BigDecimal> result = new ConcurrentHashMap<>(4);

        result.put("payrollCostTotal", BigDecimal.ZERO);
        result.put("socialSecurityCostTotal", BigDecimal.ZERO);
        result.put("providentFundCostTotal", BigDecimal.ZERO);
        result.put("costSumTotal", BigDecimal.ZERO);
        // 使用并行流处理数据
        projectLaborCosts.parallelStream().forEach(item -> {

            BigDecimal payrollCost = MoneyUtils.decryptToBigDecimal(item.getPayrollCost(), true);
            result.computeIfPresent("payrollCostTotal", (k, v) -> v.add(payrollCost));

            BigDecimal socialSecurityCost = MoneyUtils.decryptToBigDecimal(item.getSocialSecurityCost(), true);
            result.computeIfPresent("socialSecurityCostTotal", (k, v) -> v.add(socialSecurityCost));

            BigDecimal providentFundCost = MoneyUtils.decryptToBigDecimal(item.getProvidentFundCost(), true);
            result.computeIfPresent("providentFundCostTotal", (k, v) -> v.add(providentFundCost));

            BigDecimal costSum = MoneyUtils.decryptToBigDecimal(item.getCostSum(), true);
            result.computeIfPresent("costSumTotal", (k, v) -> v.add(costSum));
        });

        page.setStatistics(result);
    }


    /**
     * 导出Excel选中数据
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostVO}>
     */
    @Override
    public List<ProjectLaborCostVO> export(ProjectLaborCostDTO projectLaborCostDTO) {
        long startTime = System.currentTimeMillis();
        // 模糊查询分页列表
        this.condition(projectLaborCostDTO);
        List<ProjectLaborCost> projectLaborCostList = projectLaborCostMapper.exportList(projectLaborCostDTO);

        List<ProjectLaborCostVO> projectLaborCostVOList = new ArrayList<>();
        if (CollUtil.isEmpty(projectLaborCostList)) {
            projectLaborCostVOList.add(new ProjectLaborCostVO());
        } else {
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_LABOR_COST,
                    OperationEnum.EXPORT,
                    OperationEnum.EXPORT.getName() + "【" + projectLaborCostList.size() + "条】");
            projectLaborCostVOList = this.addVOToList(projectLaborCostList, false);
        }

        log.info("导出项目人工汇总Excel:{}ms", (System.currentTimeMillis() - startTime));
        return projectLaborCostVOList;
    }

    /**
     * 推送
     *
     * @param ids ids
     * @return PushResultVO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PushResultVO push(List<String> ids) {
        // 1、获取待推送项目详情
        List<ProjectLaborCost> projectLaborCostList = baseMapper.selList(ids);
        if (CollUtil.isEmpty(projectLaborCostList)) {
            return PushResultVO.builder()
                    .success(NumberUtils.INTEGER_ZERO)
                    .error(ids.size()).build();
        }
        // 传入的ids和获取到的项目数据个数是否一致
        Integer notPushNum = NumberUtils.INTEGER_ZERO;
        if (ids.size() != projectLaborCostList.size()) {
            notPushNum = ids.size() - projectLaborCostList.size();
        }
        List<String> salaryPaidSubjectList = projectLaborCostList
                .stream().map(ProjectLaborCost::getSalaryPaidSubject).distinct()
                .collect(Collectors.toList());
        // 根据发薪主体分组
        Map<String, List<ProjectLaborCost>> listMap = projectLaborCostList.stream()
                .collect(Collectors.groupingBy(ProjectLaborCost::getSalaryPaidSubject));
        // 2、查询组织第三方ID映射关系
        Map<Long, String> tPlusMap = remoteOutMultiDeptService
                .getThirdDeptRelation(deptCatName, DeptAssociationEnum.T_PLUS).getData();
        // 3、获取中台的所有核算部门信息 行政部门id作为key 部门信息作为value
        Map<Long, InnerDeptMapDto> innerDeptMap = remoteOutMultiDeptService.getInnerDeptMapDto(deptCatName)
                .getData()
                .stream()
                .collect(Collectors.toMap(InnerDeptMapDto::getInnerDeptId, b -> b,
                        (oldObj, newObj) -> {
                            Integer oldVirtualLevel = oldObj.getVirtualLevel();
                            Integer newVirtualLevel = newObj.getVirtualLevel();
                            return oldVirtualLevel.equals(Math.min(oldVirtualLevel, newVirtualLevel)) ? newObj : oldObj;
                        })
                );
        // 4、获取帐套信息,key:发薪主体，value:帐套信息
        Map<String, AccountingSet> accountingSetMap = accountingSetService.getAccountingSet();
        // 5、日志记录至中台
        StringBuilder sb = new StringBuilder();
        // 6、推送业务处理
        AtomicInteger succeed = new AtomicInteger();
        AtomicInteger error = new AtomicInteger();
        List<PushErrorReasonVO> errorReasonList = new ArrayList<>();
        salaryPaidSubjectList.forEach(s -> {
            List<ProjectLaborCost> projectLaborCosts = listMap.get(s);
            // 6.1、帐套信息
            AccountingSet accountingSet = accountingSetMap.get(s);
            if (Optional.ofNullable(accountingSet).isPresent()) {
                // 6.2、获取登录T+token
                String token = openApi.login(accountingSet.getAccountingAccount(), accountingSet.getAccountingPassword(), accountingSet.getAccountingCode());
                projectLaborCosts.forEach(p -> {
                    //凭证实体数据组装
                    VoucherDTO.VoucherDTOBuilder voucherDtoBuilder = VoucherDTO.builder();
                    //制单日期为归属月份的最后一日天
                    LocalDate date = LocalDate.parse(p.getYearMonthDate().replace("-", "") + "01", DateTimeFormatter.BASIC_ISO_DATE);
                    voucherDtoBuilder
                            .ExternalCode(p.getYearMonthDate() + p.getProjectId() + p.getPersonnelSecondaryDeptId() + p.getPersonnelType() + p.getSalaryPaidSubject())
                            .VoucherDate(date.with(TemporalAdjusters.lastDayOfMonth()).toString())
                            .DocType(VoucherDocTypeDTO.builder().Code(VoucherRecordEnum.DOCTYPE.getName()).build());
                    //借方凭证辅佐项集合(项目、部门)
                    List<AuxiliaryDTO> auxiliary = new ArrayList<>();
                    // 根据收入归属二级部门转换成核算组织部门名称。获取默认部门
                    InnerDeptMapDto incomeInnerDeptMapDto = innerDeptMap.get(p.getIncomeSecondaryDeptId());
                    if (Optional.ofNullable(incomeInnerDeptMapDto).isPresent()) {
                        AuxiliaryDTO drAuxiliaryDTO = AuxiliaryDTO.builder()
                                .AuxAccProject(AuxAccProjectDTO.builder().Code(p.getProjectNo()).build())
                                .AuxAccDepartment(AuxAccDepartmentDTO.builder().Code(tPlusMap.getOrDefault(incomeInnerDeptMapDto.getVirtualDeptId(), StrUtil.EMPTY)).build())
                                .build();
                        auxiliary.add(drAuxiliaryDTO);
                    }

                    // 凭证明细
                    List<VoucherDetailDTO> detailDtoArrayList = new ArrayList<>();
                    // 场景一：项目状态=在建/商机（生成3条借方凭证）
                    if (ProjectStatusEnum.SJ.getValue().equals(p.getProjectStatus()) ||
                            ProjectStatusEnum.ZJ.getValue().equals(p.getProjectStatus())) {
                        // 凭证1：@合同履约成本-直接成本-工资，取值【工资成本】
                        createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.PAYROLL.getName(),
                                MoneyUtils.getInstance().decrypt(p.getPayrollCost()), innerDeptMap, p, auxiliary);
                        // 凭证2：@合同履约成本-直接成本-社保，取值【社保成本】
                        createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.SOCIAL_SECURITY.getName(),
                                MoneyUtils.getInstance().decrypt(p.getSocialSecurityCost()), innerDeptMap, p, auxiliary);
                        // 凭证3：@合同履约成本-直接成本-公积金，取值【公积金成本】
                        createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.PROVIDENT_FUND.getName(),
                                MoneyUtils.getInstance().decrypt(p.getProvidentFundCost()), innerDeptMap, p, auxiliary);
                    } else {
                        // 场景二：项目状态=商机终止
                        if (ProjectStatusEnum.SJZZ.getValue().equals(p.getProjectStatus())) {
                            // 凭证1：@销售费用-售前，取值【成本合计】
                            createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.SALES_AFTER.getName(),
                                    MoneyUtils.getInstance().decrypt(p.getCostSum()), innerDeptMap, p, auxiliary);
                        } else
                            // 场景三：项目状态=结项
                            if (ProjectStatusEnum.JX.getValue().equals(p.getProjectStatus())) {
                                // 凭证1：@销售费用-售前，取值【成本合计】
                                createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.SALES_BEFORE.getName(),
                                        MoneyUtils.getInstance().decrypt(p.getCostSum()), innerDeptMap, p, auxiliary);
                            }
                    }
                    //贷方凭证辅佐项集合(项目、部门)
                    List<AuxiliaryDTO> crAuxiliaryList = new ArrayList<>();
                    //根据核算组织部门名称获取默认部门，项目统一取"无项目"
                    InnerDeptMapDto innerDeptMapDto = innerDeptMap.get(p.getPersonnelSecondaryDeptId());
                    if (Optional.ofNullable(innerDeptMapDto).isPresent()) {
                        AuxiliaryDTO crAuxiliary = AuxiliaryDTO.builder()
                                .AuxAccProject(AuxAccProjectDTO.builder().Code("030001").build())
                                .AuxAccDepartment(AuxAccDepartmentDTO.builder().Code(tPlusMap.getOrDefault(innerDeptMapDto.getVirtualDeptId(), StrUtil.EMPTY)).build())
                                .build();
                        crAuxiliaryList.add(crAuxiliary);
                    }

                    //贷方凭证（单条数据生成3条凭证）凭证字：@借
                    // 凭证1：@管理费用-工资，取值【工资成本】对应负数金额
                    createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.MANAGEMENT_SALARY.getName(),
                            MoneyUtils.getInstance().oppositeNumber(p.getPayrollCost()), innerDeptMap, p, crAuxiliaryList);
                    // 凭证2：@管理费用-社保，取值【社保成本】对应负数金额
                    createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.MANAGEMENT_SOCIAL_SECURITY.getName(),
                            MoneyUtils.getInstance().oppositeNumber(p.getSocialSecurityCost()), innerDeptMap, p, crAuxiliaryList);
                    // 凭证3：@管理费用-公积金，取值【公积金成本】对应负数金额
                    createVoucher(detailDtoArrayList, VoucherSubjectIdEnum.MANAGEMENT_PROVIDENT_FUND.getName(),
                            MoneyUtils.getInstance().oppositeNumber(p.getProvidentFundCost()), innerDeptMap, p, crAuxiliaryList);

                    VoucherDTO voucher = voucherDtoBuilder.Entrys(detailDtoArrayList).build();
                    //凭证创建
                    try {
                        String result = openApi.createVoucher(token, voucher);
                        p.setUpdateBy(UserUtils.getUser().getUsername());
                        p.setUpdateTime(LocalDateTime.now());
                        if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                            p.setPushStatus(PushStatusEnum.SUCCESS_PUSH.getValue());
                            baseMapper.updatePushStatus(p);
                            succeed.incrementAndGet();
                            // 记录日志
                            sb.append("【").append(p.getYearMonthDate()).append("】【")
                                    .append(p.getProjectNo()).append("】【").append(p.getIncomeDept()).append("】【")
                                    .append(p.getPersonnelSecondaryDept()).append("】【").append(PushStatusEnum.SUCCESS_PUSH.getName()).append("】");
                        } else {
                            p.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                            baseMapper.updatePushStatus(p);
                            error.incrementAndGet();
                            errorReasonList.add(PushErrorReasonVO.builder()
                                    .id(p.getYearMonthDate() + p.getProjectName() + p.getPersonnelSecondaryDept() + p.getPersonnelType() + p.getSalaryPaidSubject())
                                    .errorReason(result)
                                    .build());
                            // 记录日志
                            sb.append("【").append(p.getYearMonthDate()).append("】【")
                                    .append(p.getProjectNo()).append("】【").append(p.getIncomeDept()).append("】【")
                                    .append(p.getPersonnelSecondaryDept()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                        }
                    } catch (Exception e) {
                        throw new BusinessException("凭证创建失败");
                    }
                });
            } else {
                projectLaborCosts.stream().forEach(p -> {
                    p.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    baseMapper.updatePushStatus(p);
                    errorReasonList.add(PushErrorReasonVO.builder()
                            .id(p.getYearMonthDate() + p.getProjectName() + p.getPersonnelSecondaryDept() + p.getPersonnelType() + p.getSalaryPaidSubject())
                            .errorReason(VoucherRecordEnum.ACCOUNTING_ERROR.getName())
                            .build());
                    // 记录日志
                    sb.append("【").append(p.getYearMonthDate()).append("】【")
                            .append(p.getProjectNo()).append("】【").append(p.getIncomeDept()).append("】【")
                            .append(p.getPersonnelSecondaryDept()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                    error.incrementAndGet();
                });
            }
        });
        // 5、日志记录
        if (sb.length() > NumberUtils.INTEGER_ZERO) {
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.EXPORT, OperationEnum.PUSH.getName() + sb.toString());
        }
        return PushResultVO.builder()
                .success(succeed.get())
                .error(error.get() + notPushNum)
                .errorReasonList(errorReasonList).build();
    }

    /**
     * 根据项目人工分摊汇总数据封装为展示数据集合
     *
     * @param projectLaborCostList 项目人工分摊汇总数据
     * @param isPage               是否分页
     * @return {@link List}<{@link ProjectLaborCostVO}>
     */
    private List<ProjectLaborCostVO> addVOToList(List<ProjectLaborCost> projectLaborCostList, boolean isPage) {
        // 获取中台部门树结构 部门id对应部门名称
        Map<Long, String> deptMap = remoteOutMultiDeptService.getDeptList("行政组织", null, null).getData()
                .stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId, MultiDimensionDeptDto::getName, (a, b) -> a));
        List<ProjectLaborCostVO> projectLaborCostVOList = Collections.synchronizedList(new ArrayList<>());
        int size = 100;
        int threadCount = ThreadUtils.getInstance().getThreadCount(projectLaborCostList.size(), size);
        CountDownLatch downLatch = new CountDownLatch(threadCount);
        ThreadPoolExecutor poolExecutor = ThreadUtils.getInstance().getThreadPool(threadCount);
        if (!isPage) {
            Threads.countDownLatchThreadLocal.set(downLatch);
        }
        for (int i = 0; i < threadCount; ++i) {
            int j = i;
            poolExecutor.submit(() -> {
                try {
                    if (j != threadCount - 1) {
                        projectLaborCostList.subList((j * size), (j + 1) * size).forEach(p ->
                                projectLaborCostVOList.add(this.projectLaborCostVoDecrypt(p, deptMap))
                        );
                    } else {
                        projectLaborCostList.subList((j * size), projectLaborCostList.size()).forEach(p ->
                                projectLaborCostVOList.add(this.projectLaborCostVoDecrypt(p, deptMap))
                        );
                    }
                } catch (Exception e) {
                    log.info("Error in thread: " + Thread.currentThread().getName(), e);
                } finally {
                    downLatch.countDown();
                }
            });
        }
        if (isPage) {
            try {
                downLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("Error waiting for tasks to complete: " + e.getMessage());
            }
        }
        poolExecutor.shutdown();
        Comparator<ProjectLaborCostVO> comparator = Comparator.comparing(ProjectLaborCostVO::getYearMonthDate).reversed()
                .thenComparing(ProjectLaborCostVO::getProjectNo, Comparator.nullsLast(String::compareTo).reversed());
        projectLaborCostVOList.sort(comparator);

        return projectLaborCostVOList;
    }

    /**
     * 分页导出条件封装
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     */
    private void condition(ProjectLaborCostDTO projectLaborCostDTO) {
        // 模糊查询分页列表
        if (Optional.ofNullable(projectLaborCostDTO.getPersonnelType()).isPresent()) {
            List<Integer> list = new ArrayList<>();
            if (PersonnelTypeEnum.FORMAL.getValue().equals(projectLaborCostDTO.getPersonnelType())) {
                list.add(PersonnelTypeEnum.FORMAL.getValue());
                list.add(PersonnelTypeEnum.PROBATION.getValue());
            } else {
                list.add(projectLaborCostDTO.getPersonnelType());
            }
            projectLaborCostDTO.setPersonnelTypeList(list);
        }
    }

    /**
     * 字段解密处理
     *
     * @param p {@link ProjectLaborCost}
     * @return {@link ProjectLaborCostVO}
     */
    private ProjectLaborCostVO projectLaborCostVoDecrypt(ProjectLaborCost p, Map<Long, String> map) {
        ProjectLaborCostVO vo = BeanUtil.copyProperties(p, ProjectLaborCostVO.class);
        vo.setId(p.getYearMonthDate() + p.getProjectId() + p.getPersonnelSecondaryDeptId()
                + p.getPersonnelType() + p.getSalaryPaidSubject());
        vo.setYearMonthDate(DateUtils.dateTrans(p.getYearMonthDate()));
        vo.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, p.getProjectStatus()));
        Integer personnelType = p.getPersonnelType();
        // V1.2.0需求变更 试用期展示为正式
        if (PersonnelTypeEnum.FORMAL.getValue().equals(personnelType) || PersonnelTypeEnum.PROBATION.getValue().equals(personnelType)) {
            vo.setPersonnelTypeTxt(PersonnelTypeEnum.FORMAL.getName());
        } else {
            vo.setPersonnelTypeTxt(EnumUtils.getNameByValue(PersonnelTypeEnum.class, p.getPersonnelType()));
        }
        vo.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
        vo.setPayrollCost(MoneyUtils.getInstance().transType(p.getPayrollCost(), true));
        vo.setSocialSecurityCost(MoneyUtils.getInstance().transType(p.getSocialSecurityCost(), true));
        vo.setProvidentFundCost(MoneyUtils.getInstance().transType(p.getProvidentFundCost(), true));
        vo.setCostSum(MoneyUtils.getInstance().transType(p.getCostSum(), true));
        // 根据【是否为内部项目】展示改为【项目类型】
        Integer projectType = vo.getProjectType();
        if (InternalProjectEnum.YES.getValue().equals(projectType)) {
            vo.setProjectTypeTxt(ProjectTypeEnum.INTERNAL_PROJECT.getName());
            vo.setPushStatusTxt(CharSequenceUtil.EMPTY);
        } else if (InternalProjectEnum.NO.getValue().equals(projectType)) {
            vo.setProjectTypeTxt(ProjectTypeEnum.INCOME_PROJECT.getName());
        }
        // 【项目类型】为内部项目不归于待推送
        if (ProjectTypeEnum.INTERNAL_PROJECT.getValue().equals(vo.getProjectType())) {
            vo.setPushStatus(null);
            vo.setPushStatusTxt(null);
        }
        // 根据二级部门id获取二级部门名称
        vo.setIncomeSecondaryDept(map.get(p.getIncomeSecondaryDeptId()));
        return vo;
    }

    /**
     * 凭证创建
     */
    private void createVoucher(List<VoucherDetailDTO> detailDtoArrayList,
                               String accountName,
                               String cost,
                               Map<Long, InnerDeptMapDto> innerDeptMap,
                               ProjectLaborCost p,
                               List<AuxiliaryDTO> auxiliary) {
        if (!MoneyUtils.ZERO_THREE.equals(cost)) {
            VoucherDetailDTO payrollDr = VoucherDetailDTO.builder()
                    .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                    .Account(EntryAccountDTO.builder().Code(accountName).build())
                    .AmountDr(cost)
                    .Summary(innerDeptMap.get(p.getIncomeSecondaryDeptId()).getVirtualDeptName() + VoucherRecordEnum.LABOR_COST_SUMMARY.getName())
                    .AuxInfos(auxiliary)
                    .build();
            detailDtoArrayList.add(payrollDr);
        }
    }
}
