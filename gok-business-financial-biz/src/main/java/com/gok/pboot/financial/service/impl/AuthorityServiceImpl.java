package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.constant.CommonConstants;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.enums.YesOrNoEnum;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.mapper.AccountingSetMapper;
import com.gok.pboot.financial.dto.AccountingSetDTO;
import com.gok.pboot.financial.dto.AccountingSetPageDTO;
import com.gok.pboot.financial.enums.EnableStatusEnum;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.service.IAuthorityService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.vo.AccountingSetVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 权限业务层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Service
@RequiredArgsConstructor
public class AuthorityServiceImpl implements IAuthorityService {

    private final RemoteRoleService remoteRoleService;

    @Override
    public SysUserRoleDataVo getByClientIdAndRoleId(Long userId, String menuCode) {
        RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String clientId = request.getHeader(CommonConstants.CLIENT_ID_HEADER);
        R<SysUserRoleDataVo> r = remoteRoleService.getRoleDataDetailByUserId(Long.parseLong(clientId),
                userId,
                menuCode);
        if(NumberUtils.INTEGER_ONE.equals(r.getCode())){
            throw new BusinessException(r.getMsg());
        }
        return r.getData();
    }


}
