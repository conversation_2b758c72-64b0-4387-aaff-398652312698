package com.gok.pboot.financial.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 会计账套导入
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingSetImportDTO {

    /**
     * 账套编码
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @NotBlank(message = "账套编码不能为空")
    @ExcelProperty("账套编码")
    private String accountingCode;

    /**
     * 账套账号
     */
    @ExcelProperty("账套账号")
    @NotBlank(message = "账套账号不能为空")
    private String accountingAccount;

    /**
     * 账套密码
     */
    @ExcelProperty("账套密码")
    @NotBlank(message = "账套密码不能为空")
    private String accountingPassword;

    /**
     * 归属主体
     */
    @NotBlank(message = "归属主体不能为空")
    @ExcelProperty("归属主体")
    private String accountingSubject;

    /**
     * 启用状态
     */
    @ExcelProperty("停用状态")
    @NotBlank(message = "停用状态不能为空")
    private String enableStatusStr;

    /**
     * 默认资金账户
     */
    @ExcelProperty("默认资金账户")
    private String fundAccount;

    /**
     * 账套类型
     */
    @ExcelProperty("账套类型")
    @NotBlank(message = "账套类型不能为空")
    private String accountingType;

    /**
     * 账套备注
     */
    @ExcelProperty("账套备注")
    private String accountingRemarks;

}
