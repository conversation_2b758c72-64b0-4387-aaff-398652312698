package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 保证金台账VO
 *
 * <AUTHOR>
 * @since 2024-01-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MarginVO {

    /**
     * 流程编号
     */
    private String flowNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 选项名
     */
    private String optionName;

    /**
     * 保证金类型
     */
    private Integer marginType;

    /**
     * 保证金金额
     */
    private String marginMoney;

    /**
     * 所属一级部门
     */
    private Long firstDeptId;

    /**
     * 所属一级部门
     */
    private String firstDeptName;

    /**
     * 所属二级部门
     */
    private Long secondDeptId;

    /**
     * 所属二级部门
     */
    private String secondDeptName;


    /**
     * 客户名称
     */
    private String customerName;
}
