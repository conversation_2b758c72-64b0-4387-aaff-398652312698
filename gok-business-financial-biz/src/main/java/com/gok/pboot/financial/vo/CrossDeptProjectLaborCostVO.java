package com.gok.pboot.financial.vo;

import lombok.*;

/**
 * 项目人工成本分摊汇总
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrossDeptProjectLaborCostVO {

    /**
     * 归属月份
     */
    private String yearMonthDate;

    /**
     * 年份
     */
    private String year;

    /**
     * 月份
     */
    private String month;

    /**
     * 收入归属一级部门id
     */
    private Long incomeDeptId;

    /**
     * 收入归属一级部门
     */
    private String incomeDept;

    /**
     * 人员归属一级部门id
     */
    private Long personnelDeptId;

    /**
     * 人员归属一级部门
     */
    private String personnelDept;

    /**
     * 合计成本
     */
    private String costSum;

    /**
     * 项目类型
     * {@link com.gok.pboot.financial.enums.ProjectTypeEnum}
     */
    private Integer projectType;

}