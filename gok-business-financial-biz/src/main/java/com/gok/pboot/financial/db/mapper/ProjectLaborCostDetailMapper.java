package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.db.entity.ProjectLaborCostDetail;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目人工成本分摊明细 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
public interface ProjectLaborCostDetailMapper extends BaseMapper<ProjectLaborCostDetail> {

    /**
     * 模糊查询带分页
     *
     * @param page 分页请求
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}{@link ProjectLaborCostDetailVO}>
     */
    @BusinessDataScope(scopeDeptNameList = {"income_dept_id", "income_secondary_dept_id", "personnel_dept_id", "personnel_secondary_dept_id"})
    Page<ProjectLaborCostDetailVO> findPage(Page<ProjectLaborCostDetailVO> page, @Param("query") ProjectLaborCostDTO projectLaborCostDto);

    /**
     * 查询
     *
     * @param projectLaborCostDto 项目人工成本 DTO
     * @return {@link List }<{@link ProjectLaborCostDetail }>
     */
    @BusinessDataScope(scopeDeptNameList = {"income_dept_id", "income_secondary_dept_id", "personnel_dept_id", "personnel_secondary_dept_id"})
    List<ProjectLaborCostDetailVO> findPage(@Param("query") ProjectLaborCostDTO projectLaborCostDto);

    /**
     * 导出Excel选中数据
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostDetailVO}>
     */
    @BusinessDataScope(scopeDeptNameList = {"income_dept_id", "income_secondary_dept_id", "personnel_dept_id", "personnel_secondary_dept_id"})
    List<ProjectLaborCostDetail> exportList(@Param("query") ProjectLaborCostDTO projectLaborCostDTO);
}
