package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 会计科目
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("会计科目")
@TableName("accounting_account")
@EqualsAndHashCode(callSuper = true)
public class AccountingAccount extends Model<AccountType> {
    /**
    * id
    */
    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 科目编码
    */
    @ApiModelProperty("科目编码")
    private String accountCode;

    /**
    * 科目名称
    */
    @ApiModelProperty("科目名称")
    private String accountName;

    /**
    * 助记词
    */
    @ApiModelProperty("助记词")
    private String mnemonicWords;

    /**
     * 级次
     */
    @ApiModelProperty("级次")
    private Integer level;

    /**
    * 上级科目id
    */
    @ApiModelProperty("上级科目id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long parentId;

    /**
    * 余额方向
    */
    @ApiModelProperty("余额方向")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer balanceDirection;

    /**
    * 会计体系id
    */
    @ApiModelProperty("会计体系id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long accountingSystemId;

    /**
    * 科目类型id
    */
    @ApiModelProperty("科目类型id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long accountTypeId;

    /**
    * 现金分类
    */
    @ApiModelProperty("现金分类")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer cashClassification;

    /**
    * 账页格式
    */
    @ApiModelProperty("账页格式")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer accountPageFormat;

    /**
    * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
    */
    @ApiModelProperty("停用状态")
    private Integer enableStatus;

    /**
    * 停用日期
    */
    @ApiModelProperty("停用日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String suspensionDate;

    /**
    * 辅助核算项集合（,分割）
    */
    @ApiModelProperty("辅助核算项集合")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String auxiliaryCalculateItems;

    /**
    * 自定义核算项集合（,分割）
    */
    @ApiModelProperty("自定义核算项集合")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customizeCalculateItems;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}