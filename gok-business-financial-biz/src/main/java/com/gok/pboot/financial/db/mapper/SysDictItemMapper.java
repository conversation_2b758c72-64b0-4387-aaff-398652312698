package com.gok.pboot.financial.db.mapper;

import com.gok.components.data.datascope.BaseMapper;

import com.gok.pboot.financial.db.entity.SysDictItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 字典项表 Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-02
 **/
public interface SysDictItemMapper extends BaseMapper<SysDictItem> {

    /**
     * 根据字典类型查询字典项
     *
     * @param dictType 字典类型
     * @return {@link List}<{@link SysDictItem}>
     */
    List<SysDictItem> selByType(@Param("dictType") String dictType);
}