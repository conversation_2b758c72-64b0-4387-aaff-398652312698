package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 教育回款跟踪导出模板表
 *
 * <AUTHOR>
 * @since 2023-10-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class EducationExcelVO {

    /**
     * 收款平台value
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("收款平台")
    private String paymentPlatformTxt;

    /**
     * 收款公司
     */
    @ColumnWidth(20)
    @ExcelProperty("收款公司")
    private String paymentCompany;

    /**
     * 交易流水号
     */
    @ColumnWidth(40)
    @ExcelProperty("交易流水号")
    private String transactionNumber;

    /**
     * 到账金额
     */
    @ColumnWidth(20)
    @ExcelProperty("到账金额")
    private String receivedAmount;

    /**
     * 收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 开票状态value（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("开票状态")
    private String invoicingStatusTxt;

    /**
     * 开票日期
     */
    @ColumnWidth(20)
    @ExcelProperty("开票日期")
    private String invoicingDate;

    /**
     * 发票号码
     */
    @ColumnWidth(30)
    @ExcelProperty("发票号码")
    private String invoicingNumber;

    /**
     * 回款备注
     */
    @ColumnWidth(30)
    @ExcelProperty("备注")
    private String paymentNote;

}
