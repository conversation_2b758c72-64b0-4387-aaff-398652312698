package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 教育回款跟踪展示
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class EducationVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 单据编号
     */
    @ColumnWidth(20)
    @ExcelProperty("单据编号")
    private String documentNumber;

    /**
     * 收款平台
     */
    @ColumnWidth(20)
    @ExcelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 收款公司
     */
    @ColumnWidth(20)
    @ExcelProperty("收款公司")
    private String paymentCompany;

    /**
     * 收款平台汇率/费率
     */
    @ExcelIgnore
    private BigDecimal exchangeRate;

    /**
     * 收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收款日期")
    private String paymentDate;

    /**
     * 交易流水号
     */
    @ColumnWidth(40)
    @ExcelProperty("交易流水号")
    private String transactionNumber;

    /**
     * 到账金额
     */
    @ColumnWidth(20)
    @ExcelProperty("到账金额")
    private String receivedAmount;

    /**
     * 不含税金额
     */
    @ColumnWidth(20)
    @ExcelProperty("不含税金额")
    private String amountExcludingTax;

    /**
     * 税额
     */
    @ColumnWidth(20)
    @ExcelProperty("税额")
    private String taxAmount;

    /**
     * 手续费
     */
    @ColumnWidth(20)
    @ExcelProperty("手续费")
    private String commission;

    /**
     * 实际到账金额
     */
    @ColumnWidth(20)
    @ExcelProperty("实际金额")
    private String actualAmountReceived;

    /**
     * 开票状态code（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    @ExcelIgnore
    private Integer invoicingStatus;

    /**
     * 开票状态value（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("开票状态")
    private String invoicingStatusTxt;

    /**
     * 开票日期
     */
    @ColumnWidth(20)
    @ExcelProperty("开票日期")
    private String invoicingDate;

    /**
     * 发票号码
     */
    @ColumnWidth(30)
    @ExcelProperty("发票号码")
    private String invoicingNumber;

    /**
     * 客户名称
     */
    @ColumnWidth(20)
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ColumnWidth(20)
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 回款一级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款归属一级部门")
    private String paymentDept;

    /**
     * 回款二级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款归属二级部门")
    private String paymentSecondaryDept;

    /**
     * 学校名称
     */
    @ColumnWidth(20)
    @ExcelProperty("学校名称")
    private String schoolName;

    /**
     * 产品名称
     */
    @ColumnWidth(20)
    @ExcelProperty("产品名称")
    private String productName;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目名称
     */
    @ColumnWidth(30)
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 项目编码
     */
    @ColumnWidth(30)
    @ExcelProperty("项目编码")
    private String projectCode;

    /**
     * 大客户经理id
     */
    @ExcelIgnore
    private String keyAccountManagerId;

    /**
     * 大客户经理姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("大客户经理")
    private String keyAccountManagerName;

    /**
     * 销售主管id
     */
    @ExcelIgnore
    private Long salesExecutiveId;

    /**
     * 销售主管姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("销售主管")
    private String salesExecutiveName;

    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long accountManagerId;

    /**
     * 客户经理姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("客户经理")
    private String accountManagerName;

    /**
     * 咨询顾问id
     */
    @ExcelIgnore
    private Long consultantUserId;

    /**
     * 咨询顾问姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("咨询顾问")
    private String consultantUserName;

    /**
     * 确认收入日期
     */
    @ColumnWidth(20)
    @ExcelProperty("确认收入日期")
    private String sureRevenueDate;

    /**
     * 认领状态code（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ExcelIgnore
    private Integer claimStatus;

    /**
     * 认领状态value（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("认领状态")
    private String claimStatusTxt;

    /**
     * 锁定状态code（0已锁定，1未锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ExcelIgnore
    private Integer lockStatus;

    /**
     * 锁定状态value（0已锁定，1未锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("锁定状态")
    private String lockStatusTxt;

    /**
     * 汇总状态code（0已汇总，1未汇总）
     * {@link com.gok.pboot.financial.enums.SummaryStatusEnum}
     */
    @ExcelIgnore
    private Integer summaryStatus;

    /**
     * 汇总状态value（0已汇总，1未汇总）
     * {@link com.gok.pboot.financial.enums.SummaryStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("汇总状态")
    private String summaryStatusTxt;

    /**
     * 汇总显示状态
     */
    @ExcelIgnore
    private Boolean summaryView;

    /**
     * 创建人姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("创建人")
    private String creatorName;

    /**
     * 认领人姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("认领人")
    private String claimantName;

    /**
     * 认领日期
     */
    @ColumnWidth(20)
    @ExcelProperty("认领日期")
    private String claimantDate;

    /**
     * 回款备注
     */
    @ColumnWidth(30)
    @ExcelProperty("备注")
    private String paymentNote;

    /**
     * 认领备注
     */
    @ExcelIgnore
    private String claimRemark;

}
