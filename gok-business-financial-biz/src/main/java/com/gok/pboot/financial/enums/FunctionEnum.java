package com.gok.pboot.financial.enums;

import lombok.Getter;

/**
 * 模块/功能名称 Enum
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
public enum FunctionEnum {

    /**
     * 应收账款台帐
     */
    ACCOUNTS_RECEIVABLE("应收账款台帐"),

    /**
     * 销售收款计划
     */
    SALES_RECEIPT("销售收款计划"),

    /**
     * 项目回款跟踪表
     */
    PROJECT_PAYMENT("项目回款跟踪表"),

    /**
     * 教育回款跟踪
     */
    EDUCATION_PAYMENT("教育回款跟踪"),

    /**
     * 项目人工成本分摊汇总
     */
    PROJECT_LABOR_COST("项目人工成本分摊汇总"),

    /**
     * 项目人工成本分摊明细
     */
    PROJECT_LABOR_COST_DETAIL("项目人工成本分摊明细"),

    /**
     * 跨部门人工成本台账
     */
    CROSS_DEPT_LABOR_COSTS("跨部门人工成本台账"),

    /**
     * 跨部门人工成本台账明细
     */
    CROSS_DEPT_LABOR_COSTS_DETAIL("跨部门人工成本台账明细"),

    /**
     * 工资汇总
     */
    WAGE_COST("工资汇总"),

    /**
     * 项目收入台账
     */
    PROJECT_INCOME_ACCOUNT("项目收入台账"),

    /**
     * 收款方式
     */
    PAYMENT_METHOD("收款方式"),

    /**
     * 指标科目关系
     */
    INDICATOR_ACCOUNT_RELATIONSHIP("指标科目关系"),

    /**
     * 会计账套
     */
    ACCOUNTING_SET("会计账套"),
    /**
     * 会计体系
     */
    ACCOUNTING_SYSTEM("会计体系"),
    /**
     * 科目类型
     */
    ACCOUNT_TYPE("科目类型"),
    /**
     * 会计科目
     */
    ACCOUNTING_ACCOUNT("会计科目")
    ;

    private final String name;

    FunctionEnum(String name) {
        this.name = name;
    }
}
