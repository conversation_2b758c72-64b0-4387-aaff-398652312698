package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户台帐
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAccountVO {

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 备案人姓名
     */
    private String recordMan;

    /**
     * 备案人id
     */
    private Long recordManId;
}
