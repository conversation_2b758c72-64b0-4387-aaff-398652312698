package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 教育回款跟踪认领
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("education_payment")
@ApiModel("教育回款跟踪认领")
public class EducationPayment extends Model<EducationPayment> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 单据编号
     */
    @ApiModelProperty("单据编号")
    private String documentNumber;

    /**
     * 收款平台
     */
    @ApiModelProperty("收款平台")
    private String paymentPlatform;

    /**
     * 收款公司
     */
    @ApiModelProperty("收款公司")
    private String paymentCompany;

    /**
     * 收款日期
     */
    @ApiModelProperty("收款日期")
    private String paymentDate;

    /**
     * 交易流水号
     */
    @ApiModelProperty("交易流水号")
    private String transactionNumber;

    /**
     * 到账金额
     */
    @ApiModelProperty("到账金额")
    private BigDecimal receivedAmount;

    /**
     * 不含税金额
     */
    @ApiModelProperty("不含税金额")
    private BigDecimal amountExcludingTax;

    /**
     * 税额
     */
    @ApiModelProperty("税额")
    private BigDecimal taxAmount;

    /**
     * 手续费
     */
    @ApiModelProperty("手续费")
    private BigDecimal commission;

    /**
     * 实际到账金额
     */
    @ApiModelProperty("实际到账金额")
    private BigDecimal actualAmountReceived;

    /**
     * 回款备注
     */
    @ApiModelProperty("回款备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String paymentNote;

    /**
     * 开票状态（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    @ApiModelProperty("开票状态")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer invoicingStatus;

    /**
     * 开票日期
     */
    @ApiModelProperty("开票日期")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String invoicingDate;

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String invoicingNumber;

    /**
     * 认领状态（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ApiModelProperty("认领状态")
    private Integer claimStatus;

    /**
     * 锁定状态（0已锁定，1未锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ApiModelProperty("锁定状态")
    private Integer lockStatus;

    /**
     * 自动锁定（0自动，1取消）
     * {@link com.gok.pboot.financial.enums.AutoLockEnum}
     */
    @ApiModelProperty("锁定状态")
    private Integer autoLock;

    /**
     * 汇总状态（0已汇总，1未汇总）
     * {@link com.gok.pboot.financial.enums.SummaryStatusEnum}
     */
    @ApiModelProperty("汇总状态")
    private Integer summaryStatus;

    /**
     * 创建人id
     */
    @ApiModelProperty("创建人id")
    private Long creatorId;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String creatorName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

    /**
     * 教育回款跟踪认领
     */
    @TableField(exist = false)
    private EducationPaymentClaim educationPaymentClaim;

}