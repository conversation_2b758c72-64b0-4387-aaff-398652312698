package com.gok.pboot.financial.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 会计体系分页
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class AccountingSystemPageDTO implements Serializable {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * 账套编码
     */
    private String systemCode;

    /**
     * 账套类型
     */
    private String accountingType;

    /**
     * 停用状态（0 否， 1是）
     */
    private Integer enableStatus;

    /**
     * 备注
     */
    private String systemRemarks;

    /**
     * ids
     */
    private List<Long> idList;

    private List<String> includeExcel;

}
