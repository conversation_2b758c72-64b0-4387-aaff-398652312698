package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 跨部门人工成本台帐
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptArtificialCostDTO {

    /**
     * 导出Excel需要的部门id
     */
    private List<Long> ids;

    /**
     * 人员所属一级部门id
     */
    private Long personnelDeptId;

    /**
     * 所属月份起始
     */
    private String yearMonthDayStart;

    /**
     * 所属月份结束
     */
    private String yearMonthDayEnd;

    /**
     * 部门id
     */
    private List<Long> deptId;

    /**
     * 人工成本最小值
     */
    private BigDecimal artificialCostMin;

    /**
     * 人工成本最小值
     */
    private BigDecimal artificialCostMax;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    private List<String> includeExcel;
}
