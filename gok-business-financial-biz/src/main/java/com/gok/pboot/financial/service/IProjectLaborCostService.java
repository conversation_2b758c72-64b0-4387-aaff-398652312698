package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectLaborCost;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.vo.ProjectLaborCostVO;
import com.gok.pboot.financial.vo.PushResultVO;

import java.util.List;

/**
 * 项目人工成本分摊汇总 Service
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
public interface IProjectLaborCostService extends IService<ProjectLaborCost> {

    /**
     * 模糊查询带分页
     *
     * @param page 分页请求
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}<{@link ProjectLaborCostVO}>
     */
    StatisticsPage<ProjectLaborCostVO> findPage(Page<ProjectLaborCost> page, ProjectLaborCostDTO projectLaborCostDto);

    /**
     * 导出Excel选中数据
     *
     * @param projectLaborCostDto {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostVO}>
     */
    List<ProjectLaborCostVO> export(ProjectLaborCostDTO projectLaborCostDto);

    /**
     * 推送
     *
     * @param ids ids
     * @return {@link R}
     */
    PushResultVO push(List<String> ids);
}
