package com.gok.pboot.financial.vo;

import com.gok.pboot.financial.dto.SalesReceiptPaymentDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 销售收款计划回款任务详情VO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPaymentTaskVO {

    /**
    * 销售收款id
    */
    private Long id;

    /**
     * 是否有任务进度详情
     */
    private Boolean taskFlag;

    /**
     * 任务进度详情
     */
    private SalesReceiptTaskDetailVO taskDetailVO;

    /**
     * 回款概况
     */
    private SalesReceiptPaymentDetailVO paymentDetailVO;

    /**
     * 销售收款计划客户付款审批流程
     */
    private List<SalesReceiptPaymentProcessVO> paymentProcessVOList;
}