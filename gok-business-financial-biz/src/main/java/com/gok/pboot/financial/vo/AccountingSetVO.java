package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.gok.module.excel.api.annotation.ExcelSelected;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.excel.AccountSetDictSelect;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 会计账套视图
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class AccountingSetVO implements Serializable {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 账套编码
     */
    @ColumnWidth(20)
    @ExcelProperty("账套编码")
    private String accountingCode;

    /**
     * 归属主体
     */
    @ColumnWidth(30)
    @ExcelProperty("归属主体")
    @ExcelSelected(sourceClass = AccountSetDictSelect.class, code = DictConstant.BELONGING_SUBJECT_MAIN_ID)
    private String accountingSubject;

    /**
     * 账号
     */
    @ColumnWidth(30)
    @ExcelProperty("账套账号")
    private String accountingAccount;

    /**
     * 密码
     */
    @ColumnWidth(30)
    @ExcelProperty("账套密码")
    private String accountingPassword;

    /**
     * 停用状态{@link com.gok.pboot.financial.enums.EnableStatusEnum}
     */
    @ExcelIgnore
    private Integer enableStatus;

    /**
     * 停用状态文本{@link com.gok.pboot.financial.enums.EnableStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("停用状态")
    @ExcelSelected(source = {"是","否"})
    private String enableStatusTxt;

    /**
     * 默认资金账户
     */
    @ExcelIgnore
    private Long fundAccountId;

    /**
     * 默认资金账户
     */
    @ColumnWidth(30)
    @ExcelProperty("默认资金账户")
    @ExcelSelected(sourceClass = AccountSetDictSelect.class,code = "fundAccount")
    private String fundAccount;

    /**
     * 账套类型
     */
    @ColumnWidth(30)
    @ExcelProperty("账套类型")
    @ExcelSelected(source = {"0","1","2","3","4","5","6","7","8","9",})
    private String accountingType;

    /**
     * 账套备注
     */
    @ColumnWidth(30)
    @ExcelProperty("账套备注")
    private String accountingRemarks;
}
