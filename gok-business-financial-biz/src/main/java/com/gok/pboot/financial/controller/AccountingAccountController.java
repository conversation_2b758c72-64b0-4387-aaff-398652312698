package com.gok.pboot.financial.controller;

import cn.hutool.core.lang.tree.Tree;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.RequestExcel;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.handler.SelectedSheetWriteHandler;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.service.IAccountingAccountService;
import com.gok.pboot.financial.vo.AccountingAccountPageVO;
import com.gok.pboot.financial.vo.AccountingAccountVO;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.FundAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.CharEncoding;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 会计科目控制层
 *
 * <AUTHOR>
 * @since 2024-12-20
 * @menu 会计科目
 */
@RestController
@Api(tags = "会计科目")
@RequiredArgsConstructor
@RequestMapping("/accounting-account")
public class AccountingAccountController {

    private final IAccountingAccountService service;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link AccountingAccountPageDTO}
     * @return {@link R}<{@link Page}<{@link AccountingAccountPageVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<AccountingAccountPageVO>> findPage(@RequestBody @Valid AccountingAccountPageDTO dto) {
        return R.ok(service.findPage(dto));
    }

    /**
     * 导出Excel
     *
     * @param dto {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSetVO}>
     */
    @PostMapping("/export-excel")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(async = true, name = "会计科目",
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<AccountingAccountPageVO> exportExcel(@RequestBody AccountingAccountPageDTO dto) {
        return service.exportExcel(dto);
    }

    /**
     * 查看
     *
     * @return {@link FundAccountVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<AccountingAccountVO> info(@PathVariable Long id) {
        return service.info(id);
    }

    /**
     * 新增
     *
     * @param dto {@link AccountingAccountDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/add-edit")
    @ApiOperation(value = "新增", notes = "新增")
    public R<String> addOrEdit(@RequestBody @Valid AccountingAccountDTO dto) {
        return service.addOrEdit(dto);
    }

    /**
     * 删除
     *
     * @param dto {@link AccountingAccountPageDTO}
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R<Void> del(@RequestBody AccountingAccountDTO dto) {
        return service.del(dto.getIdList()) ? R.ok() : R.failed();
    }

    /**
     * 启用
     *
     * @param dto {@link AccountingSetDTO}
     * @return {@link R}<{@link Void>
     */
    @PutMapping("/enable")
    @ApiOperation(value = "启用", notes = "启用")
    public R<Void> enable(@RequestBody AccountingAccountDTO dto) {
        return service.enable(dto) ? R.ok() : R.failed();
    }

    @GetMapping("/excel-model")
    @ApiOperation(value = "导入模板", notes = "导入模板")
    public void excelModel(HttpServletResponse response) throws IOException {
        final String fileName = "会计科目导入模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding(CharEncoding.UTF_8);
        response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharEncoding.UTF_8) + ExcelTypeEnum.XLSX.getValue());
        int columnSize = 8;
        List<String> emptyList = new ArrayList<>();
        for (int i = 0; i < columnSize; i++) {
            emptyList.add(StringPool.EMPTY);
        }
        EasyExcelFactory.write(response.getOutputStream())
                .head(AccountingAccountImportDTO.class)
                .autoCloseStream(true)
                .excelType(ExcelTypeEnum.XLSX)
                .registerWriteHandler(new SelectedSheetWriteHandler(AccountingAccountImportDTO.class))
                .sheet("导入数据").doWrite(Collections.singletonList(emptyList));
    }

    /**
     * 查询完整科目结构
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/accountTree")
    @ApiOperation(value = "查询完整科目结构", notes = "查询完整科目结构")
    public R<List<Tree<Long>>> accountTree() {
        return R.ok(service.accountTree());
    }

    /**
     * 导入
     * @param importDTOList {@link List}<{@link AccountingAccountImportDTO}>
     * @return {@link R}<{@link String>
     */
    @PostMapping("/import-excel")
    @ApiOperation(value = "导入Excel", notes = "导入Excel")
    public R<String> importExcel(@RequestExcel List<AccountingAccountImportDTO> importDTOList) {
        return service.importExcel(importDTOList);
    }

    /**
     * 同步
     * @return
     */
    @PostMapping("/sync")
    @ApiOperation(value = "同步", notes = "同步")
    public R<String> sync(){
        return service.sync();
    }


    /**
     * 根据科目编码查看
     *
     * @return {@link FundAccountVO}
     */
    @PostMapping("info/code")
    @ApiOperation(value = "根据科目编码查看", notes = "根据科目编码查看")
    public R<AccountingAccountVO> getInfoByDto(@RequestBody @Valid AccountingAccountDTO dto) {
        return service.getInfoByDto(dto.getAccountCode());
    }
}
