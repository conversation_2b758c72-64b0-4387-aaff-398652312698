package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 业务类型枚举
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Getter
public enum BusinessTypeEnum implements ValueEnum<Integer> {

    /**
     * 教学相关
     */
    TEACHING(0, "教学相关"),

    /**
     * 非教学相关
     */
    NON_TEACHING(1, "非教学相关");

    private final Integer value;

    private final String name;

    BusinessTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
