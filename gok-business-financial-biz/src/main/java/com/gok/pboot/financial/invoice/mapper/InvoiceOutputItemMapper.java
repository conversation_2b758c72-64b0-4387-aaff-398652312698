package com.gok.pboot.financial.invoice.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BaseMapper;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.invoice.entity.domain.InvoiceOutputItem;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputItemDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputItemVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/06/10
 **/
@Mapper
public interface InvoiceOutputItemMapper extends BaseMapper<InvoiceOutputItem> {

    /**
     * 分页查询
     *
     * @param page 分页请求
     * @param dto  查询请求
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    Page<InvoiceOutputItemVO> findPage(Page<InvoiceOutputItem> page, @Param("query") InvoiceOutputItemDTO dto);

    /**
     * 获取分页合计值
     *
     * @param dto
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    Map<String, BigDecimal> findPageSum(@Param("query") InvoiceOutputItemDTO dto);

} 