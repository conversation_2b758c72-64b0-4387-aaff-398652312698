package com.gok.pboot.financial.service;

import com.gok.pboot.financial.db.entity.SalesReceiptDetails;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.vo.SalesReceiptDetailsVO;

/**
 * 销售收款明细Service
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt_details(销售收款明细)】的数据库操作Service
 * @createDate 2023-10-07 11:24:50
*/
public interface ISalesReceiptDetailsService extends IService<SalesReceiptDetails> {

    /**
     * 销售明细
     *
     * @param id id
     * @param contractId 合同id
     * @return {@link SalesReceiptDetailsVO}
     */
    SalesReceiptDetailsVO salesReceiptDetailList(Long id,Long contractId);
}
