package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 应收账款明细 展示
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountsReceivableDetailVO {

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    private String projectStatus;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    private String projectStatusTxt;

    /**
     * 项目编码
     */
    private String projectNo;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 合同签订日期
     */
    private String signingDate;

    /**
     * 合同金额
     */
    private String contractMoney;

    /**
     * 归属主体
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private String attributableSubject;

    /**
     * 归属主体
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private String attributableSubjectTxt;

    /**
     * 一级部门
     */
    private String firstDepartment;

    /**
     * 客户经理人
     */
    private String salesmanUserName;

    /**
     * 项目经理
     */
    private String managerUserName;

    /**
     * 所属月份 - 回款金额 - 开票金额
     */
    private List<MouthForMoneyVO> mouthForMoneyVOList;

    /**
     * 总计：回款金额
     */
    private String payBack;

    /**
     * 总计：开票金额
     */
    private String invoicing;
}
