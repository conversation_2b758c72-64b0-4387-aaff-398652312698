package com.gok.pboot.financial.invoice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.domain.InvoiceOutputItem;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputItemDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputItemVO;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/06/10
 **/
public interface IInvoiceOutputItemService extends IService<InvoiceOutputItem> {

    /**
     * 分页查询
     *
     * @param dto 查询请求
     * @return
     */
    StatisticsPage<InvoiceOutputItemVO> findPage(InvoiceOutputItemDTO dto);

    /**
     * 导出销项发票明细列表
     *
     * @param dto 查询请求
     * @return
     */
    List<InvoiceOutputItemVO> exportExcel(InvoiceOutputItemDTO dto);

} 