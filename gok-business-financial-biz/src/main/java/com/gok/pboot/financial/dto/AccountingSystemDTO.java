package com.gok.pboot.financial.dto;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 业务传参
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
public class AccountingSystemDTO implements Serializable {

    /**
     * ids
     */
    private List<Long> idList;

    /**
     * 启用
     */
    private Boolean enableStatusFlag;

    /**
     * id
     */
    private Long id;

    /**
     * 体系编码
     */
    @Length(max = 20, message = "体系编码不能超过20")
    private String systemCode;

    /**
     * 体系名称
     */
    @Length(max = 20, message = "体系名称不能超过20")
    private String systemName;

    /**
     * 账套类型
     */
    private String accountingType;

    /**
     * 停用状态
     */
    private Integer enableStatus;

    /**
     * 备注
     */
    private String systemRemarks;

}
