package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/6/5
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPaymentProcessDTO {
    /**
     * id
     */
    private Long id;

    /**
     * 审批节点名称
     */
    private String approvalProcess;

    /**
     * 审批状态(0未开始 1进行中 2已完成)
     */
    private Integer approvalStatus;
}
