package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 项目类型枚举
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Getter
public enum ProjectTypeEnum implements ValueEnum<Integer> {

    /**
     * 内部项目
     */
    INTERNAL_PROJECT(1, "内部项目"),

    /**
     * 收入项目
     */
    INCOME_PROJECT(2, "收入项目");

    private final Integer value;

    private final String name;

    ProjectTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    /**
     * 根据value获取name
     *
     * @param value 值
     * @return 名称
     */
    public static String getNameByVal(Integer value) {
        for (ProjectTypeEnum businessTypeEnum : ProjectTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum.name;
            }
        }
        return "";
    }

    /**
     * 根据名称获取值
     *
     * @param name 名称
     * @return 值
     */
    public static Integer getValByName(String name) {
        for (ProjectTypeEnum businessTypeEnum : ProjectTypeEnum.values()) {
            if (businessTypeEnum.getName().equals(name)) {
                return businessTypeEnum.getValue();
            }
        }
        return null;
    }

    /**
     * 根据值获取枚举类型
     *
     * @param value 值
     * @return {@link ProjectTypeEnum}
     */
    public static ProjectTypeEnum getBusinessTypeEnum(Integer value) {
        for (ProjectTypeEnum businessTypeEnum : ProjectTypeEnum.values()) {
            if (businessTypeEnum.value.equals(value)) {
                return businessTypeEnum;
            }
        }
        return null;
    }
}
