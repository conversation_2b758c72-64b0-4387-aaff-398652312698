package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 余额方向 Enum
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum BalanceDirectionEnum implements ValueEnum<Integer> {

    /**
     * 借方
     */
    DEBTOR(0, "借方"),

    /**
     * 贷方
     */
    CREDITOR(1, "贷方");

    private final Integer value;

    private final String name;

    BalanceDirectionEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
