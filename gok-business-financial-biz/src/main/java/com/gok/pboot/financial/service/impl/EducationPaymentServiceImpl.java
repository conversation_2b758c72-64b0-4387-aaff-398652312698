package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.base.admin.dto.PaymentDTO;
import com.gok.bcp.message.dto.BcpMessageTargetDTO;
import com.gok.bcp.message.entity.enums.MsgTypeEnum;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.bcp.message.entity.enums.TargetTypeEnum;
import com.gok.bcp.message.entity.model.MailModel;
import com.gok.bcp.message.feign.RemoteMailService;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.bcp.upms.feign.RemoteRoleService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.enums.YesOrNoEnum;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.RedisConstant;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.EducationPayment;
import com.gok.pboot.financial.db.entity.EducationPaymentClaim;
import com.gok.pboot.financial.db.entity.PaymentMethod;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.db.mapper.EducationPaymentClaimMapper;
import com.gok.pboot.financial.db.mapper.EducationPaymentMapper;
import com.gok.pboot.financial.db.mapper.PaymentMethodMapper;
import com.gok.pboot.financial.db.mapper.ProjectPaymentMapper;
import com.gok.pboot.financial.dto.EducationDTO;
import com.gok.pboot.financial.dto.EducationPaymentClaimDTO;
import com.gok.pboot.financial.dto.EducationPaymentDTO;
import com.gok.pboot.financial.dto.EducationPaymentExcelDTO;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IEducationPaymentService;
import com.gok.pboot.financial.util.*;
import com.gok.pboot.financial.vo.*;
import com.gok.pboot.pms.feign.IRemotePaymentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 教育回款ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EducationPaymentServiceImpl extends ServiceImpl<EducationPaymentMapper, EducationPayment>
        implements IEducationPaymentService {

    private final EducationPaymentClaimMapper educationPaymentClaimMapper;

    private final ProjectPaymentMapper projectPaymentMapper;

    private final PaymentMethodMapper paymentMethodMapper;

    private final StringRedisTemplate stringRedisTemplate;

    private final RemoteMailService remoteMailService;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final RemoteRoleService remoteRoleService;

    private final LogRecordUtils logRecordUtils;

    private final DBApi dbApi;

    private final IRemotePaymentService iRemotePaymentService;

    /**
     * 模糊查询带分页
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link Page}<{@link EducationVO}>
     */
    @Override
    public StatisticsPage<EducationVO> findPage(Page<EducationPayment> page, EducationDTO educationDTO) {
        List<EducationVO> educationVOList = new ArrayList<>();
        StatisticsPage<EducationVO> educationVOPage = new StatisticsPage<>(page.getCurrent(), page.getSize());
        // 分页查询
        Page<EducationPayment> educationPaymentPage = baseMapper.findPage(page, condition(educationDTO));
        if (CollUtil.isNotEmpty(educationPaymentPage.getRecords())) {
            BeanUtil.copyProperties(educationPaymentPage, educationVOPage, "records");
            educationVOList = this.addVoToList(educationPaymentPage.getRecords());
            // 放置费率
            List<PaymentMethod> paymentMethodList = paymentMethodMapper.selectList(Wrappers.<PaymentMethod>lambdaQuery()
                    .eq(PaymentMethod::getDelFlag, YesOrNoEnum.NO.getVal())
                    .eq(PaymentMethod::getEnableStatus, EnableStatusEnum.YES.getValue())
            );
            if (CollUtil.isNotEmpty(paymentMethodList)) {
                Map<String, BigDecimal> paymentMethodMap = paymentMethodList.stream()
                        .collect(Collectors.toMap(PaymentMethod::getName, PaymentMethod::getExchangeRate, (a, b) -> a));
                educationVOList.forEach(e -> e.setExchangeRate(paymentMethodMap.get(e.getPaymentPlatform())));
            }
            // 查询项目回款数据（已推送且已锁定）返回不支持取消汇总
            List<ProjectPayment> projectPaymentList = projectPaymentMapper.listByPushOrLock();
            if (CollUtil.isNotEmpty(projectPaymentList)) {
                Set<Long> set = new HashSet<>();
                projectPaymentList.forEach(p -> {
                    if (CharSequenceUtil.isNotBlank(p.getEducationIds())) {
                        Arrays.stream(p.getEducationIds().split(StrPool.COMMA))
                                .filter(educationId -> !CharSequenceUtil.EMPTY.equals(educationId))
                                .forEach(e -> set.add(Long.valueOf(e)));
                    }
                });
                educationVOList.forEach(e -> {
                    if (set.contains(e.getId())) {
                        e.setSummaryView(Boolean.TRUE);
                    }
                });
            }
        }
        statistics(educationVOPage, educationDTO);
        educationVOPage.setRecords(educationVOList);
        return educationVOPage;
    }

    private void statistics(StatisticsPage<EducationVO> page, EducationDTO educationDTO) {
        if (page.getTotal() == 0) {
            return;
        }
        Map<String, BigDecimal> resultMap = baseMapper.statistics(educationDTO);
        page.setStatistics(resultMap);
    }

    /**
     * 导出所选数据
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link List}<
     */
    @Override
    public List export(EducationDTO educationDTO) {
        // 导出模板表
        if (CollUtil.isNotEmpty(educationDTO.getIds())) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(DateUtils.SIMPLE_DATE_FORMAT_2);
            if (NumberUtils.LONG_MINUS_ONE.equals(educationDTO.getIds().get(NumberUtils.INTEGER_ZERO))) {
                List<EducationExcelVO> educationExcelVOList = new ArrayList<>();
                Date date = new Date();
                EducationExcelVO educationVO = EducationExcelVO.builder()
                        .paymentDate(simpleDateFormat.format(date))
                        .invoicingDate(simpleDateFormat.format(date))
                        .build();
                educationExcelVOList.add(educationVO);
                return educationExcelVOList;
            }
        }
        // 导出具体数据
        List<EducationPayment> educationPaymentList = baseMapper.findPage(condition(educationDTO));
        if (CollUtil.isNotEmpty(educationPaymentList)) {
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                    OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + educationPaymentList.size() + "条】");

            return this.addVoToList(educationPaymentList);
        }
        return Collections.singletonList(new EducationVO());
    }

    /**
     * 取消认领（已认领 -> 待认领）
     *
     * @param id 教育回款id
     * @return R {@link R}
     */
    @Override
    public R<String> claim(Long id) {
        // 已认领 + 待锁定
        EducationPayment educationPayment = baseMapper.selectOne(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getId, id)
                .eq(EducationPayment::getClaimStatus, ClaimStatusEnum.YES.getValue())
                .eq(EducationPayment::getLockStatus, LockStatusEnum.NO.getValue())
        );
        if (Optional.ofNullable(educationPayment).isPresent()) {
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                    OperationEnum.UN_CLAIM_PAYMENT,
                    "【" + educationPayment.getDocumentNumber() + "】" + OperationEnum.UN_CLAIM_PAYMENT.getName());

            educationPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());

            LambdaQueryWrapper<EducationPaymentClaim> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(EducationPaymentClaim::getEducationPaymentId, id);

            return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO
                    && educationPaymentClaimMapper.delete(queryWrapper) > NumberUtils.INTEGER_ZERO
                    ? R.ok("取消认领成功") : R.failed("取消认领失败");
        }

        return R.failed("无数据取消认领");
    }

    /**
     * 认领（待认领 -> 已认领）
     *
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     * @return {@code true} or {@code false}
     */
    @Override
    public Boolean claim(EducationPaymentClaimDTO educationPaymentClaimDTO) {
        // 1、待认领 + 待锁定
        EducationPayment educationPayment = baseMapper.selectOne(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getId, educationPaymentClaimDTO.getId())
                .eq(EducationPayment::getLockStatus, LockStatusEnum.NO.getValue())
        );
        // 2、由于认领和编辑是同一个接口 所以如果有认领数据就是更新 无认领数据就是插入
        if (Optional.ofNullable(educationPayment).isPresent()) {
            // 2.1、处理教育回款数据
            CopyOptions educationPaymentOption = CopyOptions.create()
                            .setIgnoreNullValue(true)
                            .setIgnoreError(true);
            BeanUtil.copyProperties(educationPaymentClaimDTO, educationPayment, educationPaymentOption);
            educationPayment.setClaimStatus(ClaimStatusEnum.YES.getValue());
            // 2.2、处理教育回款认领数据
            EducationPaymentClaim educationPaymentClaim = educationPaymentClaimMapper.selectOne(Wrappers.<EducationPaymentClaim>lambdaQuery()
                    .eq(EducationPaymentClaim::getDelFlag, YesOrNoEnum.NO.getVal())
                    .eq(EducationPaymentClaim::getEducationPaymentId, educationPayment.getId())
            );
            // 有认领数据做更新操作
            if (Optional.ofNullable(educationPaymentClaim).isPresent()) {
                // 赋值部分id
                this.getIdByName(educationPaymentClaim, educationPaymentClaimDTO);
                CopyOptions educationPaymentClaimOption = CopyOptions.create()
                        .setIgnoreNullValue(true)
                        .setIgnoreError(true)
                        .setIgnoreProperties("id", "paymentDeptId", "paymentSecondaryDeptId");
                BeanUtil.copyProperties(educationPaymentClaimDTO, educationPaymentClaim, educationPaymentClaimOption);
                // 教育回款默认认领人为当前用户
                educationPaymentClaim.setClaimantId(UserUtils.getUser().getId());
                educationPaymentClaim.setClaimantName(UserUtils.getUser().getName());
                // 记录日志
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                        OperationEnum.AGAIN_CLAIM_PAYMENT,
                        "【" + educationPayment.getDocumentNumber() + "】【" + educationPaymentClaimDTO.getSalesmanUserName() + "】"
                                + OperationEnum.AGAIN_CLAIM_PAYMENT.getName() + "成功");

                return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO
                        && educationPaymentClaimMapper.updateById(educationPaymentClaim) > NumberUtils.INTEGER_ZERO;
            }
            // 无认领数据做插入操作
            educationPaymentClaim = BeanUtil.copyProperties(educationPaymentClaimDTO, EducationPaymentClaim.class, "id");
            // 赋值部分id
            educationPaymentClaim.setEducationPaymentId(educationPayment.getId());
            educationPaymentClaim.setPaymentDept(null);
            educationPaymentClaim.setPaymentSecondaryDept(null);
            this.getIdByName(educationPaymentClaim, educationPaymentClaimDTO);
            // 教育回款默认认领人为当前用户
            educationPaymentClaim.setClaimantId(UserUtils.getUser().getId());
            educationPaymentClaim.setClaimantName(UserUtils.getUser().getName());
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                    OperationEnum.CLAIM_PAYMENT,
                    "【" + educationPayment.getDocumentNumber() + "】【" + educationPaymentClaimDTO.getSalesmanUserName() + "】"
                            + OperationEnum.CLAIM_PAYMENT.getName() + "成功");

            return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO
                    && educationPaymentClaimMapper.insert(educationPaymentClaim) > NumberUtils.INTEGER_ZERO;
        }

        return Boolean.TRUE;
    }

    /**
     * 新增教育回款
     *
     * @param educationPaymentDTO {@link EducationPaymentDTO}
     * @return {@code 0} or {@code 1}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Integer save(EducationPaymentDTO educationPaymentDTO) {
        EducationPayment educationPayment = BeanUtil.copyProperties(educationPaymentDTO, EducationPayment.class, "id");
        // 单据编号
        String number = RedisConstant.EDUCATION_DOCUMENT_NUMBERPREFIX +
                new SimpleDateFormat(DateUtils.YEAR_MONTH).format(new Date()) +
                String.format("%04d", stringRedisTemplate.opsForValue().increment(RedisConstant.EDUCATION_DOCUMENT_NUMBER));
        educationPayment.setDocumentNumber(number);
        // 认领状态、锁定状态、汇总状态
        educationPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());
        educationPayment.setLockStatus(LockStatusEnum.NO.getValue());
        educationPayment.setSummaryStatus(SummaryStatusEnum.NO.getValue());
        educationPayment.setCreatorId(UserUtils.getUser().getId());
        educationPayment.setCreatorName(UserUtils.getUser().getName());
        // 认领日期自动锁定
        if (DateUtils.difference(educationPaymentDTO.getPaymentDate()) > DateUtils.THIRTY_DAYS) {
            educationPayment.setLockStatus(LockStatusEnum.YES.getValue());
            educationPayment.setAutoLock(AutoLockEnum.YES.getValue());
        }
        // 记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                OperationEnum.ADD_PAYMENT_METHOD, OperationEnum.ADD_PAYMENT_METHOD.getName() + "【" + number + "】");

        return baseMapper.insert(educationPayment);
    }

    /**
     * 汇总
     *
     * @param paymentDTO {@link PaymentDTO}
     * @return {@code true} or {@code false}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean summary(PaymentDTO paymentDTO) {
        // 1、查询教育回款信息
        List<EducationPayment> educationPaymentList = baseMapper.selectList(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getSummaryStatus, SummaryStatusEnum.NO.getValue())
                .in(CollUtil.isNotEmpty(paymentDTO.getIds()), EducationPayment::getId, paymentDTO.getIds())
        );
        // 2、计算选中教育回款的到账金额 与 id组合
        StringBuilder sb = new StringBuilder();
        BigDecimal paymentAmount = BigDecimal.ZERO;
        StringBuilder ids = new StringBuilder(CharSequenceUtil.EMPTY);
        for (EducationPayment educationPayment : educationPaymentList) {
            educationPayment.setSummaryStatus(SummaryStatusEnum.YES.getValue());
            paymentAmount = paymentAmount.add(educationPayment.getReceivedAmount());
            ids.append(educationPayment.getId()).append(StrPool.COMMA);
            sb.append("【").append(educationPayment.getDocumentNumber()).append("】");
        }
        // 业务一体化
        Long id = IdUtil.getSnowflake().nextId();
        paymentDTO.setId(id);
        paymentDTO.setUserId(UserUtils.getUser().getId());
        paymentDTO.setUsername(UserUtils.getUser().getName());
        // 单据编号
        String number = RedisConstant.PROJECT_DOCUMENT_NUMBER_PREFIX +
                new SimpleDateFormat(DateUtils.YEAR_MONTH).format(new Date()) +
                String.format("%04d", stringRedisTemplate.opsForValue().increment(RedisConstant.PROJECT_DOCUMENT_NUMBER));
        paymentDTO.setDocumentNumber(number);

        // 3、生成项目回款跟踪数据
        ProjectPayment projectPayment = BeanUtil.copyProperties(paymentDTO, ProjectPayment.class, "paymentCompany");
        // 4、填充属性
        // 4.1、收款金额
        projectPayment.setPaymentAmount(paymentAmount.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
        // 4.2、认领/锁定/推送状态与收款公司枚举
        projectPayment.setClaimStatus(ClaimStatusEnum.NO.getValue());
        projectPayment.setLockStatus(LockStatusEnum.NO.getValue());
        projectPayment.setPushStatus(PushStatusEnum.WAIT_PUSH.getValue());
        String paymentCompany = paymentDTO.getPaymentCompany();
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<String, Integer> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getName,OaDictVO::getDisorder, (b, c)->b));

        if (CharSequenceUtil.isNotBlank(paymentCompany)) {
            projectPayment.setPaymentCompany(attributableSubjectMap.get(paymentCompany));
        }
        // 4.3、创建人与id
        projectPayment.setCreatorId(UserUtils.getUser().getId());
        projectPayment.setCreatorName(UserUtils.getUser().getName());
        // 4.4、教育回款id
        projectPayment.setEducationIds(ids.toString());
        // 4.5、认领日期自动锁定
        if (DateUtils.difference(paymentDTO.getPaymentDate()) > DateUtils.THIRTY_DAYS) {
            projectPayment.setLockStatus(LockStatusEnum.YES.getValue());
            projectPayment.setAutoLock(AutoLockEnum.YES.getValue());
        }
        // 4.6、推送消息 与 记录日志
        String customerName = paymentDTO.getCustomerName();
        if (CharSequenceUtil.isNotBlank(customerName)) {
            Map<String, CustomerAccountVO> customerAccountMap = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY)
                    .stream()
                    .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));
            CustomerAccountVO customerAccountVO = customerAccountMap.get(customerName);
            if (Optional.ofNullable(customerAccountVO).isPresent()) {
                this.pushMessage(paymentDTO.getPaymentDate(), number,
                        customerAccountVO.getRecordManId(), customerAccountVO.getRecordMan());
            }
        }
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                OperationEnum.SUMMARY_PAYMENT,
                OperationEnum.SUMMARY_PAYMENT.getName() + sb.toString() + "至项目回款【" + number + "】");

        return this.updateBatchById(educationPaymentList)
                && projectPaymentMapper.insert(projectPayment) > NumberUtils.INTEGER_ZERO;
    }

    /**
     * 取消汇总
     *
     * @param id 教育回款id
     * @return R {@link R}
     */
    @Override
    public R<String> unSummary(Long id) {
        // 1、教育回款已汇总才能取消汇总
        EducationPayment educationPayment = baseMapper.selectOne(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getSummaryStatus, SummaryStatusEnum.YES.getValue())
                .eq(EducationPayment::getId, id)
        );
        if (educationPayment == null) {
            return R.failed("无取消汇总数据");
        }
        // 1.1、修改教育回款汇总状态
        educationPayment.setSummaryStatus(SummaryStatusEnum.NO.getValue());
        // 2、项目回款待推送 + 推送失败 + 待锁定才能取消汇总
        List<Integer> pushStatus = new ArrayList<>();
        pushStatus.add(PushStatusEnum.WAIT_PUSH.getValue());
        pushStatus.add(PushStatusEnum.FAIL_PUSH.getValue());
        List<ProjectPayment> projectPaymentList = projectPaymentMapper.selectList(Wrappers.<ProjectPayment>lambdaQuery()
                        .in(ProjectPayment::getPushStatus, pushStatus)
                        .eq(ProjectPayment::getLockStatus, LockStatusEnum.NO.getValue())).stream()
                .filter(p -> CharSequenceUtil.isNotBlank(p.getEducationIds()) && p.getEducationIds().contains(String.valueOf(id)))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(projectPaymentList)) {
            return R.failed("无取消汇总数据");
        }
        ProjectPayment projectPayment = projectPaymentList.get(NumberUtils.INTEGER_ZERO);
        if (Optional.ofNullable(projectPayment).isPresent()) {
            final String success = "取消汇总成功";
            final String error = "取消汇总失败";
            // 日志记录
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                    OperationEnum.UN_SUMMARY_PAYMENT, "项目回款【" + projectPayment.getDocumentNumber() + "】" +
                            OperationEnum.UN_SUMMARY_PAYMENT.getName() + "【" + educationPayment.getDocumentNumber() + "】");
            // 项目回款收款金额减去教育回款到账金额
            projectPayment.setPaymentAmount(projectPayment.getPaymentAmount()
                    .subtract(educationPayment.getReceivedAmount()).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP)
            );
            // 3、查询项目回款下的教育回款id
            List<Long> educationIdList = Arrays.stream(projectPayment.getEducationIds().split(StrPool.COMMA))
                    .filter(educationId -> !CharSequenceUtil.EMPTY.equals(educationId))
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(educationIdList)) {
                // 3.1、如果教育回款集合剩下的唯一一个元素为当前传入id 直接更新 + 删除即可
                if (educationIdList.size() == NumberUtils.INTEGER_ONE
                        && educationIdList.get(NumberUtils.INTEGER_ZERO).equals(id)) {
                    projectPayment.setEducationIds(null);
                    return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO
                            && projectPaymentMapper.deleteById(projectPayment) > NumberUtils.INTEGER_ZERO
                            ? R.ok(success) : R.failed(error);
                }
                // 3.2、如果项目回款对应的教育回款不为空 都做更新
                StringBuilder ids = new StringBuilder(CharSequenceUtil.EMPTY);
                for (Long educationId : educationIdList) {
                    if (!educationId.equals(id)) {
                        ids.append(educationId).append(StrPool.COMMA);
                    }
                }
                projectPayment.setEducationIds(ids.toString());
                return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO
                        && projectPaymentMapper.updateById(projectPayment) > NumberUtils.INTEGER_ZERO
                        ? R.ok(success) : R.failed(error);
            }
            // 3.3、如果项目回款对应的教育回款为空 直接删除即可
            projectPayment.setEducationIds(null);
            return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO
                    && projectPaymentMapper.deleteById(projectPayment) > NumberUtils.INTEGER_ZERO
                    ? R.ok(success) : R.failed(error);
        }
        return R.failed("项目回款不支持取消汇总");
    }

    /**
     * 根据id获取教育回款详情
     *
     * @param id 教育回款id
     * @return {@link EducationPaymentClaimVO}
     */
    @Override
    public EducationPaymentClaimVO getOne(Long id) {
        EducationPaymentClaimVO educationPaymentClaimVO = new EducationPaymentClaimVO();
        // 查询教育回款信息
        EducationPayment educationPayment = baseMapper.selectOne(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getId, id)
        );
        if (Optional.ofNullable(educationPayment).isPresent()) {
            // 查询教育回款认领信息
            EducationPaymentClaim educationPaymentClaim = educationPaymentClaimMapper.selectOne(Wrappers.<EducationPaymentClaim>lambdaQuery()
                    .eq(EducationPaymentClaim::getEducationPaymentId, id)
            );
            // 封装详情信息
            educationPaymentClaimVO = BeanUtil.copyProperties(educationPayment, EducationPaymentClaimVO.class);
            educationPaymentClaimVO.setInvoicingStatusTxt(EnumUtils.getNameByValue(InvoicingStatusEnum.class, educationPaymentClaimVO.getInvoicingStatus()));
            BeanUtil.copyProperties(educationPaymentClaim, educationPaymentClaimVO, "id");
        }
        return educationPaymentClaimVO;
    }

    /**
     * 根据id更新教育回款详情
     *
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     * @return {@code true} or {@code false}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean update(EducationPaymentClaimDTO educationPaymentClaimDTO) {
        // 1、查询教育回款信息
        EducationPayment educationPayment = baseMapper.selectOne(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getId, educationPaymentClaimDTO.getId())
        );
        BeanUtil.copyProperties(educationPaymentClaimDTO, educationPayment);
        // 2、记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                OperationEnum.EDIT_PAYMENT, OperationEnum.EDIT_PAYMENT.getName() + "【" + educationPayment.getDocumentNumber() + "】");

        return baseMapper.updateById(educationPayment) > NumberUtils.INTEGER_ZERO;
    }

    /**
     * 锁定
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@code true} or {@code false}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean lock(EducationDTO educationDTO) {
        List<EducationPayment> educationPaymentList = new ArrayList<>();
        // 锁定状态
        Integer lockStatus = educationDTO.getLockStatus();
        educationDTO.setLockStatus(null);
        // 单据状态
        String documentStatus = educationDTO.getDocumentStatus();
        if (CharSequenceUtil.isNotBlank(documentStatus)) {
            educationDTO.setSummaryStatusByDocument(EnumUtils.getValueByName(SummaryStatusEnum.class, documentStatus));
            educationDTO.setClaimStatusByDocument(EnumUtils.getValueByName(ClaimStatusEnum.class, documentStatus));
            educationDTO.setLockStatusByDocument(EnumUtils.getValueByName(LockStatusEnum.class, documentStatus));
            educationDTO.setPushStatusByDocument(EnumUtils.getValueByName(PushStatusEnum.class, documentStatus));
        }
        // 查询项目回款信息修改锁定状态
        List<EducationPayment> educationPaymentPage = baseMapper.findPage(educationDTO);
        if (CollUtil.isNotEmpty(educationPaymentPage)) {
            StringBuilder sb = new StringBuilder();
            educationPaymentPage.forEach(e -> {
                CopyOptions copyOptions = CopyOptions.create();
                copyOptions.setIgnoreNullValue(true);
                EducationPayment educationPayment = new EducationPayment();
                BeanUtil.copyProperties(e, educationPayment, copyOptions);
                // 如果原先是待锁定并且当前日期超过收款日期30天 则后续不再进行锁定
                if (LockStatusEnum.NO.getValue().equals(e.getLockStatus())
                        && DateUtils.difference(e.getPaymentDate()) > DateUtils.THIRTY_DAYS) {
                    educationPayment.setId(e.getId());
                    educationPayment.setAutoLock(AutoLockEnum.NO.getValue());
                    educationPayment.setLockStatus(lockStatus);
                } else {
                    educationPayment.setId(e.getId());
                    educationPayment.setLockStatus(lockStatus);
                }
                educationPaymentList.add(educationPayment);
                sb.append("【").append(e.getDocumentNumber()).append("】");
            });
            // 记录日志
            if (lockStatus.equals(LockStatusEnum.YES.getValue())) {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                        OperationEnum.LOCK_PAYMENT, OperationEnum.LOCK_PAYMENT.getName() + sb.toString());
            } else {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                        OperationEnum.UNLOCK_PAYMENT, OperationEnum.UNLOCK_PAYMENT.getName() + sb.toString());
            }

            return this.updateBatchById(educationPaymentList);
        }
        return Boolean.TRUE;
    }

    /**
     * 删除
     *
     * @param ids {@link List}
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> delete(List<Long> ids) {
        // 已汇总状态无法删除教育回款跟踪
        List<EducationPayment> educationPaymentList = baseMapper.selectList(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getSummaryStatus, SummaryStatusEnum.NO.getValue())
                .in(CollUtil.isNotEmpty(ids), EducationPayment::getId, ids)
        );
        if (CollUtil.isNotEmpty(educationPaymentList)) {
            // 记录日志
            StringBuilder sb = new StringBuilder();
            educationPaymentList.forEach(e -> sb.append("【").append(e.getDocumentNumber()).append("】"));
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                    OperationEnum.DELETE_PAYMENT, OperationEnum.DELETE_PAYMENT.getName() + sb.toString());
            // 教育回款认领
            List<EducationPaymentClaim> educationPaymentClaimList = educationPaymentClaimMapper.selectList(Wrappers.<EducationPaymentClaim>lambdaQuery()
                    .in(CollUtil.isNotEmpty(ids), EducationPaymentClaim::getEducationPaymentId, ids)
            );
            if (CollUtil.isNotEmpty(educationPaymentClaimList)) {
                return baseMapper.deleteBatchIds(educationPaymentList) > NumberUtils.INTEGER_ZERO
                        && educationPaymentClaimMapper.deleteBatchIds(educationPaymentClaimList) > NumberUtils.INTEGER_ZERO
                        ? R.ok("删除回款跟踪数据成功") : R.failed("删除回款跟踪数据失败");
            }

            return baseMapper.deleteBatchIds(educationPaymentList) > NumberUtils.INTEGER_ZERO
                    ? R.ok("删除回款跟踪数据成功") : R.failed("删除回款跟踪数据失败");
        }

        return R.failed("无数据删除");
    }

    /**
     * 导入
     *
     * @param educationPaymentExcelDTOList {@link List}<{@link EducationPaymentExcelDTO}>
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> importExcel(List<EducationPaymentExcelDTO> educationPaymentExcelDTOList) {
        List<CustomerAccountVO> customerAccountList = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY);
        // 获取收款平台对应的费率
        Map<String, BigDecimal> paymentMap = paymentMethodMapper.selectList(Wrappers.<PaymentMethod>lambdaQuery()
                .eq(PaymentMethod::getEnableStatus, EnableStatusEnum.YES.getValue())
        ).stream().collect(Collectors.toMap(PaymentMethod::getName, PaymentMethod::getExchangeRate, (a, b) -> a));
        // 通过校验后插入到数据库
        List<EducationPayment> educationPaymentList = new ArrayList<>();
        if (CollUtil.isNotEmpty(educationPaymentExcelDTOList)) {
            StringBuilder error = new StringBuilder();
            AtomicInteger i = new AtomicInteger(2);
            educationPaymentExcelDTOList.forEach(e -> {
                // 校验
                this.checkImport(e, error, i.getAndIncrement());
                // 1、教育回款信息
                EducationPayment educationPayment = BeanUtil.copyProperties(e, EducationPayment.class);
                // 单据编号
                String number = RedisConstant.EDUCATION_DOCUMENT_NUMBERPREFIX +
                        new SimpleDateFormat(DateUtils.YEAR_MONTH).format(new Date()) +
                        String.format("%04d", stringRedisTemplate.opsForValue().increment(RedisConstant.EDUCATION_DOCUMENT_NUMBER));
                educationPayment.setDocumentNumber(number);
                // 设置枚举值
                educationPayment.setInvoicingStatus(EnumUtils.getValueByName(InvoicingStatusEnum.class, e.getInvoicingStatusTxt()));
                educationPayment.setClaimStatus(EnumUtils.getValueByName(ClaimStatusEnum.class, e.getClaimantName()));
                educationPayment.setLockStatus(EnumUtils.getValueByName(LockStatusEnum.class, e.getLockStatusTxt()));
                educationPayment.setSummaryStatus(EnumUtils.getValueByName(SummaryStatusEnum.class, e.getSummaryStatusTxt()));
                // 设置创建人与id
                educationPayment.setCreatorId(UserUtils.getUser().getId());
                educationPayment.setCreatorName(UserUtils.getUser().getName());
                // 计算四种金额
                BigDecimal receivedAmount = e.getReceivedAmount();
                // 不含税金额
                BigDecimal amountExcludingTax = receivedAmount.divide(new BigDecimal("1.03"), NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                educationPayment.setAmountExcludingTax(amountExcludingTax);
                // 税额
                educationPayment.setTaxAmount(receivedAmount.subtract(amountExcludingTax).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                // 获取费率
                BigDecimal exchangeRate = paymentMap.get(e.getPaymentPlatform());
                if (Optional.ofNullable(exchangeRate).isPresent()) {
                    exchangeRate = exchangeRate.divide(new BigDecimal("100"));
                    // 手续费
                    BigDecimal commission = receivedAmount.multiply(exchangeRate).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
                    educationPayment.setCommission(commission);
                    // 实际金额
                    educationPayment.setActualAmountReceived(receivedAmount.subtract(commission).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP));
                }
                // 收款日期
                String paymentDate = e.getPaymentDate();
                if (CharSequenceUtil.isNotBlank(paymentDate)) {
                    educationPayment.setPaymentDate(DateUtils.formatTime(paymentDate).replaceAll(StrPool.SLASH, StrPool.DASHED));
                }
                // 开票日期
                String invoicingDate = e.getInvoicingDate();
                if (CharSequenceUtil.isNotBlank(invoicingDate)) {
                    educationPayment.setInvoicingDate(DateUtils.formatTime(invoicingDate).replaceAll(StrPool.SLASH, StrPool.DASHED));
                }
                // 2、推送消息
                String customerName = e.getCustomerName();
                if (CharSequenceUtil.isNotBlank(customerName)) {
                    Map<String, CustomerAccountVO> customerAccountMap = customerAccountList.stream()
                            .collect(Collectors.toMap(CustomerAccountVO::getCustomerName, a -> a, (a, b) -> a));
                    CustomerAccountVO customerAccountVO = customerAccountMap.get(customerName);
                    if (Optional.ofNullable(customerAccountVO).isPresent()) {
                        this.pushMessage(e.getPaymentDate(), number, customerAccountVO.getRecordManId(), customerAccountVO.getRecordMan());
                    }
                }
                educationPaymentList.add(educationPayment);
            });
            if (error.toString().contains("空")) {
                return R.failed("导入失败,原因是" + error.toString());
            }
            // 3、记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.EDUCATION_PAYMENT,
                    OperationEnum.IMPORT, OperationEnum.IMPORT.getName() + "【" + educationPaymentExcelDTOList.size() + "条】");

            return this.saveBatch(educationPaymentList) ? R.ok("导入成功") : R.failed("导入失败");
        }

        return R.ok("无数据导入");
    }

    /**
     * 待认领且收款日期超过三十天
     *
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> updateLockStatus() {
        List<EducationPayment> educationPaymentList = this.list(Wrappers.<EducationPayment>lambdaQuery()
                .eq(EducationPayment::getClaimStatus, ClaimStatusEnum.NO.getValue())
                .eq(EducationPayment::getAutoLock, AutoLockEnum.YES.getValue())
        );
        if (educationPaymentList.isEmpty()) {
            return R.ok("无需要自动锁定数据");
        }

        educationPaymentList.forEach(e -> {
            if (DateUtils.difference(e.getPaymentDate()) > DateUtils.THIRTY_DAYS
                    && ClaimStatusEnum.NO.getValue().equals(e.getClaimStatus())) {
                e.setLockStatus(LockStatusEnum.YES.getValue());
                e.setAutoLock(LockStatusEnum.NO.getValue());
            }
        });
        return this.updateBatchById(educationPaymentList) ? R.ok() : R.failed();
    }

    /**
     * 条件封装
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link EducationDTO}
     */
    private EducationDTO condition(EducationDTO educationDTO) {
        // 1、条件封装 单据状态
        List<String> documentStatus = educationDTO.getDocumentStatusList();

        if (CollectionUtils.isNotEmpty(documentStatus)) {
            List<String> summaryStatusList = documentStatus.stream().filter(d -> d.equals(SummaryStatusEnum.YES.getName()) || d.equals(SummaryStatusEnum.NO.getName())).collect(Collectors.toList());
            List<String> claimStatusList = documentStatus.stream().filter(d -> d.equals(ClaimStatusEnum.YES.getName()) || d.equals(ClaimStatusEnum.NO.getName())).collect(Collectors.toList());
            List<String> lockStatusList = documentStatus.stream().filter(d -> d.equals(LockStatusEnum.YES.getName()) || d.equals(LockStatusEnum.NO.getName())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(summaryStatusList) && NumberUtils.INTEGER_ONE.equals(summaryStatusList.size())) {
                summaryStatusList.stream().forEach(s ->
                        educationDTO.setSummaryStatusByDocument(EnumUtils.getValueByName(SummaryStatusEnum.class, s)));
            }
            if (CollectionUtils.isNotEmpty(claimStatusList) && NumberUtils.INTEGER_ONE.equals(claimStatusList.size())) {
                claimStatusList.stream().forEach(c ->
                        educationDTO.setClaimStatusByDocument(EnumUtils.getValueByName(ClaimStatusEnum.class, c)));
            }
            if (CollectionUtils.isNotEmpty(lockStatusList) && NumberUtils.INTEGER_ONE.equals(lockStatusList.size())) {
                lockStatusList.stream().forEach(l ->
                        educationDTO.setLockStatusByDocument(EnumUtils.getValueByName(LockStatusEnum.class, l)));
            }
        }

        // 2、数据权限 || 【认领人】无值/未认领状态，该数据所有人可见
        SysUserRoleDataVo auth = remoteRoleService.getRoleDataDetailByUserId(educationDTO.getClientId(),
                UserUtils.getUser().getId(), educationDTO.getMenuCode()).getData();
        log.info("教育回款数据权限: {}", auth);
        if (Boolean.FALSE.equals(auth.getIsAll())) {
            // 不为全部权限
            educationDTO.setAuthority(Boolean.TRUE);
            // 可以查看到下级人员id列表(中台已返回自身id)
            educationDTO.setUserId(auth.getUserIdList());
            // 可以查看到部门id列表
            //List<Long> deptIdList = auth.getDeptIdList();
            //deptIdList.add(NumberUtils.LONG_MINUS_ONE);
            //educationDTO.setDeptId(deptIdList);
        }
        return educationDTO;
    }

    /**
     * 封装实体集合展示
     *
     * @param educationPaymentList {@link List}<{@link EducationPayment}>
     * @return {@link List}<{@link EducationVO}>
     */
    private List<EducationVO> addVoToList(List<EducationPayment> educationPaymentList) {
        List<EducationVO> educationVOList = new ArrayList<>();
        List<ProjectAccountVO> projectAccount = dbApi.getProjectAccount(null);
        Map<String, String> projectMap = projectAccount.stream().collect(Collectors.toMap(ProjectAccountVO::getProjectName, ProjectAccountVO::getProjectNumber, (a, b) -> a));
        educationPaymentList.forEach(p -> {
            EducationVO educationVO = BeanUtil.copyProperties(p, EducationVO.class);
            // 设置枚举值
            educationVO.setLockStatusTxt(EnumUtils.getNameByValue(LockStatusEnum.class, p.getLockStatus()));
            educationVO.setInvoicingStatusTxt(EnumUtils.getNameByValue(InvoicingStatusEnum.class, p.getInvoicingStatus()));
            educationVO.setClaimStatusTxt(EnumUtils.getNameByValue(ClaimStatusEnum.class, p.getClaimStatus()));
            if (SummaryStatusEnum.YES.getValue().equals(p.getSummaryStatus())) {
                educationVO.setSummaryView(Boolean.FALSE);
            }
            educationVO.setSummaryStatusTxt(EnumUtils.getNameByValue(SummaryStatusEnum.class, p.getSummaryStatus()));
            // 设置金额
            educationVO.setReceivedAmount(MoneyUtils.getInstance().transType(p.getReceivedAmount()));
            educationVO.setAmountExcludingTax(MoneyUtils.getInstance().transType(p.getAmountExcludingTax()));
            educationVO.setTaxAmount(MoneyUtils.getInstance().transType(p.getTaxAmount()));
            educationVO.setCommission(MoneyUtils.getInstance().transType(p.getCommission()));
            educationVO.setActualAmountReceived(MoneyUtils.getInstance().transType(p.getActualAmountReceived()));
            // 设置认领信息
            EducationPaymentClaim educationPaymentClaim = p.getEducationPaymentClaim();
            educationVO.setCustomerName(educationPaymentClaim.getCustomerName());
            educationVO.setSalesmanUserName(educationPaymentClaim.getSalesmanUserName());
            educationVO.setPaymentDept(educationPaymentClaim.getPaymentDept());
            educationVO.setPaymentSecondaryDept(educationPaymentClaim.getPaymentSecondaryDept());
            educationVO.setSchoolName(educationPaymentClaim.getSchoolName());
            educationVO.setProductName(educationPaymentClaim.getProductName());
            educationVO.setProjectId(educationPaymentClaim.getProjectId());
            educationVO.setProjectName(educationPaymentClaim.getProjectName());
            educationVO.setKeyAccountManagerName(educationPaymentClaim.getKeyAccountManagerName());
            educationVO.setSalesExecutiveName(educationPaymentClaim.getSalesExecutiveName());
            educationVO.setAccountManagerName(educationPaymentClaim.getAccountManagerName());
            educationVO.setConsultantUserName(educationPaymentClaim.getConsultantUserName());
            educationVO.setClaimantName(educationPaymentClaim.getClaimantName());
            educationVO.setSureRevenueDate(educationPaymentClaim.getSureRevenueDate());
            educationVO.setClaimantDate(DateUtils.getStringDate(educationPaymentClaim.getCreateTime(),DateUtils.SIMPLE_DATE_FORMAT));
            educationVO.setProjectCode(projectMap.getOrDefault(educationPaymentClaim.getProjectName(),""));
            educationVO.setClaimRemark(educationPaymentClaim.getClaimRemark());
            educationVOList.add(educationVO);
        });

        return educationVOList;
    }

    /**
     * 校验导入参数
     *
     * @param dto {@link EducationPaymentExcelDTO}
     * @param error {@link StringBuilder}
     * @param row 行数
     */
    private void checkImport(EducationPaymentExcelDTO dto, StringBuilder error, Integer row) {
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getPaymentPlatform())) {
            error.append("收款平台不能为空");
        }
        if (CharSequenceUtil.isBlank(dto.getTransactionNumber())) {
            error.append("交易流水号不能为空");
        }
        if (!Optional.ofNullable(dto.getReceivedAmount()).isPresent()) {
            error.append("到账金额不能为空");
        }
        if (!Optional.ofNullable(dto.getPaymentDate()).isPresent()) {
            error.append("收款日期不能为空");
        }
    }

    /**
     * 根据名称更新id
     *
     * @param educationPaymentClaim    {@link EducationPaymentClaim}
     * @param educationPaymentClaimDTO {@link EducationPaymentClaimDTO}
     */
    private void getIdByName(EducationPaymentClaim educationPaymentClaim, EducationPaymentClaimDTO educationPaymentClaimDTO) {
        // 回款一级部门
        Map<String, Long> deptMap = remoteOutMultiDeptService
                .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, null)
                .getData()
                .stream()
                .collect(Collectors.toMap(MultiDimensionDeptDto::getName, MultiDimensionDeptDto::getDeptId, (a, b) -> a));
        if (CharSequenceUtil.isEmpty(educationPaymentClaimDTO.getPaymentDept())) {
            educationPaymentClaim.setPaymentDeptId(null);
        } else if (!educationPaymentClaimDTO.getPaymentDept().equals(educationPaymentClaim.getPaymentDept())) {
            String paymentDept = educationPaymentClaimDTO.getPaymentDept();
            if (CharSequenceUtil.isNotBlank(paymentDept)) {
                educationPaymentClaim.setPaymentDept(paymentDept);
                educationPaymentClaim.setPaymentDeptId(deptMap.get(paymentDept));
                // 回款二级部门
                String paymentSecondaryDept = educationPaymentClaimDTO.getPaymentSecondaryDept();
                if (CharSequenceUtil.isNotBlank(paymentSecondaryDept)) {
                    educationPaymentClaim.setPaymentSecondaryDept(paymentSecondaryDept);
                    educationPaymentClaim.setPaymentSecondaryDeptId(deptMap.get(paymentSecondaryDept));
                }
            }
        }

    }

    /**
     * 消息推送
     *
     * @param paymentDate    收款日期
     * @param documentNumber 单据编号
     * @param targetId       目标人员id
     * @param targetName     目标人员姓名
     */
    private void pushMessage(String paymentDate, String documentNumber, Long targetId, String targetName) {
        // 没有目标人信息直接返回
        if (!Optional.ofNullable(targetId).isPresent() || !Optional.ofNullable(targetName).isPresent()) {
            return;
        }

        // 消息内容
        final String content = "你有一笔【" + paymentDate + "】的项目回款【" + documentNumber + "】等待认领，请及时处理~";
        // 消息实体
        MailModel model = new MailModel();
        model.setSource(SourceEnum.PROJECT.getValue());
        model.setType(MsgTypeEnum.TEXT_MSG.getValue());
        model.setTitle("业务一体化项目回款待认领");
        model.setContent(content);
        model.setSenderId(FinancePropertiesUtils.PORTAL_APP_ID);
        model.setSender("业务一体化系统");
        model.setTargetType(TargetTypeEnum.USERS.getValue());
        model.setRedirectUrl(FinancePropertiesUtils.PROJECT_PAYMENT_URL + "?documentNumber=" + documentNumber);
        // 接受消息的人列表
        List<BcpMessageTargetDTO> list = new ArrayList<>();

        BcpMessageTargetDTO person = new BcpMessageTargetDTO();
        person.setTargetId(targetId.toString());
        person.setTargetName(targetName);
        list.add(person);

        model.setTargetList(list);

        try {
            remoteMailService.sendMsg(model);
        } catch (Exception e) {
            log.info("中台推送消息失败, 对应的目标人员id为: {}, 姓名为: {}", targetId, targetName);
        }
    }

}
