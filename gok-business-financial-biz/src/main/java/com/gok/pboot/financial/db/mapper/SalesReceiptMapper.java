package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.SalesReceipt;
import com.gok.pboot.financial.dto.SalesReceiptDTO;
import com.gok.pboot.financial.vo.SalesReceiptInDetailVO;
import com.gok.pboot.financial.vo.SalesReceiptPushVO;
import com.gok.pboot.financial.vo.SalesReceiptTaskReminderVO;
import com.gok.pboot.financial.vo.SalesReceiptVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 销售收款计划Mapper
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt(销售收款计划)】的数据库操作Mapper
 * @createDate 2023-09-27 16:08:24
 * @Entity com.gok.pboot.financial.db.entity.SalesReceipt
 */
public interface SalesReceiptMapper extends BaseMapper<SalesReceipt> {
    /**
     * 分页查询
     *
     * @param page 分页条件
     * @param dto  查询条件
     * @return page
     */
    Page<SalesReceipt> querySalesReceiptPage(@Param("page") Page<SalesReceipt> page, @Param("dto") SalesReceiptDTO dto);


    /**
     * 查询全部的项款名称
     *
     * @param dto {@link SalesReceiptDTO}
     * @return 名称列表
     */
    List<String> currentPaymentNameBox(@Param("dto") SalesReceiptDTO dto);

    /**
     * 查询全部的项目状态
     *
     * @return 状态列表
     */
    List<String> projectStatusBox();

    /**
     * 获取指定的列表
     *
     * @param dto dto
     * @return {@link List}<{@link SalesReceipt}>
     */
    List<SalesReceipt> querySalesReceiptList(@Param("dto") SalesReceiptDTO dto);

    /**
     * 根据id查找
     *
     * @param id id
     * @return {@link SalesReceiptInDetailVO}
     */
    SalesReceiptInDetailVO selectVoById(@Param("id") Long id);


    /**
     * 获取推送消息所需的信息
     *
     * @return {@link List}<{@link SalesReceiptPushVO}>
     */
    List<SalesReceiptPushVO> queryPushVoV1();

    /**
     * 分页查询
     *
     * @param page 分页条件
     * @param dto  查询条件
     * @return page
     */
    Page<SalesReceiptVO> queryPage(@Param("page") Page<SalesReceiptVO> page, @Param("dto") SalesReceiptDTO dto);

    List<SalesReceiptVO> queryPage(@Param("dto") SalesReceiptDTO dto);

    /**
     * 获取任务提醒所需的信息
     * @return
     */
    List<SalesReceiptTaskReminderVO> selTaskReminder();

    /**
     * 获取推送消息所需的信息
     * @return
     */
    List<SalesReceiptPushVO> queryPushVo();

    /**
     * 获取销售收款任务更新提醒所需的信息
     * @return
     */
    List<SalesReceiptPushVO> queryTaskUpdateReminderVo();

    /**
     * 获取销售收款任务未更新提醒所需的信息
     * @return
     */
    List<SalesReceiptPushVO> queryTaskNotUpdateReminderVo();

    /**
     * 统计销售收款计划数据
     * @param dto 查询条件
     * @return 统计结果
     */
    Map<String, BigDecimal> statistics(@Param("dto") SalesReceiptDTO dto);
}