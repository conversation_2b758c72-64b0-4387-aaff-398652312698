package com.gok.pboot.financial.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.gok.pboot.financial.db.entity.AccountType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 会计科目
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingAccountVO{
    /**
    * id
    */
    private Long id;

    /**
    * 科目编码
    */
    private String accountCode;

    /**
    * 科目名称
    */
    private String accountName;

    /**
    * 助记词
    */
    private String mnemonicWords;

    /**
     * 级次
     */
    private Integer level;

    /**
    * 上级科目id
    */
    private Long parentId;

    /**
    * 余额方向
    */
    private Integer balanceDirection;

    /**
     * 余额方向
     */
    private String balanceDirectionTxt;

    /**
    * 会计体系id
    */
    private Long accountingSystemId;

    /**
     * 会计体系
     */
    private String accountingSystem;

    /**
    * 科目类型id
    */
    private Long accountTypeId;

    /**
     * 科目类型
     */
    private String accountType;

    /**
    * 现金分类
    */
    private Integer cashClassification;

    /**
     * 现金分类Txt
     */
    private String cashClassificationTxt;

    /**
    * 账页格式
    */
    private Integer accountPageFormat;

    /**
     * 账页格式Txt
     */
    private String accountPageFormatTxt;

    /**
    * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
    */
    private Integer enableStatus;

    /**
     * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
     */
    private String enableStatusTxt;

    /**
    * 停用日期
    */
    private String suspensionDate;

    /**
    * 辅助核算项集合（,分割）
    */
    private String auxiliaryCalculateItems;

    /**
     * 辅助核算项集合
     */
    private List<String> auxiliaryCalculateItemList;

    /**
    * 自定义核算项集合（,分割）
    */
    private String customizeCalculateItems;

    /**
     * 辅助核算项集合
     */
    private List<Long> customizeCalculateItemList;


}