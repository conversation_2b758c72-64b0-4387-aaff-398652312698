package com.gok.pboot.financial.dto;

import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Data;

import java.io.Serializable;

@Data
public class FundAccountDTO  implements Serializable {

    private Long id;

    /**
     * 编码
     */
    @StringVerify(message = "编码不能为空",required = true,name = "编码")
    private String code;

    /**
     * 资金账户
     */
    @StringVerify(message = "资金账户不能为空",required = true,name = "资金账户")
    private String fundAccount;


}
