package com.gok.pboot.financial.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 业务传参
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
public class AccountingSetDTO implements Serializable {

    /**
     * ids
     */
    private List<Long> idList;

    /**
     * 启用
     */
    private Boolean enableStatusFlag;

    /**
     * id
     */
    private Long id;

    /**
     * 账套编码
     */
    @NotBlank(message = "账套编码不能为空")
    private String accountingCode;

    /**
     * 账套账号
     */
    @NotBlank(message = "账套账号不能为空")
    private String accountingAccount;

    /**
     * 账套密码
     */
    @NotBlank(message = "账套密码不能为空")
    private String accountingPassword;

    /**
     * 归属主体
     */
    @NotBlank(message = "归属主体不能为空")
    private String accountingSubject;

    /**
     * 启用状态
     */
    @NotNull(message = "启用状态不能为空")
    private Integer enableStatus;

    /**
     * 默认资金账户
     */
    private Long fundAccountId;

    /**
     * 账套类型
     */
    private String accountingType;

    /**
     * 账套备注
     */
    private String accountingRemarks;
}
