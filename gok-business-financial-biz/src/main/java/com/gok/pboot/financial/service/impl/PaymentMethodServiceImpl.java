package com.gok.pboot.financial.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.StrPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.PaymentMethod;
import com.gok.pboot.financial.dto.PaymentMethodDTO;
import com.gok.pboot.financial.enums.EnableStatusEnum;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IPaymentMethodService;
import com.gok.pboot.financial.db.mapper.PaymentMethodMapper;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.PaymentMethodVO;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 收款方式ServiceImpl
 *
 * <AUTHOR>
 * @description 针对表【payment_method(收款方式)】的数据库操作Service实现
 * @createDate 2023-09-27 10:46:49
 */
@Service
public class PaymentMethodServiceImpl extends ServiceImpl<PaymentMethodMapper, PaymentMethod>
        implements IPaymentMethodService {

    @Resource
    private LogRecordUtils logRecordUtils;

    /**
     * 分页查询
     *
     * @param page 查询条件
     * @return {@link Page}<{@link PaymentMethodVO}>
     */
    @Override
    public Page<PaymentMethodVO> findPage(Page<PaymentMethodVO> page) {
        baseMapper.queryPaymentMethodPage(page);
        List<PaymentMethodVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            records.forEach(r -> {
                PaymentMethodVO pageVo = Convert.convert(PaymentMethodVO.class, r);
                pageVo.setEnableStatusStr(EnumUtils.getNameByValue(EnableStatusEnum.class, pageVo.getEnableStatus()));
                r = pageVo;
            });
        }
        return page;
    }

    /**
     * 新增or修改接口
     *
     * @param dto dto类
     * @return {@link R}
     */
    @Override
    public R<String> addOrEdit(PaymentMethodDTO dto) {
        if (!Optional.ofNullable(dto.getId()).isPresent()) {
            PaymentMethod paymentMethod = new PaymentMethod();
            BeanUtils.copyProperties(dto, paymentMethod);
            paymentMethod.setId(null);
            baseMapper.insert(paymentMethod);
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PAYMENT_METHOD,
                    OperationEnum.ADD_PAYMENT2_METHOD,
                    OperationEnum.ADD_PAYMENT2_METHOD.getName() + "【" + dto.getCode() + "】");
            return R.ok("新增成功");
        }
        PaymentMethod paymentMethod = baseMapper.selectById(dto.getId());
        if (paymentMethod == null) {
            return R.failed("改信息不存在");
        }
        // 记录日志
        if (!dto.getExchangeRate().equals(paymentMethod.getExchangeRate())) {
            String logContent = "【" + dto.getCode() + "】【" + exchangeRate(paymentMethod.getExchangeRate())
                    + "%】变更为【" + exchangeRate(dto.getExchangeRate()) + "%】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.PAYMENT_METHOD,
                    OperationEnum.EDIT_PAYMENT2_METHOD, logContent);
        }
        if (!dto.getEnableStatus().equals(paymentMethod.getEnableStatus())) {
            String logContent = "【" + dto.getCode() + "】【" + EnumUtils.getNameByValue(EnableStatusEnum.class, paymentMethod.getEnableStatus())
                    + "】变更为【" + EnumUtils.getNameByValue(EnableStatusEnum.class, dto.getEnableStatus()) + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.PAYMENT_METHOD,
                    OperationEnum.EDIT_PAYMENT2_METHOD, logContent);
        }
        BeanUtils.copyProperties(dto, paymentMethod);
        baseMapper.updateById(paymentMethod);

        return R.ok("修改成功");
    }

    /**
     * 删除
     *
     * @param idList id列表
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> delete(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return R.ok("无传入id");
        }
        // 记录日志
        StringBuilder sb = new StringBuilder();
        baseMapper.selectList(Wrappers.<PaymentMethod>lambdaQuery()
                .in(PaymentMethod::getId, idList)
        ).forEach(p -> sb.append("【").append(p.getCode()).append("】"));
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PAYMENT_METHOD,
                OperationEnum.DELETE_PAYMENT2_METHOD,
                OperationEnum.DELETE_PAYMENT2_METHOD.getName() + sb.toString());

        baseMapper.deleteBatchIds(idList);
        return R.ok("删除成功");
    }

    /**
     * 费率转为字符串
     *
     * @param exchangeRate {@link BigDecimal} 费率
     * @return 费率
     */
    private String exchangeRate(BigDecimal exchangeRate) {
        if (!Optional.ofNullable(exchangeRate).isPresent()) {
            return MoneyUtils.TYPE;
        }
        String exchangeRateStr = exchangeRate.toString();
        if (exchangeRateStr.contains(".00")) {
            return exchangeRateStr.substring(NumberUtils.INTEGER_ZERO, exchangeRateStr.indexOf(StrPool.DOT));
        }

        return exchangeRateStr;
    }
}




