package com.gok.pboot.financial.db.mapper;

import com.gok.components.data.datascope.BaseMapper;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.db.entity.CrossDeptLaborCosts;
import com.gok.pboot.financial.dto.DeptArtificialCostDTO;
import com.gok.pboot.financial.vo.CrossDeptLaborCostsDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 跨部门人工成本台账Mapper
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface CrossDeptLaborCostsMapper extends BaseMapper<CrossDeptLaborCosts> {

    /**
     * 列表展示跨部门人工成本台帐数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    @BusinessDataScope(scopeDeptNameList = {"personnel_secondary_dept_id"})
    List<CrossDeptLaborCostsDetailVO> findDetailList(@Param("query") DeptArtificialCostDTO deptArtificialCostDTO);

}