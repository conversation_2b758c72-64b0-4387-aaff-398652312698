package com.gok.pboot.financial.invoice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.domain.InvoiceOutput;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputVO;
import com.gok.pboot.financial.vo.OaFileInfoVo;

import java.util.List;


/**
 * 销项发票台账服务类
 *
 * <AUTHOR>
 * @create 2025/06/09
 **/
public interface IInvoiceOutputService extends IService<InvoiceOutput> {

    /**
     * 分页查询发票规划列表
     *
     * @param dto 查询请求
     * @return 分页结果
     */
    StatisticsPage<InvoiceOutputVO> findPage(InvoiceOutputDTO dto);

    /**
     * 导出发票规划列表
     *
     * @param dto 查询请求
     * @return
     */
    List<InvoiceOutputVO> exportExcel(InvoiceOutputDTO dto);

    /**
     * 获取销项发票附件集合
     *
     * @param id 销项发票台账ID
     * @return
     */
    List<OaFileInfoVo> getAttachments(Long id);

}
