package com.gok.pboot.financial.dto;

import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Data;

@Data
public class CustomizeCalculateDTO {
    private Long id;

    /**
     * 编码
     */
    @StringVerify(message = "编码不能为空",required = true,name = "编码")
    private String code;

    /**
     * 自定义辅助核算项
     */
    @StringVerify(message = "自定义辅助核算项不能为空",required = true,name = "资金账户")
    private String customizeCalculate;
}
