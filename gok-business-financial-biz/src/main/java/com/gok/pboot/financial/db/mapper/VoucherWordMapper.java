package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.VoucherWord;
import com.gok.pboot.financial.dto.VoucherWordFindDTO;
import com.gok.pboot.financial.vo.VoucherWordVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 凭证字数据访问层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface VoucherWordMapper extends BaseMapper<VoucherWord> {

    Page<VoucherWordVO> queryPage(@Param("page") Page<VoucherWord> page, @Param("dto")VoucherWordFindDTO dto);

    List<VoucherWord> selectByWord(@Param("voucherWord") String voucherWord);

    int noDefaultAll();
}