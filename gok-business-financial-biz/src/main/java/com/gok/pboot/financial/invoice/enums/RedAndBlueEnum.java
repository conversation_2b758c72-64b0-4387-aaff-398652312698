package com.gok.pboot.financial.invoice.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 红蓝字枚举
 *
 * <AUTHOR>
 * @create 2025/06/20
 **/
@Getter
@AllArgsConstructor
public enum RedAndBlueEnum implements ValueEnum<Integer> {

    /**
     * 蓝字
     */
    BLUE(0, "蓝字"),

    /**
     * 红字
     */
    RED(1, "红字");

    private final Integer value;

    private final String name;


}
