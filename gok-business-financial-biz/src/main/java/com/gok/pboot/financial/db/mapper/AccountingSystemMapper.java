package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.dto.AccountingSystemPageDTO;
import com.gok.pboot.financial.vo.AccountingSystemCorrelationVO;
import com.gok.pboot.financial.vo.AccountingSystemVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccountingSystemMapper extends BaseMapper<AccountingSystem> {

    /**
     * 模糊查询
     *
     * @param page
     * @param dto
     * @return
     */
    Page<AccountingSystemVO> findPage(Page<AccountingSystem> page, @Param("query") AccountingSystemPageDTO dto);

    /**
     * 模糊查询导出列表
     *
     * @param dto
     * @return
     */
    List<AccountingSystemVO> findPage(@Param("query") AccountingSystemPageDTO dto);

    /**
     * 删除
     *
     * @param idList
     * @return
     */
    boolean delByIds(@Param("idList") List<Long> idList);

    /**
     * 查询会计体系关联科目类型 会计科目数量
     * @param idList
     * @return
     */
    List<AccountingSystemCorrelationVO> selCorrelationNum(@Param("idList") List<Long> idList);
}