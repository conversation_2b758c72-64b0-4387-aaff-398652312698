package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;

/**
 * 财务处理单
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FinanceProcessFormVO {
    /**
     * id
     */
    @ExcelIgnore
    private Long id;
    /**
     * 流程id
     */
    @ExcelIgnore
    private Long requestid;
    /**
     * 单据类型
     */
    @ExcelIgnore
    private Integer billType;

    /**
     * 单据类型
     */
    @ExcelProperty("单据类型")
    private String billTypeTxt;
    /**
     * 报账编号
     */
    @ExcelProperty("报账编号")
    private String billingNumber;

    /**
     * 报账金额
     */
    @ExcelProperty("报账金额")
    private String billingAmount;
    /**
     * 应付金额
     */
    @ExcelProperty("应付金额")
    private String payableAmount;
    /**
     * 冲销金额
     */
    @ExcelProperty("冲销金额")
    private String offsetAmount;

    /**
     * 申请人id
     */
    @ExcelIgnore
    private Long applicantId;

    /**
     * 申请人
     */
    @ExcelProperty("申请人")
    private String applicantName;

    /**
     * 申请人部门id
     */
    @ExcelIgnore
    private Long applicantDeptId;
    /**
     * 申请人部门
     */
    @ExcelProperty("申请人部门")
    private String applicantDeptName;

    /**
     * 申请日期
     */
    @ExcelProperty("申请日期")
    private LocalDate applicationDate;
    /**
     * 所属公司
     */
    @ExcelProperty("所属公司")
    private String belongCompany;

    /**
     * 申请事由
     */
    @ExcelProperty("申请事由")
    private String reason;
    /**
     * 财务处理编号
     */
    @ExcelProperty("财务处理编号")
    private String financialDocNumber;
    /**
     * 审批人员id
     */
    @ExcelIgnore
    private String approverIds;
    /**
     * 审批人员
     */
    @ExcelProperty("审批人员")
    private String approverNames;
    /**
     * 审批状态
     */
    @ExcelIgnore
    private Integer approvalStatus;

    /**
     * 流程关联人id
     */
    @ExcelIgnore
    private String relatedIds;
    /**
     * 审批状态
     */
    @ExcelProperty("审批状态")
    private String approvalStatusTxt;
    /**
     * 接收日期
     */
    @ExcelProperty("接收日期")
    private LocalDate receiveDate;
    /**
     * 凭证状态
     */
    @ExcelIgnore
    private Integer voucherStatus;

    /**
     * 凭证状态
     */
    @ExcelProperty("凭证状态")
    private String voucherStatusTxt;
    /**
     * 凭证号
     */
    @ExcelProperty("凭证号")
    private String voucherNumber;
    /**
     * 凭证制单日期
     */
    @ExcelProperty("凭证制单日期")
    private LocalDate voucherDate;
    /**
     * 收单状态
     */
    @ExcelIgnore
    private Integer receiptStatus;

    /**
     * 收单状态
     */
    @ExcelProperty("收单状态")
    private String receiptStatusTxt;
    /**
     * 收单日期
     */
    @ExcelProperty("收单日期")
    private LocalDate receiptDate;

    /**
     * 结算人员id
     */
    @ExcelIgnore
    private Long settlementPersonId;
    /**
     * 结算人员
     */
    @ExcelProperty("结算人员")
    private String settlementPersonName;

    /**
     * 结算状态
     */
    @ExcelIgnore
    private Integer settlementStatus;

    /**
     * 结算状态
     */
    @ExcelProperty("结算状态")
    private String settlementStatusTxt;
    /**
     * 结算日期
     */
    @ExcelProperty("结算日期")
    private LocalDate settlementDate;
    /**
     * 资金账户
     */
    @ExcelProperty("资金账户")
    private String fundAccount;


}
