package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.db.entity.ProjectPaymentClaim;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目回款认领 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface ProjectPaymentClaimMapper extends BaseMapper<ProjectPaymentClaim> {

    /**
     * 批量插入
     *
     * @param projectPaymentClaimList {@link List}<{@link ProjectPaymentClaim}>
     * @return {@code true} or {@code false}
     */
    boolean saveBatch(@Param("list") List<ProjectPaymentClaim> projectPaymentClaimList);
}