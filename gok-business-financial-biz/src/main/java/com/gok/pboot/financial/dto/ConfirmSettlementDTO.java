package com.gok.pboot.financial.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 *确认结算参数
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
public class ConfirmSettlementDTO implements Serializable {

    /**
     * 财务处理单id
     */
    @NotEmpty(message = "财务处理单id不能为空")
    private List<Long> ids;



}

