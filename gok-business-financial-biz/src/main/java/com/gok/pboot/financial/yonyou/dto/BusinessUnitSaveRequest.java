/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 业务单元保存请求DTO
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务单元保存请求DTO
 * 用于业务单元新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessUnitSaveRequest {

    /**
     * 业务单元数据
     */
    private BusinessUnitData data;

    /**
     * 业务单元数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BusinessUnitData {

        /**
         * 主键ID，业务单元ID 新增时无需填写，修改时必填
         */
        private String id;

        /**
         * 业务单元编码
         */
        private String code;

        /**
         * 业务单元名称，支持多语
         */
        private MultiLangName name;

        /**
         * 上级组织单元ID
         */
        private String parent;

        /**
         * 启用状态（1:启用，0:停用）
         */
        private Integer enable;

        /**
         * 描述
         */
        private String description;

        /**
         * 排序号
         */
        private Integer sortno;

        /**
         * 操作标识, Insert:新增、Update:更新
         */
        private String _status;
    }

    /**
     * 多语言名称
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {

        /**
         * 中文名称
         */
        private String zh_CN;

        /**
         * 英文名称
         */
        private String en_US;
    }
}