package com.gok.pboot.financial.vo;

import lombok.*;

/**
 * 工资成本
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WageCostPushVO {

    /**
     * 归属月份
     */
    private String yearMonthDate;

    /**
     * 当前部门id
     */
    private Long currentDeptId;

    /**
     * 当前部门
     */
    private String currentDept;


    /**
     * 一级部门id
     */
    private Long primaryDeptId;

    /**
     * 一级部门
     */
    private String primaryDept;

    /**
     * 二级部门id
     */
    private Long secondaryDeptId;

    /**
     * 二级部门
     */
    private String secondaryDept;

    /**
     * 三级部门id
     */
    private Long tertiaryDeptId;

    /**
     * 三级部门
     */
    private String tertiaryDept;

    /**
     * 单位社保
     */
    private String unitSocialSecurity;

    /**
     * 单位公积金
     */
    private String unitProvidentFund;

    /**
     * 工资成本
     */
    private String payrollCost;

    /**
     * 协议补偿
     */
    private String agreementCompensation;

    /**
     * 绩效工资
     */
    private String performance;

    /**
     * 发薪主体
     */
    private String salaryPaidSubject;

    /**
     * 科目编号
     */
    private String accountCode;

}
