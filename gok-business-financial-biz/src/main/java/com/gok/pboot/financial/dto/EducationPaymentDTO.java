package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 新增教育回款追踪
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EducationPaymentDTO {

    private Long id;

    /**
     * 批量取消汇总
     */
    private List<Long> ids;

    /**
     * 收款平台
     */
    @NotBlank(message = "收款平台不能为空")
    private String paymentPlatform;

    /**
     * 收款公司
     */
    private String paymentCompany;

    /**
     * 收款日期
     */
    @NotBlank(message = "收款日期不能为空")
    private String paymentDate;

    /**
     * 交易流水号
     */
    @NotBlank(message = "交易流水号不能为空")
    private String transactionNumber;

    /**
     * 到账金额
     */
    @NotNull(message = "到账金额不能为空")
    private BigDecimal receivedAmount;

    /**
     * 不含税金额
     */
    private BigDecimal amountExcludingTax;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 手续费
     */
    private BigDecimal commission;

    /**
     * 实际到账金额
     */
    private BigDecimal actualAmountReceived;

    /**
     * 回款备注
     */
    private String paymentNote;

    /**
     * 开票状态（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    private Integer invoicingStatus;

    /**
     * 开票日期
     */
    private String invoicingDate;

    /**
     * 发票号码
     */
    private String invoicingNumber;

}
