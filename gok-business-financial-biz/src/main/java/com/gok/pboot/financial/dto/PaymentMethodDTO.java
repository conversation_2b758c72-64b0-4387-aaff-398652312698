package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 收款方式DTO
 * <AUTHOR>
 * @since 2023-09-27
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentMethodDTO {
    /**
     * id
     */
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 启用状态（0启用、1禁用）
     */
    private Integer enableStatus;

    /**
     * 方式备注
     */
    private String methodRemarks;

    /**
     * 删除Ids
     */
    private List<Long> ids;

}

