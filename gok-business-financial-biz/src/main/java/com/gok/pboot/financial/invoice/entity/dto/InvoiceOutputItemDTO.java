package com.gok.pboot.financial.invoice.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/06/10
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceOutputItemDTO {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * 销项发票台账ID
     */
    private Long outputId;

    /**
     * 发票号码/原蓝字发票号码
     */
    private String invoiceNo;

    /**
     * 流程编号/发票规划ID
     */
    private String requestIdOrPlanId;

    /**
     * 项目名称或编码
     */
    private String projectNameOrNo;

    /**
     * 合同名称或编码
     */
    private String contractNameOrNo;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 商品/服务名称ID
     */
    private Integer goodsServiceName;

    /**
     * 合同签约主体
     */
    private Integer contractSubject;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 红蓝字 0=蓝字 1=红字
     */
    private Integer redAndBlue;

    /**
     * 业务归属部门id集合
     */
    private List<Long> businessDeptIds;

    /**
     * 人员名称
     */
    private String memberName;

    /**
     * excel导出字段集合
     */
    private List<String> includeExcel;

} 