package com.gok.pboot.financial.common.client;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * - OA配置类 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
@ConfigurationProperties(prefix = "gok-oa")
public class OaProperties {

    /**
     * url
     */
    private String url;

    /**
     * appid
     */
    private String appid;

    /**
     * 密钥
     */
    private String secret;

    /**
     * spk
     */
    private String spk;









}
