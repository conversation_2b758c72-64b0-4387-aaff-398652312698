package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 结项启动条件Enum
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Getter
public enum ClosingStartConditionsEnum implements ValueEnum<Integer> {

    /**
     * 合同结项（全部结项）
     */
    ALL(0, "合同结项（全部结项）"),

    /**
     * 内部项目结项
     */
    INNER(1, "内部项目结项"),

    /**
     * 合同结项（阶段验收）
     */
    STAGE(2, "合同结项（阶段验收）"),

    /**
     * 项目中止
     */
    STOP(3, "项目中止");

    private final Integer value;

    private final String name;

    ClosingStartConditionsEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
