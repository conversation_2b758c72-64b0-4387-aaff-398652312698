package com.gok.pboot.financial.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

/**
 * 财务处理单分页
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@Accessors(chain = true)
public class FinancialProcessFormPageDTO implements Serializable {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * '审批状态'
     * {@link com.gok.pboot.financial.enums.TabTypeEnum}
     */
    private Integer tabType = 0;

    /**
     * 单据id集合
     */
    private List<Long> ids;

    /**
     * 单据类型
     */
    private List<Integer> billTypes;

    /**
     * 单据编号
     */
    private String billNumber;

    /**
     * 申请事由
     */
    private String applicationReason;

    /**
     * 所属公司
     */
    private String belongCompany;

    /**
     * 所属部门
     */
    private List<Long> deptIds;

    /**
     * '审批状态'
     * {@link com.gok.pboot.financial.enums.ApprovalStatusEnum}
     */
    private Set<Integer> approvalStatus;

    /**
     * '收单状态'
     * {@link com.gok.pboot.financial.enums.ReceiptStatusEnum}
     */
    private Set<Integer> receiptStatus;

    /**
     * '凭证状态'
     * {@link com.gok.pboot.financial.enums.VoucherStatusEnum}
     */
    private Integer voucherStatus;

    /**
     * '结算状态'
     * {@link com.gok.pboot.financial.enums.SettlementStatusEnum}
     */
    private Set<Integer> settlementStatus;

    /**
     * 相关人员
     */
    private String relevantPersonnel;

    /**
     * 开始日期
     */
    private LocalDate startDate;

    /**
     * 结束日期
     */
    private LocalDate endDate;

    /**
     * 最小应付金额
     */
    private BigDecimal payableAmountMin;

    /**
     * 最大应付金额
     */
    private BigDecimal payableAmountMax;

    /**
     * 是否导出
     */
    private Boolean ifExport = false;

    private List<String> includeExcel;

}

