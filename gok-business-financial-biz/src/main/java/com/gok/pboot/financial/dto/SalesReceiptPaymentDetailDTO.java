package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * 销售收款计划回款概况dto
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPaymentDetailDTO {

    /**
     * 款项金额
     */
    private String currentPaymentMoney;

    /**
     * 实际款项金额
     */
    private String actualPaymentAmount;

    /**
     * 款项差额
     */
    private String paymentDifference;

    /**
     * 里程碑
     */
    private String milestones;

    /**
     * 里程碑计划日期（【预计完成日期】）
     */
    private LocalDate expectedCompleteDate;

    /**
     * 里程碑达成日期（【实际完成日期】）
     */
    private LocalDate actualCompleteDate;

    /**
     * 回款账期（天）
     */
    private Integer collectDays;

    /**
     * 目标回款日期
     */
    private LocalDate targetPaymentDate;

    /**
    * 预计回款日期
    */
    private String expectedPaymentDate;

    /**
     * 实际追款时长
     */
    private String actualCollectionTime;

    /**
     * 实际款项日期
     */
    private String paymentDate;

    /**
     * 客户付款窗口期
     */
    private String customerPaymentWindowPeriod;


}