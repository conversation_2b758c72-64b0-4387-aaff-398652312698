package com.gok.pboot.financial.vo;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 销售收款计划客户付款审批流程
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPaymentProcessVO {
    /**
    * id
    */
    private Long id;

    /**
    * 销售收款id
    */
    private Long salesReceiptId;

    /**
     * 审批节点名称
     */
    private String approvalProcess;

    /**
    * 审批状态(0未开始 1进行中 2已完成)
    */
    private Integer approvalStatus;

    /**
     * 审批状态Txt
     */
    private String approvalStatusTxt;
}