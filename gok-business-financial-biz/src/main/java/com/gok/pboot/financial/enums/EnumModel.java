package com.gok.pboot.financial.enums;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
public class EnumModel {
    Object name;
    Object value;

    public Object getName() {
        return name;
    }

    public void setName(Object name) {
        this.name = name;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

}
