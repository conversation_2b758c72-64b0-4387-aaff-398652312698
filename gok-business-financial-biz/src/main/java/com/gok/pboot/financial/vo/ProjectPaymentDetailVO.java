package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentDetailVO {
    /**
     * 收款公司code
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private String paymentCompany;

    /**
     * 收款公司value
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private String paymentCompanyTxt;


    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    private String paymentDate;

    /**
     * 收款金额
     */
    private String paymentAmount;

    /**
     * 收款平台
     */
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private String budgetCollectionAmount;

    /**
     * 备注
     */
    private String paymentNote;

    /**
     * 资金账户
     * （原银行账户）
     */
    private String bankAccount;

}
