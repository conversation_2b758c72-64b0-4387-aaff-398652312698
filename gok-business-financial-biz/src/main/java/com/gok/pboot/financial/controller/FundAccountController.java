package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.service.IFundAccountService;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.FundAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 资金账户控制层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@RestController
@Api(tags = "资金账户")
@RequiredArgsConstructor
@RequestMapping("/fund_account")
public class FundAccountController {

    private final IFundAccountService fundAccountService;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link FundAccountFindDTO}
     * @return {@link R}<{@link Page}<{@link FundAccountVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<FundAccountVO>> findPage(@RequestBody @Valid FundAccountFindDTO dto) {
        return R.ok(fundAccountService.findPage(dto));
    }

    @GetMapping("/list")
    @ApiOperation(value = "下拉列表",notes = "下拉列表")
    public R<List<FundAccountVO>> findList() {
        return R.ok(fundAccountService.findList());
    }

    /**
     * 新增
     *
     * @param dto {@link FundAccountDTO}
     * @return {@link R}<>
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    public R addOrEdit(@RequestBody @Valid FundAccountDTO dto) {
        return fundAccountService.addOrEdit(dto);
    }

    /**
     * 查看
     *
     * @return {@link FundAccountVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<FundAccountVO> info(@PathVariable Long id) {
        return fundAccountService.info(id);
    }

    /**
     * 编辑
     *
     * @param dto {@link FundAccountDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/edit/{id}")
    @ApiOperation(value = "编辑", notes = "编辑")
    public R addOrEdit(@PathVariable Long id,@RequestBody @Valid FundAccountDTO dto) {
        dto.setId(id);
        return fundAccountService.addOrEdit(dto) ;
    }

    /**
     * 删除
     *
     * @param dto {@link FundAccountIdsDTO}
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R del(@RequestBody FundAccountIdsDTO dto) {
        return fundAccountService.del(dto.getIdList()) ? R.ok() : R.failed();
    }


}
