package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 *  收入类型枚举
 * <AUTHOR>
 */
@Getter
public enum IncomeTypeEnum implements ValueEnum<String> {

    /**
     * 产品收入 科目ID
     */
    PRODUCT_REVENUE("产业板块-产品收入", VoucherSubjectIdEnum.PRODUCT_REVENUE.getName()),

    /**
     * 服务收入 科目ID
     */
    SERVICE_REVENUE("产业板块-服务收入", VoucherSubjectIdEnum.SERVICE_REVENUE.getName()),

    /**
     * 教育B端 科目ID
     */
    EDUCATION_B_END("人才板块-教育B端", VoucherSubjectIdEnum.EDUCATION_B_END.getName()),

    /**
     * 教育C端 科目ID
     */
    EDUCATION_C_END("人才板块-教育C端", VoucherSubjectIdEnum.EDUCATION_C_END.getName()),

    /**
     * 人才服务 科目ID
     */
    TALENT_SERVICES("人才板块-人才服务", VoucherSubjectIdEnum.TALENT_SERVICES.getName()),

    /**
     * 培训认证 科目ID
     */
    TRAINING_CERTIFICATION("人才板块-培训认证", VoucherSubjectIdEnum.TRAINING_CERTIFICATION.getName());

    private final String value;

    private final String name;

    IncomeTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }
}
