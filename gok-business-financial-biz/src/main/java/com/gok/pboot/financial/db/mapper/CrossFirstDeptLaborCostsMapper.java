package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.db.entity.CrossFirstDeptLaborCosts;
import com.gok.pboot.financial.dto.DeptArtificialCostDTO;
import com.gok.pboot.financial.vo.CrossDeptLaborCostsVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 跨部门一级人工成本台账 Mapper
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
public interface CrossFirstDeptLaborCostsMapper extends BaseMapper<CrossFirstDeptLaborCosts> {

    /**
     * 列表展示跨部门人工成本台帐数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    @BusinessDataScope(scopeDeptNameList = {"personnel_dept_id"})
    List<CrossDeptLaborCostsVO> findList(@Param("query") DeptArtificialCostDTO deptArtificialCostDTO);
}