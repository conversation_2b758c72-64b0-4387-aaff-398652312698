package com.gok.pboot.financial.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.AccountingAccount;
import com.gok.pboot.financial.dto.AccountingAccountDTO;
import com.gok.pboot.financial.dto.AccountingAccountImportDTO;
import com.gok.pboot.financial.dto.AccountingAccountPageDTO;
import com.gok.pboot.financial.vo.AccountingAccountPageVO;
import com.gok.pboot.financial.vo.AccountingAccountVO;

import java.util.List;

/**
 * 会计科目
 * <AUTHOR>
 */
public interface IAccountingAccountService extends IService<AccountingAccount> {
    /**
     * 分页查询
     * @param dto
     * @return
     */
    Page<AccountingAccountPageVO> findPage(AccountingAccountPageDTO dto);

    /**
     * 查看
     * @param id
     * @return
     */
    R<AccountingAccountVO> info(Long id);

    /**
     * 新增或编辑
     * @param dto
     * @return
     */
    R<String> addOrEdit(AccountingAccountDTO dto);

    /**
     * 删除
     * @param idList
     * @return
     */
    Boolean del(List<Long> idList);

    /**
     * 导出
     * @param dto
     * @return
     */
    List<AccountingAccountPageVO> exportExcel(AccountingAccountPageDTO dto);

    /**
     * 启用
     * @param dto
     * @return
     */
    Boolean enable(AccountingAccountDTO dto);

    /**
     * 查询完整科目结构
     * @return
     */
    List<Tree<Long>> accountTree();

    /**
     * 同步
     * @return
     */
    R<String> sync();

    /**
     * 导入
     * @param importDTOList
     * @return
     */
    R<String> importExcel(List<AccountingAccountImportDTO> importDTOList);

    /**
     * 获取详情
     * @param code
     * @return
     */
    R<AccountingAccountVO> getInfoByDto(String code);
}
