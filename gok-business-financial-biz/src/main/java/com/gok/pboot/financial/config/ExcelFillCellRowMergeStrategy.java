//package com.gok.pboot.financial.config;
//
//import com.alibaba.excel.metadata.Head;
//import com.alibaba.excel.metadata.data.WriteCellData;
//import com.alibaba.excel.write.handler.CellWriteHandler;
//import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
//import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
//import com.gok.module.excel.api.annotation.CustomMerge;
//import org.apache.poi.ss.usermodel.Cell;
//import org.apache.poi.ss.usermodel.CellType;
//import org.apache.poi.ss.usermodel.Row;
//import org.apache.poi.ss.usermodel.Sheet;
//import org.apache.poi.ss.util.CellRangeAddress;
//
//import java.lang.reflect.Field;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * @Author: xiongwh
// * @Date: 2025/6/5 9:45
// * @Description: Excel单元格行合并处理策略
// */
//
//public class ExcelFillCellRowMergeStrategy implements CellWriteHandler {
//
//    //数组存放这一行需要合并那几列  [0,1,2] 在这mergeRowIndex行中合并 0、1、2列
//    private int[] mergeColumnIndex;
//
//    // 存放需要向上合并的行
//    private int mergeRowIndex = 1;
//
//    // 不要合并的行
//    private Integer noMergeRowIndex;
//
//    // 标记是否已经初始化合并列信息
//    private boolean initialized = false;
//
//    // 存储列索引与是否需要合并的映射关系
//    private Map<Integer, Boolean> columnMergeMap = new HashMap<>();
//
//    /**
//     * 通过Head信息检查当前列是否需要合并
//     *
//     * @param head Head信息
//     * @param columnIndex 列索引
//     * @return 是否需要合并
//     */
//    private boolean shouldMergeColumnByHead(Head head, Integer columnIndex) {
//        if (head == null || head.getField() == null) {
//            return false;
//        }
//        try {
//            Field field = head.getField();
//            // 检查字段是否有@CustomMerge注解
//            return field.isAnnotationPresent(CustomMerge.class);
//        } catch (Exception e) {
//            // 如果出现异常，返回false
//            return false;
//        }
//    }
//
//    @Override
//    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
//
//    }
//
//    @Override
//    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
//
//    }
//
//    @Override
//    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, WriteCellData<?> cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
//
//    }
//
//    @Override
//    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
//                                 List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
//        int curRowIndex = cell.getRowIndex();
//        int curColIndex = cell.getColumnIndex();
//
//        // 如果是表头，跳过
//        if (isHead != null && isHead) {
//            return;
//        }
//
//        // 如果当前行索引小于等于合并起始行，跳过
//        if (curRowIndex <= mergeRowIndex) {
//            return;
//        }
//
//        // 检查当前列是否需要合并
//        boolean shouldMerge = false;
//
//        // 如果已经初始化了mergeColumnIndex，使用预设的列索引
//        if (initialized && mergeColumnIndex != null && mergeColumnIndex.length > 0) {
//            for (int index : mergeColumnIndex) {
//                if (curColIndex == index) {
//                    shouldMerge = true;
//                    break;
//                }
//            }
//        } else {
//            // 如果没有预设的列索引，尝试通过Head信息动态判断
//            // 首先检查缓存
//            if (columnMergeMap.containsKey(curColIndex)) {
//                shouldMerge = columnMergeMap.get(curColIndex);
//            } else {
//                // 动态检查当前列是否需要合并
//                shouldMerge = shouldMergeColumnByHead(head, curColIndex);
//                // 缓存结果
//                columnMergeMap.put(curColIndex, shouldMerge);
//            }
//        }
//
//        // 如果需要合并，执行合并逻辑
//        if (shouldMerge) {
//            mergeWithPrevRow(writeSheetHolder, cell, curRowIndex, curColIndex);
//        }
//    }
//
//    /**
//     * 当前单元格向上合并
//     * .
//     * @param writeSheetHolder writeSheetHolder
//     * @param cell             当前单元格
//     * @param curRowIndex      当前行
//     * @param curColIndex      当前列
//     */
//    private void mergeWithPrevRow(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
//        try {
//            Object curData = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : cell.getNumericCellValue();
//            Row preRow = cell.getSheet().getRow(curRowIndex - 1);
//            if (preRow == null) {
//                // 当获取不到上一行数据时，使用缓存sheet中数据
//                preRow = writeSheetHolder.getCachedSheet().getRow(curRowIndex - 1);
//            }
//
//            if (preRow == null) {
//                return;
//            }
//
//            Cell preCell = preRow.getCell(curColIndex);
//            if (preCell == null) {
//                return;
//            }
//
//            Object preData = preCell.getCellType() == CellType.STRING ? preCell.getStringCellValue() : preCell.getNumericCellValue();
//
//            //不需要合并的列直接跳出
//            if ((noMergeRowIndex != null) && noMergeRowIndex == (curRowIndex - 1)) {
//                return;
//            }
//
//            // 将当前单元格数据与上一个单元格数据比较
//            boolean dataBool = preData != null && preData.equals(curData);
//
//            // 获取每一行第一列数据和上一行第一列数据进行比较，如果相等则合并
//            Cell currentFirstCell = cell.getRow().getCell(0);
//            Cell prevFirstCell = cell.getSheet().getRow(curRowIndex - 1).getCell(0);
//
//            if (currentFirstCell == null || prevFirstCell == null) {
//                return;
//            }
//
//            String currentFirstCellValue = currentFirstCell.getStringCellValue();
//            String prevFirstCellValue = prevFirstCell.getStringCellValue();
//            boolean equals = currentFirstCellValue != null && currentFirstCellValue.equals(prevFirstCellValue);
//
//            if (dataBool && equals) {
//                Sheet sheet = writeSheetHolder.getSheet();
//                List<CellRangeAddress> mergeRegions = sheet.getMergedRegions();
//                boolean isMerged = false;
//                for (int i = 0; i < mergeRegions.size() && !isMerged; i++) {
//                    CellRangeAddress cellRangeAddr = mergeRegions.get(i);
//                    // 若上一个单元格已经被合并，则先移出原有的合并单元，再重新添加合并单元
//                    if (cellRangeAddr.isInRange(curRowIndex - 1, curColIndex)) {
//                        sheet.removeMergedRegion(i);
//                        cellRangeAddr.setLastRow(curRowIndex);
//                        sheet.addMergedRegion(cellRangeAddr);
//                        isMerged = true;
//                    }
//                }
//                // 若上一个单元格未被合并，则新增合并单元
//                if (!isMerged) {
//                    CellRangeAddress cellRangeAddress = new CellRangeAddress(curRowIndex - 1, curRowIndex, curColIndex, curColIndex);
//                    sheet.addMergedRegion(cellRangeAddress);
//                }
//            }
//        } catch (Exception e) {
//            // 如果合并过程中出现异常，记录日志但不影响整体流程
//            System.err.println("合并单元格时出现异常: " + e.getMessage());
//        }
//    }
//}
//
