package com.gok.pboot.financial.enums;

import lombok.Getter;

/**
 *  凭证创建T+科目id
 * <AUTHOR>
 */
@Getter
public enum VoucherSubjectIdEnum {

    /**
     * 合同履约成本-直接成本-工资 科目id
     */
    PAYROLL(0, "14750301"),
    /**
     * 合同履约成本-直接成本-社保 科目id
     */
    SOCIAL_SECURITY(1, "14750302"),

    /**
     * 合同履约成本-直接成本-公积金 科目id
     */
    PROVIDENT_FUND(2, "14750303"),

    /**
     * 管理费用-工资 科目id
     */
    MANAGEMENT_SALARY(3, "560201"),

    /**
     * 管理费用-社保 科目id
     */
    MANAGEMENT_SOCIAL_SECURITY(4, "560202"),

    /**
     * 管理费用-公积金 科目id
     */
    MANAGEMENT_PROVIDENT_FUND(5, "560203"),

    /**
     * 管理费用-离职补偿金 科目id
     */
    MANAGEMENT_AGREEMENT_COMPENSATION(25, "560241"),

    /**
     * 管理费用-绩效 科目id
     */
    MANAGEMENT_PERFORMANCE(26, "560236"),

    /**
     * 销售费用-售前 科目id
     */
    SALES_AFTER(6, "560125"),

    /**
     * 销售费用-售后 科目id
     */
    SALES_BEFORE(7, "560124"),

    /**
     * 销售费用-工资 科目id
     */
    SALE_SALARY(8, "560101"),

    /**
     * 销售费用-社保 科目ID
     */
    SALE_SOCIAL_SECURITY(9, "560102"),

    /**
     * 销售费用-公积金 科目ID
     */
    SALE_PROVIDENT_FUND(10, "560103"),

    /**
     * 销售费用-离职补偿金 科目ID
     */
    SALE_AGREEMENT_COMPENSATION(27, "560129"),

    /**
     * 销售费用-绩效 科目ID
     */
    SALE_PERFORMANCE(28, "560128"),

    /**
     * 研发支出–费用化支出–人员人工费用-工资 科目id
     */
    RESEARCH_SALARY(11, "4301010101"),

    /**
     * 研发支出–费用化支出–人员人工费用-社保 科目id
     */
    RESEARCH_SOCIAL_SECURITY(12, "4301010102"),

    /**
     * 研发支出–费用化支出–人员人工费用-公积金 科目id
     */
    RESEARCH_PROVIDENT_FUND(13, "4301010103"),

    /**
     * 应付职工薪酬-应付职工工资 科目ID
     */
    PAYROLL_PAYABLE(14, "221101"),

    /**
     * 应付职工薪酬-应付社会保险费 科目ID
     */
    PAYABLE_SOCIAL_INSURANCE(15, "221104"),

    /**
     * 应付职工薪酬-应付住房公积金 科目ID
     */
    PAYABLE_HOUSING_PROVIDENT_FUND(16, "221105"),

    /**
     * 应付职工薪酬-离职补偿金 科目ID
     */
    PAYABLE_AGREEMENT_COMPENSATION(26, "221109"),

    /**
     * 应付职工薪酬-绩效 科目ID
     */
    PAYABLE_PERFORMANCE(27, "221111"),

    /**
     * 应收账款-非关联单位 科目ID
     */
    ACCOUNTS_RECEIVABLE(17,"112203"),

    /**
     * 其他应收款-非关联单位 科目ID
     */
    OTHER_ACCOUNTS_RECEIVABLE(17,"122103"),

    /**
     * 合同结算-价款结算 科目ID
     */
    PRICE_SETTLEMENT(18,"112401"),

    /**
     * 主营业务收入-综合服务 科目ID
     */
    COMPREHENSIVE_SERVICES(19,"500101"),

    /**
     * 产品收入 科目ID
     */
    PRODUCT_REVENUE(20, "********"),

    /**
     * 服务收入 科目ID
     */
    SERVICE_REVENUE(21, "********"),

    /**
     * 教育B端 科目ID
     */
    EDUCATION_B_END(22, "********"),

    /**
     * 教育C端 科目ID
     */
    EDUCATION_C_END(23, "********"),

    /**
     * 人才服务 科目ID
     */
    TALENT_SERVICES(24, "********"),

    /**
     * 培训认证 科目ID
     */
    TRAINING_CERTIFICATION(25, "********");

    private final Integer value;

    private final String name;

    VoucherSubjectIdEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
