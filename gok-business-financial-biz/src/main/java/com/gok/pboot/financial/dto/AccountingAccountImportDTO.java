package com.gok.pboot.financial.dto;


import com.alibaba.excel.annotation.ExcelProperty;
import com.gok.module.excel.api.annotation.ExcelSelected;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;


/**
    * 会计科目导入
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingAccountImportDTO {

    /**
     * 科目编码
     */
    @NotBlank(message = "级次不能为空")
    @ExcelProperty("科目编码")
    private String accountCode;

    /**
     * 科目名称
     */
    @ExcelProperty("科目名称")
    @NotBlank(message = "科目名称不能为空")
    private String accountName;

    /**
     * 助记词
     */
    
    @ExcelProperty("助记词")
    private String mnemonicWords;

    /**
     * 上级科目
     */
    @ExcelProperty("上级科目")
    private String parentAccount;

    /**
     * 余额方向
     */
    @ExcelProperty("余额方向")
    @ExcelSelected(source = {"借方","贷方"})
    @NotBlank(message = "余额方向不能为空")
    private String balanceDirectionTxt;

    /**
     * 会计体系
     */
    @ExcelProperty("会计体系")
    @NotBlank(message = "会计体系不能为空")
    private String accountingSystemTxt;

    /**
     * 科目类型
     */
    @ExcelProperty("科目类型")
    private String accountType;

    /**
     * 现金分类
     */
    @ExcelProperty("现金分类")
    @ExcelSelected(source = {"现金科目","银行科目","现金等价物"})
    @NotBlank(message = "现金分类不能为空")
    private String cashClassificationTxt;

    /**
     * 账页格式
     */
    @ExcelProperty("账页格式")
    @ExcelSelected(source = {"金额式","外币金额式"})
    @NotBlank(message = "账页格式不能为空")
    private String accountPageFormatTxt;

    /**
     * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
     */
    @ExcelProperty("停用状态")
    @ExcelSelected(source = {"是","否"})
    @NotBlank(message = "停用状态不能为空")
    private String enableStatusTxt;

    /**
     * 停用日期
     */
    @ExcelProperty("停用日期")
    private String suspensionDate;

    /**
     * 部门核算
     */
    @ExcelProperty("部门核算")
    @ExcelSelected(source = {"是","否"})
    private String deptCalculate;

    /**
     * 职员核算
     */
    @ExcelProperty("职员核算")
    @ExcelSelected(source = {"是","否"})
    private String staffCalculate;

    /**
     * 客户核算
     */
    @ExcelProperty("客户核算")
    @ExcelSelected(source = {"是","否"})
    private String customerCalculate;

    /**
     * 供应商核算
     */
    @ExcelProperty("供应商核算")
    @ExcelSelected(source = {"是","否"})
    private String supplierCalculate;

    /**
     * 往来单位核算
     */
    @ExcelProperty("往来单位核算")
    @ExcelSelected(source = {"是","否"})
    private String contactUnitsCalculate;

    /**
     * 项目核算
     */
    @ExcelProperty("项目核算")
    @ExcelSelected(source = {"是","否"})
    private String projectCalculate;

    /**
     * 资金账户核算
     */
    @ExcelProperty("资金账户核算")
    @ExcelSelected(source = {"是","否"})
    private String fundAccountCalculate;

    /**
     * 存货核算
     */
    @ExcelProperty("存货核算")
    @ExcelSelected(source = {"是","否"})
    private String inventoryCalculate;

    /**
     * 计量单位核算
     */
    @ExcelProperty("计量单位核算")
    @ExcelSelected(source = {"是","否"})
    private String meteringCalculate;

    /**
     * 现金流量核算
     */
    @ExcelProperty("现金流量核算")
    @ExcelSelected(source = {"是","否"})
    private String cashFlowCalculate;

    /**
     * 成本中心核算
     */
    @ExcelProperty("成本中心核算")
    @ExcelSelected(source = {"是","否"})
    private String costCenterCalculate;

    /**
     * 自定义辅助核算类型编码1
     */
    @ExcelProperty("自定义辅助核算类型编码1")
    private String customizeCalculateOne;

    /**
     * 自定义辅助核算类型编码2
     */
    @ExcelProperty("自定义辅助核算类型编码2")
    private String customizeCalculateTwo;

    /**
     * 自定义辅助核算类型编码3
     */
    @ExcelProperty("自定义辅助核算类型编码3")
    private String customizeCalculateThree;

    /**
     * 自定义辅助核算类型编码4
     */
    @ExcelProperty("自定义辅助核算类型编码4")
    private String customizeCalculateFour;

    /**
     * 自定义辅助核算类型编码5
     */
    @ExcelProperty("自定义辅助核算类型编码5")
    private String customizeCalculateFive;

    /**
     * 自定义辅助核算类型编码6
     */
    @ExcelProperty("自定义辅助核算类型编码6")
    private String customizeCalculateSix;

    /**
     * 自定义辅助核算类型编码7
     */
    @ExcelProperty("自定义辅助核算类型编码7")
    private String customizeCalculateSeven;

    /**
     * 自定义辅助核算类型编码8
     */
    @ExcelProperty("自定义辅助核算类型编码8")
    private String customizeCalculateEight;

    /**
     * 自定义辅助核算类型编码9
     */
    @ExcelProperty("自定义辅助核算类型编码9")
    private String customizeCalculateNine;
}