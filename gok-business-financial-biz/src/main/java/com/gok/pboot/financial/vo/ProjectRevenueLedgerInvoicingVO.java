package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 关联发票信息展示
 *
 * <AUTHOR>
 * @since 2023-11-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectRevenueLedgerInvoicingVO {

    /**
     * 发票id
     */
    private Long invoicingId;

    /**
     * 发票日期
     */
    private String invoicingDate;

    /**
     * 发票号码
     */
    private String invoicingNumber;

    /**
     * 开票金额（含税）
     */
    private String invoicingAmountIncludingTax;

    /**
     * 税额
     */
    private String taxAmount;

    /**
     * 开票金额（不含税）
     */
    private String invoicingAmount;

    /**
     * 税率
     */
    private String taxRate;

    /**
     * 开票主体
     */
    private String invoicingSubject;

}
