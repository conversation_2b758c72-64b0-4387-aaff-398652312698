package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.common.enums.DeptAssociationEnum;
import com.gok.bcp.upms.dto.InnerDeptMapDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.user.UserUtils;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.enums.YesOrNoEnum;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.OpenApi;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedger;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedgerInvoicing;
import com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerDetailMapper;
import com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerMapper;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAccountingSetService;
import com.gok.pboot.financial.service.IProjectRevenueLedgerInvoicingService;
import com.gok.pboot.financial.service.IProjectRevenueLedgerService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 项目收入台帐ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectRevenueLedgerServiceImpl extends ServiceImpl<ProjectRevenueLedgerMapper, ProjectRevenueLedger>
        implements IProjectRevenueLedgerService {

    private final IProjectRevenueLedgerInvoicingService projectRevenueLedgerInvoicingService;

    private final ProjectRevenueLedgerDetailMapper projectRevenueLedgerDetailMapper;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final IAccountingSetService accountingSetService;

    private final LogRecordUtils logRecordUtils;

    private final DBApi dbApi;

    private final OpenApi openApi;

    @Value("${middlePlatform.deptCatName}")
    private String deptCatName;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link Page}<{@link ProjectRevenueLedgerVO}>
     */
    @Override
    public StatisticsPage<ProjectRevenueLedgerVO> findPage(ProjectRevenueLedgerDTO dto) {
        StatisticsPage<ProjectRevenueLedgerVO> voPage = new StatisticsPage<>(dto.getCurrent(), dto.getSize());
        // 1、分页查询获取项目收入台账
        Page<ProjectRevenueLedger2VO> page = baseMapper.findPage(new Page<>(dto.getCurrent(), dto.getSize()), dto);
        List<ProjectRevenueLedger2VO> records = page.getRecords();
        if (CollUtil.isEmpty(records)) {
            return voPage;
        }
        // 2、获取项目结项信息
        Map<Long, List<ProjectRevenueLedgerDetail>> detailListMap = projectRevenueLedgerDetailMapper
                .selectList(Wrappers.lambdaQuery(ProjectRevenueLedgerDetail.class))
                .stream()
                .collect(Collectors.groupingBy(ProjectRevenueLedgerDetail::getRequestId));
        // 3、分页数据封装
        if (CollUtil.isNotEmpty(records)) {
            voPage.setRecords(this.addVoToList(records, detailListMap));
            voPage.setTotal(page.getTotal());
        }
        statistics(voPage,dto,detailListMap);
        return voPage;
    }

    private void statistics(StatisticsPage<ProjectRevenueLedgerVO> voPage,
                            ProjectRevenueLedgerDTO dto,
                            Map<Long, List<ProjectRevenueLedgerDetail>> detailListMap) {
        if (voPage.getTotal()==0) {
            return;
        }
        List<ProjectRevenueLedger2VO> voList = baseMapper.findPage(dto);

        Map<String, BigDecimal> resultMap = new HashMap<>(3);
        resultMap.put("incomeAmountTaxTotal", BigDecimal.ZERO);
        resultMap.put("incomeAmountTotal", BigDecimal.ZERO);
        resultMap.put("incomeDetailAmountTotal", BigDecimal.ZERO);

        voList.forEach(item->{
            BigDecimal incomeAmountTax = MoneyUtils.decryptToBigDecimal(item.getIncomeAmountTax(),false);
            resultMap.merge("incomeAmountTaxTotal", incomeAmountTax, BigDecimal::add);

            BigDecimal incomeAmount = MoneyUtils.decryptToBigDecimal(item.getIncomeAmount(),false);
            resultMap.merge("incomeAmountTotal", incomeAmount, BigDecimal::add);

            BigDecimal incomeDetailAmount = detailListMap.getOrDefault(item.getId(), Collections.emptyList())
                    .stream()
                    .map(ProjectRevenueLedgerDetail::getIncomeDetailAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            resultMap.merge("incomeDetailAmountTotal", incomeDetailAmount, BigDecimal::add);
        });

        voPage.setStatistics(resultMap);
    }

    /**
     * 修改或新增备注
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 备注
     * @return {@code true} or {@code false}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Boolean updateRemarks(ProjectRevenueLedgerDTO projectRevenueLedgerDTO) {
        // 记录日志
        ProjectRevenueLedger projectRevenueLedger = baseMapper.selectOneById(projectRevenueLedgerDTO.getId());
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_INCOME_ACCOUNT,
                OperationEnum.EDIT_RECORDS,
                "【" + projectRevenueLedger.getProcessCoding() + "】" + OperationEnum.EDIT_RECORDS.getName());

        return baseMapper.updateProjectById(projectRevenueLedgerDTO.getId(), projectRevenueLedgerDTO.getRemarks());
    }

    /**
     * 导出
     *
     * @param dto {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link List}<{@link ProjectRevenueLedgerExcelVO}>
     */
    @Override
    public List<ProjectRevenueLedgerExcelVO> export(ProjectRevenueLedgerDTO dto) {
        List<ProjectRevenueLedgerExcelVO> list = new ArrayList<>();
        // 1、获取项目收入台账
        List<ProjectRevenueLedger> projectRevenueLedgerList = baseMapper.findList(dto);
        // 2、获取项目确认结项
        Map<Long, List<ProjectRevenueLedgerDetail>> detailList = projectRevenueLedgerDetailMapper.getList(dto)
                .stream()
                .collect(Collectors.groupingBy(ProjectRevenueLedgerDetail::getRequestId));
        // 3、导出
        if (CollUtil.isNotEmpty(projectRevenueLedgerList)) {
            // 3.1、记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_INCOME_ACCOUNT,
                    OperationEnum.EXPORT,
                    OperationEnum.EXPORT.getName() + "【" + projectRevenueLedgerList.size() + "条】");
            // 3.2、导出信息封装
            list = this.exportVO(projectRevenueLedgerList, detailList);
        } else {
            list.add(new ProjectRevenueLedgerExcelVO());
        }
        return list;
    }

    /**
     * 获取发票信息
     *
     * @param id           项目收入台帐id
     * @param contractCode 合同编码
     * @return {@link List}<{@link ProjectRevenueLedgerInvoicingVO}>
     */
    @Override
    public List<ProjectRevenueLedgerInvoicingVO> getInvoice(Long id, String contractCode) {
        List<ProjectRevenueLedgerInvoicingVO> list = new ArrayList<>();
        // 1、获取合同对应的发票数据
        List<ProjectRevenueLedgerInvoicingVO> relationInvoicingList = dbApi.getRelationInvoicing(contractCode);
        // 2、获取全部被关联发票的数据并排除该项目收入关联的发票
        List<ProjectRevenueLedgerInvoicing> existList = projectRevenueLedgerInvoicingService.list(Wrappers.<ProjectRevenueLedgerInvoicing>lambdaQuery()
                .eq(ProjectRevenueLedgerInvoicing::getDelFlag, YesOrNoEnum.NO.getVal())
                .notIn(ProjectRevenueLedgerInvoicing::getRevenueLedgerId, id)
        );
        if (CollUtil.isNotEmpty(existList)) {
            relationInvoicingList.forEach(r ->
                    existList.forEach(e -> {
                        if (e.getInvoicingId().equals(r.getInvoicingId())) {
                            list.add(r);
                        }
                    })
            );
        }
        relationInvoicingList.removeAll(list);
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        relationInvoicingList.forEach(r -> {
            r.setInvoicingSubject(attributableSubjectMap.getOrDefault(Integer.valueOf(r.getInvoicingSubject()),StrUtil.EMPTY));
            r.setInvoicingAmountIncludingTax(MoneyUtils.getInstance().transType(r.getInvoicingAmountIncludingTax(), false));
            r.setTaxAmount(MoneyUtils.getInstance().transType(r.getTaxAmount(), false));
            r.setInvoicingAmount(MoneyUtils.getInstance().transType(r.getInvoicingAmount(), false));
        });
        return relationInvoicingList;
    }

    /**
     * 关联发票
     *
     * @param relationInvoiceDTO 项目id与发票集合
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> relationInvoice(RelationInvoiceDTO relationInvoiceDTO) {
        // 1、根据项目应收id获取流程编号用于日志记录
        ProjectRevenueLedger projectRevenueLedger = baseMapper.selectOneById(relationInvoiceDTO.getId());
        // 2、查询已关联的发票进行全部删除
        List<ProjectRevenueLedgerInvoicing> relationsList = projectRevenueLedgerInvoicingService.list(Wrappers.<ProjectRevenueLedgerInvoicing>lambdaQuery()
                .eq(ProjectRevenueLedgerInvoicing::getDelFlag, YesOrNoEnum.NO.getVal())
                .eq(ProjectRevenueLedgerInvoicing::getRevenueLedgerId, relationInvoiceDTO.getId()));
        List<Long> relationsIdList = relationsList.stream().map(ProjectRevenueLedgerInvoicing::getId).collect(Collectors.toList());
        List<Long> relationsInvoicingIdList = relationsList.stream().map(ProjectRevenueLedgerInvoicing::getInvoicingId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(relationInvoiceDTO.getInvoicingList())) {
            StringBuilder s1 = new StringBuilder();
            // 3、关联发票
            List<ProjectRevenueLedgerInvoicing> projectRevenueLedgerInvoicingList = new ArrayList<>();
            relationInvoiceDTO.getInvoicingList().forEach(i -> {
                ProjectRevenueLedgerInvoicing projectRevenueLedgerInvoicing =
                        BeanUtil.copyProperties(i, ProjectRevenueLedgerInvoicing.class, "taxRate");
                projectRevenueLedgerInvoicing.setRevenueLedgerId(relationInvoiceDTO.getId());
                // 将千分位改为正常金额
                String invoicingAmountIncludingTax = i.getInvoicingAmountIncludingTax();
                if (Optional.ofNullable(invoicingAmountIncludingTax).isPresent() && invoicingAmountIncludingTax.contains(StrPool.COMMA)) {
                    projectRevenueLedgerInvoicing.setInvoicingAmountIncludingTax(invoicingAmountIncludingTax.replaceAll(StrPool.COMMA, CharSequenceUtil.EMPTY));
                } else if (MoneyUtils.TYPE.equals(invoicingAmountIncludingTax)) {
                    projectRevenueLedgerInvoicing.setInvoicingAmountIncludingTax(null);
                }
                String taxAmount = i.getTaxAmount();
                if (Optional.ofNullable(taxAmount).isPresent() && taxAmount.contains(StrPool.COMMA)) {
                    projectRevenueLedgerInvoicing.setTaxAmount(taxAmount.replaceAll(StrPool.COMMA, CharSequenceUtil.EMPTY));
                } else if (MoneyUtils.TYPE.equals(taxAmount)) {
                    projectRevenueLedgerInvoicing.setTaxAmount(null);
                }
                String invoicingAmount = i.getInvoicingAmount();
                if (Optional.ofNullable(invoicingAmount).isPresent() && invoicingAmount.contains(StrPool.COMMA)) {
                    projectRevenueLedgerInvoicing.setInvoicingAmount(invoicingAmount.replaceAll(StrPool.COMMA, CharSequenceUtil.EMPTY));
                } else if (MoneyUtils.TYPE.equals(invoicingAmount)) {
                    projectRevenueLedgerInvoicing.setInvoicingAmount(null);
                }
                if (CharSequenceUtil.isNotBlank(i.getTaxRate())) {
                    projectRevenueLedgerInvoicing.setTaxRate(Integer.valueOf(i.getTaxRate().replace("%", CharSequenceUtil.EMPTY)));
                }
                projectRevenueLedgerInvoicingList.add(projectRevenueLedgerInvoicing);

                s1.append("【").append(i.getInvoicingNumber()).append("】");
            });
            // 4.1、获取取消关联的数据（已关联数据未出现前端传来的数据则是取消关联数据）
            List<Long> list = projectRevenueLedgerInvoicingList.stream()
                    .map(ProjectRevenueLedgerInvoicing::getInvoicingId)
                    .collect(Collectors.toList());
            List<Long> unRelationsIdList = CollUtil.subtractToList(relationsInvoicingIdList, list);
            // 4.2、记录日志
            if (CollUtil.isNotEmpty(unRelationsIdList)) {
                StringBuilder s2 = new StringBuilder();
                projectRevenueLedgerInvoicingService.list(Wrappers.<ProjectRevenueLedgerInvoicing>lambdaQuery()
                        .eq(ProjectRevenueLedgerInvoicing::getDelFlag, YesOrNoEnum.NO.getVal())
                        .in(ProjectRevenueLedgerInvoicing::getInvoicingId, unRelationsIdList)
                ).forEach(p -> s2.append("【").append(p.getInvoicingNumber()).append("】"));

                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_INCOME_ACCOUNT,
                        OperationEnum.RELATION_INVOICE,
                        "【" + projectRevenueLedger.getProcessCoding() + "】关联" + s1.toString() + "且取消关联" + s2.toString());
            } else {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_INCOME_ACCOUNT,
                        OperationEnum.RELATION_INVOICE,
                        "【" + projectRevenueLedger.getProcessCoding() + "】关联" + s1.toString());
            }
            projectRevenueLedgerInvoicingService.saveBatch(projectRevenueLedgerInvoicingList);
        } else {
            StringBuilder s1 = new StringBuilder();
            relationsList.forEach(p -> s1.append("【").append(p.getInvoicingNumber()).append("】"));
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_INCOME_ACCOUNT,
                    OperationEnum.RELATION_INVOICE,
                    "【" + projectRevenueLedger.getProcessCoding() + "】取消关联" + s1.toString());
        }
        // 将删除放后是为了记录日志查询时防止查询为空的问题
        projectRevenueLedgerInvoicingService.removeBatchByIds(relationsIdList);

        return R.ok("关联发票成功");
    }

    @Override
    public PushResultVO push(List<String> ids) {
        StringBuilder sb = new StringBuilder();
        AtomicInteger succeed = new AtomicInteger();
        AtomicInteger error = new AtomicInteger();
        List<PushErrorReasonVO> errorReasonList = new ArrayList<>();
        // 1、获取待推送项目详情
        List<ProjectRevenueLedgerPushVO> projectRevenueLedgerList = baseMapper.selList(ids);
        if (ids.size() != projectRevenueLedgerList.size()) {
            List<Long> exitIds = projectRevenueLedgerList.stream().map(ProjectRevenueLedgerPushVO::getId).collect(Collectors.toList());
            List<String> collect = ids.stream().filter(i -> !exitIds.contains(Long.valueOf(i))).collect(Collectors.toList());
            for (String id : collect) {
                ProjectRevenueLedger projectRevenueLedger = baseMapper.selectOneById(Long.valueOf(id));
                if (Optional.ofNullable(projectRevenueLedger).isPresent()) {
                    projectRevenueLedger.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    projectRevenueLedger.setUpdateBy(UserUtils.getUser().getName());
                    projectRevenueLedger.setUpdateTime(LocalDateTime.now());
                    baseMapper.updatePushStatus(projectRevenueLedger);
                    // 记录日志
                    sb.append("【").append(projectRevenueLedger.getProcessCoding()).append("】【")
                            .append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                }
                PushErrorReasonVO errorReason = PushErrorReasonVO.builder()
                        .id(String.valueOf(id))
                        .errorReason(VoucherRecordEnum.ERROR_STR.getName())
                        .build();
                errorReasonList.add(errorReason);
                error.incrementAndGet();
            }
            // 日志记录
            if (sb.length() > NumberUtils.INTEGER_ZERO) {
                logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                        OperationEnum.PUSH, OperationEnum.PUSH.getName() + sb.toString());
            }

        }
        // 获取中台的所有核算部门信息
        List<InnerDeptMapDto> innerDeptMapDtoList = remoteOutMultiDeptService.getInnerDeptMapDto(deptCatName).getData();
        // 行政部门id作为key 部门信息作为value
        Map<Long, InnerDeptMapDto> innerDeptMap = innerDeptMapDtoList.stream()
                .collect(Collectors.toMap(InnerDeptMapDto::getInnerDeptId, b -> b,
                        (oldObj, newObj) -> {
                            Integer oldVirtualLevel = oldObj.getVirtualLevel();
                            Integer newVirtualLevel = newObj.getVirtualLevel();
                            return oldVirtualLevel.equals(Math.min(oldVirtualLevel, newVirtualLevel)) ? newObj : oldObj;
                        })
                );

        projectRevenueLedgerList.forEach(p -> {
            Long deptId = p.getBusinessSecondaryDeptId();
            if (Optional.ofNullable(deptId).isPresent()) {
                InnerDeptMapDto innerDeptMapDto = innerDeptMap.get(deptId);
                if (Optional.ofNullable(innerDeptMapDto).isPresent()) {
                    p.setVirtualDeptName(innerDeptMapDto.getVirtualDeptName());
                    p.setVirtualDeptId(innerDeptMapDto.getVirtualDeptId());
                }
            }
        });
        List<ProjectRevenueLedgerDetail> detailList = projectRevenueLedgerDetailMapper.selectList(Wrappers.<ProjectRevenueLedgerDetail>lambdaQuery());
        Map<Long, List<ProjectRevenueLedgerDetail>> detailMap = detailList.stream().collect(Collectors.groupingBy(ProjectRevenueLedgerDetail::getRequestId));


        //获取所有发薪主体
        List<String> salaryPaidSubjectList = projectRevenueLedgerList.stream().map(ProjectRevenueLedgerPushVO::getInvoicingSubject).distinct().collect(Collectors.toList());
        //根据发薪主体分组
        Map<String, List<ProjectRevenueLedgerPushVO>> listMap = projectRevenueLedgerList.stream().collect(Collectors.groupingBy(ProjectRevenueLedgerPushVO::getInvoicingSubject));

        //获取帐套信息,key:发薪主体，value:帐套信息
        Map<String, AccountingSet> accountingSetMap = accountingSetService.getAccountingSet();

        //查询组织第三方ID映射关系
        Map<Long, String> tPlusMap = remoteOutMultiDeptService.getThirdDeptRelation(deptCatName, DeptAssociationEnum.T_PLUS).getData();

        //获取客户信息,key:客户id，value:客户信息
        List<CustomerAccountVO> customerAccountList = dbApi.getCustomerAccount(CharSequenceUtil.EMPTY);
        Map<Long, CustomerAccountVO> tCustomerAccountMap = customerAccountList.stream().collect(Collectors.toMap(CustomerAccountVO::getCustomerId, a -> a, (a, b) -> a));

        salaryPaidSubjectList.forEach(s -> {
            List<ProjectRevenueLedgerPushVO> revenueLedgerPushVOList = listMap.get(s);
            //帐套信息
            AccountingSet accountingSet = accountingSetMap.get(s);
            if (Optional.ofNullable(accountingSet).isPresent()) {
                //获取登录T+token
                String token = openApi.login(accountingSet.getAccountingAccount(), accountingSet.getAccountingPassword(), accountingSet.getAccountingCode());
                revenueLedgerPushVOList.forEach(p -> {
                    //凭证实体数据组装
                    VoucherDTO.VoucherDTOBuilder voucherDtoBuilder = VoucherDTO.builder();
                    //制单日期为归属月份的最后一日天
                    LocalDate date = LocalDate.parse(p.getIncomeDate().replace("-", ""), DateTimeFormatter.BASIC_ISO_DATE);
                    LocalDate lastDay = date.with(TemporalAdjusters.lastDayOfMonth());
                    voucherDtoBuilder
                            .ExternalCode(String.valueOf(p.getId()))
                            .VoucherDate(lastDay.toString())
                            .DocType(VoucherDocTypeDTO.builder().Code(VoucherRecordEnum.DOCTYPE.getName()).build());

                    //核算辅助项
                    AuxiliaryDTO.AuxiliaryDTOBuilder drAuxiliaryDto = AuxiliaryDTO.builder()
                            .AuxAccProject(AuxAccProjectDTO.builder().Code(p.getProjectNo()).build())
                            .AuxAccDepartment(AuxAccDepartmentDTO.builder().Code(tPlusMap.getOrDefault(p.getVirtualDeptId(), StrUtil.EMPTY)).build());


                    //贷方凭证辅佐项集合(项目、部门)
                    List<AuxiliaryDTO> crAuxiliary = new ArrayList<>();
                    crAuxiliary.add(drAuxiliaryDto.build());

                    CustomerAccountVO tCustomer = tCustomerAccountMap.get(p.getCustomerId());
                    if (Optional.ofNullable(tCustomer).isPresent()) {
                        drAuxiliaryDto.AuxAccCustomer(AuxAccCustomerDTO.builder().Code(tCustomer.getCustomerCode()).build());
                    } else {
                        //如果客户名称不存在。来往单位  默认-厦门硕翔计算机技术有限公司
                        drAuxiliaryDto.AuxAccCustomer(AuxAccCustomerDTO.builder().Code("CW-************").build());
                    }
                    //借方凭证辅佐项集合(项目、往来单位、部门)
                    List<AuxiliaryDTO> drAuxiliary = new ArrayList<>();
                    drAuxiliary.add(drAuxiliaryDto.build());
                    //凭证明细
                    ArrayList<VoucherDetailDTO> detailDtoArrayList = new ArrayList<>();

                    //借方凭证-合同结算-价款结算 科目 部门+往来单位+项目
                    // 结转收入*@【收入年月】*@【客户名称】*@【项目名称】
                    VoucherDetailDTO drVoucher = VoucherDetailDTO.builder()
                            .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                            .Account(EntryAccountDTO.builder().Code(VoucherSubjectIdEnum.PRICE_SETTLEMENT.getName()).build())
                            .AmountDr(p.getIncomeAmount())
                            .Summary(VoucherRecordEnum.LEDGER_STR.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getIncomeDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getCustomerName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getProjectName())
                            .AuxInfos(drAuxiliary)
                            .build();
                    detailDtoArrayList.add(drVoucher);

                    List<ProjectRevenueLedgerDetail> details = detailMap.get(p.getId());
                    if (CollUtil.isNotEmpty(details)) {
                        details.forEach(d -> {
                            //贷方凭证-主营业务收入-综合服务 科目 部门+项目
                            VoucherDetailDTO crVoucher = voucher(EnumUtils.getNameByValue(IncomeTypeEnum.class, d.getIncomeType()), d, p, crAuxiliary);
                            detailDtoArrayList.add(crVoucher);
                        });
                    }


                    VoucherDTO voucher = voucherDtoBuilder.Entrys(detailDtoArrayList).build();
                    //凭证创建
                    try {
                        String result = openApi.createVoucher(token, voucher);
                        ProjectRevenueLedger projectRevenueLedger = BeanUtil.copyProperties(p, ProjectRevenueLedger.class);
                        projectRevenueLedger.setUpdateBy(UserUtils.getUser().getUsername());
                        projectRevenueLedger.setUpdateTime(LocalDateTime.now());
                        if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                            projectRevenueLedger.setPushStatus(PushStatusEnum.SUCCESS_PUSH.getValue());
                            baseMapper.updatePushStatus(projectRevenueLedger);
                            succeed.incrementAndGet();
                            // 记录日志
                            sb.append("【").append(p.getProcessCoding()).append("】【").append(PushStatusEnum.SUCCESS_PUSH.getName()).append("】");
                        } else {
                            projectRevenueLedger.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                            baseMapper.updatePushStatus(projectRevenueLedger);
                            error.incrementAndGet();
                            errorReasonList.add(PushErrorReasonVO.builder()
                                    .id(String.valueOf(projectRevenueLedger.getId()))
                                    .errorReason(result)
                                    .build());
                            // 记录日志
                            sb.append("【").append(p.getProcessCoding()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                        }
                    } catch (Exception e) {
                        throw new BusinessException("凭证创建失败");
                    }
                });
            } else {
                revenueLedgerPushVOList.stream().forEach(p -> {
                    ProjectRevenueLedger projectRevenueLedger = BeanUtil.copyProperties(p, ProjectRevenueLedger.class);
                    projectRevenueLedger.setPushStatus(PushStatusEnum.FAIL_PUSH.getValue());
                    baseMapper.updateById(projectRevenueLedger);
                    errorReasonList.add(PushErrorReasonVO.builder()
                            .id(String.valueOf(projectRevenueLedger.getId()))
                            .errorReason(VoucherRecordEnum.ACCOUNTING_ERROR.getName())
                            .build());
                    // 记录日志
                    sb.append("【").append(p.getProcessCoding()).append("】【").append(PushStatusEnum.FAIL_PUSH.getName()).append("】");
                    error.incrementAndGet();
                });
            }
        });
        // 日志记录
        if (sb.length() > NumberUtils.INTEGER_ZERO) {
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_PAYMENT,
                    OperationEnum.PUSH, OperationEnum.PUSH.getName() + sb.toString());
        }

        return PushResultVO.builder()
                .success(succeed.get())
                .error(error.get())
                .errorReasonList(errorReasonList).build();
    }

    private VoucherDetailDTO voucher(String account,
                                     ProjectRevenueLedgerDetail d,
                                     ProjectRevenueLedgerPushVO p,
                                     List<AuxiliaryDTO> crAuxiliary) {
        return VoucherDetailDTO.builder()
                .Currency(EntryCurrencyDTO.builder().Code(VoucherRecordEnum.CURRENCY.getName()).build())
                .Account(EntryAccountDTO.builder().Code(account).build())
                .AmountCr(d.getIncomeDetailAmount().toString())
                .Summary(VoucherRecordEnum.LEDGER_STR.getName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getIncomeDate() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getCustomerName() + VoucherRecordEnum.PAYMENT_SUMMARY.getName() + p.getProjectName())
                .AuxInfos(crAuxiliary)
                .build();
    }

    /**
     * 封装分页展示与导出
     *
     * @param projectRevenueLedgerList {@link List}<{@link ProjectRevenueLedger}>
     * @param detailList               {@link Map}<{@link Long}, {@link List}<{@link ProjectRevenueLedgerDetail}>>
     * @return {@link List}<{@link ProjectRevenueLedgerVO}>
     */
    private List<ProjectRevenueLedgerVO> addVoToList(List<ProjectRevenueLedger2VO> projectRevenueLedgerList,
                                                     Map<Long, List<ProjectRevenueLedgerDetail>> detailList) {
        // 获取中台的所有行政部门信息
        List<InnerDeptMapDto> innerDeptMapDtoList = remoteOutMultiDeptService.getInnerDeptMapDto(deptCatName).getData();
        // 行政部门id作为key 虚拟部门名称作为value
        Map<Long, String> innerDeptMap = innerDeptMapDtoList.stream().filter(i -> i.getVirtualLevel().equals(NumberUtils.INTEGER_TWO))
                .collect(Collectors.toMap(InnerDeptMapDto::getInnerDeptId, InnerDeptMapDto::getVirtualDeptName, (a, b) -> a));

        List<ProjectRevenueLedgerVO> list = new ArrayList<>();
        projectRevenueLedgerList.forEach(p -> {
            ProjectRevenueLedgerVO vo = BeanUtil.copyProperties(p, ProjectRevenueLedgerVO.class);
            vo.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
            vo.setClosingStartConditionsTxt(EnumUtils.getNameByValue(ClosingStartConditionsEnum.class, p.getClosingStartConditions()));
            vo.setBusinessDirectionTxt(EnumUtils.getNameByValue(BusinessDirectionEnum.class, p.getBusinessDirection()));
            vo.setAcceptanceReportTxt("附件");
            vo.setIncomeAmount(MoneyUtils.getInstance().transType(p.getIncomeAmount(), false));
            vo.setIncomeAmountTax(MoneyUtils.getInstance().transType(p.getIncomeAmountTax(), false));
            vo.setContractMoney(MoneyUtils.getInstance().transType(p.getContractMoney(), false));
            Long businessFirstDeptId = p.getBusinessFirstDeptId();
            if (Optional.ofNullable(businessFirstDeptId).isPresent()) {
                vo.setVirtualDeptName(innerDeptMap.get(businessFirstDeptId));
            }
            // v1.2.4 项目结项与收入确认流程新增
            List<ProjectRevenueLedgerDetail> details = detailList.get(p.getId());
            if (details != null && !details.isEmpty()) {
                vo.setIncomeTypeList(details.stream()
                        .map(ProjectRevenueLedgerDetail::getIncomeType)
                        .collect(Collectors.toList())
                );
                vo.setIncomeDetailAmountList(details.stream()
                        .map(d -> MoneyUtils.getInstance().transType(d.getIncomeDetailAmount()))
                        .collect(Collectors.toList())
                );
                vo.setIncomeDetailAmountTaxList(details.stream()
                        .map(d -> MoneyUtils.getInstance().transType(d.getIncomeDetailAmountTax()))
                        .collect(Collectors.toList())
                );
                vo.setTaxTateList(details.stream()
                        .map(tax -> {
                            if (MoneyUtils.ZERO.equals(tax.getTaxTate())) {
                                return MoneyUtils.TYPE;
                            }
                            return tax.getTaxTate();
                        })
                        .collect(Collectors.toList())
                );
            }
            list.add(vo);
        });
        return list;
    }

    /**
     * 导出
     *
     * @param projectRevenueLedgerList {@link List}<{@link ProjectRevenueLedger}>
     * @param detailList               {@link Map}<{@link Long}, {@link List}<{@link ProjectRevenueLedgerDetail}>>
     * @return {@link List}<{@link ProjectRevenueLedgerExcelVO}>
     */
    private List<ProjectRevenueLedgerExcelVO> exportVO(List<ProjectRevenueLedger> projectRevenueLedgerList,
                                                       Map<Long, List<ProjectRevenueLedgerDetail>> detailList) {
        List<ProjectRevenueLedgerExcelVO> voList = new ArrayList<>();
        projectRevenueLedgerList.forEach(p -> {
            ProjectRevenueLedgerExcelVO vo = BeanUtil.copyProperties(p, ProjectRevenueLedgerExcelVO.class, "incomeDetailAmountTax");
            // 项目收入结项
            List<ProjectRevenueLedgerDetail> details = detailList.get(p.getId());
            if (details != null && !details.isEmpty()) {
                details.forEach(d -> {
                    // 枚举处理
                    vo.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
                    vo.setClosingStartConditionsTxt(EnumUtils.getNameByValue(ClosingStartConditionsEnum.class, p.getClosingStartConditions()));
                    vo.setBusinessDirectionTxt(EnumUtils.getNameByValue(BusinessDirectionEnum.class, p.getBusinessDirection()));
                    vo.setAcceptanceReportTxt("附件");
                    // 金额处理
                    vo.setIncomeAmount(MoneyUtils.getInstance().transType(p.getIncomeAmount()));
                    vo.setIncomeAmountTax(MoneyUtils.getInstance().transType(p.getIncomeAmountTax()));
                    vo.setContractMoney(MoneyUtils.getInstance().transType(p.getContractMoney()));
                    // 结项信息
                    vo.setIncomeDetailAmountList(MoneyUtils.getInstance().transType(d.getIncomeDetailAmountTax()));
                    vo.setIncomeTypeList(d.getIncomeType());
                    vo.setTaxTateList(d.getTaxTate());
                    voList.add(vo);
                });
            } else {
                // 枚举处理
                vo.setPushStatusTxt(EnumUtils.getNameByValue(PushStatusEnum.class, p.getPushStatus()));
                vo.setClosingStartConditionsTxt(EnumUtils.getNameByValue(ClosingStartConditionsEnum.class, p.getClosingStartConditions()));
                vo.setBusinessDirectionTxt(EnumUtils.getNameByValue(BusinessDirectionEnum.class, p.getBusinessDirection()));
                vo.setAcceptanceReportTxt("附件");
                // 金额处理
                vo.setIncomeAmount(MoneyUtils.getInstance().transType(p.getIncomeAmount()));
                vo.setIncomeAmountTax(MoneyUtils.getInstance().transType(p.getIncomeAmountTax()));
                vo.setContractMoney(MoneyUtils.getInstance().transType(p.getContractMoney()));
                voList.add(vo);
            }
        });
        return voList;
    }

}
