package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 凭证明细实体类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoucherDetailDTO {
    /**
     * 摘要
     */
    private String Summary;

    /**
     * 科目编号
     */
    private EntryAccountDTO Account;

    /**
     * 货币
     */
    private EntryCurrencyDTO Currency;

    /**
     * 贷方金额
     */
    private String AmountCr;

    /**
     * 借方金额
     */
    private String AmountDr;

    /**
     * 辅佐项集合
     */
    private List<AuxiliaryDTO> AuxInfos;
}
