package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.analysis.enums.YesOrNoEnum;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.OpenApi;
import com.gok.pboot.financial.db.entity.AccountType;
import com.gok.pboot.financial.db.entity.AccountingAccount;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.db.entity.CustomizeCalculate;
import com.gok.pboot.financial.db.mapper.*;
import com.gok.pboot.financial.dto.AccountingAccountDTO;
import com.gok.pboot.financial.dto.AccountingAccountImportDTO;
import com.gok.pboot.financial.dto.AccountingAccountPageDTO;
import com.gok.pboot.financial.dto.EducationPaymentExcelDTO;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAccountingAccountService;
import com.gok.pboot.financial.util.DateUtils;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.vo.AccountingAccountPageVO;
import com.gok.pboot.financial.vo.AccountingAccountSyncVO;
import com.gok.pboot.financial.vo.AccountingAccountVO;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 科目类型
 * <AUTHOR>
 */

@Service
@RequiredArgsConstructor
public class AccountingAccountServiceImpl extends ServiceImpl<AccountingAccountMapper, AccountingAccount>
        implements IAccountingAccountService {

    private final LogRecordUtils logRecordUtils;
    private final AccountingSystemMapper systemMapper;
    private final AccountTypeMapper accountTypeMapper;
    private final CustomizeCalculateMapper customizeCalculateMapper;
    private final AccountingSetMapper accountingSetMapper;
    private final OpenApi openApi;
    private static final String CHANGE = "】变更为【";

    @Override
    public Page<AccountingAccountPageVO> findPage(AccountingAccountPageDTO dto) {
        // 1、获取分页信息
        Page<AccountingAccount> page = new Page<>(dto.getCurrent(), dto.getSize());
        Page<AccountingAccountPageVO> accountingAccountPage = baseMapper.findPage(page,dto);
        // 2、封装分页信息
        if (ObjectUtils.isNotEmpty(accountingAccountPage.getRecords())) {
            accountingAccountPage.getRecords().forEach(r -> {
                r.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, r.getEnableStatus()));
                r.setAccountPageFormatTxt(EnumUtils.getNameByValue(AccountPageFormatEnum.class, r.getAccountPageFormat()));
                r.setBalanceDirectionTxt(EnumUtils.getNameByValue(BalanceDirectionEnum.class, r.getBalanceDirection()));
                r.setCashClassificationTxt(EnumUtils.getNameByValue(CashClassificationEnum.class, r.getCashClassification()));
                if (Optional.ofNullable(r.getAuxiliaryCalculateItems()).isPresent() && !StrUtil.EMPTY.equals(r.getAuxiliaryCalculateItems())) {
                    String auxiliaryCalculateItems = Arrays.stream(r.getAuxiliaryCalculateItems().split(","))
                            .collect(Collectors.toList())
                            .stream()
                            .collect(Collectors.joining(","));
                    r.setAuxiliaryCalculateItems(auxiliaryCalculateItems);
                }
            });
        }
        return accountingAccountPage;
    }

    @Override
    public R<AccountingAccountVO> info(Long id) {
        AccountingAccountVO vo = baseMapper.selById(id);
        if(!Optional.ofNullable(vo).isPresent()){
            return R.failed("查询不到数据");
        }
        beanToVo(vo);
        return R.ok(vo);
    }

    /**
     * 添加或编辑
     *
     * @param dto DTO
     * @return {@link R }<{@link String }>
     */
    @Override
    public R<String> addOrEdit(AccountingAccountDTO dto) {

        boolean exists = lambdaQuery().eq(AccountingAccount::getAccountCode, dto.getAccountCode())
                .eq(AccountingAccount::getAccountingSystemId, dto.getAccountingSystemId())
                .ne(dto.getId() != null, AccountingAccount::getId, dto.getId())
                .exists();

        String auxiliaryCalculateItems = StrUtil.EMPTY;
        if (CollectionUtil.isNotEmpty(dto.getAuxiliaryCalculateItems())) {
            auxiliaryCalculateItems = String.join(StrPool.COMMA, dto.getAuxiliaryCalculateItems());
        }
        String customizeCalculateItems = StrUtil.EMPTY;
        if (CollectionUtil.isNotEmpty(dto.getCustomizeCalculateItems())) {
            customizeCalculateItems = dto.getCustomizeCalculateItems()
                    .stream().map(Object::toString)
                    .collect(Collectors.joining(StrPool.COMMA));
        }
        if (exists) {
            return R.failed("科目编码不可重复");
        }
        AccountingAccount as = getById(dto.getId());
        if (Optional.ofNullable(as).isPresent()) {

            // 2.1、记录日志
            final String s = OperationEnum.EDIT_ACCOUNTING_ACCOUNT.getName() + "【"
                    + dto.getAccountCode() + "】【" + dto.getAccountName() + "】";
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_ACCOUNT,
                    OperationEnum.EDIT_ACCOUNTING_ACCOUNT, s);
            // 2.2、更新数据
            AccountingAccount accountingAccount = BeanUtil.copyProperties(dto, AccountingAccount.class);
            accountingAccount.setAuxiliaryCalculateItems(auxiliaryCalculateItems);
            accountingAccount.setCustomizeCalculateItems(customizeCalculateItems);
            this.updateById(accountingAccount);
            return R.ok("编辑成功");
        }

        // 3、新增操作
        AccountingAccount accountingAccount = BeanUtil.copyProperties(dto, AccountingAccount.class, "id");
        accountingAccount.setAuxiliaryCalculateItems(auxiliaryCalculateItems);
        accountingAccount.setCustomizeCalculateItems(customizeCalculateItems);
        // 3.1、记录日志
        final String s = OperationEnum.ADD_ACCOUNTING_ACCOUNT.getName() + "【"
                + dto.getAccountCode() + "】【" + dto.getAccountName() + "】";
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_ACCOUNT,
                OperationEnum.ADD_ACCOUNTING_ACCOUNT, s);
        // 3.2、入库
        this.save(accountingAccount);
        return R.ok("新增成功");
    }

    @Override
    public Boolean del(List<Long> idList) {
        // 1、防止空数组删除全数据
        if (ObjectUtils.isEmpty(idList)) {
            throw new BusinessException("删除接口传参有误");
        }
        // 2、查询数据记录日志
        StringBuilder s = new StringBuilder();
        s.append(OperationEnum.DELETE_ACCOUNTING_ACCOUNT.getName());
        baseMapper.selectList(Wrappers.<AccountingAccount>lambdaQuery().in(AccountingAccount::getId, idList)
                .eq(AccountingAccount::getEnableStatus, NumberUtils.INTEGER_ONE))
                .forEach(a -> s.append("【").append(a.getAccountCode()).append(a.getAccountName()).append("】"));
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_ACCOUNT,
                OperationEnum.DELETE_ACCOUNTING_ACCOUNT, s.toString());
        // 3、删除数据
        return baseMapper.delByIds(idList);
    }

    @Override
    public List<AccountingAccountPageVO> exportExcel(AccountingAccountPageDTO dto) {
        List<AccountingAccountPageVO> accountingAccountList = new ArrayList<>();
        // 导出模板表
        if (CollUtil.isNotEmpty(dto.getIdList())) {
            if (NumberUtils.LONG_MINUS_ONE.equals(dto.getIdList().get(NumberUtils.INTEGER_ZERO))) {
                AccountingAccountPageVO accountingAccountVo = AccountingAccountPageVO.builder()
                        .build();
                accountingAccountList.add(accountingAccountVo);
                return accountingAccountList;
            }
        }
        // 1、查询数据
        accountingAccountList = baseMapper.findPage(dto);
        // 2、返回数据
        if (ObjectUtils.isNotEmpty(accountingAccountList)) {
            accountingAccountList.forEach(r -> {
                r.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, r.getEnableStatus()));
                r.setAccountPageFormatTxt(EnumUtils.getNameByValue(AccountPageFormatEnum.class, r.getAccountPageFormat()));
                r.setBalanceDirectionTxt(EnumUtils.getNameByValue(BalanceDirectionEnum.class, r.getBalanceDirection()));
                r.setCashClassificationTxt(EnumUtils.getNameByValue(CashClassificationEnum.class, r.getCashClassification()));
                if (Optional.ofNullable(r.getAuxiliaryCalculateItems()).isPresent() && !StrUtil.EMPTY.equals(r.getAuxiliaryCalculateItems())) {
                    String auxiliaryCalculateItems = Arrays.stream(r.getAuxiliaryCalculateItems().split(","))
                            .collect(Collectors.toList())
                            .stream()
                            .collect(Collectors.joining(","));
                    r.setAuxiliaryCalculateItems(auxiliaryCalculateItems);
                }
            });
        }
        return accountingAccountList;
    }

    @Override
    public Boolean enable(AccountingAccountDTO dto) {
        List<Long> allIdList = new ArrayList<>();
        //获取所有科目 包含下级科目
        allIdList = getJuniorIdList(dto.getIdList(), allIdList, dto.getEnableStatusFlag());
        // 1、获取数据
        allIdList.addAll(dto.getIdList());
        List<AccountingAccount> accountingAccountList = baseMapper.selectList(Wrappers.<AccountingAccount>lambdaQuery()
                .in(AccountingAccount::getId, allIdList));

        if (!accountingAccountList.isEmpty()) {
            // 1、启用状态 前者用来修改信息 后者用来记录日志
            Integer enableStatus = EnableStatusEnum.NO.getValue();
            String enableStatusTxt = EnableStatusEnum.YES.getName();
            if (Boolean.TRUE.equals(dto.getEnableStatusFlag())) {
                enableStatus = EnableStatusEnum.YES.getValue();
                enableStatusTxt = EnableStatusEnum.NO.getName();
            }
            // 2、记录日志
            StringBuilder s = new StringBuilder();
            s.append("【").append(enableStatusTxt).append(CHANGE)
                    .append(EnumUtils.getNameByValue(EnableStatusEnum.class, enableStatus))
                    .append("】: ");
            for (AccountingAccount a : accountingAccountList) {
                if (!enableStatus.equals(a.getEnableStatus())) {
                    a.setEnableStatus(enableStatus);
                    s.append("【").append(a.getAccountCode()).append(a.getAccountName()).append("】");
                }
            }
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_ACCOUNT, OperationEnum.EDIT_STATUS, s.toString());
            // 3、更新数据库
            return this.updateBatchById(accountingAccountList);
        }
        return true;
    }

    @Override
    public List<Tree<Long>> accountTree() {
        List<AccountingAccount> data = baseMapper.selectList(new QueryWrapper<>());
        List<TreeNode<Long>> deptTree = data.stream().map(account -> {
            TreeNode<Long> node = new TreeNode<>();
            node.setId(account.getId());
            node.setName(account.getAccountName());
            node.setParentId(account.getParentId());
            node.setWeight(0);
            // 扩展属性
            Map<String, Object> extra = new HashMap<>(16);
            extra.put("sortOrder", 0);
            extra.put("isDept", true);
            extra.put("accountCode",account.getAccountCode());
            node.setExtra(extra);
            return node;
        }).collect(Collectors.toList());

        return TreeUtil.build(deptTree, NumberUtils.LONG_ZERO);
    }

    @Override
    public R<String> sync() {
        String token = openApi.login("208", "gok208", "102");
        List<AccountingAccountSyncVO> syncVOList = openApi.accountQuery(token);

        if (CollectionUtil.isEmpty(syncVOList)){
            return R.failed("无数据同步");
        }
        Map<Integer, AccountingAccountSyncVO> syncVOByIdMap = syncVOList.stream().collect(Collectors.toMap(AccountingAccountSyncVO::getId, a -> a, (a, b) -> a));
        List<AccountingAccount> accountList = baseMapper.selectList(new QueryWrapper<>());
        Map<String, AccountingAccount> accountByCodeMap = accountList.stream()
                .collect(Collectors.toMap(AccountingAccount::getAccountCode, a -> a, (a, b) -> a));
        QueryWrapper<AccountingSystem> accountingAccountQueryWrapper = new QueryWrapper<>();
        AccountingSystem system = systemMapper.selectList(accountingAccountQueryWrapper.lambda().orderByAsc(AccountingSystem::getCreateTime))
                .get(NumberUtils.INTEGER_ZERO);
        Map<String, AccountType> typeByNameMap = accountTypeMapper.selectList(new QueryWrapper<>()).stream()
                .collect(Collectors.toMap(AccountType::getTypeName, a -> a, (a, b) -> a));
        List<CustomizeCalculate> customizeList = customizeCalculateMapper.selectList(new QueryWrapper<>());

        List<AccountingAccount> accountingAccountList = syncVOList.stream().map(sync -> {
            AccountingAccount accountingAccount = new AccountingAccount();
            accountingAccount.setId(sync.getId().longValue());
            AccountingAccount as = accountByCodeMap.getOrDefault(sync.getCode(), new AccountingAccount());
            if (Optional.ofNullable(as).isPresent()) {
                accountingAccount.setId(as.getId());
            }
            accountingAccount.setAccountCode(sync.getCode());
            accountingAccount.setAccountName(sync.getName());
            accountingAccount.setMnemonicWords(sync.getShorthand());
            AccountType accountType = typeByNameMap.getOrDefault(sync.getAccountTypeDTO_Name(), null);
            accountingAccount.setAccountTypeId(Optional.ofNullable(accountType).isPresent()?accountType.getId():null);
            accountingAccount.setEnableStatus(sync.getDisabled());
            accountingAccount.setBalanceDirection(EnumUtils.getValueByName(BalanceDirectionEnum.class, sync.getDCDirection_Name()));
            accountingAccount.setCashClassification(NumberUtils.INTEGER_ONE.equals(sync.getIscash()) ? NumberUtils.INTEGER_ZERO
                    : NumberUtils.INTEGER_ONE.equals(sync.getIsbank()) ? NumberUtils.INTEGER_ONE
                    : NumberUtils.INTEGER_ONE.equals(sync.getIscashequivalents()) ? NumberUtils.INTEGER_TWO : null);
            accountingAccount.setAccountPageFormat(NumberUtils.INTEGER_ZERO);
            AccountingAccountSyncVO parentSyncVO = syncVOByIdMap.getOrDefault(sync.getIdParent(), null);
            AccountingAccount parentAccount = accountByCodeMap.getOrDefault(Optional.ofNullable(parentSyncVO).isPresent() ?parentSyncVO.getCode():StrUtil.EMPTY, null);
            accountingAccount.setParentId(Optional.ofNullable(parentAccount).isPresent() ? parentAccount.getId():Optional.ofNullable(sync.getIdParent()).isPresent()?sync.getIdParent().longValue():NumberUtils.LONG_ZERO);
            accountingAccount.setAccountingSystemId(system.getId());
            accountingAccount.setLevel(sync.getCode().length()/2 - 1);
            StringBuilder auxiliaryItems = new StringBuilder();
            syncAuxiliaryItems(sync,auxiliaryItems);
            accountingAccount.setAuxiliaryCalculateItems(auxiliaryItems.toString());
            StringBuilder customizeItems = new StringBuilder();
            syncCustomizeCalculate(sync,customizeList,customizeItems);
            accountingAccount.setCustomizeCalculateItems(customizeItems.toString());

            return accountingAccount;
        }).collect(Collectors.toList());

        return this.saveOrUpdateBatch(accountingAccountList) ? R.ok("同步成功") : R.failed("同步失败");
    }



    @Override
    public R<String> importExcel(List<AccountingAccountImportDTO> importDTOList) {
        // 遍历封装后插入数据库
        List<AccountingAccount> accountingAccountList = new ArrayList<>();
        if (CollUtil.isEmpty(importDTOList)) {
            return R.ok("无数据导入");
        }
        List<AccountType> accountTypeList = accountTypeMapper.selectList(new QueryWrapper<>());
        Table<Long, String, AccountType> accountTypeTable = HashBasedTable.create(accountTypeList.size(), Math.max(1, accountTypeList.size()));
        for (AccountType accountType : accountTypeMapper.selectList(new QueryWrapper<>())) {
            accountTypeTable.put(accountType.getAccountingSystemId(),accountType.getTypeName(),accountType);
        }

        Map<String, AccountingSystem> systemByNameMap = systemMapper.selectList(new QueryWrapper<>()).stream()
                .collect(Collectors.toMap(AccountingSystem::getSystemName, a -> a, (a, b) -> a));
        Map<String, CustomizeCalculate> customizeByNameMap = customizeCalculateMapper.selectList(new QueryWrapper<>()).stream()
                .collect(Collectors.toMap(CustomizeCalculate::getCode, a -> a, (a, b) -> a));
        StringBuilder error = new StringBuilder();
        AtomicInteger i = new AtomicInteger(2);
        // 校验
        importDTOList.forEach(importDTO ->{
            this.checkImport(importDTO,systemByNameMap, error, i.getAndIncrement());
        });
        if (error.toString().contains("不")) {
            return R.failed("导入失败,原因是" + error.toString());
        }

        importDTOList.forEach(p -> {
            List<AccountingAccount> accountList = baseMapper.selectList(new QueryWrapper<>());
            Map<String, AccountingAccount> accountByCodeMap = accountList.stream()
                    .collect(Collectors.toMap(AccountingAccount::getAccountCode, a -> a, (a, b) -> a));

            // 1、会计科目
            AccountingAccount accountingAccount = BeanUtil.copyProperties(p, AccountingAccount.class);
            AccountingAccount as = accountByCodeMap.getOrDefault(p.getAccountCode(), new AccountingAccount());
            if (Optional.ofNullable(as).isPresent()) {
                accountingAccount.setId(as.getId());
            }
            accountingAccount.setEnableStatus(EnumUtils.getValueByName(EnableStatusEnum.class, p.getEnableStatusTxt()));
            accountingAccount.setSuspensionDate(Optional.ofNullable(p.getSuspensionDate()).isPresent() ?
                    DateUtils.formatTime(p.getSuspensionDate()).replaceAll(StrPool.SLASH, StrPool.DASHED):null);
            accountingAccount.setBalanceDirection(EnumUtils.getValueByName(BalanceDirectionEnum.class, p.getBalanceDirectionTxt()));
            accountingAccount.setCashClassification(EnumUtils.getValueByName(CashClassificationEnum.class, p.getCashClassificationTxt()));
            accountingAccount.setAccountPageFormat(EnumUtils.getValueByName(AccountPageFormatEnum.class, p.getAccountPageFormatTxt()));
            AccountingAccount parentAccount = accountByCodeMap.get(p.getAccountCode().substring(0,p.getAccountCode().length()-NumberUtils.INTEGER_TWO));
            accountingAccount.setParentId(Optional.ofNullable(parentAccount).isPresent()?parentAccount.getId():NumberUtils.LONG_ZERO);
            AccountingSystem accountingSystem = systemByNameMap.getOrDefault(p.getAccountingSystemTxt(),null);
            accountingAccount.setAccountingSystemId(Optional.ofNullable(accountingSystem).isPresent()?accountingSystem.getId():null);
            AccountType accountType = accountTypeTable.get(accountingSystem.getId(),p.getAccountType());
            accountingAccount.setAccountTypeId(Optional.ofNullable(accountType).isPresent()?accountType.getId():null);
            accountingAccount.setLevel(p.getAccountCode().length()/2 - 1);
            StringBuilder auxiliaryItems = new StringBuilder();
            auxiliaryItems(p,auxiliaryItems);
            accountingAccount.setAuxiliaryCalculateItems(auxiliaryItems.toString());
            StringBuilder customizeItems = new StringBuilder();
            customizeCalculate(p,customizeByNameMap,customizeItems);
            accountingAccount.setCustomizeCalculateItems(customizeItems.toString());
            accountingAccountList.add(accountingAccount);
            this.saveOrUpdate(accountingAccount);
        });

        // 2、记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SYSTEM,
                OperationEnum.IMPORT,
                OperationEnum.IMPORT.getName() + "【" + accountingAccountList.size() + "条】");

        return R.ok("导入成功");

    }

    @Override
    public R<AccountingAccountVO> getInfoByDto(String code) {

        AccountingAccountVO vo = baseMapper.getInfoByDto(code);
        if(!Optional.ofNullable(vo).isPresent()){
            return R.ok(new AccountingAccountVO());
        }
        beanToVo(vo);
        return R.ok(vo);
    }

    private void auxiliaryItems(AccountingAccountImportDTO p,StringBuilder auxiliaryItems){
        if (YesOrNoEnum.yes.getName().equals(p.getDeptCalculate())) {
            auxiliaryItems.append("部门,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getStaffCalculate())) {
            auxiliaryItems.append("职员,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getCustomerCalculate())) {
            auxiliaryItems.append("客户,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getSupplierCalculate())) {
            auxiliaryItems.append("供应商,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getContactUnitsCalculate())) {
            auxiliaryItems.append("往来单位,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getProjectCalculate())) {
            auxiliaryItems.append("项目,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getFundAccountCalculate())) {
            auxiliaryItems.append("资金账户,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getInventoryCalculate())) {
            auxiliaryItems.append("存货,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getMeteringCalculate())) {
            auxiliaryItems.append("计量单位,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getCashFlowCalculate())) {
            auxiliaryItems.append("现金流量,");
        }
        if (YesOrNoEnum.yes.getName().equals(p.getCostCenterCalculate())) {
            auxiliaryItems.append("成本中心");
        }
    }


    private void customizeCalculate(AccountingAccountImportDTO p, Map<String, CustomizeCalculate> customizeByNameMap, StringBuilder customizeItems) {
        CustomizeCalculate customizeCalculateOne = customizeByNameMap.getOrDefault(p.getCustomizeCalculateOne(), null);
        if (Optional.ofNullable(customizeCalculateOne).isPresent()) {
            customizeItems.append(customizeCalculateOne.getId() + ",");
        }
        CustomizeCalculate customizeCalculateTwo = customizeByNameMap.getOrDefault(p.getCustomizeCalculateTwo(), null);
        if (Optional.ofNullable(customizeCalculateTwo).isPresent()) {
            customizeItems.append(customizeCalculateTwo.getId() + ",");
        }
        CustomizeCalculate customizeCalculateThree = customizeByNameMap.getOrDefault(p.getCustomizeCalculateThree(), null);
        if (Optional.ofNullable(customizeCalculateThree).isPresent()) {
            customizeItems.append(customizeCalculateThree.getId() + ",");
        }
        CustomizeCalculate customizeCalculateFour = customizeByNameMap.getOrDefault(p.getCustomizeCalculateFour(), null);
        if (Optional.ofNullable(customizeCalculateFour).isPresent()) {
            customizeItems.append(customizeCalculateFour.getId() + ",");
        }
        CustomizeCalculate customizeCalculateFive = customizeByNameMap.getOrDefault(p.getCustomizeCalculateFive(), null);
        if (Optional.ofNullable(customizeCalculateFive).isPresent()) {
            customizeItems.append(customizeCalculateFive.getId() + ",");
        }
        CustomizeCalculate customizeCalculateSix = customizeByNameMap.getOrDefault(p.getCustomizeCalculateSix(), null);
        if (Optional.ofNullable(customizeCalculateSix).isPresent()) {
            customizeItems.append(customizeCalculateSix.getId() + ",");
        }
        CustomizeCalculate customizeCalculateSeven = customizeByNameMap.getOrDefault(p.getCustomizeCalculateSeven(), null);
        if (Optional.ofNullable(customizeCalculateSeven).isPresent()) {
            customizeItems.append(customizeCalculateSeven.getId() + ",");
        }
        CustomizeCalculate customizeCalculateEight = customizeByNameMap.getOrDefault(p.getCustomizeCalculateEight(), null);
        if (Optional.ofNullable(customizeCalculateEight).isPresent()) {
            customizeItems.append(customizeCalculateEight.getId() + ",");
        }
        CustomizeCalculate customizeCalculateNine = customizeByNameMap.getOrDefault(p.getCustomizeCalculateNine(), null);
        if (Optional.ofNullable(customizeCalculateNine).isPresent()) {
            customizeItems.append(customizeCalculateNine.getId() + ",");
        }
    }

    /**
     * 校验导入参数
     *
     * @param dto {@link EducationPaymentExcelDTO}
     * @param errorAll {@link StringBuilder}
     * @param row 行数
     */
    private void checkImport(AccountingAccountImportDTO dto,Map<String, AccountingSystem> systemByNameMap,
                             StringBuilder errorAll, Integer row) {
        StringBuilder error = new StringBuilder();
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getAccountCode())) {
            error.append("科目编码不能为空!");
        }else if (12<dto.getAccountCode().length() || 4>dto.getAccountCode().length() || dto.getAccountCode().length()%2 !=0){
            error.append("科目编码不符合编码规则!");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountName())) {
            error.append("科目名称不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getBalanceDirectionTxt())) {
            error.append("余额方向不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountingSystemTxt())) {
            error.append("会计体系不能为空!");
        }else if (!Optional.ofNullable(systemByNameMap.getOrDefault(dto.getAccountingSystemTxt(),null)).isPresent()){
            error.append("会计体系不存在!");
        }
        if (CharSequenceUtil.isBlank(dto.getCashClassificationTxt())) {
            error.append("现金分类不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountPageFormatTxt())) {
            error.append("账页格式不能为空!");
        }
        if (CharSequenceUtil.isBlank(dto.getEnableStatusTxt())) {
            error.append("停用状态不能为空!");
        }

        if (error.toString().contains("不")){
            errorAll.append(error);
        }
    }


    public void beanToVo(AccountingAccountVO vo){
        vo.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, vo.getEnableStatus()));
        vo.setAccountPageFormatTxt(EnumUtils.getNameByValue(AccountPageFormatEnum.class, vo.getAccountPageFormat()));
        vo.setBalanceDirectionTxt(EnumUtils.getNameByValue(BalanceDirectionEnum.class, vo.getBalanceDirection()));
        vo.setCashClassificationTxt(EnumUtils.getNameByValue(CashClassificationEnum.class, vo.getCashClassification()));
        if (Optional.ofNullable(vo.getAuxiliaryCalculateItems()).isPresent() && !StrUtil.EMPTY.equals(vo.getAuxiliaryCalculateItems())) {
            List<String> auxiliaryCalculateItemList = Arrays.stream(vo.getAuxiliaryCalculateItems().split(","))
                    .collect(Collectors.toList());
            vo.setAuxiliaryCalculateItemList(auxiliaryCalculateItemList);
        }
        if (Optional.ofNullable(vo.getCustomizeCalculateItems()).isPresent() && !StrUtil.EMPTY.equals(vo.getCustomizeCalculateItems())) {
            List<Long> customizeCalculateItems = Arrays.stream(vo.getCustomizeCalculateItems().split(","))
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            vo.setCustomizeCalculateItemList(customizeCalculateItems);
        }
    }


    private void syncAuxiliaryItems(AccountingAccountSyncVO p,StringBuilder auxiliaryItems){
        if (YesOrNoEnum.no.getValue().equals(p.getIsauxaccdepartment())) {
            auxiliaryItems.append("部门,");
        }
        if (YesOrNoEnum.no.getValue().equals(p.getIsauxaccperson())) {
            auxiliaryItems.append("职员,");
        }
        if (YesOrNoEnum.no.getValue().equals(p.getIsauxacccustomer())) {
            auxiliaryItems.append("往来单位,");
        }
        if (YesOrNoEnum.no.getValue().equals(p.getIsauxaccproject())) {
            auxiliaryItems.append("项目,");
        }
        if (YesOrNoEnum.no.getValue().equals(p.getIsauxaccinventory())) {
            auxiliaryItems.append("存货,");
        }
    }

    private void syncCustomizeCalculate(AccountingAccountSyncVO sync, List<CustomizeCalculate> customizeList, StringBuilder customizeItems) {
        if (customizeList.size() > 1) {
            CustomizeCalculate customizeCalculateOne = customizeList.get(0);
            if (Optional.ofNullable(customizeCalculateOne).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc1())) {
                customizeItems.append(customizeCalculateOne.getId() + ",");
            }
        }
        if (customizeList.size() > 2) {
            CustomizeCalculate customizeCalculateTwo = customizeList.get(1);
            if (Optional.ofNullable(customizeCalculateTwo).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc2())) {
                customizeItems.append(customizeCalculateTwo.getId() + ",");
            }
        }
        if (customizeList.size() > 3) {
            CustomizeCalculate customizeCalculateThree = customizeList.get(2);
            if (Optional.ofNullable(customizeCalculateThree).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc3())) {
                customizeItems.append(customizeCalculateThree.getId() + ",");
            }
        }
        if (customizeList.size() > 4) {
            CustomizeCalculate customizeCalculateFour = customizeList.get(3);
            if (Optional.ofNullable(customizeCalculateFour).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc4())) {
                customizeItems.append(customizeCalculateFour.getId() + ",");
            }
        }
        if (customizeList.size() > 5) {
            CustomizeCalculate customizeCalculateFive = customizeList.get(4);
            if (Optional.ofNullable(customizeCalculateFive).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc5())) {
                customizeItems.append(customizeCalculateFive.getId() + ",");
            }
        }
        if (customizeList.size() > 6) {
            CustomizeCalculate customizeCalculateSix = customizeList.get(5);
            if (Optional.ofNullable(customizeCalculateSix).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc6())) {
                customizeItems.append(customizeCalculateSix.getId() + ",");
            }
        }
        if (customizeList.size() > 7) {
            CustomizeCalculate customizeCalculateSeven = customizeList.get(6);
            if (Optional.ofNullable(customizeCalculateSeven).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc7())) {
                customizeItems.append(customizeCalculateSeven.getId() + ",");
            }
        }
        if (customizeList.size() > 8) {
            CustomizeCalculate customizeCalculateEight = customizeList.get(7);
            if (Optional.ofNullable(customizeCalculateEight).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc8())) {
                customizeItems.append(customizeCalculateEight.getId() + ",");
            }
        }
        if (customizeList.size() > 9) {
            CustomizeCalculate customizeCalculateNine = customizeList.get(8);
            if (Optional.ofNullable(customizeCalculateNine).isPresent() && NumberUtils.INTEGER_ONE.equals(sync.getIsexauxacc9())) {
                customizeItems.append(customizeCalculateNine.getId() + ",");
            }
        }
    }

    private List<Long> getJuniorIdList(List<Long> idList,List<Long> allIdList,Boolean enableStatusFlag){
        // 1、获取数据
        List<AccountingAccount> accountingAccountList = baseMapper.selectList(Wrappers.<AccountingAccount>lambdaQuery()
                .in(AccountingAccount::getParentId, idList)
                .eq(AccountingAccount::getEnableStatus,enableStatusFlag?NumberUtils.INTEGER_ONE:NumberUtils.INTEGER_ZERO));
        if (CollectionUtil.isNotEmpty(accountingAccountList)) {
            List<Long> juniorIdList = accountingAccountList.stream().map(AccountingAccount::getId).collect(Collectors.toList());
            allIdList.addAll(juniorIdList);
            getJuniorIdList(juniorIdList,allIdList,enableStatusFlag);
        }
        return allIdList;
    }
}
