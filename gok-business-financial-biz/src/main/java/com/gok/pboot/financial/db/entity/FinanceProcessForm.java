package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 财务处理单
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("finance_process_form")
@ApiModel("财务处理单")
public class FinanceProcessForm extends Model<FinanceProcessForm> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;
    /**
     * 流程id
     */
    @ApiModelProperty("流程id")
    private Long requestid;
    /**
     * 报账编号
     */
    @ApiModelProperty("报账编号")
    private String billingNumber;  
    /**
     * 单据类型
     */
    @ApiModelProperty("单据类型")
    private Integer billType;  
    /**
     * 报账金额
     */
    @ApiModelProperty("报账金额")
    private BigDecimal billingAmount;  
    /**
     * 应付金额
     */
    @ApiModelProperty("应付金额")
    private BigDecimal payableAmount;  
    /**
     * 冲销金额
     */
    @ApiModelProperty("冲销金额")
    private BigDecimal offsetAmount;  
    /**
     * 申请日期
     */
    @ApiModelProperty("申请日期")
    private LocalDate applicationDate;
    /**
     * 申请人id
     */
    @ApiModelProperty("申请人id")
    private Long applicantId;  
    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applicantName;  
    /**
     * 申请人部门id
     */
    @ApiModelProperty("申请人部门id")
    private Long applicantDeptId;  
    /**
     * 申请人部门
     */
    @ApiModelProperty("申请人部门")
    private String applicantDeptName;  
    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String belongCompany;
    /**
     * 申请事由
     */
    @ApiModelProperty("申请事由")
    private String reason;  
    /**
     * 财务处理编号
     */
    @ApiModelProperty("财务处理编号")
    private String financialDocNumber;  
    /**
     * 审批人员id
     */
    @ApiModelProperty("审批人员id")
    private String approverIds;  
    /**
     * 审批人员
     */
    @ApiModelProperty("审批人员")
    private String approverNames;
    /**
     * 流程关联人id
     */
    @ApiModelProperty("流程关联人id")
    private String relatedIds;
    /**
    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;  
    /**
     * 接收日期
     */
    @ApiModelProperty("接收日期")
    private LocalDate receiveDate;
    /**
     * 凭证状态
     */
    @ApiModelProperty("凭证状态")
    private Integer voucherStatus;  
    /**
     * 凭证号
     */
    @ApiModelProperty("凭证号")
    private String voucherNumber;  
    /**
     * 凭证制单日期
     */
    @ApiModelProperty("凭证制单日期")
    private LocalDate voucherDate;
    /**
     * 收单状态
     */
    @ApiModelProperty("收单状态")
    private Integer receiptStatus;  
    /**
     * 收单日期
     */
    @ApiModelProperty("收单日期")
    private LocalDate receiptDate;
    /**
     * 结算人员id
     */
    @ApiModelProperty("结算人员id")
    private Long settlementPersonId;  
    /**
     * 结算人员
     */
    @ApiModelProperty("结算人员")
    private String settlementPersonName;  
    /**
     * 结算状态
     */
    @ApiModelProperty("结算状态")
    private Integer settlementStatus;  
    /**
     * 结算日期
     */
    @ApiModelProperty("结算日期")
    private LocalDate settlementDate;
    /**
     * 资金账户
     */
    @ApiModelProperty("资金账户")
    private String fundAccount;  

}
