package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目收入账款及发票信息导出
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ProjectRevenueLedgerExcelVO {

    /**
     * 流程id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 发票id
     */
    @ExcelIgnore
    private Long invoicingId;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 流程编码
     */
    @ColumnWidth(30)
    @ExcelProperty("流程编号")
    private String processCoding;

    /**
     * 项目编号
     */
    @ColumnWidth(30)
    @ExcelProperty("项目编码")
    private String projectNo;

    /**
     * 项目名称
     */
    @ColumnWidth(30)
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同编码
     */
    @ColumnWidth(30)
    @ExcelProperty("合同编码")
    private String contractCode;

    /**
     * 结项启动条件
     * {@link com.gok.pboot.financial.enums.ClosingStartConditionsEnum}
     */
    @ExcelIgnore
    private Integer closingStartConditions;

    /**
     * 结项启动条件
     * {@link com.gok.pboot.financial.enums.ClosingStartConditionsEnum}
     */
    @ColumnWidth(30)
    @ExcelProperty("结项启动条件")
    private String closingStartConditionsTxt;

    /**
     * 收入日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收入日期")
    private String incomeDate;

    /**
     * 收入金额（含税）
     */
    @ColumnWidth(30)
    @ExcelProperty("收入金额（含税）")
    private String incomeAmountTax;

    /**
     * 收入金额（不含税）
     */
    @ColumnWidth(30)
    @ExcelProperty("收入金额（不含税）")
    private String incomeAmount;

    /**
     * 收入类型 v1.2.4 新增
     */
    @ColumnWidth(20)
    @ExcelProperty("收入类型")
    private String incomeTypeList;

    /**
     * 收入明细金额不含税 v1.2.4 新增
     */
    @ColumnWidth(20)
    @ExcelProperty("收入明细金额（不含税）")
    private String incomeDetailAmountList;

    /**
     * 税率 v1.2.4 新增
     */
    @ColumnWidth(20)
    @ExcelProperty("税率")
    private String taxTateList;

    /**
     * 归属主体 v1.2.4 新增
     */
    @ColumnWidth(30)
    @ExcelProperty("归属主体")
    private String contractCompany;

    /**
     * 验收报告（0:是，1:否）
     * {@link com.gok.pboot.financial.enums.AcceptanceReportEnum}
     */
    @ExcelIgnore
    private Integer acceptanceReport;

    /**
     * 验收报告（0:是，1:否）
     * {@link com.gok.pboot.financial.enums.AcceptanceReportEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("验收报告")
    private String acceptanceReportTxt;

    /**
     * 验收日期
     */
    @ColumnWidth(20)
    @ExcelProperty("验收日期")
    private String acceptanceDate;

    /**
     * 客户id
     */
    @ExcelIgnore
    private Long customerId;

    /**
     * 客户名称
     */
    @ColumnWidth(30)
    @ExcelProperty("签约客户")
    private String customerName;

    /**
     * 业务方向
     * {@link com.gok.pboot.financial.enums.BusinessDirectionEnum}
     */
    @ExcelIgnore
    private Integer businessDirection;

    /**
     * 业务方向
     * {@link com.gok.pboot.financial.enums.BusinessDirectionEnum}
     */
    @ColumnWidth(30)
    @ExcelProperty("业务方向")
    private String businessDirectionTxt;

    /**
     * 合同名称
     */
    @ColumnWidth(30)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 合同金额
     */
    @ColumnWidth(30)
    @ExcelProperty("合同金额")
    private String contractMoney;

    /**
     * 业务归属一级部门
     */
    @ColumnWidth(30)
    @ExcelProperty("业务归属一级部门")
    private String businessFirstDept;

    /**
     * 业务归属二级部门
     */
    @ColumnWidth(30)
    @ExcelProperty("业务归属二级部门")
    private String businessSecondaryDept;

    /**
     * 客户经理
     */
    @ColumnWidth(20)
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 申请人
     */
    @ColumnWidth(20)
    @ExcelProperty("申请人")
    private String applicantUserName;

    /**
     * 推送状态（0待推送，1已推送，2推送失败）
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ExcelIgnore
    private Integer pushStatus;

    /**
     * 推送状态（0待推送，1已推送，2推送失败）
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("推送状态")
    private String pushStatusTxt;

    /**
     * 备注
     */
    @ColumnWidth(40)
    @ExcelProperty("备注")
    private String remarks;

}
