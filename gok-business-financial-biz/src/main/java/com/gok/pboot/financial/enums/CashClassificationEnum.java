package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 现金分类 Enum
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum CashClassificationEnum implements ValueEnum<Integer> {

    /**
     * 现金科目
     */
    CASH(0, "现金科目"),

    /**
     * 银行科目
     */
    BANK_ACCOUNT(1, "银行科目"),

    /**
     * 现金等价物
     */
    CASH_EQUIVALENTS(2, "现金等价物");

    private final Integer value;

    private final String name;

    CashClassificationEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
