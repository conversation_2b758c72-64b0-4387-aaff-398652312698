package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 归属业务线Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum BusinessLineEnum implements ValueEnum<Integer> {

    /**
     * 课酬
     */
    PAID_FOR_THE_COURSE(0, "课酬"),

    /**
     * 企业培训
     */
    BUSINESS_TRAIN(1, "企业培训"),

    /**
     * 个人培训
     */
    PERSONAL_TRAIN(2, "个人培训"),

    /**
     * 考试券
     */
    EXAMINATION_PAPER(3, "考试券"),

    /**
     * ICT
     */
    ICT(4, "ICT"),

    /**
     * 软件
     */
    SOFTWARE(5, "软件"),

    /**
     * 信安
     */
    XIN_AN(6, "信安"),

    /**
     * 人力外包
     */
    HUMAN_OUTSOURCING(7, "人力外包"),

    /**
     * 保证金
     */
    DEPOSIT(8, "保证金"),

    /**
     * 项目补贴
     */
    PROJECT_SUBSIDY(9, "项目补贴"),

    /**
     * 考试服务
     */
    EXAM_SERVICE(10, "考试服务"),

    /**
     * 采购返点回款
     */
    PAYBACK(11, "采购返点回款"),

    /**
     * 其它
     */
    OTHERS(12, "其它");

    private final Integer value;

    private final String name;

    BusinessLineEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
