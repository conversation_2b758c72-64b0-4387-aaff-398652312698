package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 指标科目关系
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("indicator_account_relationship")
public class IndicatorAccountRelationship extends Model<IndicatorAccountRelationship> {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 科目编码
     */
    private String code;

    /**
     * 科目名称
     */
    private String name;


    /**
     * 部门id
     */
    private Long accountingDeptId;

    /**
     * 核算部门编码
     */
    private String accountingDepartmentCode;

    /**
     * 核算部门
     */
    private String accountingDepartment;

    /**
     * 核算部门排序
     */
    private Integer accountingDepartmentSort;

    /**
     * 启用状态（0启用、1禁用）
     */
    private Integer enableStatus;

    /**
     * 会计体系
     */
    private Long accountingSystemId;




    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    private Long tenantId;

}