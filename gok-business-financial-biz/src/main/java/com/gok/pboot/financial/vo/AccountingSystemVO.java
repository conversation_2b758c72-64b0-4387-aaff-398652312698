package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.gok.module.excel.api.annotation.ExcelSelected;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 会计体系
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class AccountingSystemVO implements Serializable {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 体系编码
     */
    @ColumnWidth(20)
    @ExcelProperty("体系编码")
    private String systemCode;

    /**
     * 体系名称
     */
    @ColumnWidth(30)
    @ExcelProperty("体系名称")
    private String systemName;

    /**
     * 账套类型
     */
    @ColumnWidth(30)
    @ExcelProperty("账套类型")
    @ExcelSelected(source = {"0","1","2","3","4","5","6","7","8","9",})
    private String accountingType;

    /**
     * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
     */
    @ExcelIgnore
    private Integer enableStatus;

    /**
     * 停用状态文本{@link com.gok.pboot.financial.enums.EnableStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("停用状态")
    @ExcelSelected(source = {"是","否"})
    private String enableStatusTxt;

    /**
     * 备注
     */
    @ColumnWidth(20)
    @ExcelProperty("备注")
    private String systemRemarks;
}
