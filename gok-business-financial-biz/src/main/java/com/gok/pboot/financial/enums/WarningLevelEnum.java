package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 预警等级枚举
 * <AUTHOR>
 * @since 2023-09-29
 */
@Getter
public enum WarningLevelEnum implements ValueEnum<Integer> {

    /**
     * 0级
     */
    ZERO(0,"0"),

    /**
     * 一级
     */
    ONE(1,"1"),

    /**
     * 二级
     */
    TWO(2,"2"),

    /**
     * 三级
     */
    THREE(3,"3"),

    /**
     * ---
     */
    GAN(4,"---")
    ;

    private final Integer value;

    private final String name;

    WarningLevelEnum(Integer value,String name){
        this.name=name;
        this.value=value;
    }
}
