package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.SalesReceiptCollectionRecords;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.pboot.financial.vo.SalesReceiptCollectionRecordsVO;

import java.util.List;

/**
 * 销售收款计划催款记录Service
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
public interface ISalesReceiptCollectionRecordsService extends IService<SalesReceiptCollectionRecords> {

    /**
     * 催款记录
     *
     * @param salesReceiptId 销售收款id
     * @return {@link List}<{@link SalesReceiptCollectionRecordsVO}>
     */
    List<SalesReceiptCollectionRecordsVO> recordsList(Long salesReceiptId);

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@code true or false}
     */
    boolean saveRecords(SalesReceiptCollectionRecordsDTO dto);

    /**
     *  根据合同id查询合同催收记录
     * @param id 合同id
     * @return 催收记录
     */
    List<SalesReceiptCollectionRecordsVO> getContractCollectionVoList(Long id);
}
