package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.EducationPayment;
import com.gok.pboot.financial.dto.EducationDTO;
import com.gok.pboot.financial.vo.EducationVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 教育回款Mapper
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
public interface EducationPaymentMapper extends BaseMapper<EducationPayment> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}<{@link EducationPayment}>
     * @param educationDTO {@link EducationDTO}
     * @return {@link Page}<{@link EducationVO}>
     */
    Page<EducationPayment> findPage(Page<EducationPayment> page, @Param("query") EducationDTO educationDTO);

    /**
     * 模糊查询
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link List}<{@link EducationVO}>
     */
    List<EducationPayment> findPage(@Param("query") EducationDTO educationDTO);

    /**
     * 统计金额
     *
     * @param educationDTO {@link EducationDTO}
     * @return {@link Map}<{@link String}, {@link java.math.BigDecimal}>
     */
    Map<String, java.math.BigDecimal> statistics(@Param("query") EducationDTO educationDTO);
}