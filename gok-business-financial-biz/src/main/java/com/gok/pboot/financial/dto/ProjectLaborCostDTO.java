package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目人工成本分摊汇总 Dto
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectLaborCostDTO {

    /**
     * 当前页
     */
    private Integer current;

    /**
     * 每页条数
     */
    private Integer size;

    /**
     * 导出Excel所需要的id集合
     */
    private List<String> ids;

    /**
     * 所属月份
     */
    private String yearMonthDateStart;

    /**
     * 所属月份
     */
    private String yearMonthDateEnd;

    /**
     * 项目名称/编码
     */
    private String project;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    private String projectStatus;

    /**
     * 项目类型
     * {@link com.gok.pboot.financial.enums.ProjectTypeEnum}
     */
    private String projectType;

    /**
     * 业务所属部门
     */
    private List<Long> incomeDeptIds;

    /**
     * 人员所属部门
     */
    private List<Long> personnelDeptIds;

    /**
     * 人工类型/人员类型
     * {@link com.gok.pboot.financial.enums.PersonnelTypeEnum}
     */
    private Integer personnelType;

    /**
     * 人工类型/人员类型
     * {@link com.gok.pboot.financial.enums.PersonnelTypeEnum}
     */
    private List<Integer> personnelTypeList;

    /**
     * 归属主体
     */
    private String attributableSubject;

    /**
     * 人员名称
     * 项目人工成本分摊汇总不需要传
     * 项目人工成本分摊明细可传可不传
     */
    private String name;

    /**
     * 推送状态key
     * 项目人工成本分摊汇总可传可不传
     * 项目人工成本分摊明细不需要传
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private Integer pushStatus;

    /**
     * 应用id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 权限设置 人员二级部门
     */
    private List<Long> personnelSecondaryDept;

    private List<String> includeExcel;

}
