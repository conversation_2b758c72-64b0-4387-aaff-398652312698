package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 用友关联类型枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Getter
public enum YyRelateTypeEnum implements ValueEnum<Integer> {

    /**
     * 部门
     */
    DEPT(1, "部门"),

    /**
     * 人员
     */
    PERSONNEL(2, "人员"),

    /**
     * 主体
     */
    COMPANY(3, "主体"),

    /**
     * EPM组织
     */
    EPM_ORG(4, "EPM组织"),

    ;


    private final Integer value;

    private final String name;

    YyRelateTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
