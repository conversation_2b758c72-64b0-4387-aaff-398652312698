package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.FundAccount;
import com.gok.pboot.financial.dto.FundAccountDTO;
import com.gok.pboot.financial.dto.FundAccountFindDTO;
import com.gok.pboot.financial.vo.FundAccountVO;

import java.util.List;

/**
 * 资金账户业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface IFundAccountService extends IService<FundAccount> {


    Page<FundAccountVO> findPage(FundAccountFindDTO dto);

    R<FundAccountVO> addOrEdit(FundAccountDTO dto);

    R<FundAccountVO> info(Long id);

    boolean del(List<Long> idList);

    List<FundAccountVO> findList();
}
