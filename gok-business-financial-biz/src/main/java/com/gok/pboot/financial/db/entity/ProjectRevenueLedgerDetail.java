package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目收入台账明细
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("项目收入台账明细")
@TableName("project_revenue_ledger_detail")
@EqualsAndHashCode(callSuper = true)
public class ProjectRevenueLedgerDetail extends Model<ProjectRevenueLedgerDetail> {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    /**
     * id
     */
    @ApiModelProperty("mainid")
    private Long mainid;

    /**
     * requestId
     */
    @ApiModelProperty("requestId")
    private Long requestId;

    /**
     * 收入明细金额含税
     */
    @ApiModelProperty("收入明细金额含税")
    private BigDecimal incomeDetailAmountTax;

    /**
     * 收入明细金额不含税
     */
    @ApiModelProperty("收入明细金额不含税")
    private BigDecimal incomeDetailAmount;

    /**
     * 税率
     */
    @ApiModelProperty("税率")
    private String taxTate;

    /**
     * 收入类型
     */
    @ApiModelProperty("收入类型")
    private String incomeType;

}