package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 启用状态（0 启用， 1停用） Enum
 * 停用状态（0 否， 1是）
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum EnableStatusEnum implements ValueEnum<Integer> {

    /**
     * 启用
     */
    YES(0, "否"),

    /**
     * 禁用
     */
    NO(1, "是");

    private final Integer value;

    private final String name;

    EnableStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
