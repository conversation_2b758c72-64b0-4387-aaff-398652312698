package com.gok.pboot.financial.invoice.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.invoice.entity.domain.InvoiceOutput;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 销项发票台账表 Mapper 接口
 *
 * <AUTHOR>
 * @create 2025/06/04
 */
@Mapper
public interface InvoiceOutputMapper extends BaseMapper<InvoiceOutput> {

    /**
     * 分页查询
     *
     * @param page 分页请求
     * @param dto  查询请求
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    Page<InvoiceOutputVO> findPage(Page<InvoiceOutput> page, @Param("query") InvoiceOutputDTO dto);

    /**
     * 获取分页合计值
     *
     * @param dto
     * @return
     */
    @BusinessDataScope(deptOrUser = "user", scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    Map<String, BigDecimal> findPageSum(@Param("query") InvoiceOutputDTO dto);

}