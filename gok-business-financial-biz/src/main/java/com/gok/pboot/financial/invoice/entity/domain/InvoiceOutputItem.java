package com.gok.pboot.financial.invoice.entity.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 销项发票明细表实体类
 *
 * <AUTHOR>
 * @create 2025/06/10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("invoice_output_item")
public class InvoiceOutputItem extends Model<InvoiceOutputItem> {

    /**
     * 销项发票明细ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("销项发票明细ID")
    private Long id;

    /**
     * 销项发票台账ID
     */
    @ApiModelProperty("销项发票台账ID")
    private Long outputId;

    /**
     * 发票规划ID
     */
    @ApiModelProperty("发票规划ID")
    private Long invoicePlanId;

    /**
     * 发票号码
     */
    @ApiModelProperty("发票号码")
    private String invoiceNo;

    /**
     * 税收分类编码
     */
    @ApiModelProperty("税收分类编码")
    private String taxClassificationCode;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 规格型号
     */
    @ApiModelProperty("规格型号")
    private String modelSpec;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 数量
     */
    @ApiModelProperty("数量")
    private BigDecimal num;

    /**
     * 单价
     */
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 实际开票金额_含税
     */
    @ApiModelProperty("实际开票金额_含税")
    private BigDecimal actualAmountIncludedTax;

    /**
     * 实际开票金额_不含税
     */
    @ApiModelProperty("实际开票金额_不含税")
    private BigDecimal actualAmountExcludingTax;

    /**
     * 实际开票税率
     */
    @ApiModelProperty("实际开票税率")
    private Integer actualTaxRate;

    /**
     * 发票申请流程
     */
    @ApiModelProperty("发票申请流程")
    private Long applyRequestId;

    /**
     * 发票作废流程
     */
    @ApiModelProperty("发票作废流程")
    private Long cancelRequestId;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
} 