package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedgerInvoicing;
import com.gok.pboot.financial.dto.ProjectRevenueLedgerDTO;
import com.gok.pboot.financial.dto.RelationInvoiceDTO;
import com.gok.pboot.financial.dto.UniqueFlagDTO;
import com.gok.pboot.financial.enums.BusinessDirectionEnum;
import com.gok.pboot.financial.enums.ClosingStartConditionsEnum;
import com.gok.pboot.financial.service.IProjectRevenueLedgerInvoicingService;
import com.gok.pboot.financial.service.IProjectRevenueLedgerService;
import com.gok.pboot.financial.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目收入台账
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@RestController
@RequestMapping("/project-income-account")
@RequiredArgsConstructor
@Api(tags = "项目收入台账")
public class ProjectRevenueLedgerController {

    private final IProjectRevenueLedgerService projectRevenueLedgerService;

    private final IProjectRevenueLedgerInvoicingService projectRevenueLedgerInvoicingService;

    /**
     * 模糊查询带分页
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件封装
     * @return {@link R}<{@link Page}<{@link ProjectRevenueLedgerVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('project-revenue')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<ProjectRevenueLedgerVO>> findPage(@RequestBody ProjectRevenueLedgerDTO projectRevenueLedgerDTO) {
        return R.ok(projectRevenueLedgerService.findPage(projectRevenueLedgerDTO));
    }

    /**
     * 导出（异步导出）
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件封装
     * @return {@link List}<{@link ProjectRevenueLedgerExcelVO}>
     */
    @ResponseExcel(async = true, name = "项目收入台账", functionEnum = FunctionEnum.FINANCIAL_PROJECT_INCOME_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('project-revenue/export')")
    @ApiOperation(value = "导出", notes = "导出")
    public List<ProjectRevenueLedgerExcelVO> export(@RequestBody ProjectRevenueLedgerDTO projectRevenueLedgerDTO) {
        return projectRevenueLedgerService.export(projectRevenueLedgerDTO);
    }

    /**
     * 修改或添加备注
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件封装
     * @return {@link R}
     */
    @PostMapping("/updateRemarks")
    @ApiOperation(value = "修改或添加备注", notes = "修改或添加备注")
    public R<String> updateRemarks(@Valid @RequestBody ProjectRevenueLedgerDTO projectRevenueLedgerDTO) {
        return Boolean.TRUE.equals(projectRevenueLedgerService.updateRemarks(projectRevenueLedgerDTO)) ? R.ok("操作成功") : R.failed("操作失败");
    }

    /**
     * 获取发票信息
     *
     * @param id 项目收入台帐id
     * @param contractCode 合同编码
     * @return {@link R}<{@link List}<{@link ProjectRevenueLedgerInvoicingVO}>>
     */
    @GetMapping("/getInvoice")
    @ApiOperation(value = "获取发票信息", notes = "获取发票信息")
    public R<List<ProjectRevenueLedgerInvoicingVO>> getInvoice(@RequestParam("id") Long id,
                                                               @RequestParam("contractCode") String contractCode) {
        return R.ok(projectRevenueLedgerService.getInvoice(id, contractCode));
    }

    /**
     * 关联发票
     *
     * @param relationInvoiceDTO {@link RelationInvoiceDTO} 项目id与发票集合
     * @return {@link R}
     */
    @PostMapping("/relationInvoice")
    @ApiOperation(value = "关联发票", notes = "关联发票")
    public R<String> relationInvoice(@RequestBody RelationInvoiceDTO relationInvoiceDTO) {
        return projectRevenueLedgerService.relationInvoice(relationInvoiceDTO);
    }

    /**
     * 业务方向下拉框
     *
     * @return {@link R}<{@link List}<{@link BusinessDirectionVO}>>
     */
    @GetMapping("/businessDirectionList")
    @ApiOperation(value = "结项启动条件下拉框", notes = "结项启动条件下拉框")
    public R<List<BusinessDirectionVO>> businessDirectionList() {
        List<BusinessDirectionVO> businessDirectionList = new ArrayList<>();
        for (BusinessDirectionEnum businessDirectionEnum : BusinessDirectionEnum.values()) {
            BusinessDirectionVO businessDirectionVO = BusinessDirectionVO.builder()
                    .value(businessDirectionEnum.getValue())
                    .name(businessDirectionEnum.getName())
                    .build();
            businessDirectionList.add(businessDirectionVO);
        }
        return R.ok(businessDirectionList);
    }

    /**
     * 结项启动条件下拉框
     *
     * @return {@link R}<{@link List}<{@link ClosingStartConditionsVO}>>
     */
    @GetMapping("/closingStartConditionList")
    @ApiOperation(value = "结项启动条件下拉框", notes = "结项启动条件下拉框")
    public R<List<ClosingStartConditionsVO>> closingStartConditionList() {
        List<ClosingStartConditionsVO> closingStartConditionsList = new ArrayList<>();
        for (ClosingStartConditionsEnum closingStartConditionsEnum : ClosingStartConditionsEnum.values()) {
            ClosingStartConditionsVO closingStartConditionsVO = ClosingStartConditionsVO.builder()
                    .value(closingStartConditionsEnum.getValue())
                    .name(closingStartConditionsEnum.getName())
                    .build();
            closingStartConditionsList.add(closingStartConditionsVO);
        }
        return R.ok(closingStartConditionsList);
    }

    /**
     * 列表发薪主体
     *
     * @return {@link R}<{@link List}>
     */
    @GetMapping("/salary")
    @ApiOperation(value = "列表发薪主体", notes = "列表发薪主体")
    public R<List<String>> getSalaryList() {
        return R.ok(projectRevenueLedgerInvoicingService.list().stream()
                .map(ProjectRevenueLedgerInvoicing::getInvoicingSubject)
                .filter(p -> Optional.ofNullable(p).isPresent())
                .distinct().collect(Collectors.toList())
        );
    }

    /**
     * 推送
     *
     * @param ids ids
     * @return PushResultVO
     */
    @PostMapping("/push")
    @PreAuthorize("@pms.hasPermission('project-revenue/push')")
    @ApiOperation(value = "推送", notes = "推送")
    public PushResultVO push(@Valid @RequestBody UniqueFlagDTO ids) {
        return projectRevenueLedgerService.push(ids.getIds());
    }

}
