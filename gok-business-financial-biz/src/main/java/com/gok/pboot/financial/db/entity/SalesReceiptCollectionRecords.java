package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 销售收款计划催款记录
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("sales_receipt_collection_records")
@ApiModel("销售收款计划催款记录")
public class SalesReceiptCollectionRecords extends Model<SalesReceiptCollectionRecords> {

    /**
     * 催款记录id
     */
    @ApiModelProperty("催款记录id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 销售计划id
     */
    @ApiModelProperty("销售计划id")
    private Long salesReceiptId;

    /**
     * 预计收款日期
     */
    @ApiModelProperty("预计收款日期")
    private LocalDateTime expectedReceiptDate;

    /**
     * 催收日期
     */
    @ApiModelProperty("催收日期")
    private LocalDateTime collectionDate;

    /**
     * 催收金额
     */
    @ApiModelProperty("催收金额")
    private BigDecimal collectionAmount;

    /**
     * 催款方式
     * {@link com.gok.pboot.financial.enums.CollectionMethodEnum}
     */
    @ApiModelProperty("催款方式")
    private Integer collectionMethod;

    /**
     * 催款情况
     */
    @ApiModelProperty("催款情况")
    private String collectionSituation;

    /**
     * 催款备注
     */
    @ApiModelProperty("催款备注")
    private String collectionRemarks;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}