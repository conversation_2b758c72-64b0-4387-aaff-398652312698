package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 归属合同收款明细
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractPaymentVO {

    /**
     * 合同款项id
     */
    private Long id;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 款项名称
     */
    private String paymentName;

    /**
     * 款项实际金额
     */
    private BigDecimal price;

}
