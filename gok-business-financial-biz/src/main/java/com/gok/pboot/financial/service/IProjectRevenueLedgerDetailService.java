package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail;

import java.util.List;

/**
 * 项目收入台账明细业务层
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
public interface IProjectRevenueLedgerDetailService extends IService<ProjectRevenueLedgerDetail> {

    /**
     * 归属主体（合同所属公司）查询列表
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getContractCompany();

    /**
     * 收入类型查询列表
     *
     * @return {@link List}<{@link String}>
     */
    List<String> getIncomeType();
}
