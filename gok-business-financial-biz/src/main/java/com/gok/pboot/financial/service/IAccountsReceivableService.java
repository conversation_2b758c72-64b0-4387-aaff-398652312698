package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.AccountsReceivable;
import com.gok.pboot.financial.dto.AccountsReceivableDTO;
import com.gok.pboot.financial.vo.AccountsReceivableVO;

import java.util.List;

/**
 * 应收账款台账 Service
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
public interface IAccountsReceivableService extends IService<AccountsReceivable> {

    /**
     * 模糊查询带分页
     *
     * @param page 分页请求
     * @param accountsReceivableDto 模糊查询属性封装实体
     * @return {@link Page}<{@link AccountsReceivableVO}>
     */
    StatisticsPage<AccountsReceivableVO> findPage(Page<AccountsReceivable> page, AccountsReceivableDTO accountsReceivableDto);

    /**
     * 导出Excel选中数据
     *
     * @param accountsReceivableDto {@link AccountsReceivableDTO}
     * @return {@link List}<{@link AccountsReceivableVO}>
     */
    List<AccountsReceivableVO> export(AccountsReceivableDTO accountsReceivableDto);

    /**
     * 手动同步刷新数据
     *
     * @return {@link R}
     */
    R<String> refresh();
}
