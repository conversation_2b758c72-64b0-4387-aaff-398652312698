package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.CustomizeCalculate;
import com.gok.pboot.financial.dto.CustomizeCalculateDTO;
import com.gok.pboot.financial.dto.CustomizeCalculateFindDTO;
import com.gok.pboot.financial.vo.CustomizeCalculateVO;

import java.util.List;

/**
 * 自定义辅助核算项业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface ICustomizeCalculateService extends IService<CustomizeCalculate> {


    Page<CustomizeCalculateVO> findPage(CustomizeCalculateFindDTO dto);

    R addOrEdit(CustomizeCalculateDTO dto);

    R<CustomizeCalculateVO> info(Long id);

    boolean del(List<Long> idList);

    List<CustomizeCalculateVO> findList();

    /**
     * 自动同步科目时 执行插入或更新
     */
    void insertOrUpdateByCode(List<CustomizeCalculateDTO> list);
}
