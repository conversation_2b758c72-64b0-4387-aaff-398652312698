package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 跨部门人工成本分摊
 *
 * <AUTHOR>
 * @since 2023-11-14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrossDeptLaborShareCostsDTO {

    /**
     * 人员归属部门id
     */
    private Long deptId;

    /**
     * 所属月份初
     */
    private String yearMonthDayStart;

    /**
     * 所属月份末
     */
    private String yearMonthDayEnd;
}
