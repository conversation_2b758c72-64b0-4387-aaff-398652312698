package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 教育回款条件查询
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EducationDTO {

    /**
     * 当前页
     */
    private Long current;

    /**
     * 每页条数
     */
    private Long size;

    /**
     * 导出Excel/汇总/删除 所需要的id集合
     */
    private List<Long> ids;

    /**
     * 单据编号/交易流水号
     */
    private String documentNumber;

    /**
     * 收款平台
     */
    private String paymentPlatform;

    /**
     * 收款公司
     */
    private String paymentCompany;

    /**
     * 收款日期开始时间
     */
    private String paymentBegin;

    /**
     * 收款日期结束时间
     */
    private String paymentEnd;

    /**
     * 开票状态
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    private Integer invoicingStatus;

    /**
     * 开票号码
     */
    private String invoicingNumber;

    /**
     * 客户信息（客户名称/学校名称）
     */
    private String customerOrSchool;

    /**
     * 回款归属一级部门与二级部门id
     */
    private List<Long> paymentDeptId;

    /**
     * 相关人人员
     */
    private String salesmanUserName;

    /**
     * 产品名称
     */
    private String businessLineOrProduct;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 单据状态(推送状态/汇总状态/认领状态/锁定状态)
     */
    private String documentStatus;

    /**
     * 认领状态（0已认领，1待认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    private Integer claimStatusByDocument;

    /**
     * 汇总状态
     * {@link com.gok.pboot.financial.enums.SummaryStatusEnum}
     */
    private Integer summaryStatusByDocument;

    /**
     * 锁定状态（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    private Integer lockStatusByDocument;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private Integer pushStatusByDocument;

    /**
     * 认领状态
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    private Integer claimStatus;

    /**
     * 汇总状态
     * {@link com.gok.pboot.financial.enums.SummaryStatusEnum}
     */
    private Integer summaryStatus;

    /**
     * 锁定状态
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    private Integer lockStatus;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 是否全部权限表示
     */
    private Boolean authority;

    /**
     * 可以查看的人员id列表
     */
    private List<Long> userId;

    /**
     * 可以查看的部门id列表
     */
    private List<Long> deptId;

    /**
     * 单据状态多选(推送状态/汇总状态/认领状态/锁定状态)
     */
    private List<String> documentStatusList;

    private List<String> includeExcel;
}
