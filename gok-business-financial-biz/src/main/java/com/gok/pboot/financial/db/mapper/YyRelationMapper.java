package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.db.entity.YyRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用友关联表Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface YyRelationMapper extends BaseMapper<YyRelation> {

    /**
     * 根据关联ID和关联类型查询用友关联关系
     *
     * @param relateId 关联ID
     * @param relateType 关联类型
     * @return 用友关联关系
     */
    YyRelation selectByRelateIdAndType(@Param("relateId") Long relateId, @Param("relateType") Integer relateType);

    /**
     * 根据关联ID列表和关联类型查询用友关联关系列表
     *
     * @param relateIds 关联ID列表
     * @param relateType 关联类型
     * @return 用友关联关系列表
     */
    List<YyRelation> selectByRelateIdsAndType(@Param("relateIds") List<Long> relateIds, @Param("relateType") Integer relateType);

    /**
     * 根据用友ID查询关联关系
     *
     * @param yyId 用友ID
     * @return 用友关联关系
     */
    YyRelation selectByYyId(@Param("yyId") String yyId);

}
