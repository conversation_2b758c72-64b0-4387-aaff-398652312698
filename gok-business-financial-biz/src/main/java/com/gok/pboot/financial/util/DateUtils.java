package com.gok.pboot.financial.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.gok.module.excel.api.exception.ExcelException;
import com.gok.pboot.common.core.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Date;
import java.util.Optional;

/**
 * 日期转换前端展示
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Slf4j
public class DateUtils {

    private DateUtils() {}

    /**
     * 年月格式
     */
    public static final String YEAR_MONTH = "yyyyMM";

    /**
     * 年-月-日 秒级格式
     */
    public static final String SIMPLE_DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 年/月/日 秒级格式
     */
    public static final String SIMPLE_DATE_FORMAT_2 = "yyyy/MM/dd";

    /**
     * 年-月-日 时-分-秒 毫秒级格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时分秒初始值
     */
    public static final String BLACK_HOUR_MINUTE_SECOND = " 00:00:00";

    /**
     * 每月平均30天
     */
    public static final Integer THIRTY_DAYS = 30;

    /**
     * 转换日期 年月 与 -
     * 2023年 9月 -> 2023-09-00; 2023年10月 -> 2023-10-00
     * 2023-09 -> 2023年 9月; 2023-10 -> 2023年10月
     *
     * @param time 日期
     * @return 日期
     */
    public static String dateTrans(String time) {
        if (!Optional.ofNullable(time).isPresent()) {
            return CharSequenceUtil.EMPTY;
        }
        if (time.contains("年")) {
            if (NumberUtils.INTEGER_ONE.equals(time.substring(time.indexOf("年") + 1, time.indexOf("月")).trim().length())) {
                time = time.substring(0, time.indexOf("年") + 1).replace('年','-') + "0"
                        + time.substring(time.indexOf("年") + 2).replace('月','-') + "00";
            } else {
                time = time.replace('年','-').replace('月','-') + "00";
            }
        } else {
            if ('0' == (time.charAt(time.length() - 1))) {
                time = time.substring(0, time.indexOf("-") + 1).replace('-','年')
                        + time.substring(time.indexOf("-") + 1) + "月";
            } else {
                time = time.substring(0, time.indexOf("-") + 1).replace('-','年')
                        + time.substring(time.indexOf("-") + 1).replace("0"," ") + "月";
            }
        }

        return time;
    }

    /**
     * 日期转换
     * 2023/10月/1日 -> 2023/10/01
     * 2023/ 8月/1日 -> 2023/08/01
     *
     * @param time 时间
     * @return 时间
     */
    public static String signingDateTrans(String time) {
        // 时间为空直接返回
        if (!Optional.ofNullable(time).isPresent()) {
            return CharSequenceUtil.EMPTY;
        }
        if (time.charAt(5) == '0') {
            time = time.substring(0, 5) + time.substring(6, time.length());
        }
        return time;
    }

    /**
     * 判断当前时间与收款日期的差距天数
     *
     * @param paymentDate 收款日期
     * @return 差距天数
     */
    public static int difference(String paymentDate) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(SIMPLE_DATE_FORMAT);
        // 收款日期转换时间格式初始化
        Date date = null;
        // 当前时间
        Date now = new Date();
        try {
            date = simpleDateFormat.parse(paymentDate);
            now = simpleDateFormat.parse(simpleDateFormat.format(now));
        } catch (ParseException e) {
            throw new BusinessException("时间格式化有误");
        }
        if (null == date) {
            log.error("时间解析有误");
            return THIRTY_DAYS + NumberUtils.INTEGER_ONE;
        }

        return (int) ((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 导入时将日期格式化
     * 比如: 2023/6/6 -> 2023/06/06
     *
     * @param date 导入表的日期
     * @return String 日期
     */
    public static String formatTime(String date) {
        if (!date.contains("/")) {
            return StrUtil.EMPTY;
        }
        String[] strings = date.split("/");
        if (strings.length != 3) {
            return StrUtil.EMPTY;
        }
        for (int i = 1; i < strings.length; ++i) {
            if (strings[i].length() == 1) {
                strings[i] = "0" + strings[i];
            }
        }
        return strings[0] + "/" + strings[1] + "/" + strings[2];
    }


    /**
     * yyyy-MM-dd格式字符串
     *
     * @param localDateTime 日期
     * @return {@link String }
     * 2023-03-13
     */
    public static final String getStringDate(LocalDateTime localDateTime, String pattern) {
        if (Optional.ofNullable(localDateTime).isPresent()) {
            ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());
            Instant instant = zonedDateTime.toInstant();
            Date date = Date.from(instant);
            return DateFormatUtils.format(date, pattern);
        }
        return null;
    }
}
