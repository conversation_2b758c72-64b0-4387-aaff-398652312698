package com.gok.pboot.financial.common.client;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.*;

import com.gok.bcp.upms.dto.OaGetTokenDTO;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * - OA后台 -
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@BaseRequest(
        headers = {
                "Accept-Charset: utf-8",
                "Content-Type: application/json"
        }
)
@Component
public interface OaClient {

    /**
     * 获取token
     *
     * @return WxCpGetTokenDTO
     */
    @Post(url = "{url}/api/ec/dev/auth/applytoken", maxRetryInterval = 3, headers = {"appid:${appid}", "secret:${secret}"})
    OaGetTokenDTO getToken(@Var("url") String url, @Var("appid") String appid, @Var("secret") String secret);


    /**
     * 提交流程
     * @param requestId 流程ID
     * @param mainData 主表参数
     * @param otherParams 其他参数
     * @return
     */
    @Post(url = "/api/workflow/paService/submitRequest?requestId={requestId}&" +
            "&mainData={mainData}&otherParams={otherParams}", interceptor = OaRequestInterceptor.class,
            headers = { "userid:${userid}"}
    )
    Map submitRequest(
                        @Var("userid") String userid,
                        @Var("requestId") String requestId,
                        @Var("mainData") String mainData,
                        @Var("otherParams") String otherParams);

    /**
     * 创建流程
     *
     * @param workflowId  流程ID
     * @param requestName 请求名字
     * @param mainData    主表参数
     * @param otherParams 其他参数
     * @return
     */
    @Post(url = "{url}/api/workflow/paService/doCreateRequest",
            headers = {
                    "token:${token}",
                    "appid:${appid}",
                    "userid:${userid}",
                    "Content-Type: application/x-www-form-urlencoded"
            },
            data = "workflowId={workflowId}&requestName={requestName}&mainData={mainData}&otherParams={otherParams}&detailData={detailData}")
    JSONObject doCreateRequest(@Var("token") String token,
                               @Var("appid") String appid,
                               @Var("userid") String userid,
                               @Var("url") String url,
                               @Var("workflowId") String workflowId, @Var("requestName") String requestName,
                               @Var("mainData") String mainData, @Var("otherParams") String otherParams,
                               @Var("detailData") String detailData);

    /**
     * 获取流程相关资源
     */
    @Get(
            url = "{url}/api/workflow/paService/getRequestResources?requestid={requestid}",
            headers = {"token:${token}", "appid:${appid}", "userid:${userid}"}
    )
    Map getRequestResources(
            @Var("url") String url,
            @Var("requestid") String requestid,
            @Var("token") String token,
            @Var("appid") String appid,
            @Var("userid") String userid
    );

}
