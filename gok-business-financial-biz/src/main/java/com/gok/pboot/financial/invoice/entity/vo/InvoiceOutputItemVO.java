package com.gok.pboot.financial.invoice.entity.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @create 2025/06/10
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "销项发票明细返回对象")
@ColumnWidth(20)
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class InvoiceOutputItemVO {

    /**
     * 销项发票明细ID
     */
    @ExcelIgnore
    private Long id;

    /**
     * 销项发票台账ID
     */
    @ExcelIgnore
    private Long outputId;

    /**
     * 发票号码
     */
    @ExcelProperty("发票号码")
    private String invoiceNo;

    /**
     * 原蓝字发票号码
     */
    @ExcelProperty("原蓝字发票号码")
    private String blueInvoiceNo;

    /**
     * 红蓝字 0=红字 1=蓝字
     */
    @ExcelIgnore
    private Integer redAndBlue;

    @ExcelProperty("红蓝字")
    private String redAndBlueTxt;

    /**
     * 商品服务名称ID
     */
    @ExcelIgnore
    private Integer goodsServiceName;

    @ExcelProperty("商品和服务名称")
    private String goodsServiceNameTxt;

    /**
     * 税收分类编码
     */
    @ExcelProperty("税务分类编码")
    private String taxClassificationCode;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String productName;

    /**
     * 规格型号
     */
    @ExcelProperty("规格型号")
    private String modelSpec;

    /**
     * 单位
     */
    @ExcelProperty("单位")
    private String unit;

    /**
     * 数量
     */
    @ExcelProperty("数量")
    private BigDecimal num;

    /**
     * 单价
     */
    @ExcelProperty("单价")
    private BigDecimal unitPrice;

    /**
     * 实际开票金额_含税
     */
    @ExcelIgnore
    private BigDecimal actualAmountIncludedTax;

    @ExcelProperty("实际开票金额_含税")
    private String actualAmountIncludedTaxTxt;

    /**
     * 实际开票税率
     */
    @ExcelIgnore
    private Integer actualTaxRate;

    @ExcelProperty("实际开票税率")
    private String actualTaxRateTxt;

    /**
     * 实际开票金额_不含税
     */
    @ExcelIgnore
    private BigDecimal actualAmountExcludingTax;

    @ExcelProperty("实际开票金额_不含税")
    private String actualAmountExcludingTaxTxt;

    /**
     * 开票客户ID
     */
    @ExcelIgnore
    private Long invoiceCustomerId;

    /**
     * 开票客户名称
     */
    @ExcelProperty("开票客户名称")
    private String invoiceCustomerName;

    /**
     * 开票申请人ID
     */
    @ExcelIgnore
    private Long invoiceApplicationId;

    /**
     * 开票申请人
     */
    @ExcelProperty("开票申请人")
    private String invoiceApplication;

    /**
     * 发票申请流程
     */
    @ExcelProperty("发票申请流程")
    private Long applyRequestId;

    /**
     * 发票作废流程
     */
    @ExcelProperty("发票作废流程")
    private Long cancelRequestId;

    /**
     * 发票规划ID
     */
    @ExcelProperty("发票规划ID")
    private Long invoicePlanId;

    /**
     * 合同ID
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同名称
     */
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 合同编号
     */
    @ExcelProperty("合同编号")
    private String contractNo;

    /**
     * 合同签约客户id
     */
    @ExcelIgnore
    private Long contractCustomerId;

    /**
     * 合同签约客户名称
     */
    @ExcelProperty("合同签约客户")
    private String contractCustomerName;

    /**
     * 合同签约主体
     */
    @ExcelIgnore
    private Integer contractSubject;

    @ExcelProperty("合同签约主体")
    private String contractSubjectTxt;

    /**
     * 合同结算方式
     */
    @ExcelIgnore
    private Integer contractSettlementType;

    @ExcelProperty("合同结算方式")
    private String contractSettlementTypeTxt;

    /**
     * 合同起始日期
     */
    @ExcelProperty("合同起始日期")
    private LocalDate contractStartDate;

    /**
     * 合同截止日期
     */
    @ExcelProperty("合同截止日期")
    private LocalDate contractEndDate;

    /**
     * 项目ID
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目名称
     */
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 项目编号
     */
    @ExcelProperty("项目编号")
    private String projectNo;

    /**
     * 业务归属部门id
     */
    @ExcelIgnore
    private Long businessDeptId;

    /**
     * 业务归属部门
     */
    @ExcelProperty("业务归属部门")
    private String businessDept;

    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 项目经理id
     */
    @ExcelIgnore
    private Long managerUserId;

    /**
     * 项目经理
     */
    @ExcelProperty("项目经理")
    private String managerUserName;

    /**
     * 收款日期
     */
    @ExcelProperty("收款日期")
    private LocalDate receiptDate;

    /**
     * 付款客户ID
     */
    @ExcelIgnore
    private Long receiptCustomerId;

    /**
     * 付款客户名称
     */
    @ExcelProperty("付款客户名称")
    private String receiptCustomerName;

    /**
     * 收款平台
     */
    @ExcelIgnore
    private Integer receiptPlatform;

    /**
     * 收款平台
     */
    @ExcelProperty("收款平台")
    private String receiptPlatformTxt;

    /**
     * 发票状态
     */
    @ExcelIgnore
    private Integer invoiceStatus;

    /**
     * 发票状态
     */
    @ExcelProperty("发票状态")
    private String invoiceStatusTxt;

} 