package com.gok.pboot.financial.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.module.excel.api.utils.Threads;
import com.gok.pboot.common.secret.AESEncryptor;
import com.gok.pboot.financial.db.entity.CrossDeptLaborCosts;
import com.gok.pboot.financial.db.mapper.CrossDeptLaborCostsMapper;
import com.gok.pboot.financial.db.mapper.CrossFirstDeptLaborCostsMapper;
import com.gok.pboot.financial.db.mapper.ProjectLaborCostMapper;
import com.gok.pboot.financial.dto.CrossDeptLaborShareCostsDTO;
import com.gok.pboot.financial.dto.DeptArtificialCostDTO;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.ICrossDeptLaborCostsService;
import com.gok.pboot.financial.util.FinancePropertiesUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.util.ThreadUtils;
import com.gok.pboot.financial.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 跨部门人工成本台账ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrossDeptLaborCostsImpl extends ServiceImpl<CrossDeptLaborCostsMapper, CrossDeptLaborCosts>
        implements ICrossDeptLaborCostsService {

    private final ProjectLaborCostMapper projectLaborCostMapper;

    private final CrossFirstDeptLaborCostsMapper crossFirstDeptLaborCostsMapper;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final LogRecordUtils logRecordUtils;

    /**
     * 列表展示跨部门人工成本台帐数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    @Override
    public List<CrossDeptLaborCostsVO> findList(DeptArtificialCostDTO deptArtificialCostDTO) {
        List<CrossDeptLaborCostsVO> crossDeptLaborCostsList = crossFirstDeptLaborCostsMapper.findList(deptArtificialCostDTO);
        if (crossDeptLaborCostsList.isEmpty()) {
            return new ArrayList<>();
        }
        return this.packageCrossDeptLaborCostsVO(crossDeptLaborCostsList, deptArtificialCostDTO, true);
    }

    /**
     * 跨部门人工成本台账导出（异步导出）
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    @Override
    public List<CrossDeptLaborCostsVO> export(DeptArtificialCostDTO deptArtificialCostDTO) {
        List<CrossDeptLaborCostsVO> crossDeptLaborCostsList = crossFirstDeptLaborCostsMapper.findList(deptArtificialCostDTO);
        if (crossDeptLaborCostsList.isEmpty()) {
            crossDeptLaborCostsList.add(new CrossDeptLaborCostsVO());
            return crossDeptLaborCostsList;
        }
        // 记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.CROSS_DEPT_LABOR_COSTS,
                OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + crossDeptLaborCostsList.size() + "条】");

        return this.packageCrossDeptLaborCostsVO(crossDeptLaborCostsList, deptArtificialCostDTO, false);
    }

    /**
     * 列表展示跨部门人工成本台帐明细数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    @Override
    public List<CrossDeptLaborCostsDetailVO> findDetailList(DeptArtificialCostDTO deptArtificialCostDTO) {
        List<CrossDeptLaborCostsDetailVO> crossDeptLaborCostsList = baseMapper.findDetailList(deptArtificialCostDTO);
        if (crossDeptLaborCostsList.isEmpty()) {
            return new ArrayList<>();
        }

        return this.deptDetail(crossDeptLaborCostsList, deptArtificialCostDTO, true);
    }

    /**
     * 跨部门人工成本台账明细导出（异步导出）
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    @Override
    public List<CrossDeptLaborCostsDetailVO> detailExport(DeptArtificialCostDTO deptArtificialCostDTO) {
        List<CrossDeptLaborCostsDetailVO> crossDeptLaborCostsList = baseMapper.findDetailList(deptArtificialCostDTO);
        if (crossDeptLaborCostsList.isEmpty()) {
            crossDeptLaborCostsList.add(new CrossDeptLaborCostsDetailVO());
            return crossDeptLaborCostsList;
        }
        // 记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.CROSS_DEPT_LABOR_COSTS_DETAIL,
                OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + crossDeptLaborCostsList.size() + "条】");

        return this.deptDetail(crossDeptLaborCostsList, deptArtificialCostDTO, false);
    }

    /**
     * 列表展示跨部门人工成本台账分摊数据
     *
     * @param crossDeptLaborShareCostsDTO {@link CrossDeptLaborShareCostsDTO} 条件
     * @return {@link CrossDeptShareVO}
     */
    @Override
    public CrossIncomeDeptVO findShareVoList(CrossDeptLaborShareCostsDTO crossDeptLaborShareCostsDTO) {
        // 根据人员归属一级部门查询项目汇总中的数据
        List<CrossDeptProjectLaborCostVO> projectLaborCostList = projectLaborCostMapper.incomeDeptList(crossDeptLaborShareCostsDTO);
        //根据收入部门分组
        Map<Long, List<CrossDeptProjectLaborCostVO>> incomeDeptIdListMap = projectLaborCostList.stream().collect(Collectors.groupingBy(CrossDeptProjectLaborCostVO::getIncomeDeptId));
        List<Long> incomeDeptIdList = incomeDeptIdListMap.keySet().stream().collect(Collectors.toList());
        List<CrossDeptShareCostVO> crossDeptShareCostVOS = new ArrayList<>();
        List<String> yearMonthList = new ArrayList<>();
        List<TableYearMonthVO> tableYearMonthVOS = new ArrayList<>();
        incomeDeptIdList.stream().forEach(incomeDeptId -> {
            List<YearMonthDateCostVO> yearMonthDateCostVOS = new ArrayList<>();
            List<CrossDeptProjectLaborCostVO> projectLaborCosts = incomeDeptIdListMap.get(incomeDeptId);
            if (CollectionUtils.isNotEmpty(projectLaborCosts)) {
                Map<String, List<CrossDeptProjectLaborCostVO>> yearMonthDateMap = projectLaborCosts.stream().collect(Collectors.groupingBy(CrossDeptProjectLaborCostVO::getYearMonthDate));
                List<String> yearMonthDateList = yearMonthDateMap.keySet().stream().collect(Collectors.toList());
                yearMonthList.addAll(yearMonthDateList);
                yearMonthDateList.stream().forEach(yearMonthDate -> {
                    List<CrossDeptProjectLaborCostVO> yearMonthDateCosts = yearMonthDateMap.get(yearMonthDate);
                    BigDecimal yearMonthCost = yearMonthDateCosts.stream()
                            .map(e -> new BigDecimal(MoneyUtils.getInstance().decrypt(e.getCostSum())))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    YearMonthDateCostVO build = YearMonthDateCostVO.builder().yearMonth(yearMonthDate)
                            .costSum(MoneyUtils.getInstance().transType(yearMonthCost)).build();
                    yearMonthDateCostVOS.add(build);

                    String year = yearMonthDate.split("-")[0];
                    String month = yearMonthDate.split("-")[1];
                    TableYearMonthVO tableYearMonthVO = TableYearMonthVO.builder().year(year).month(month).yearMonth(yearMonthDate).build();
                    tableYearMonthVOS.add(tableYearMonthVO);
                });
                CrossDeptProjectLaborCostVO projectLaborCost = projectLaborCosts.get(NumberUtils.INTEGER_ZERO);
                BigDecimal cost = projectLaborCosts.stream()
                        .map(e -> new BigDecimal(MoneyUtils.getInstance().decrypt(e.getCostSum())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                CrossDeptShareCostVO build = CrossDeptShareCostVO.builder()
                        .incomeDeptId(incomeDeptId)
                        .incomeDept(projectLaborCost.getIncomeDept())
                        .projectCost(MoneyUtils.getInstance().transType(cost))
                        .yearMonthDateList(yearMonthDateCostVOS).build();
                crossDeptShareCostVOS.add(build);
            }
        });

        Map<String, List<TableYearMonthVO>> stringListMap = tableYearMonthVOS.stream().distinct().collect(Collectors.groupingBy(TableYearMonthVO::getYear));
        List<String> yearList = stringListMap.keySet().stream().collect(Collectors.toList());

        List<TableYearVO> tableYearVOList = new ArrayList<>();
        yearList.stream().forEach(year -> {
            List<TableYearMonthVO> yearMonthVOS = stringListMap.get(year);
            List<TableMonthVO> tableMonthList = new ArrayList<>();
            yearMonthVOS.stream().forEach(yearMonth -> {
                TableMonthVO build = TableMonthVO.builder()
                        .month(yearMonth.getMonth())
                        .yearMonth(yearMonth.getYearMonth())
                        .build();
                tableMonthList.add(build);
            });
            TableYearVO build = TableYearVO.builder().year(year)
                    .tableMonthList(tableMonthList).build();

            tableYearVOList.add(build);
        });

        return CrossIncomeDeptVO.builder().crossDeptShareCostVO(crossDeptShareCostVOS).tableYearVO(tableYearVOList).build();
    }


    /**
     * 对跨部门人工成本台帐数据包装
     *
     * @param crossDeptLaborCostsList {@link List}<{@link CrossDeptLaborCostsVO}>
     * @param deptArtificialCostDTO   {@link DeptArtificialCostDTO} 金额条件
     * @param isPage                  是否分页
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    private List<CrossDeptLaborCostsVO> packageCrossDeptLaborCostsVO(List<CrossDeptLaborCostsVO> crossDeptLaborCostsList,
                                                                     DeptArtificialCostDTO deptArtificialCostDTO,
                                                                     boolean isPage) {
        // 1、人工成本过滤
        BigDecimal artificialCostMin = deptArtificialCostDTO.getArtificialCostMin();
        BigDecimal artificialCostMax = deptArtificialCostDTO.getArtificialCostMax();
        if (Optional.ofNullable(artificialCostMin).isPresent() || Optional.ofNullable(artificialCostMax).isPresent()) {
            crossDeptLaborCostsList = crossDeptLaborCostsList.stream()
                    .filter(c -> {
                        boolean flag = true;
                        BigDecimal laborCosts = new BigDecimal(AESEncryptor.justDecrypt(c.getLaborCosts()));
                        if (Optional.ofNullable(artificialCostMin).isPresent() &&
                                laborCosts.compareTo(artificialCostMin) < NumberUtils.INTEGER_ZERO) {
                            flag = false;
                        }
                        if (Optional.ofNullable(artificialCostMax).isPresent() &&
                                laborCosts.compareTo(artificialCostMax) > NumberUtils.INTEGER_ZERO) {
                            flag = false;
                        }
                        return flag;
                    }).collect(Collectors.toList());
        }
        // 2、根据当前部门 返回其二级部门的id列表
        List<Long> firstDeptIdList = remoteOutMultiDeptService
                .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, NumberUtils.INTEGER_ONE)
                .getData()
                .stream().map(MultiDimensionDeptDto::getDeptId).collect(Collectors.toList());
        List<MultiDimensionDeptDto> secondDeptList = remoteOutMultiDeptService
                .getDeptList(FinancePropertiesUtils.ADMIN_DEPT_CAT_NAME, null, NumberUtils.INTEGER_TWO)
                .getData();
        Map<Long, List<Long>> idMap = new ConcurrentHashMap<>(firstDeptIdList.size());
        firstDeptIdList.forEach(f -> {
            List<Long> idList = new ArrayList<>();
            secondDeptList.forEach(s -> {
                if (s.getParentId().equals(f)) {
                    idList.add(s.getDeptId());
                }
            });
            idMap.put(f, idList);
        });
        // 3、数据解密
        int size = 5;
        int threadCount = ThreadUtils.getInstance().getThreadCount(crossDeptLaborCostsList.size(), size);
        CountDownLatch downLatch = new CountDownLatch(threadCount);
        ThreadPoolExecutor threadPool = ThreadUtils.getInstance().getThreadPool(threadCount);
        if (!isPage) {
            Threads.countDownLatchThreadLocal.set(downLatch);
        }
        List<CrossDeptLaborCostsVO> list = new ArrayList<>(crossDeptLaborCostsList);
        for (int i = 0; i < threadCount; ++i) {
            int j = i;
            threadPool.submit(() -> {
                try {
                    if (j != threadCount - 1) {
                        list.subList((j * size), (j + 1) * size)
                                .forEach(c -> this.crossDeptLaborCostsVODecrypt(c, idMap));
                    } else {
                        list.subList((j * size), list.size())
                                .forEach(c -> this.crossDeptLaborCostsVODecrypt(c, idMap));
                    }
                } catch (Exception e) {
                    log.info("Error in thread: " + Thread.currentThread().getName(), e);
                } finally {
                    downLatch.countDown();
                }
            });
        }
        if (isPage) {
            try {
                downLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("Error waiting for tasks to complete: " + e.getMessage());
            } finally {
                threadPool.shutdown();
            }
        }

        // 4、排序返回
        return list.stream()
                .sorted(Comparator.comparing(CrossDeptLaborCostsVO::getSortOrder, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    /**
     * 对跨部门人工成本台帐明细数据包装
     *
     * @param crossDeptLaborCostsList {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     * @param deptArtificialCostDTO   {@link DeptArtificialCostDTO} 金额条件
     * @param isPage                  是否分页
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    private List<CrossDeptLaborCostsDetailVO> deptDetail(List<CrossDeptLaborCostsDetailVO> crossDeptLaborCostsList,
                                                         DeptArtificialCostDTO deptArtificialCostDTO,
                                                         boolean isPage) {
        // 1、条件过滤数据
        BigDecimal artificialCostMin = deptArtificialCostDTO.getArtificialCostMin();
        BigDecimal artificialCostMax = deptArtificialCostDTO.getArtificialCostMax();
        if (Optional.ofNullable(artificialCostMin).isPresent() ||
                Optional.ofNullable(artificialCostMax).isPresent()) {
            crossDeptLaborCostsList = crossDeptLaborCostsList.stream()
                    .filter(c -> {
                        boolean flag = true;
                        BigDecimal laborCosts = new BigDecimal(AESEncryptor.justDecrypt(c.getLaborCosts()));
                        if (Optional.ofNullable(artificialCostMin).isPresent() &&
                                laborCosts.compareTo(artificialCostMin) < NumberUtils.INTEGER_ZERO) {
                            flag = false;
                        }
                        if (Optional.ofNullable(artificialCostMax).isPresent() &&
                                laborCosts.compareTo(artificialCostMax) > NumberUtils.INTEGER_ZERO) {
                            flag = false;
                        }
                        return flag;
                    }).collect(Collectors.toList());
        }
        // 2、解密金额
        List<CrossDeptLaborCostsDetailVO> tempList = new ArrayList<>(crossDeptLaborCostsList);
        int size = 5;
        int threadCount = ThreadUtils.getInstance().getThreadCount(crossDeptLaborCostsList.size(), size);
        CountDownLatch downLatch = new CountDownLatch(threadCount);
        ThreadPoolExecutor threadPool = ThreadUtils.getInstance().getThreadPool(threadCount);
        if (!isPage) {
            Threads.countDownLatchThreadLocal.set(downLatch);
        }
        for (int i = 0; i < threadCount; ++i) {
            int j = i;
            threadPool.submit(() -> {
                try {
                    if (j != threadCount - 1) {
                        tempList.subList((j * size), (j + 1) * size)
                                .forEach(this::crossDeptLaborCostsDetailVODecrypt);
                    } else {
                        tempList.subList((j * size), tempList.size())
                                .forEach(this::crossDeptLaborCostsDetailVODecrypt);
                    }
                } catch (Exception e) {
                    log.error("Error in thread: {}, 异常信息为: {}", Thread.currentThread().getName(), e.getMessage());
                    throw e;
                } finally {
                    downLatch.countDown();
                }
            });
        }
        if (isPage) {
            try {
                downLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.info("Error waiting for tasks to complete: " + e.getMessage());
            }
        }
        threadPool.shutdown();
        // 4、排序返回
        return tempList.stream()
                .sorted(Comparator.comparing(CrossDeptLaborCostsDetailVO::getSecondaryDeptSortOrder, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    /**
     * 跨部门汇总解密金额
     *
     * @param c {@link CrossDeptLaborCostsVO}
     */
    private void crossDeptLaborCostsVODecrypt(CrossDeptLaborCostsVO c, Map<Long, List<Long>> idMap) {
        // 1、二级部门列表id
        c.setPersonnelSecondaryDeptId(idMap.get(c.getPersonnelDeptId()));
        // 2、金额解密
        c.setLaborCosts(MoneyUtils.getInstance().transType(c.getLaborCosts(), true));
        c.setProjectCost(MoneyUtils.getInstance().transType(c.getProjectCost(), true));
        c.setStrategicProjectsCost(MoneyUtils.getInstance().transType(c.getStrategicProjectsCost(), true));
        c.setManageCostProjectsCost(MoneyUtils.getInstance().transType(c.getManageCostProjectsCost(), true));
        c.setUnallocatedCost(MoneyUtils.getInstance().transType(c.getUnallocatedCost(), true));
        c.setStudentInternCost(MoneyUtils.getInstance().transType(c.getStudentInternCost(), true));
        c.setStudentInternProjectsCost(MoneyUtils.getInstance().transType(c.getStudentInternProjectsCost(), true));
        c.setStudentInternInternalProjectsCost(MoneyUtils.getInstance().transType(c.getStudentInternInternalProjectsCost(), true));
        c.setStudentInternUnallocatedCost(MoneyUtils.getInstance().transType(c.getStudentInternUnallocatedCost(), true));
        c.setStudentOutsideCost(MoneyUtils.getInstance().transType(c.getStudentOutsideCost(), true));
        c.setPerCapitaMonthlyCostOwn(MoneyUtils.getInstance().transType(c.getPerCapitaMonthlyCostOwn(), true));
        c.setPerCapitaMonthlyCostStusen(MoneyUtils.getInstance().transType(c.getPerCapitaMonthlyCostStusen(), true));
        // 3、占比百分比
        c.setStrategicProjectsCostProportion(MoneyUtils.getInstance().proportion(c.getStrategicProjectsCostProportion()));
        c.setProjectCostProportion(MoneyUtils.getInstance().proportion(c.getProjectCostProportion()));
        c.setManageCostProjectsCostProportion(MoneyUtils.getInstance().proportion(c.getManageCostProjectsCostProportion()));
        c.setUnallocatedCostProportion(MoneyUtils.getInstance().proportion(c.getUnallocatedCostProportion()));
        c.setStudentInternCostProportion(MoneyUtils.getInstance().proportion(c.getStudentInternCostProportion()));
    }

    /**
     * 跨部门明细汇总解密金额
     *
     * @param c {@link CrossDeptLaborCostsDetailVO}
     */
    private void crossDeptLaborCostsDetailVODecrypt(CrossDeptLaborCostsDetailVO c) {
        // 金额处理
        c.setLaborCosts(MoneyUtils.getInstance().transType(c.getLaborCosts(), true));
        c.setProjectCost(MoneyUtils.getInstance().transType(c.getProjectCost(), true));
        c.setStrategicProjectsCost(null);// 内部项目战略性预留字段
        c.setManageCostProjectsCost(MoneyUtils.getInstance().transType(c.getManageCostProjectsCost(), true));
        c.setUnallocatedCost(MoneyUtils.getInstance().transType(c.getUnallocatedCost(), true));
        c.setStudentInternCost(MoneyUtils.getInstance().transType(c.getStudentInternCost(), true));
        c.setStudentInternProjectsCost(MoneyUtils.getInstance().transType(c.getStudentInternProjectsCost(), true));
        c.setStudentInternInternalProjectsCost(MoneyUtils.getInstance().transType(c.getStudentInternInternalProjectsCost(), true));
        c.setStudentInternUnallocatedCost(MoneyUtils.getInstance().transType(c.getStudentInternUnallocatedCost(), true));
        c.setStudentOutsideCost(MoneyUtils.getInstance().transType(c.getStudentOutsideCost(), true));
        c.setPerCapitaMonthlyCostOwn(MoneyUtils.getInstance().transType(c.getPerCapitaMonthlyCostOwn(), false));
        c.setPerCapitaMonthlyCostStusen(MoneyUtils.getInstance().transType(c.getPerCapitaMonthlyCostStusen(), false));
        c.setTotalPersonnelProjectCost(MoneyUtils.getInstance().transType(c.getTotalPersonnelProjectCost(), true));
        c.setTotalPersonnelProjectHours(MoneyUtils.getInstance().transType(c.getTotalPersonnelProjectHours(), false));
        c.setTotalPersonnelProjectPrice(MoneyUtils.getInstance().transType(c.getTotalPersonnelProjectPrice(), false));
        c.setExcludingInternsProjectCost(MoneyUtils.getInstance().transType(c.getExcludingInternsProjectCost(), true));
        c.setExcludingInternsProjectHours(MoneyUtils.getInstance().transType(c.getExcludingInternsProjectHours(), false));
        c.setExcludingInternsProjectPrice(MoneyUtils.getInstance().transType(c.getExcludingInternsProjectPrice(), false));
        c.setInternsProjectCost(MoneyUtils.getInstance().transType(c.getInternsProjectCost(), true));
        c.setInternsProjectHours(MoneyUtils.getInstance().transType(c.getInternsProjectHours(), false));
        c.setInternsProjectPrice(MoneyUtils.getInstance().transType(c.getInternsProjectPrice(), false));
        // 占比
        c.setStrategicProjectsCostProportion(null);// 内部项目战略性占比预留字段
        c.setManageCostProjectsCostProportion(MoneyUtils.getInstance().proportion(c.getManageCostProjectsCostProportion()));
        c.setProjectCostProportion(MoneyUtils.getInstance().proportion(c.getProjectCostProportion()));
        c.setUnallocatedCostProportion(MoneyUtils.getInstance().proportion(c.getUnallocatedCostProportion()));
        c.setStudentInternCostProportion(MoneyUtils.getInstance().proportion(c.getStudentInternCostProportion()));
    }

}
