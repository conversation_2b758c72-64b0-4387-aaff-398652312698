package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.FinanceProcessForm;
import com.gok.pboot.financial.dto.ConfirmSettlementDTO;
import com.gok.pboot.financial.dto.FinancialProcessFormPageDTO;
import com.gok.pboot.financial.vo.ConfirmSettlementBatchVO;
import com.gok.pboot.financial.vo.FinanceProcessFormVO;

import java.util.List;


/**
 * 财务处理单业务层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IFinanceProcessFormService extends IService<FinanceProcessForm> {
    /**
     * 条件查询带分页
     *
     * @param dto 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link FinanceProcessFormVO}>
     */
    StatisticsPage<FinanceProcessFormVO> findPage(FinancialProcessFormPageDTO dto);

    /**
     *  确认结算
     * @param id 请求封装实体
     * @return 响应信息
     */
    String confirmSettlement(Long id) throws Exception;


    /**
     *  批量确认结算
     * @param dto 请求封装实体
     * @return 响应信息
     */
    R<ConfirmSettlementBatchVO> confirmSettlementBatch(ConfirmSettlementDTO dto);
    /**
     *  条件导出
     * @param dto 导出条件封装实体
     * @return 导出信息
     */
    List<FinanceProcessFormVO> export(FinancialProcessFormPageDTO dto);
}
