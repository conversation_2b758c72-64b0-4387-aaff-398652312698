package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 教育回款跟踪认领
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("education_payment_claim")
@ApiModel("教育回款跟踪认领")
public class EducationPaymentClaim extends Model<EducationPaymentClaim> {

    /**
     * 主键id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 教育回款id
     */
    @ApiModelProperty("教育回款id")
    private Long educationPaymentId;

    /**
     * 客户经理id
     */
    @ApiModelProperty("客户经理id")
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ApiModelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 大客户经理id
     */
    @ApiModelProperty("大客户经理id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long keyAccountManagerId;

    /**
     * 大客户经理姓名
     */
    @ApiModelProperty("大客户经理姓名")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String keyAccountManagerName;

    /**
     * 销售主管id
     */
    @ApiModelProperty("销售主管id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long salesExecutiveId;

    /**
     * 销售主管姓名
     */
    @ApiModelProperty("销售主管姓名")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String salesExecutiveName;

    /**
     * 客户经理id
     */
    @ApiModelProperty("客户经理id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long accountManagerId;

    /**
     * 客户经理姓名
     */
    @ApiModelProperty("客户经理姓名")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String accountManagerName;

    /**
     * 咨询顾问id
     */
    @ApiModelProperty("咨询顾问id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long consultantUserId;

    /**
     * 咨询顾问姓名
     */
    @ApiModelProperty("咨询顾问姓名")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String consultantUserName;

    /**
     * 回款一级部门id
     */
    @ApiModelProperty("回款一级部门id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long paymentDeptId;

    /**
     * 回款一级部门
     */
    @ApiModelProperty("回款一级部门")
    private String paymentDept;

    /**
     * 回款二级部门id
     */
    @ApiModelProperty("回款二级部门id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long paymentSecondaryDeptId;

    /**
     * 回款二级部门
     */
    @ApiModelProperty("回款二级部门")
    private String paymentSecondaryDept;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String customerName;

    /**
     * 归属业务线
     */
    @ApiModelProperty("归属业务线")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String businessLine;

    /**
     * 学校名称
     */
    @ApiModelProperty("学校名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String schoolName;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String productName;

    /**
     * 认领人id
     */
    @ApiModelProperty("认领人id")
    private Long claimantId;

    /**
     * 认领人姓名
     */
    @ApiModelProperty("认领人姓名")
    private String claimantName;

    /**
     * 确认收入日期
     */
    @ApiModelProperty("确认收入日期")
    private String sureRevenueDate;

    /**
     * 认领备注
     */
    @ApiModelProperty("认领备注")
    private String claimRemark;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}