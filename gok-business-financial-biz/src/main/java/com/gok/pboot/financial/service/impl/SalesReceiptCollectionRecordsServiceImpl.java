package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.user.UserUtils;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.db.entity.SalesReceipt;
import com.gok.pboot.financial.db.entity.SalesReceiptCollectionRecords;
import com.gok.pboot.financial.db.mapper.SalesReceiptCollectionRecordsMapper;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.pboot.financial.enums.CollectionMethodEnum;
import com.gok.pboot.financial.service.ISalesReceiptCollectionRecordsService;
import com.gok.pboot.financial.service.ISalesReceiptService;
import com.gok.pboot.financial.util.DecimalFormatUtil;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.SalesReceiptCollectionRecordsVO;
import com.gok.pboot.pms.feign.IRemotePaymentService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 销售收款计划催款记录ServiceImpl
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Service
@RequiredArgsConstructor
public class SalesReceiptCollectionRecordsServiceImpl
        extends ServiceImpl<SalesReceiptCollectionRecordsMapper, SalesReceiptCollectionRecords>
        implements ISalesReceiptCollectionRecordsService {

    private final ISalesReceiptService salesReceiptService;

    private final IRemotePaymentService iRemotePaymentService;

    /**
     * 催款记录
     *
     * @param salesReceiptId 销售收款id
     * @return {@link List}<{@link SalesReceiptCollectionRecordsVO}>
     */
    @Override
    public List<SalesReceiptCollectionRecordsVO> recordsList(Long salesReceiptId) {
        List<SalesReceiptCollectionRecordsVO> voList = new ArrayList<>();
        baseMapper.selectList(Wrappers.<SalesReceiptCollectionRecords>lambdaQuery()
                .eq(SalesReceiptCollectionRecords::getSalesReceiptId, salesReceiptId)
                .orderByDesc(SalesReceiptCollectionRecords::getCreateTime)
        ).forEach(s -> {
            SalesReceiptCollectionRecordsVO vo = BeanUtil.copyProperties(s, SalesReceiptCollectionRecordsVO.class);
            vo.setCollectionMethodTxt(EnumUtils.getNameByValue(CollectionMethodEnum.class, s.getCollectionMethod()));
            vo.setCollectionAmount(MoneyUtils.getInstance().transType(s.getCollectionAmount()));
            voList.add(vo);
        });
        return voList;
    }

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@code true or false}
     */
    @Override
    public boolean saveRecords(SalesReceiptCollectionRecordsDTO dto) {
        // 业务一体化
        //if (UserUtils.getUser() != null) {
        //    if (Boolean.FALSE.equals(iRemotePaymentService.saveRecords(dto).getData())) {
        //        throw new BusinessException("业务一体化新增催收记录操作失败");
        //    }
        //}
        return baseMapper.insert(BeanUtil.copyProperties(dto, SalesReceiptCollectionRecords.class)) > NumberUtils.INTEGER_ZERO;
    }

    @Override
    public List<SalesReceiptCollectionRecordsVO> getContractCollectionVoList(Long id) {
        List<Long> salesReceiptIds = salesReceiptService.list(Wrappers.<SalesReceipt>lambdaQuery()
                .eq(SalesReceipt::getContractId, id)).stream().map(s -> s.getId()).collect(Collectors.toList());
        List<SalesReceiptCollectionRecordsVO> voList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(salesReceiptIds)){
            baseMapper.selectList(Wrappers.<SalesReceiptCollectionRecords>lambdaQuery()
                    .in(SalesReceiptCollectionRecords::getSalesReceiptId, salesReceiptIds)
                    .orderByDesc(SalesReceiptCollectionRecords::getCollectionDate)
            ).forEach(s -> {
                SalesReceiptCollectionRecordsVO vo = BeanUtil.copyProperties(s, SalesReceiptCollectionRecordsVO.class);
                vo.setCollectionMethodTxt(EnumUtils.getNameByValue(CollectionMethodEnum.class, s.getCollectionMethod()));
                vo.setCollectionAmountTxt(DecimalFormatUtil.setThousandthAndTwoDecimal((Optional.ofNullable(vo.getCollectionAmount()).isPresent()?
                        new BigDecimal(vo.getCollectionAmount()) :null),DecimalFormatUtil.ZERO));
                voList.add(vo);
            });
        }
        return voList;
    }
}
