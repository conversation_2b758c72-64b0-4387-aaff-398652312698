/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 企业绩效组织体系成员新增请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业绩效组织体系成员新增请求DTO
 * 用于企业绩效组织体系节点新增虚组织
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EpmOrgSaveRequest {

    /**
     * 插入的数据集
     */
    private EpmOrgData data;
    
    /**
     * 外部数据
     */
    private Object externalData;

    /**
     * 企业绩效组织数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EpmOrgData {
        
        /**
         * 主体单元编码
         */
        private String code;
        
        /**
         * 主体单元名称，支持多语
         */
        private MultiLangName name;
        
        /**
         * 简称
         */
        private MultiLangName shortname;
        
        /**
         * 公司类型id
         */
        private String companytype;
        
        /**
         * 公司类型名称
         */
        private String companytype_name;
        
        /**
         * 父节点id
         */
        private String parent;
        
        /**
         * 父节点名称
         */
        private String parent_name;
        
        /**
         * 组织类型
         */
        private Boolean orgtype;
        
        /**
         * 部门类型id
         */
        private String depttype;
        
        /**
         * 部门类型名称
         */
        private String depttype_name;
        
        /**
         * 负责人id
         */
        private String principal;
        
        /**
         * 负责人名称
         */
        private String principal_name;
        
        /**
         * 分管领导id
         */
        private String branchleader;
        
        /**
         * 分管领导名称
         */
        private String branchleader_name;
        
        /**
         * 集团id
         */
        private String corpid;
        
        /**
         * 集团名称
         */
        private String corpid_name;
        
        /**
         * 地区id
         */
        private String locationid;
        
        /**
         * 地区名称
         */
        private String locationid_name;
        
        /**
         * 纳税人识别号
         */
        private String taxpayerid;
        
        /**
         * 纳税人名称
         */
        private String taxpayername;
        
        /**
         * 实际使用纳税人识别号
         */
        private String usedtaxpayerid;
        
        /**
         * 实际使用纳税人名称
         */
        private String usedtaxpayername;
        
        /**
         * 纳税人类型
         */
        private String taxpayertype;
        
        /**
         * 汇率
         */
        private String exchangerate;
        
        /**
         * 汇率名称
         */
        private String exchangerate_name;
        
        /**
         * 语言
         */
        private String language;
        
        /**
         * 语言选择
         */
        private String language_select_language;
        
        /**
         * 时区
         */
        private String timezone;
        
        /**
         * 联系人
         */
        private String contact;
        
        /**
         * 电话
         */
        private String telephone;
        
        /**
         * 地址，支持多语
         */
        private MultiLangName address;
        
        /**
         * 描述，支持多语
         */
        private MultiLangName description;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
        
        /**
         * 对象id
         */
        private String objid;
        
        /**
         * 财务组织
         */
        private FinanceOrg financeOrg;
        
        /**
         * 资金组织
         */
        private FundsOrg fundsOrg;
        
        /**
         * 销售组织
         */
        private SalesOrg salesOrg;
        
        /**
         * 采购组织
         */
        private PurchaseOrg purchaseOrg;
        
        /**
         * 库存组织
         */
        private InventoryOrg inventoryOrg;
        
        /**
         * 工厂组织
         */
        private FactoryOrg factoryOrg;
        
        /**
         * 资产组织
         */
        private AssetsOrg assetsOrg;
        
        /**
         * 纳税人组织
         */
        private TaxpayerOrg taxpayerOrg;
        
        /**
         * 行政组织
         */
        private AdminOrg adminOrg;
        
        /**
         * 状态
         */
        private String _status;
    }

    /**
     * 多语言名称内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {
        
        /**
         * 中文名称
         */
        private String zh_CN;
        
        /**
         * 英文名称
         */
        private String en_US;
        
        /**
         * 繁体中文名称
         */
        private String zh_TW;
    }
    
    /**
     * 财务组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FinanceOrg {
        
        /**
         * 父节点id
         */
        private String parentid;
        
        /**
         * 父节点名称
         */
        private String parentid_name;
        
        /**
         * 币种id
         */
        private String currency;
        
        /**
         * 币种名称
         */
        private String currency_name;
        
        /**
         * 会计期间方案id
         */
        private String periodschema;
        
        /**
         * 会计期间方案名称
         */
        private String periodschema_name;
        
        /**
         * 是否外部核算
         */
        private String isexternalaccounting;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 资金组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FundsOrg {
        
        /**
         * 财务组织id
         */
        private String finorgid;
        
        /**
         * 财务组织名称
         */
        private String finorgid_name;
        
        /**
         * 父节点id
         */
        private String parenrid;
        
        /**
         * 父节点名称
         */
        private String parentid_name;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 销售组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SalesOrg {
        
        /**
         * 父节点id
         */
        private String parentid;
        
        /**
         * 父节点名称
         */
        private String parentid_name;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 采购组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PurchaseOrg {
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 库存组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class InventoryOrg {
        
        /**
         * 财务组织id
         */
        private String finorgid;
        
        /**
         * 财务组织名称
         */
        private String finorgid_name;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 工厂组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class FactoryOrg {
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 资产组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AssetsOrg {
        
        /**
         * 财务组织id
         */
        private String finorgid;
        
        /**
         * 财务组织名称
         */
        private String finorgid_name;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
    
    /**
     * 纳税人组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaxpayerOrg {
        
        /**
         * 父节点id
         */
        private String parentid;
        
        /**
         * 父节点名称
         */
        private String parentid_name;
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
        
        /**
         * 是否为纳税人
         */
        private Integer isTaxpayer;
    }
    
    /**
     * 行政组织内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AdminOrg {
        
        /**
         * 启用状态
         */
        private Integer enable;
        
        /**
         * id
         */
        private String id;
    }
}