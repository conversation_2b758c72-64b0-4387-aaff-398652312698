package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 会计体系关联科目类型 会计科目数量
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountingSystemCorrelationVO {

    /**
     * id
     */
    private Long id;

    /**
     * 会计科目数量
     */
    private Integer accountNum;

    /**
     * 科目类型数量
     */
    private Integer accountTypeNum;
}
