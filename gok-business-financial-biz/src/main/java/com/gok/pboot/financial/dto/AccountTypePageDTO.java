package com.gok.pboot.financial.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 科目类型分页
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class AccountTypePageDTO implements Serializable {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * 类型编码
     */
    private String typeCode;


    /**
     * 会计体系
     */
    private Long accountingSystemId;

    /**
     * ids
     */
    private List<Long> idList;
}
