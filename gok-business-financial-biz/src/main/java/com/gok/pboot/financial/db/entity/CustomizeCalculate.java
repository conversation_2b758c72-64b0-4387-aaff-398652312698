package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 自定义辅助核算项
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("自定义辅助核算项")
@TableName("customize_calculate")
@EqualsAndHashCode(callSuper = true)
public class CustomizeCalculate extends Model<CustomizeCalculate> {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     */
    @ApiModelProperty("编码")
    private String code;

    /**
     * 自定义辅助核算项
     */
    @ApiModelProperty("自定义辅助核算项")
    private String customizeCalculate;

    /**
     * 账套账号
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}