package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.vo.ProjectPaymentClaimExcelVO;
import com.gok.pboot.financial.vo.ProjectPaymentPushVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目回款跟踪 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface ProjectPaymentMapper extends BaseMapper<ProjectPayment> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link Page}<{@link ProjectPayment}>
     */
    Page<ProjectPayment> findPage(Page<ProjectPayment> page, @Param("query") ProjectPaymentDTO projectPaymentDTO);

    /**
     * 模糊查询
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}<{@link ProjectPayment}>
     */
    List<ProjectPayment> findPage(@Param("query") ProjectPaymentDTO projectPaymentDTO);

    /**
     * 【客户名称】无值且项目干系人未认领，该数据所有人可见
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link List}<{@link ProjectPayment}>
     */
    List<ProjectPayment> listByAuth(@Param("query") ProjectPaymentDTO projectPaymentDTO);

    /**
     * 查询已推送或已锁定的数据
     *
     * @return {@link List}<{@link ProjectPayment}>
     */
    List<ProjectPayment> listByPushOrLock();

    /**
     * 推送
     *
     * @param ids id集合
     * @return {@link List}<{@link ProjectPaymentPushVO}>
     */
    List<ProjectPaymentPushVO> selPushVo(@Param("ids") List<Long> ids);

//    Page<Long> findPageIds(Page<ProjectPayment> page,@Param("query") ProjectPaymentDTO projectPaymentDTO);
    Page<Long> findPageIdsV1(Page<ProjectPayment> page,@Param("query") ProjectPaymentDTO projectPaymentDTO);

    List<Long> findPageIdsV1(@Param("query") ProjectPaymentDTO projectPaymentDTO);


    List<Long> findContractPaymentIds(@Param("query") ProjectPaymentDTO projectPaymentDTO);

    /**
     * 查询数据 包括【客户名称】无值且项目干系人未认领，该数据所有人可见
     * @param projectPaymentDTO
     * @return
     */
    List<Long> findPageIds(@Param("query") ProjectPaymentDTO projectPaymentDTO);


    /**
     * @param pageIds
     * @return
     */
    List<ProjectPaymentClaimExcelVO> findListByIds(@Param("pageIds") List<Long> pageIds);

    /**
     * 统计支付金额、合同支付金额和认领金额
     * @param query 查询条件
     * @return 统计结果
     */
    BigDecimal paymentAmountStatistics(@Param("query") ProjectPaymentDTO query);

    BigDecimal claimMoneyStatistics(@Param("query") ProjectPaymentDTO query);

}
