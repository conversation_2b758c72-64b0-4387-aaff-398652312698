package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
    * 科目类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("科目类型")
@TableName("account_type")
@EqualsAndHashCode(callSuper = true)
public class AccountType  extends Model<AccountType> {
    /**
    * id
    */
    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 类型编码
    */
    @ApiModelProperty("类型编码")
    private String typeCode;

    /**
    * 类型名称
    */
    @ApiModelProperty("类型名称")
    private String typeName;

    /**
    * 会计体系id
    */
    @ApiModelProperty("会计体系id")
    private Long accountingSystemId;

    /**
    * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
    */
    @ApiModelProperty("停用状态")
    private Boolean enableStatus;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}