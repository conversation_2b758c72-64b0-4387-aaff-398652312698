package com.gok.pboot.financial.invoice.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/06/09
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceOutputDTO {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * 主键ID集合
     */
    private List<Long> ids;

    /**
     * 发票规划ID
     */
    private Long invoicePlanId;

    /**
     * 商品/服务名称ID
     */
    private Integer goodsServiceName;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 发票状态
     */
    private Integer invoiceStatus;

    /**
     * 项目名称或编码
     */
    private String projectNameOrNo;

    /**
     * 合同名称或编码
     */
    private String contractNameOrNo;

    /**
     * 税率
     */
    private Integer taxRate;

    /**
     * 人员名称
     */
    private String memberName;

    /**
     * 业务归属部门id集合
     */
    private List<Long> businessDeptIds;

    /**
     * 合同签约主体
     */
    private Integer contractSubject;

    /**
     * excel导出字段集合
     */
    private List<String> includeExcel;

}
