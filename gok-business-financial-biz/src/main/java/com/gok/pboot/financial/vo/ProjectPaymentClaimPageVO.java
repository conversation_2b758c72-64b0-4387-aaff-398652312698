package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目回款追踪页面展示
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ProjectPaymentClaimPageVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long projectPaymentId;

    /**
     * 回款类型
     */
    @ExcelIgnore
    private Integer paymentType;

    /**
     * 回款类型
     */
    @ColumnWidth(20)
    @ExcelProperty("回款类型")
    private String paymentTypeText;

    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ColumnWidth(20)
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编码
     */
    @ColumnWidth(25)
    @ExcelProperty("项目编码")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ColumnWidth(25)
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 关联客户
     */
    @ColumnWidth(25)
    @ExcelProperty("关联客户")
    private String relatedCustomerName;

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同编码
     */
    @ColumnWidth(25)
    @ExcelProperty("合同编码")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ColumnWidth(25)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 款项id
     */
    private String contractPaymentId;

    /**
     * 款项名称
     * 原合同款项明细
     */
    @ColumnWidth(25)
    @ExcelProperty("款项名称")
    private String contractPayment;

    /**
     * 款项金额
     */
    @ColumnWidth(20)
    @ExcelProperty("款项金额")
    private String paymentMoney;

    /**
     * 认领金额
     */
    @ColumnWidth(20)
    @ExcelProperty("认领金额")
    private String claimMoney;

    /**
     * 回款归属一级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款归属一级部门")
    private String paymentDept;

    /**
     * 回款归属二级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款归属二级部门")
    private String paymentSecondaryDept;

    /**
     * 业务板块
     */
    @ExcelIgnore
    private Integer businessBlock;

    /**
     * 业务板块
     */
    @ColumnWidth(20)
    @ExcelProperty("业务板块")
    private String businessBlockTxt;

    /**
     * 技术类型
     */
    @ExcelIgnore
    private Integer skillType;

    /**
     * 技术类型
     */
    @ColumnWidth(20)
    @ExcelProperty("技术类型")
    private String skillTypeTxt;

    /**
     * 保证金编号
     */
    @ColumnWidth(20)
    @ExcelProperty("保证金编号")
    private String marginCode;

    /**
     * 保证金类型
     */
    @ExcelIgnore
    private Integer marginType;

    /**
     * 保证金类型
     */
    @ColumnWidth(20)
    @ExcelProperty("保证金类型")
    private String marginTypeTxt;

    /**
     * 保证金金额
     */
    @ColumnWidth(20)
    @ExcelProperty("保证金金额")
    private String marginMoney;

    /**
     * 认领日期
     */
    @ExcelProperty("认领日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private String claimantDate;

    /**
     * 认领人id
     */
    @ExcelIgnore
    private Long claimantId;

    /**
     * 认领人姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("认领人")
    private String claimantName;

    /**
     * 认领备注
     */
    @ColumnWidth(20)
    @ExcelProperty("认领备注")
    private String claimRemark;
}
