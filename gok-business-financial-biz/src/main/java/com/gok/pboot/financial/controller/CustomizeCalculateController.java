package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.service.ICustomizeCalculateService;
import com.gok.pboot.financial.vo.CustomizeCalculateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义辅助项控制层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@RestController
@Api(tags = "自定义辅助项")
@RequiredArgsConstructor
@RequestMapping("/customize_calculate")
public class CustomizeCalculateController {

    private final ICustomizeCalculateService customizeCalculateService;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link CustomizeCalculateFindDTO}
     * @return {@link R}<{@link Page}<{@link CustomizeCalculateVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<CustomizeCalculateVO>> findPage(@RequestBody @Valid CustomizeCalculateFindDTO dto) {
        return R.ok(customizeCalculateService.findPage(dto));
    }

    /**
     * 模糊查询带分页
     *
     * @return {@link R}<{@link Page}<{@link CustomizeCalculateVO}>>
     */
    @GetMapping("/list")
    @ApiOperation(value = "下拉列表", notes = "下拉列表")
    public R<List<CustomizeCalculateVO>> findList() {
        return R.ok(customizeCalculateService.findList());
    }

    /**
     * 新增
     *
     * @param dto {@link CustomizeCalculateDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增", notes = "新增")
    public R addOrEdit(@RequestBody @Valid CustomizeCalculateDTO dto) {
        return customizeCalculateService.addOrEdit(dto);
    }

    /**
     * 查看
     *
     * @return {@link CustomizeCalculateVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<CustomizeCalculateVO> info(@PathVariable Long id) {
        return customizeCalculateService.info(id);
    }

    /**
     * 编辑
     *
     * @param dto {@link CustomizeCalculateDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/edit/{id}")
    @ApiOperation(value = "编辑", notes = "编辑")
    public R addOrEdit(@PathVariable Long id,@RequestBody @Valid CustomizeCalculateDTO dto) {
        dto.setId(id);
        return customizeCalculateService.addOrEdit(dto) ;
    }

    /**
     * 删除
     *
     * @param dto {@link CustomizeCalculateIdsDTO}
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R del(@RequestBody CustomizeCalculateIdsDTO dto) {
        return customizeCalculateService.del(dto.getIdList()) ? R.ok() : R.failed();
    }

    /*@PostMapping("/batch")
    @Inner(value = false)
    public  R batch(@RequestBody CustomizeCalculateIdsDTO dto){
        List<CustomizeCalculateDTO> list = dto.getList();
        customizeCalculateService.insertOrUpdateByCode(list);
        return R.ok();
    }*/

}
