package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 跨部门项目人工成本展示
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrossDeptShareCostVO {

    /**
     * 收入归属一级部门id
     */
    private Long incomeDeptId;

    /**
     * 收入归属一级部门
     */
    private String incomeDept;

    /**
     * 项目成本
     */
    private String projectCost;

    /**
     * 所属月份
     */
    private List<YearMonthDateCostVO> yearMonthDateList;
}
