package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 验收报告Enum
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Getter
public enum AcceptanceReportEnum implements ValueEnum<Integer> {

    /**
     * 已验收
     */
    YES(0, "是"),

    /**
     * 未验收
     */
    NO(1, "否");

    private final Integer value;

    private final String name;

    AcceptanceReportEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
