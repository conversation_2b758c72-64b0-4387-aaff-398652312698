package com.gok.pboot.financial.invoice.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 成本科目类型枚举类
 *
 * <AUTHOR>
 * @create 2025/06/20
 **/
@Getter
@AllArgsConstructor
public enum InvoiceStatusEnum implements ValueEnum<Integer> {

    /**
     * 正常
     */
    ZC(0, "正常"),

    /**
     * 作废
     */
    ZF(1, "作废"),

    /**
     * 部分红冲
     */
    BFHC(2, "部分红冲");

    private final Integer value;

    private final String name;

}
