package com.gok.pboot.financial.common;

/**
 * Redis
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public class RedisConstant {

    private RedisConstant() {}

    public static final String APPLICATION_CODE = "FINANCIAL";

    /**
     * 项目回款单据编号
     */
    public static final String PROJECT_DOCUMENT_NUMBER = APPLICATION_CODE + ":DOCUMENT_NUMBER:PROJECT";

    /**
     * 项目回款单据编号
     */
    public static final String PROJECT_DOCUMENT_NUMBER_PREFIX = "XMHK";

    /**
     * 教育回款单据编号
     */
    public static final String EDUCATION_DOCUMENT_NUMBER = APPLICATION_CODE + ":DOCUMENT_NUMBER:EDUCATION";

    /**
     * 教育回款单据编号
     */
    public static final String EDUCATION_DOCUMENT_NUMBERPREFIX = "JYHK";

    /**
     * 用友开放平台AccessToken缓存键
     */
    public static final String YONYOU_ACCESS_TOKEN = APPLICATION_CODE + ":YONYOU:ACCESS_TOKEN5";

    /**
     * 用友开放平台AccessTokenV2缓存键（多数据中心）
     */
    public static final String YONYOU_ACCESS_TOKEN_V2 = APPLICATION_CODE + ":YONYOU:ACCESS_TOKEN_V2";
}
