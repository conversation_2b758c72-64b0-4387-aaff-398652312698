package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 一对多查询封装为同一条行数据
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectRevenueLedgerPushVO {

    /**
     * 流程id
     */
    private Long id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目编号
     */
    private String projectNo;

    /**
     * 流程编码
     */
    private String processCoding;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 收入日期
     */
    private String incomeDate;

    /**
     * 收入金额（含税）
     */
    private String incomeAmountTax;

    /**
     * 收入金额（不含税）
     */
    private String incomeAmount;

    /**
     * 验收日期
     */
    private String acceptanceDate;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 核算部门
     */
    private Long virtualDeptId;

    /**
     * 核算部门
     */
    private String virtualDeptName;

    /**
     * 业务归属一级部门id
     */
    private Long businessFirstDeptId;

    /**
     * 业务归属一级部门
     */
    private String businessFirstDept;

    /**
     * 业务归属二级部门id
     */
    private Long businessSecondaryDeptId;

    /**
     * 业务归属二级部门
     */
    private String businessSecondaryDept;


    /**
     * 发票明细 开票主体
     */
    private String invoicingSubject;
}
