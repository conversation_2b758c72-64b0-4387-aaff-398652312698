package com.gok.pboot.financial.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 新增指标科目
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IndicatorAccountRelationshipDTO {

    /**
     * 导出表/删除 所需要的id集合
     */
    @ExcelIgnore
    private List<Long> ids;

    /**
     * 更新表需要的id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 科目编码
     */
    @ExcelProperty("科目编码")
    private String code;

    /**
     * 科目名称
     */
    @ExcelProperty("科目名称")
    @NotBlank(message = "科目名称不能为空")
    private String name;

    /**
     * 核算部门编码
     */
    @ExcelProperty("核算部门编码")
    private String accountingDepartmentCode;

    /**
     * 核算部门
     */
    @ExcelProperty("核算部门")
    @NotBlank(message = "核算部门不能为空")
    private String accountingDepartment;


}
