package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.AccountsReceivable;
import com.gok.pboot.financial.db.mapper.AccountsReceivableMapper;
import com.gok.pboot.financial.dto.AccountsReceivableDTO;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IAccountsReceivableService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.AccountsReceivableVO;
import com.gok.pboot.financial.vo.OaDictVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收账款台账 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountsReceivableServiceImpl extends ServiceImpl<AccountsReceivableMapper, AccountsReceivable>
        implements IAccountsReceivableService {

    /**
     * 手动刷新工作流url
     */
    @Value("${dolphin.url}")
    private String dolphinUrl;

    /**
     * 海豚调度token
     */
    @Value("${dolphin.token}")
    private String dolphinToken;

    /**
     * 海豚调度项目id
     */
    @Value("${dolphin.projectId}")
    private String dolphinProjectId;

    /**
     * 海豚调度执行id
     */
    @Value("${dolphin.processDefinitionCode}")
    private String dolphinProcessDefinitionCode;

    private final LogRecordUtils logRecordUtils;

    private final DBApi dbApi;

    /**
     * 模糊查询带分页
     *
     * @param page 分页请求
     * @param accountsReceivableDto 模糊查询属性封装实体
     * @return {@link Page}<{@link AccountsReceivableVO}>
     */
    @Override
    public StatisticsPage<AccountsReceivableVO> findPage(Page<AccountsReceivable> page, AccountsReceivableDTO accountsReceivableDto) {
        // 1、条件查询封装
        this.condition(accountsReceivableDto);

        // 2、分页（V1.1.1优化需要查询当期数据）


        if (CharSequenceUtil.isNotBlank(accountsReceivableDto.getYearMonth())) {
            baseMapper.findPageByNow(page, accountsReceivableDto);
        } else {
             baseMapper.findPage(page, accountsReceivableDto);
        }

        StatisticsPage<AccountsReceivableVO> voPage = new StatisticsPage<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<AccountsReceivable> accountsReceivableList = page.getRecords();
        if (CollUtil.isEmpty(accountsReceivableList)) {
            return voPage;
        }
        voPage.setRecords(this.addVOToList(accountsReceivableList, true));
        statistics(voPage,accountsReceivableDto);
        return voPage;
    }

    private void statistics(StatisticsPage<AccountsReceivableVO> voPage, AccountsReceivableDTO accountsReceivableDto) {
        List<AccountsReceivable> accountsReceivableList;
        if (CharSequenceUtil.isNotBlank(accountsReceivableDto.getYearMonth())) {
            accountsReceivableList = baseMapper.findPageByNow(accountsReceivableDto);
        } else {
            accountsReceivableList = baseMapper.findPage(accountsReceivableDto);
        }
        BigDecimal zero = BigDecimal.ZERO;
        Map<String, BigDecimal> resultMap = new HashMap<>(12);
        resultMap.put("invoicingTotal", zero);
        resultMap.put("payBackTotal", zero);
        resultMap.put("revenueTotal", zero);
        resultMap.put("withinThirtyTotal", zero);
        resultMap.put("thirtySixtyTotal", zero);
        resultMap.put("sixtyHundredTwentyTotal", zero);
        resultMap.put("aboveHundredTwentyTotal", zero);
        resultMap.put("customerInvoicingTotal", zero);
        resultMap.put("customerCollectedTotal", zero);
        resultMap.put("invoicingCollectedTotal", zero);
        resultMap.put("revenueCollectedTotal", zero);
        resultMap.put("contractMoneyTotal", zero);
        accountsReceivableList.forEach(item -> {
            BigDecimal invoicing = item.getInvoicing() == null ? zero : item.getInvoicing();
            resultMap.merge("invoicingTotal", invoicing, BigDecimal::add);

            BigDecimal payBack = item.getPayBack() == null ? zero : item.getPayBack();
            resultMap.merge("payBackTotal", payBack, BigDecimal::add);

            BigDecimal revenue = item.getRevenue() == null ? zero : item.getRevenue();
            resultMap.merge("revenueTotal", revenue, BigDecimal::add);

            BigDecimal withinThirty = item.getWithinThirty() == null ? zero : item.getWithinThirty();
            resultMap.merge("withinThirtyTotal", withinThirty, BigDecimal::add);
            
            BigDecimal thirtySixty = item.getThirtySixty() == null ? zero : item.getThirtySixty();
            resultMap.merge("thirtySixtyTotal", thirtySixty, BigDecimal::add);

            BigDecimal sixtyHundredTwenty = item.getSixtyHundredTwenty() == null ? zero : item.getSixtyHundredTwenty();
            resultMap.merge("sixtyHundredTwentyTotal", sixtyHundredTwenty, BigDecimal::add);

            BigDecimal aboveHundredTwenty = item.getAboveHundredTwenty() == null ? zero : item.getAboveHundredTwenty();
            resultMap.merge("aboveHundredTwentyTotal", aboveHundredTwenty, BigDecimal::add);

            BigDecimal customerInvoicing = item.getCustomerInvoicing() == null ? zero : item.getCustomerInvoicing();
            resultMap.merge("customerInvoicingTotal", customerInvoicing, BigDecimal::add);

            BigDecimal customerCollected = item.getCustomerCollected() == null ? zero : item.getCustomerCollected();
            resultMap.merge("customerCollectedTotal", customerCollected, BigDecimal::add);

            BigDecimal invoicingCollected = item.getInvoicingCollected() == null ? zero : item.getInvoicingCollected();
            resultMap.merge("invoicingCollectedTotal", invoicingCollected, BigDecimal::add);

            BigDecimal revenueCollected = item.getRevenueCollected() == null ? zero : item.getRevenueCollected();
            resultMap.merge("revenueCollectedTotal", revenueCollected, BigDecimal::add);

            BigDecimal contractMoney = item.getContractMoney() == null ? zero : item.getContractMoney();
            resultMap.merge("contractMoneyTotal", contractMoney, BigDecimal::add);
        });
        voPage.setStatistics(resultMap);
    }

    /**
     * 导出Excel选中数据
     *
     * @param accountsReceivableDTO {@link AccountsReceivableDTO}
     * @return {@link List}<{@link AccountsReceivableVO}>
     */
    @Override
    public List<AccountsReceivableVO> export(AccountsReceivableDTO accountsReceivableDTO) {
        long startTime = System.currentTimeMillis();
        // 1、条件查询封装
        this.condition(accountsReceivableDTO);
        List<AccountsReceivable> accountsReceivableList;
        if (CharSequenceUtil.isNotBlank(accountsReceivableDTO.getYearMonth())) {
            accountsReceivableList = baseMapper.exportListByNow(accountsReceivableDTO);
        } else {
            accountsReceivableList = baseMapper.exportList(accountsReceivableDTO);
        }
        // 2、记录日志
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTS_RECEIVABLE,
                OperationEnum.EXPORT, OperationEnum.EXPORT.getName() + "【" + accountsReceivableList.size() + "条】");

        List<AccountsReceivableVO> accountsReceivableVOList = new ArrayList<>();
        if (CollUtil.isEmpty(accountsReceivableList)) {
            accountsReceivableVOList.add(new AccountsReceivableVO());
        } else {
            accountsReceivableVOList = this.addVOToList(accountsReceivableList, false);
        }

        log.info("应收账款导出Excel:{}ms", (System.currentTimeMillis() - startTime));
        return accountsReceivableVOList;
    }

    /**
     * 手动同步刷新数据
     *
     * @return {@link R}
     */
    @Override
    public R<String> refresh() {
        final String url = dolphinUrl + dolphinProjectId + "/executors/start-process-instance";
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        PostMethod postMethod = new PostMethod(url);
        postMethod.setRequestHeader("token", dolphinToken) ;
        postMethod.setRequestHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8") ;
        NameValuePair[] data = {
                new NameValuePair("processDefinitionCode", dolphinProcessDefinitionCode),
                new NameValuePair("failureStrategy", "CONTINUE"),
                new NameValuePair("warningType", "NONE"),
                new NameValuePair("execType", "START_PROCESS"),
                new NameValuePair("taskDependType", "TASK_POST"),
                new NameValuePair("complementDependentMode", "OFF_MODE"),
                new NameValuePair("runMode", "RUN_MODE_SERIAL"),
                new NameValuePair("processInstancePriority", "MEDIUM"),
                new NameValuePair("dryRun", "0"),
                new NameValuePair("scheduleTime", "{" + "complementStartDate" + ":" + date + ", " + "complementEndDate" + ":" + date + "}")
        };
        
        postMethod.setRequestBody(data);
        try {
            new HttpClient().executeMethod(postMethod);
            log.info("responseBody:{}", postMethod.getResponseBodyAsString());
            // 推送日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTS_RECEIVABLE,
                    OperationEnum.REFRESH, OperationEnum.REFRESH.getName());
        } catch (IOException e) {
            return R.failed("手动刷新失败");
        }
        return R.ok("手动刷新成功");
    }

    /**
     * 应收账款封装
     *
     * @param accountsReceivableList 封装集合
     * @param isPage 是否分页
     * @return {@link List}<{@link AccountsReceivableVO}>
     */
    private List<AccountsReceivableVO> addVOToList(List<AccountsReceivable> accountsReceivableList, boolean isPage) {
        List<AccountsReceivableVO> accountsReceivableVOList = Collections.synchronizedList(new ArrayList<>());
        List<OaDictVO> oaDictVOList = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOList.stream()
                .collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));
        accountsReceivableList.forEach(a ->
                accountsReceivableVOList.add(this.accountsReceivableSetScale(a,attributableSubjectMap))
        );
        Comparator<AccountsReceivableVO> comparator = Comparator.comparing(AccountsReceivableVO::getProjectNo,
                Comparator.nullsLast(String::compareTo)).reversed();
        accountsReceivableVOList.sort(comparator);

        return accountsReceivableVOList;
    }

    /**
     * 分页导出条件查询封装
     *
     * @param accountsReceivableDTO {@link AccountsReceivableDTO}
     */
    private void condition(AccountsReceivableDTO accountsReceivableDTO) {
        // 账龄区间
        if (Optional.ofNullable(accountsReceivableDTO.getAging()).isPresent()
                && (accountsReceivableDTO.getAging().length == NumberUtils.INTEGER_ZERO
                || accountsReceivableDTO.getAging()[NumberUtils.INTEGER_ZERO] != null)) {
                Integer[] aging = accountsReceivableDTO.getAging();
                String[] agingArr = new String[aging.length];
                for (int i = 0; i < aging.length; ++i) {
                    agingArr[i] = EnumUtils.getNameByValue(AgingEnum.class, aging[i]);
                }
                accountsReceivableDTO.setAgingTxt(agingArr);

        }
        // 排序规则（默认项目编码降序）
        if (Optional.ofNullable(accountsReceivableDTO.getSort()).isPresent() && Optional.ofNullable(accountsReceivableDTO.getSortRule()).isPresent()) {
            accountsReceivableDTO.setSortTxt(EnumUtils.getNameByValue(SortEnum.class, accountsReceivableDTO.getSort()));
            accountsReceivableDTO.setSortRuleTxt(EnumUtils.getNameByValue(SortRuleEnum.class, accountsReceivableDTO.getSortRule()));
        } else {
            accountsReceivableDTO.setSortTxt(SortEnum.DEFAULT.getName());
            accountsReceivableDTO.setSortRuleTxt(SortRuleEnum.DESC.getName());
        }
        // 主体名称转为code
        String attributableSubject = accountsReceivableDTO.getAttributableSubject();
        if (CharSequenceUtil.isNotBlank(attributableSubject)) {
            List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
            Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(a, b)->a));
            accountsReceivableDTO.setAttributableSubject(attributableSubjectMap.getOrDefault(Integer.valueOf(attributableSubject), StrUtil.EMPTY));
        }
    }

    /**
     * 字段保留两位小数返回
     *
     * @param a                      {@link AccountsReceivable}
     * @param attributableSubjectMap 可归属主题图
     * @return {@link AccountsReceivableVO}
     */
    private AccountsReceivableVO accountsReceivableSetScale(AccountsReceivable a, Map<Integer, String> attributableSubjectMap) {
        AccountsReceivableVO vo = BeanUtil.copyProperties(a, AccountsReceivableVO.class);
        vo.setWithinThirty(MoneyUtils.getInstance().transType(a.getWithinThirty()));
        vo.setThirtySixty(MoneyUtils.getInstance().transType(a.getThirtySixty()));
        vo.setSixtyHundredTwenty(MoneyUtils.getInstance().transType(a.getSixtyHundredTwenty()));
        vo.setAboveHundredTwenty(MoneyUtils.getInstance().transType(a.getAboveHundredTwenty()));
        vo.setPayBack(MoneyUtils.getInstance().transType(a.getPayBack()));
        vo.setInvoicing(MoneyUtils.getInstance().transType(a.getInvoicing()));
        vo.setRevenueCollected(MoneyUtils.getInstance().transType(a.getRevenueCollected()));
        vo.setInvoicingCollected(MoneyUtils.getInstance().transType(a.getInvoicingCollected()));
        vo.setCustomerCollected(MoneyUtils.getInstance().transType(a.getCustomerCollected()));
        vo.setCustomerInvoicing(MoneyUtils.getInstance().transType(a.getCustomerInvoicing()));
        vo.setContractMoney(MoneyUtils.getInstance().transType(a.getContractMoney()));
        vo.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, a.getProjectStatus()));
        vo.setAttributableSubjectTxt(Optional.ofNullable(a.getAttributableSubject()).isPresent() ?
                attributableSubjectMap.getOrDefault(Integer.valueOf(a.getAttributableSubject()),StrUtil.EMPTY)
                : StrUtil.EMPTY);
        return vo;
    }

}
