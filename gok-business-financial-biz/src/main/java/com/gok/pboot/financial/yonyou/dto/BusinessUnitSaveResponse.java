/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 业务单元保存响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 业务单元保存响应DTO
 * 业务单元保存接口的返回数据结构
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessUnitSaveResponse {

    /**
     * 业务单元ID
     */
    private String id;

    /**
     * 业务单元编码
     */
    private String code;

    /**
     * 业务单元名称，支持多语
     */
    private MultiLangName name;

    /**
     * 父业务单元编码
     */
    private String parent_code;

    /**
     * 启用状态（1:启用，0:停用）
     */
    private Integer enable;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序号
     */
    private Integer sortno;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private String creationtime;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 修改时间
     */
    private String modifiedtime;

    /**
     * 时间戳,格式为:yyyy-MM-dd HH:mm:ss
     */
    private String pubts;

    /**
     * 多语言名称
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {

        /**
         * 中文名称
         */
        private String zh_CN;

        /**
         * 英文名称
         */
        private String en_US;
    }
}