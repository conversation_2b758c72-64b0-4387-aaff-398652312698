package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 根据认领日期自动锁定枚举
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Getter
public enum AutoLockEnum implements ValueEnum<Integer> {

    /**
     * 已锁定
     */
    YES(0, "自动"),

    /**
     * 待锁定
     */
    NO(1, "取消");

    private final Integer value;

    private final String name;

    AutoLockEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
