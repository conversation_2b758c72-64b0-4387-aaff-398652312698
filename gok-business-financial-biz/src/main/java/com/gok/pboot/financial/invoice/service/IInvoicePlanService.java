/*
 * @Author: <PERSON> <EMAIL>
 * @Date: 2025-06-03 16:08:15
 * @LastEditors: <PERSON> <EMAIL>
 * @LastEditTime: 2025-06-11 15:15:40
 * @FilePath: \display-platform\gok-business-financial\gok-business-financial-biz\src\main\java\com\gok\pboot\financial\invoice\service\IInvoicePlanService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package com.gok.pboot.financial.invoice.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.domain.InvoicePlan;
import com.gok.pboot.financial.invoice.entity.dto.InvoicePlanDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoicePlanVO;

import java.util.List;


/**
 * 发票规划台账服务类
 *
 * <AUTHOR>
 * @create 2025/05/29
 **/
public interface IInvoicePlanService extends IService<InvoicePlan> {

    /**
     * 分页查询发票规划列表
     *
     * @param dto 查询请求
     * @return 分页结果
     */
    StatisticsPage<InvoicePlanVO> findPage(InvoicePlanDTO dto);

    /**
     * 导出发票规划列表
     *
     * @param dto 查询请求
     * @return
     */
    List<InvoicePlanVO> exportExcel(InvoicePlanDTO dto);

    /**
     * 申请开票
     *
     * @param id
     * @return
     */
    R<String> applyInvoice(Long id);

}
