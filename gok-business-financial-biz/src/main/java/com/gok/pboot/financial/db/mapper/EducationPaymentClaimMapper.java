package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.db.entity.EducationPaymentClaim;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 教育回款Mapper
 *
 * <AUTHOR>
 * @since 2023-10-07
 */
public interface EducationPaymentClaimMapper extends BaseMapper<EducationPaymentClaim> {

    /**
     * 批量插入
     *
     * @param educationPaymentClaimList {@link List}<{@link EducationPaymentClaim}>
     * @return {@code true} or {@code false}
     */
    boolean saveBatch(@Param("list") List<EducationPaymentClaim> educationPaymentClaimList);
}