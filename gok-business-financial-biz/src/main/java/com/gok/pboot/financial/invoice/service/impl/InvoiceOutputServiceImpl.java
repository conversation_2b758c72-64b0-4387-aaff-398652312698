package com.gok.pboot.financial.invoice.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.admin.feign.RemoteBcpDictService;
import com.gok.bcp.admin.vo.DictKvVo;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.domain.InvoiceOutput;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputVO;
import com.gok.pboot.financial.invoice.enums.InvoiceStatusEnum;
import com.gok.pboot.financial.invoice.mapper.InvoiceOutputMapper;
import com.gok.pboot.financial.invoice.service.IInvoiceOutputService;
import com.gok.pboot.financial.service.IFinancialDocImageFileService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.vo.OaFileInfoVo;
import com.google.common.collect.ImmutableList;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/06/09
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class InvoiceOutputServiceImpl extends ServiceImpl<InvoiceOutputMapper, InvoiceOutput> implements IInvoiceOutputService {

    private final RemoteBcpDictService remoteBcpDictService;
    private final IFinancialDocImageFileService pmsDocImageFileService;

    @Override
    public StatisticsPage<InvoiceOutputVO> findPage(InvoiceOutputDTO dto) {
        Page<InvoiceOutputVO> invoiceOutputPage = baseMapper.findPage(new Page<>(dto.getCurrent(), dto.getSize()), dto);
        StatisticsPage<InvoiceOutputVO> statisticsPage =
                new StatisticsPage<>(invoiceOutputPage.getCurrent(), invoiceOutputPage.getSize(), invoiceOutputPage.getTotal());
        List<InvoiceOutputVO> records = invoiceOutputPage.getRecords();
        if (CollUtil.isEmpty(records)) {
            return statisticsPage;
        }

        Map<String, BigDecimal> pageSum = baseMapper.findPageSum(dto);
        statisticsPage.setStatistics(pageSum);

        handleInvoiceOutputVO(records);
        statisticsPage.setRecords(records);
        return statisticsPage;
    }

    @Override
    public List<InvoiceOutputVO> exportExcel(InvoiceOutputDTO dto) {
        dto.setCurrent(0L);
        dto.setSize(Long.MAX_VALUE);
        return CollUtil.emptyIfNull(findPage(dto).getRecords());
    }

    @Override
    public List<OaFileInfoVo> getAttachments(Long id) {
        InvoiceOutput invoiceOutput = baseMapper.selectById(id);
        if (null == invoiceOutput || StrUtil.isBlank(invoiceOutput.getAttachment())) {
            return ListUtil.empty();
        }

        List<OaFileInfoVo> oaFileVoList = pmsDocImageFileService.getOaFileVoList(invoiceOutput.getAttachment());

        return oaFileVoList;
    }

    /**
     * 批量处理InvoicePlanVO
     *
     * @param voList InvoicePlanVO集合
     */
    private void handleInvoiceOutputVO(List<InvoiceOutputVO> voList) {
        if (CollUtil.isEmpty(voList)) {
            return;
        }

        // 获取中台字典
        Set<String> dictKeys = new HashSet<>(Arrays.asList("税率", "开票类型", "商品/服务分类", "所属公司", "结算方式", "红蓝字", "收款平台"));
        Map<String, List<DictKvVo>> allDictMap = remoteBcpDictService.getDictKvBatchList(dictKeys).getData();
        Map<Integer, String> taxRateMap = allDictMap.getOrDefault("税率", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> invoiceTypeMap = allDictMap.getOrDefault("开票类型", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> goodsServiceNameMap = allDictMap.getOrDefault("商品/服务分类", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> contractSubjectMap = allDictMap.getOrDefault("所属公司", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> settlementMethodMap = allDictMap.getOrDefault("结算方式", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> redAndBlueMap = allDictMap.getOrDefault("红蓝字", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));
        Map<Integer, String> receiptPlatformMap = allDictMap.getOrDefault("收款平台", ImmutableList.of()).stream()
                .collect(Collectors.toMap(
                        key -> Integer.parseInt(key.getValue()),
                        DictKvVo::getName));

        // 数字处理成千分位
        DecimalFormat formatter = new DecimalFormat("##,##0.00");

        for (InvoiceOutputVO vo : voList) {
            vo.setRedAndBlueTxt(null != vo.getRedAndBlue() ? redAndBlueMap.get(vo.getRedAndBlue()) : null);
            vo.setInvoiceTypeTxt(null != vo.getInvoiceType() ? invoiceTypeMap.get(vo.getInvoiceType()) : null);
            vo.setReceiptPlatformTxt(null != vo.getReceiptPlatform() ? receiptPlatformMap.get(vo.getReceiptPlatform()) : null);
            vo.setContractSubjectTxt(null != vo.getContractSubject() ? contractSubjectMap.get(vo.getContractSubject()) : null);
            vo.setGoodsServiceNameTxt(null != vo.getGoodsServiceName() ? goodsServiceNameMap.get(vo.getGoodsServiceName()) : null);
            vo.setPlanTaxRateTxt(Optional.ofNullable(vo.getPlanTaxRate()).isPresent() ? taxRateMap.get(vo.getPlanTaxRate()) : null);
            vo.setActualTaxRateTxt(Optional.ofNullable(vo.getActualTaxRate()).isPresent() ? taxRateMap.get(vo.getActualTaxRate()) : null);
            vo.setContractSettlementTypeTxt(null != vo.getContractSettlementType() ? settlementMethodMap.get(vo.getContractSettlementType()) : null);
            vo.setPlanAmountIncludedTaxTxt(null != vo.getPlanAmountIncludedTax() ? formatter.format(vo.getPlanAmountIncludedTax()) : null);
            vo.setPlanAmountExcludingTaxTxt(null != vo.getPlanAmountExcludingTax() ? formatter.format(vo.getPlanAmountExcludingTax()) : null);
            vo.setActualAmountIncludedTaxTxt(null != vo.getActualAmountIncludedTax() ? formatter.format(vo.getActualAmountIncludedTax()) : null);
            vo.setActualAmountExcludingTaxTxt(null != vo.getActualAmountExcludingTax() ? formatter.format(vo.getActualAmountExcludingTax()) : null);
            vo.setInvoiceStatusTxt(EnumUtils.getNameByValue(InvoiceStatusEnum.class, vo.getInvoiceStatus()));
        }
    }

}
