package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.dto.AccountingSetPageDTO;
import com.gok.pboot.financial.vo.AccountingSetVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会计账套数据访问层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface AccountingSetMapper extends BaseMapper<AccountingSet> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}
     * @param pageDTO {@link AccountingSetPageDTO}
     * @return {@link Page}<{@link AccountingSet}>
     */
    Page<AccountingSetVO> findPage(Page<AccountingSet> page, @Param("query") AccountingSetPageDTO pageDTO);

    /**
     * 模糊查询导出列表
     * @param pageDTO
     * @return
     */
    List<AccountingSetVO> findPage(@Param("query") AccountingSetPageDTO pageDTO);

    /**
     * 删除
     * @param idList
     * @return
     */
    Boolean delByIds(@Param("idList") List<Long> idList);

    /**
     * 查看
     * @param id
     * @return
     */
    AccountingSetVO selById(@Param("id")  Long id);
}