package com.gok.pboot.financial.db.entity;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;


import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 工资成本
 *
 * <AUTHOR>
 * @since 2023-09-04
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("wage_cost")
@ApiModel("工资成本")
public class WageCost extends Model<WageCost> {

    /**
     * 归属月份
     */
    @ApiModelProperty("归属月份")
    private String yearMonthDate;

    /**
     * 当前部门id
     */
    @ApiModelProperty("当前部门id")
    private Long currentDeptId;

    /**
     * 当前部门
     */
    @ApiModelProperty("当前部门")
    private String currentDept;


    /**
     * 一级部门id
     */
    @ApiModelProperty("一级部门id")
    private Long primaryDeptId;

    /**
     * 一级部门
     */
    @ApiModelProperty("一级部门")
    private String primaryDept;

    /**
     * 二级部门id
     */
    @ApiModelProperty("二级部门id")
    private Long secondaryDeptId;

    /**
     * 二级部门
     */
    @ApiModelProperty("二级部门")
    private String secondaryDept;

    /**
     * 三级部门id
     */
    @ApiModelProperty("三级部门id")
    private Long tertiaryDeptId;

    /**
     * 三级部门
     */
    @ApiModelProperty("三级部门")
    private String tertiaryDept;

    /**
     * 应发合计
     */
    @ApiModelProperty("应发合计")
    private String salary;

    /**
     * 个人所得税
     */
    @ApiModelProperty("个人所得税")
    private String individualIncomeTax;

    /**
     * 个人社保
     */
    @ApiModelProperty("个人社保")
    private String individualSocialSecurity;

    /**
     * 个人公积金
     */
    @ApiModelProperty("个人公积金")
    private String individualProvidentFund;

    /**
     * 补发/补扣
     */
    @ApiModelProperty("补发/补扣")
    private String backPay;

    /**
     * 上月个税差异
     */
    @ApiModelProperty("上月个税差异")
    private String incomeTaxDifferenceFromLastMonth;

    /**
     * 实发合计
     */
    @ApiModelProperty("实发合计")
    private String actualPay;

    /**
     * 单位社保
     */
    @ApiModelProperty("单位社保")
    private String unitSocialSecurity;

    /**
     * 单位公积金
     */
    @ApiModelProperty("单位公积金")
    private String unitProvidentFund;

    /**
     * 残保金
     */
    @ApiModelProperty("残保金")
    private String disabledSecurityFund;


    /**
     * 工资成本
     */
    @ApiModelProperty("工资成本")
    private String payrollCost;

    /**
     * 协议补偿
     */
    @ApiModelProperty("协议补偿")
    private String agreementCompensation;

    /**
     * 实发协议补偿
     */
    @ApiModelProperty("实发协议补偿")
    private String actualAgreementCompensation;

    /**
     * 绩效工资
     */
    @ApiModelProperty("绩效工资")
    private String performance;

    /**
     * 实发绩效工资
     */
    @ApiModelProperty("实发绩效工资")
    private String actualPerformance;

    /**
     * 实付工资总计
     */
    @ApiModelProperty("实付工资总计")
    private String actualPaySum;

    /**
     * 人数
     */
    @ApiModelProperty("人数")
    private Integer peopleNum;

    /**
     * 推送状态
     */
    @ApiModelProperty("推送状态")
    private Integer pushStatus;

    /**
     * 发薪主体
     */
    @ApiModelProperty("发薪主体")
    private String salaryPaidSubject;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 逻辑删除
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("逻辑删除")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}
