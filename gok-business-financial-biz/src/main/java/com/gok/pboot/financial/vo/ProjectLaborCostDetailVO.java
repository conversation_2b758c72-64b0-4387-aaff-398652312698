package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 项目人工成本分摊明细 展示
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ProjectLaborCostDetailVO {

    /**
     * id
     */
    @ExcelIgnore
    private String id;
    /**
     * id
     */
    @ExcelIgnore
    private Long userId;
    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 所属月份
     */
    @ColumnWidth(15)
    @ExcelProperty("所属月份")
    private String yearMonthDate;

    /**
     * 项目编号
     */
    @ColumnWidth(25)
    @ExcelProperty("项目编码")
    private String projectNo;

    /**
     * 项目名称
     */
    @ColumnWidth(60)
    @ExcelProperty("项目名称")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT)
    private String projectName;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    @ColumnWidth(15)
    @ExcelProperty("项目状态")
    private String projectStatusTxt;

    /**
     * 业务归属一级部门id
     */
    @ExcelIgnore
    private Long incomeDeptId;

    /**
     * 业务归属一级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("业务归属一级部门")
    private String incomeDept;

    /**
     * 业务归属二级部门id
     */
    @ExcelIgnore
    private Long incomeSecondaryDeptId;

    /**
     * 业务归属二级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("业务归属二级部门")
    private String incomeSecondaryDept;

    /**
     * 姓名
     */
    @ColumnWidth(15)
    @ExcelProperty("姓名")
    private String name;

    /**
     * 出勤天数
     */
    @ColumnWidth(15)
    @ExcelProperty("出勤天数")
    private BigDecimal daysAttendance;

    /**
     * 项目耗用天数
     */
    @ColumnWidth(20)
    @ExcelProperty("项目耗用天数")
    private BigDecimal expendDays;

    /**
     * 加班天数
     */
    @ColumnWidth(15)
    @ExcelProperty("加班天数")
    private BigDecimal addDays;

    /**
     * 税前工资
     */
    @ColumnWidth(20)
    @ExcelProperty("税前工资")
    private String salary;

    /**
     * 单位社保/公司承担社保
     */
    @ColumnWidth(20)
    @ExcelProperty("公司承担社保")
    private String unitSocialSecurity;

    /**
     * 单位公积金/公司承担公积金
     */
    @ColumnWidth(20)
    @ExcelProperty("公司承担公积金")
    private String unitProvidentFund;

    /**
     * 工资成本
     */
    @ColumnWidth(20)
    @ExcelProperty("工资成本")
    private String payrollCost;

    /**
     * 社保成本
     */
    @ColumnWidth(20)
    @ExcelProperty("社保成本")
    private String socialSecurityCost;

    /**
     * 公积金成本
     */
    @ColumnWidth(20)
    @ExcelProperty("公积金成本")
    private String providentFundCost;

    /**
     * 合计成本
     */
    @ColumnWidth(20)
    @ExcelProperty("合计成本")
    private String costSum;

    /**
     * 发薪主体/工资发放主体
     */
    @ColumnWidth(20)
    @ExcelProperty("发薪主体")
    private String salaryPaidSubject;

    /**
     * 人员归属二级部门id
     */
    @ExcelIgnore
    private Long personnelSecondaryDeptId;

    /**
     * 人员归属二级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("人员归属二级部门")
    private String personnelSecondaryDept;

    /**
     * 人员归属一级部门id
     */
    @ExcelIgnore
    private Long personnelDeptId;

    /**
     * 人员归属一级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("人员归属一级部门")
    private String personnelDept;

    /**
     * 项目类型
     * {@link com.gok.pboot.financial.enums.ProjectTypeEnum}
     */
    @ExcelIgnore
    private Integer projectType;

    /**
     * 项目类型
     * {@link com.gok.pboot.financial.enums.ProjectTypeEnum}
     */
    @ColumnWidth(15)
    @ExcelProperty("项目类型")
    private String projectTypeTxt;

    /**
     * 人员类型
     * {@link com.gok.pboot.financial.enums.PersonnelTypeEnum}
     */
    @ExcelIgnore
    private Integer personnelType;

    /**
     * 人员类型
     * {@link com.gok.pboot.financial.enums.PersonnelTypeEnum}
     */
    @ColumnWidth(15)
    @ExcelProperty("人员类型")
    private String personnelTypeTxt;

}
