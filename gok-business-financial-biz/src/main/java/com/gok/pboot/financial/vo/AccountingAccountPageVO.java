package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.gok.module.excel.api.annotation.ExcelSelected;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 科目类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingAccountPageVO {
    /**
    * id
    */
    @ExcelIgnore
    private Long id;

    /**
     * 级次
     */
    @ColumnWidth(20)
    @ExcelProperty("级次")
    private Integer level;

    /**
     * 科目编码
     */
    @ColumnWidth(20)
    @ExcelProperty("科目编码")
    private String accountCode;

    /**
     * 科目名称
     */
    @ColumnWidth(20)
    @ExcelProperty("科目名称")
    private String accountName;

    /**
     * 助记词
     */
    @ColumnWidth(20)
    @ExcelProperty("助记词")
    private String mnemonicWords;

    /**
     * 科目类型id
     */
    @ExcelIgnore
    private Long accountTypeId;

    /**
     * 科目类型
     */
    @ColumnWidth(20)
    @ExcelProperty("科目类型")
    private String accountType;

    /**
     * 余额方向
     */
    @ExcelIgnore
    private Integer balanceDirection;

    /**
     * 余额方向
     */
    @ColumnWidth(20)
    @ExcelProperty("余额方向")
    @ExcelSelected(source = {"借方","贷方"})
    private String balanceDirectionTxt;

    /**
     * 辅助核算项集合（,分割）
     */
    @ColumnWidth(20)
    @ExcelProperty("辅助核算项集合")
    private String auxiliaryCalculateItems;

    /**
     * 账页格式
     */
    @ExcelIgnore
    private Integer accountPageFormat;

    /**
     * 账页格式
     */
    @ColumnWidth(20)
    @ExcelProperty("账页格式")
    @ExcelSelected(source = {"金额式","外币金额式"})
    private String accountPageFormatTxt;

    /**
     * 现金分类
     */
    @ExcelIgnore
    private Integer cashClassification;

    /**
     * 现金分类
     */
    @ColumnWidth(20)
    @ExcelProperty("现金分类")
    @ExcelSelected(source = {"现金科目","银行科目","现金等价物"})
    private String cashClassificationTxt;

    /**
     * 会计体系id
     */
    @ExcelIgnore
    private Long accountingSystemId;

    /**
     * 会计体系
     */
    @ColumnWidth(20)
    @ExcelProperty("会计体系")
    private String accountingSystem;


    /**
     * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
     */
    @ExcelIgnore
    private Integer enableStatus;

    /**
     * 启用状态（0启用；1禁用）停用状态（0 否， 1是）
     */
    @ColumnWidth(20)
    @ExcelProperty("停用状态")
    @ExcelSelected(source = {"是","否"})
    private String enableStatusTxt;
}