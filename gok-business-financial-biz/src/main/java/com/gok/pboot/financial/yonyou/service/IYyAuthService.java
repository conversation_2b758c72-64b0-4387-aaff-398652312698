/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 用友认证服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.service;

import com.gok.pboot.financial.yonyou.model.AccessTokenResponse;
import com.gok.pboot.financial.yonyou.model.DcUrlResult;
import com.gok.pboot.financial.yonyou.model.RefreshTokenResponse;
import com.gok.pboot.financial.yonyou.model.UserTokenResponse;

import java.io.IOException;

/**
 * 用友认证服务接口
 * 提供用友开放平台认证相关的业务服务
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface IYyAuthService {

    /**
     * 多数据中心适配之前通过appKey获取accessToken
     *
     * @return {@link AccessTokenResponse} 访问令牌响应
     */
    String getAccessToken() ;

    /**
     * 多数据中心适配之后通过tenantId和appKey获取accessToken
     *
     * @return {@link AccessTokenResponse} 访问令牌响应
     */
    AccessTokenResponse getAccessTokenV2();

    /**
     * 通过appkey获取租户id
     *
     * @return {@link String} 租户ID
     */
    String getTenantId();

    /**
     * 通过accessToken和友空间免登code获取userToken
     *
     * @param accessToken 开放平台accessToken
     * @param code        友空间免登code
     * @return {@link UserTokenResponse} 用户令牌响应
     * @throws Exception 异常
     */
    UserTokenResponse getUserToken(String accessToken, String code) throws Exception;

    /**
     * 使用refreshToken刷新userToken
     *
     * @param refreshToken 刷新令牌
     * @return {@link RefreshTokenResponse} 刷新令牌响应
     * @throws Exception 异常
     */
    RefreshTokenResponse refreshUserToken(String refreshToken) throws Exception;

    DcUrlResult getGateway(String tenantId) throws IOException;
}