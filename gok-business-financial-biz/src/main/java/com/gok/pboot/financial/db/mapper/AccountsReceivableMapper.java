package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.db.entity.AccountsReceivable;
import com.gok.pboot.financial.dto.AccountsReceivableDTO;
import com.gok.pboot.financial.vo.AccountsReceivableVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 应收账款台账 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
public interface AccountsReceivableMapper extends BaseMapper<AccountsReceivable> {

    /**
     * 模糊查询带分页
     *
     * @param page 分页请求
     * @param accountsReceivableDto 模糊查询属性封装实体
     * @return {@link Page}<{@link AccountsReceivable}>
     */
    @BusinessDataScope(deptOrUser = "user",
            scopeUserNameList = {"salesman_user_id","manager_user_id"})
    Page<AccountsReceivable> findPage(Page<AccountsReceivable> page, @Param("query") AccountsReceivableDTO accountsReceivableDto);

    /**
     * 查询
     *
     * @param accountsReceivableDto 应收账款 DTO
     * @return {@link List }<{@link AccountsReceivable }>
     */
    @BusinessDataScope(deptOrUser = "user",
            scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    List<AccountsReceivable> findPage(@Param("query") AccountsReceivableDTO accountsReceivableDto);

    /**
     * 模糊查询带分页（V1.1.1优化当期数据）
     *
     * @param page                  分页请求
     * @param accountsReceivableDto 模糊查询属性封装实体
     * @return {@link Page}<{@link AccountsReceivable}>
     */
    @BusinessDataScope(deptOrUser = "dept_user",
            scopeDeptNameList = {"first_department_id", "second_department_id"},
            scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    Page<AccountsReceivable> findPageByNow(Page<AccountsReceivable> page, @Param("query") AccountsReceivableDTO accountsReceivableDto);

    /**
     * 查询
     *
     * @param accountsReceivableDto 应收账款 DTO
     * @return {@link List }<{@link AccountsReceivable }>
     */
    @BusinessDataScope(deptOrUser = "dept_user",
            scopeDeptNameList = {"first_department_id", "second_department_id"},
            scopeUserNameList = {"salesman_user_id", "manager_user_id"})
    List<AccountsReceivable> findPageByNow(@Param("query") AccountsReceivableDTO accountsReceivableDto);

    /**
     * 根据项目id与合同id获取应收账款台账
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return 应收账款台账
     */
    AccountsReceivable selectOneByProjectIdAndContractId(@Param("projectId") Long projectId,
                                                         @Param("contractId") Long contractId);

    /**
     * 导出Excel选中数据
     *
     * @param accountsReceivableDTO {@link AccountsReceivableDTO}
     * @return {@link List}<{@link AccountsReceivableVO}>
     */
    @BusinessDataScope(deptOrUser = "dept_user", scopeDeptNameList = {"first_department_id"}, scopeUserNameList = {"salesman_user_id","manager_user_id"})
    List<AccountsReceivable> exportList(@Param("query") AccountsReceivableDTO accountsReceivableDTO);

    /**
     * 导出Excel选中数据（V1.1.1优化当期数据）
     *
     * @param accountsReceivableDto 模糊查询属性封装实体
     * @return {@link Page}<{@link AccountsReceivable}>
     */
    @BusinessDataScope(deptOrUser = "dept_user", scopeDeptNameList = {"first_department_id"}, scopeUserNameList = {"salesman_user_id","manager_user_id"})
    List<AccountsReceivable> exportListByNow(@Param("query") AccountsReceivableDTO accountsReceivableDto);

    /**
     * 统计应收账款数据
     *
     * @param accountsReceivableDto 查询条件
     * @return 统计结果
     */
    Map<String, BigDecimal> statistics(@Param("query") AccountsReceivableDTO accountsReceivableDto);

    /**
     * 统计当期应收账款数据
     *
     * @param accountsReceivableDto 查询条件
     * @return 统计结果
     */
    Map<String, BigDecimal> statisticsByNow(@Param("query") AccountsReceivableDTO accountsReceivableDto);
}
