package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 催款记录-收款方式枚举
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Getter
@AllArgsConstructor
public enum CollectionMethodEnum implements ValueEnum<Integer> {

    /**
     * 电话
     */
    PHONE(0, "电话"),

    /**
     * 上门
     */
    DROP_IN(1, "上门"),

    /**
     * 邮件
     */
    MAIL(2, "邮件"),

    /**
     * 催款函
     */
    DUNNING_LETTER(3, "催款函"),

    /**
     * 律师函
     */
    LAWYER_LETTER(4, "律师函"),

    /**
     * 诉讼
     */
    LAWSUIT(5, "诉讼"),

    /**
     * 其他（备注）
     */
    OTHERS(6, "其他（备注）");

    private final Integer value;

    private final String name;
}
