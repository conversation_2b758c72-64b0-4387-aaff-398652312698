package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门数据展示
 *
 * <AUTHOR>
 * @since 2023-11-13
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptVO {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门编号
     */
    private String deptCode;

    /**
     * 上级部门编号
     */
    private String parentDeptCode;

    /**
     * 上级部门名称
     */
    private String parentDeptName;

    /**
     * 所属组织编码
     */
    private String orgCode;

    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 部门层级
     */
    private Integer deptLevel;

    /**
     * 部门类型（1-行政部门，2-虚拟部门）
     */
    private Integer deptType;

    /**
     * 部门类型描述
     */
    private String deptTypeDesc;

    /**
     * 部门状态（0-禁用，1-启用）
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 排序号
     */
    private Integer sortOrder;

    /**
     * 部门负责人ID
     */
    private Long managerId;

    /**
     * 部门负责人姓名
     */
    private String managerName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 子部门列表
     */
    private List<DeptVO> children;
}
