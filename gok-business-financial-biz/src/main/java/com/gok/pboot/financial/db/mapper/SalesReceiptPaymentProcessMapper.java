package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gok.pboot.financial.db.entity.SalesReceiptPaymentProcess;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SalesReceiptPaymentProcessMapper extends BaseMapper<SalesReceiptPaymentProcess> {

    /**
     * 删除
     * @param salesReceiptId
     */
    void deleteBySalesReceiptId(@Param("salesReceiptId") Long salesReceiptId);

    /**
     * 批量保存
     * @param processList
     */
    void batchSave(@Param("processList") List<SalesReceiptPaymentProcess> processList);
}