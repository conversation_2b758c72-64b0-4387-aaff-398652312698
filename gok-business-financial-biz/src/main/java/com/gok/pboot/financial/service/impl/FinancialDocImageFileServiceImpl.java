package com.gok.pboot.financial.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.db.entity.FinancialDocImageFile;
import com.gok.pboot.financial.db.mapper.FinancialDocImageFileMapper;
import com.gok.pboot.financial.service.IFinancialDocImageFileService;
import com.gok.pboot.financial.util.OaUtil;
import com.gok.pboot.financial.vo.OaFileInfoVo;
import com.gok.pboot.financial.vo.OaFileVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * OA文件映射
 * Service
 *
 * <AUTHOR>
 * @date 2023/11/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinancialDocImageFileServiceImpl extends ServiceImpl<FinancialDocImageFileMapper, FinancialDocImageFile> implements IFinancialDocImageFileService {

    private final static String FILE_SEPARATOR = ",";

    private final OaUtil oaUtil;

    @Override
    public List<OaFileInfoVo> getOaFileVoList(String docId) {
        List<OaFileInfoVo> oaFileInfoVoList = new ArrayList<>();
        if (StringUtils.isEmpty(docId)) {
            return oaFileInfoVoList;
        }

        //文件映射表
        QueryWrapper<FinancialDocImageFile> financialDocImageFileQueryWrapper = new QueryWrapper<>();
        List<String> list1 = Arrays.asList(docId.split(FILE_SEPARATOR));
        financialDocImageFileQueryWrapper.lambda().in(FinancialDocImageFile::getDocId, list1);
        List<FinancialDocImageFile> financialDocImageFileList = this.list(financialDocImageFileQueryWrapper);
        if (CollUtil.isEmpty(financialDocImageFileList)) {
            return oaFileInfoVoList;
        }
        financialDocImageFileList.stream().forEach(f -> {
            if (f.getRequestId() == null || f.getOperateUserId() == null) {
                return;
            }
            List<OaFileVo> resourcesData = oaUtil.getResourcesData(String.valueOf(f.getRequestId()),
                    String.valueOf(f.getOperateUserId()));
            resourcesData.forEach(r -> {
                if (Long.valueOf(r.getId()).equals(f.getImageFileId())) {
                    OaFileInfoVo oaFileInfoVo = new OaFileInfoVo();
                    oaFileInfoVo.setFileId(String.valueOf(f.getDocId()));
                    oaFileInfoVo.setImageFileId(r.getId().toString());
                    oaFileInfoVo.setDownloadUrl(r.getDownloadUrl());
                    oaFileInfoVo.setFilename(r.getName());
                    oaFileInfoVoList.add(oaFileInfoVo);
                }
            });
        });
        return oaFileInfoVoList;
    }
}
