package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.base.admin.vo.AccountRelationshipVO;
import com.gok.bcp.upms.common.UpmsConstants;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.util.R;
import com.gok.pboot.common.core.enums.YesOrNoEnum;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.db.entity.IndicatorAccountRelationship;
import com.gok.pboot.financial.db.mapper.AccountingSystemMapper;
import com.gok.pboot.financial.db.mapper.IndicatorAccountRelationshipMapper;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipDTO;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipPageDTO;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IIndicatorAccountRelationshipService;
import com.gok.pboot.financial.util.FinancePropertiesUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.vo.IndicatorAccountRelationshipVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 指标科目关系 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Slf4j
@Service
public class IndicatorAccountRelationshipServiceImpl
        extends ServiceImpl<IndicatorAccountRelationshipMapper, IndicatorAccountRelationship>
        implements IIndicatorAccountRelationshipService {
    @Resource
    private LogRecordUtils logRecordUtils;

    @Resource
    private RemoteOutMultiDeptService remoteOutMultiDeptService;

    @Resource
    private AccountingSystemMapper accountingSystemMapper;

    /**
     * 分页查询
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO} 分页条件
     * @return {@link Page}<{@link IndicatorAccountRelationshipVO}>
     */
    @Override
    public Page<IndicatorAccountRelationshipVO> findPage(IndicatorAccountRelationshipPageDTO dto) {
        Page<IndicatorAccountRelationshipVO> voPage = new Page<>(dto.getCurrent(), dto.getSize());
        // 1、条件查询指定科目列表
        baseMapper.findPage(voPage, dto);
        return voPage;
    }

    /**
     * 根据id获取详情
     *
     * @param id 科目id
     * @return {@link IndicatorAccountRelationshipVO}
     */
    @Override
    public IndicatorAccountRelationshipVO getOne(Long id) {
        // 条件查询指定科目详情
        IndicatorAccountRelationship indicatorAccountRelationship = getById(id);
        // 封装
        return BeanUtil.copyProperties(indicatorAccountRelationship, IndicatorAccountRelationshipVO.class);
    }


    /**
     * 导出选中数据
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO}
     * @return {@link List}<{@link IndicatorAccountRelationshipVO}>
     */
    @Override
    public List<IndicatorAccountRelationshipVO> export(IndicatorAccountRelationshipPageDTO dto) {
        return baseMapper.findPage(dto);
    }

    /**
     * 导入Excel
     *
     * @param indicatorAccountRelationshipDTOList {@link List}<{@link IndicatorAccountRelationshipDTO}>
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> importExcel(List<IndicatorAccountRelationshipDTO> indicatorAccountRelationshipDTOList) {
        // 1、获取核算部门排序
        Map<String, Integer> deptTreeMap = getAccountingDeptSortMap();
        // 2、遍历列表
        List<IndicatorAccountRelationship> indicatorAccountRelationshipList = new ArrayList<>();
        if (CollUtil.isNotEmpty(indicatorAccountRelationshipDTOList)) {
            StringBuilder error = new StringBuilder();
            Integer index = 2;
            indicatorAccountRelationshipDTOList.forEach(i -> {
                error.append("第").append(index).append("行: ");
                if (CharSequenceUtil.isBlank(i.getName())) {
                    error.append("科目名称不能为空。");
                }
                if (CharSequenceUtil.isBlank(i.getAccountingDepartment())) {
                    error.append("核算部门不能为空。");
                }
                // 2.1、基础赋值与枚举
                IndicatorAccountRelationship indicatorAccountRelationship = BeanUtil.copyProperties(i, IndicatorAccountRelationship.class);
                // 2.2 核算部门排序
                indicatorAccountRelationship.setAccountingDepartmentSort(deptTreeMap.get(i.getAccountingDepartmentCode()));

                indicatorAccountRelationshipList.add(indicatorAccountRelationship);
            });
            if (error.toString().contains("空")) {
                return R.failed("导入失败,原因是" + error);
            }

            return this.saveBatch(indicatorAccountRelationshipList) ? R.ok("导入成功") : R.failed("导入失败");
        }

        return R.ok("无数据导入");
    }

    @Override
    public Map<String, String> getIndicatorAccountRelationship() {
        // 获取所有启用的会计体系
        List<AccountingSystem> accountingSystemList = accountingSystemMapper
                .selectList(Wrappers.lambdaQuery(AccountingSystem.class)
                        .eq(AccountingSystem::getEnableStatus, YesOrNoEnum.NO.getVal())
                        .orderByAsc(AccountingSystem::getCreateTime));
        List<IndicatorAccountRelationship> indicatorList = ListUtil.empty();
        if (CollUtil.isNotEmpty(accountingSystemList)){
            //首个会计体系下的指标科目关系
            Long accountingSystemId = accountingSystemList.get(0).getId();
            // 查询科目指标列表
            indicatorList = lambdaQuery()
                    .eq(IndicatorAccountRelationship::getEnableStatus, YesOrNoEnum.NO.getVal())
                    .eq(IndicatorAccountRelationship::getAccountingSystemId, accountingSystemId)
                    .list();
        }


        //key->核算部门编码,value->科目编码
        return indicatorList.stream()
                .filter(e -> e.getAccountingDeptId() != null && e.getCode() != null)
                .collect(Collectors.toMap(
                        e -> e.getAccountingDeptId().toString(),
                        IndicatorAccountRelationship::getCode,
                        (a, b) -> a));
    }

    /**
     * 关联会计科目
     *
     * @param ids  ids
     * @param code code
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void relatedAccountingSubjects(Set<Long> ids, String code) {
        if (CollUtil.isEmpty(ids)) {
            throw new BusinessException("请选择关联的会计科目~");
        }
        List<IndicatorAccountRelationship> indicatorAccountRelationshipList = listByIds(ids);
        List<IndicatorAccountRelationship> updateList = indicatorAccountRelationshipList.stream()
                .peek(e -> e.setCode(code))
                .collect(Collectors.toList());
        updateBatchById(updateList);

        indicatorAccountRelationshipList.forEach(ship -> {
            String content = "【" + ship.getCode() + "】【" + ship.getAccountingDepartmentCode()
                    + "】变更为【" + code + "】【" + ship.getAccountingDepartmentCode() + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.INDICATOR_ACCOUNT_RELATIONSHIP,
                    OperationEnum.EDIT_INDICATOR_ACCOUNT_RELATIONSHIP, content);
        });
    }

    /**
     * 初始化关联关系
     *
     * @param accountingSystemIds 会计系统 ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initRelationship(Set<Long> accountingSystemIds) {
        // 获取已存在的会计体系
        Set<Long> existAccountingSystemIds = lambdaQuery()
                .in(IndicatorAccountRelationship::getAccountingSystemId, accountingSystemIds)
                .groupBy(IndicatorAccountRelationship::getAccountingSystemId)
                .list().stream()
                .map(IndicatorAccountRelationship::getAccountingSystemId)
                .collect(Collectors.toSet());

        // 获取所有核算组织
        List<MultiDimensionDeptDto> accountingDeptList = getAccountingDeptList();
        // 去除已存在的会计体系
        accountingSystemIds.removeAll(existAccountingSystemIds);

        List<IndicatorAccountRelationship> indicatorAccountRelationshipList = new ArrayList<>();
        accountingSystemIds.forEach(accountingSystemId -> {
            List<IndicatorAccountRelationship> collect = accountingDeptList.stream()
                    .map(dept -> IndicatorAccountRelationship.builder()
                            .accountingDepartmentCode(dept.getAssociationId())
                            .accountingDeptId(dept.getDeptId())
                            .accountingDepartment(dept.getName())
                            .accountingDepartmentSort(dept.getSort())
                            .accountingSystemId(accountingSystemId)
                            .enableStatus(YesOrNoEnum.NO.getVal())
                            .build()
                    ).collect(Collectors.toList());
            indicatorAccountRelationshipList.addAll(collect);
        });
        // 批量保存
        saveBatch(indicatorAccountRelationshipList, 200);
    }

    /**
     * 同步更新关系
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncUpdateRelationship() {
        // 获取所有核算组织
        List<MultiDimensionDeptDto> accountingDeptList = getAccountingDeptList();
        if (CollUtil.isEmpty(accountingDeptList)) {
            return;
        }
        // 获取所有启用的会计体系
        List<Long> accountingSystemIds = accountingSystemMapper
                .selectList(Wrappers.lambdaQuery(AccountingSystem.class)
                        .eq(AccountingSystem::getEnableStatus, YesOrNoEnum.NO.getVal()))
                .stream()
                .map(AccountingSystem::getId)
                .collect(Collectors.toList());

        List<IndicatorAccountRelationship> existingRelationships;
        for (Long accountingSystemId : accountingSystemIds) {
            // 获取现有的指标科目关系
            existingRelationships = lambdaQuery()
                    .eq(IndicatorAccountRelationship::getAccountingSystemId, accountingSystemId)
                    .eq(IndicatorAccountRelationship::getEnableStatus, YesOrNoEnum.NO.getVal())
                    .list();
            batchUpdateRelationship(accountingSystemId, accountingDeptList, existingRelationships);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateRelationship(Long accountingSystemId,
                                        List<MultiDimensionDeptDto> accountingDeptList,
                                        List<IndicatorAccountRelationship> existingRelationships) {

        // 当前已存在的关系映射（以部门ID为Key）
        Map<Long, List<IndicatorAccountRelationship>> existingMap = existingRelationships.stream()
                .collect(Collectors.groupingBy(IndicatorAccountRelationship::getAccountingDeptId));

        // 新增、更新、删除列表
        List<IndicatorAccountRelationship> toAdd = new ArrayList<>();
        List<IndicatorAccountRelationship> toUpdate = new ArrayList<>();
        Set<Long> deptIdSet = new HashSet<>();
        for (MultiDimensionDeptDto dept : accountingDeptList) {
            deptIdSet.add(dept.getDeptId());
            if (!existingMap.containsKey(dept.getDeptId())) {
                // 新增
                toAdd.add(IndicatorAccountRelationship.builder()
                        .accountingSystemId(accountingSystemId)
                        .accountingDepartmentCode(dept.getAssociationId())
                        .accountingDeptId(dept.getDeptId())
                        .accountingDepartment(dept.getName())
                        .accountingDepartmentSort(dept.getSort())
                        .enableStatus(YesOrNoEnum.NO.getVal())
                        .build());
            } else {
                List<IndicatorAccountRelationship> relationships = existingMap.getOrDefault(dept.getDeptId(),Collections.emptyList());
                relationships.forEach(existing -> {
                    existing.setAccountingDepartment(dept.getName());
                    existing.setAccountingDepartmentSort(dept.getSort());
                    existing.setAccountingDepartmentCode(dept.getAssociationId());
                    toUpdate.add(existing);
                });
            }
        }

        // 删除：已存在但不在新部门列表中的记录
        List<IndicatorAccountRelationship> toDelete = existingRelationships.stream()
                .filter(r -> !deptIdSet.contains(r.getAccountingDeptId()))
                .collect(Collectors.toList());

        // 执行操作
        removeByIds(toDelete);
        saveBatch(toAdd, 200);
        updateBatchById(toUpdate, 200);
    }

    private Map<String, Integer> getAccountingDeptSortMap() {
        return getAccountingDeptList().stream()
                .collect(Collectors.toMap(d -> d.getDeptId().toString(),
                        MultiDimensionDeptDto::getSort, (a, b) -> a));
    }

    private List<MultiDimensionDeptDto> getAccountingDeptList() {
        return remoteOutMultiDeptService.getDeptList(FinancePropertiesUtils.DEPT_CAT_NAME, null, null)
                .getData().stream()
                .filter(dept -> UpmsConstants.STATUS_NORMAL.equals(dept.getStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 通过会计体系编码查询指标科目关系列表
     *
     * @param systemCode 会计体系编码
     * @return {@link List}<{@link IndicatorAccountRelationshipVO}>
     */
    @Override
    public List<AccountRelationshipVO> getBySystemCode(String systemCode) {
        if (CharSequenceUtil.isBlank(systemCode)) {
            return ListUtil.empty();
        }
        return baseMapper.getBySystemCode(systemCode);
    }
//    /**
//     * 根据id更新科目信息
//     *
//     * @param dto {@link IndicatorAccountRelationshipDTO}
//     * @return {@code int}
//     */
//    @Override
//    @Transactional(rollbackFor = RuntimeException.class)
//    public int update(IndicatorAccountRelationshipDTO dto) {
//        // 1、获取核算部门排序
//        Map<String, Integer> deptTreeMap = getAccountingDeptTreeMap();
//        // 2、条件查询指定科目详情
//        IndicatorAccountRelationship ship = baseMapper.selectOne(Wrappers.<IndicatorAccountRelationship>lambdaQuery()
//                .eq(IndicatorAccountRelationship::getId, dto.getId())
//        );
//        if (Optional.ofNullable(ship).isPresent()) {
//            // 3、记录日志
//            if (!dto.getAccountingDepartmentCode().equals(ship.getAccountingDepartmentCode())) {
//                String content = "【" + ship.getCode() + "】【" + ship.getAccountingDepartmentCode()
//                        + "】变更为【" + dto.getCode() + "】【" + dto.getAccountingDepartmentCode() + "】";
//                logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.INDICATOR_ACCOUNT_RELATIONSHIP,
//                        OperationEnum.EDIT_INDICATOR_ACCOUNT_RELATIONSHIP, content);
//            }
//
//            // 4、核算部门排序变更
//            if (!ship.getAccountingDepartmentCode().equals(dto.getAccountingDepartmentCode())) {
//                ship.setAccountingDepartmentSort(deptTreeMap.get(dto.getAccountingDepartmentCode()));
//            }
//            ship = BeanUtil.copyProperties(dto, IndicatorAccountRelationship.class);
//
//            return baseMapper.updateById(ship);
//        }
//
//        return NumberUtils.INTEGER_ZERO;
//    }
//
//    /**
//     * 插入指定科目关系
//     *
//     * @param dto {@link IndicatorAccountRelationshipDTO}
//     * @return {@code int}
//     */
//    @Override
//    @Transactional(rollbackFor = RuntimeException.class)
//    public int save(IndicatorAccountRelationshipDTO dto) {
//        // 1、获取核算部门排序
//        Map<String, Integer> deptTreeMap = getAccountingDeptTreeMap();
//        // 2、条件查询指定科目详情
//        IndicatorAccountRelationship ship = baseMapper.selectOne(Wrappers.<IndicatorAccountRelationship>lambdaQuery()
//                .eq(IndicatorAccountRelationship::getId, dto.getId())
//        );
//        OperationEnum operationEnum = OperationEnum.ADD_INDICATOR_ACCOUNT_RELATIONSHIP;
//        if (Optional.ofNullable(ship).isPresent()) {
//            // 3、记录日志
//            if (!dto.getAccountingDepartmentCode().equals(ship.getAccountingDepartmentCode())) {
//                String content = "【" + ship.getCode() + "】【" + ship.getAccountingDepartmentCode()
//                        + "】变更为【" + dto.getCode() + "】【" + dto.getAccountingDepartmentCode() + "】";
//                logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.INDICATOR_ACCOUNT_RELATIONSHIP,
//                        OperationEnum.EDIT_INDICATOR_ACCOUNT_RELATIONSHIP, content);
//            }
//
//            ship = BeanUtil.copyProperties(dto, IndicatorAccountRelationship.class);
//            ship.setAccountingDepartmentSort(deptTreeMap.get(dto.getAccountingDepartmentCode()));
//            return baseMapper.updateById(ship);
//        }
//        // 3、记录日志
//        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.INDICATOR_ACCOUNT_RELATIONSHIP,
//                operationEnum, operationEnum.getName() + "【" + dto.getAccountingDepartmentCode() + "】");
//
//        ship = BeanUtil.copyProperties(dto, IndicatorAccountRelationship.class, "id");
//        ship.setAccountingDepartmentSort(deptTreeMap.get(dto.getAccountingDepartmentCode()));
//
//        return baseMapper.insert(ship);
//    }
//


//    /**
//     * 根据id删除科目信息
//     *
//     * @param ids 科目id集合
//     * @return {@link R}
//     */
//    @Override
//    @Transactional(rollbackFor = RuntimeException.class)
//    public R<String> delete(List<Long> ids) {
//        // 启用状态无法删除
//        List<IndicatorAccountRelationship> list = lambdaQuery()
//                .in(CollUtil.isNotEmpty(ids), IndicatorAccountRelationship::getId, ids)
//                .list();
//        if (CollUtil.isNotEmpty(list)) {
//            // 记录日志
//            StringBuilder sb = new StringBuilder();
//            list.forEach(i -> sb.append("【").append(i.getAccountingDepartmentCode()).append("】"));
//            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.INDICATOR_ACCOUNT_RELATIONSHIP,
//                    OperationEnum.DELETE_INDICATOR_ACCOUNT_RELATIONSHIP,
//                    OperationEnum.DELETE_INDICATOR_ACCOUNT_RELATIONSHIP.getName() + sb);
//
//            return baseMapper.deleteBatchIds(list.stream().map(IndicatorAccountRelationship::getId).collect(Collectors.toList()))
//                    > NumberUtils.INTEGER_ZERO ? R.ok("删除数据成功") : R.failed("删除数据失败");
//        }
//
//        return R.failed("无数据删除");
//    }


}
