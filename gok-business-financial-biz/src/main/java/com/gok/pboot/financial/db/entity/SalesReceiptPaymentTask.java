package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售收款计划回款任务
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptPaymentTask {
    /**
    * id
    */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    /**
    * 销售收款id
    */
    @ApiModelProperty("销售收款id")
    private Long salesReceiptId;

    /**
     * 客户付款审批流程
     */
    @ApiModelProperty("客户付款审批流程")
    private String customerPaymentProcess;

    /**
    * 当前审批节点
    */
    @ApiModelProperty("当前审批节点")
    private String currentApprovalProcess;

    /**
    * 审批进度
    */
    @ApiModelProperty("审批进度")
    private String approvalProgress;

    /**
    * 更新日期
    */
    @ApiModelProperty("更新日期")
    private String updateDate;

    /**
    * 所需材料
    */
    @ApiModelProperty("所需材料")
    private String requiredMaterials;

    /**
    * 回款风险(0有 1无)
    */
    @ApiModelProperty("回款风险")
    private Integer paymentRisk;

    /**
    * 进度说明
    */
    @ApiModelProperty("进度说明")
    private String progressDescription;

    /**
    * 下一步工作
    */
    @ApiModelProperty("下一步工作")
    private String nextStepWork;

    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;

    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建人")
    private String createBy;

    /**
    * 修改人
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
    * 所属租户
    */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}