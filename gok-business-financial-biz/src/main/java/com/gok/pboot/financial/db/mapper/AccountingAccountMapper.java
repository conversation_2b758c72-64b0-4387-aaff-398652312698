package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.AccountingAccount;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.dto.AccountingAccountPageDTO;
import com.gok.pboot.financial.vo.AccountingAccountPageVO;
import com.gok.pboot.financial.vo.AccountingAccountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会计科目
 * <AUTHOR>
 */
public interface AccountingAccountMapper  extends BaseMapper<AccountingAccount> {

    /**
     * 分页查询
     * @param page
     * @param dto
     * @return
     */
    Page<AccountingAccountPageVO> findPage(Page<AccountingAccount> page, @Param("query") AccountingAccountPageDTO dto);

    /**
     * 列表查询
     * @param dto
     * @return
     */
    List<AccountingAccountPageVO> findPage(@Param("query") AccountingAccountPageDTO dto);

    /**
     * 删除
     * @param idList
     * @return
     */
    Boolean delByIds(@Param("idList") List<Long> idList);

    /**
     * 查看详情
     * @param id
     * @return
     */
    AccountingAccountVO selById(@Param("id") Long id);

    /**
     * 查询详情
     * @param code
     * @return
     */
    AccountingAccountVO getInfoByDto(@Param("code") String code);
}