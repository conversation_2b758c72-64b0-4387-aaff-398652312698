package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 人员类型枚举
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Getter
public enum PersonnelTypeEnum implements ValueEnum<Integer> {

    /**
     * 正式
     */
    FORMAL(0, "正式"),

    /**
     * 实习
     */
    PRACTICE(1, "实习"),

    /**
     * 试用期
     */
    PROBATION(2, "试用期"),

    /**
     * 兼职
     */
    SIDELINE(3, "兼职");

    private final Integer value;

    private final String name;

    PersonnelTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
