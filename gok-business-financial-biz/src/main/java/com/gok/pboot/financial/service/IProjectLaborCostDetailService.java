package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectLaborCostDetail;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;

import java.util.List;

/**
 * 项目人工成本分摊明细 Service
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
public interface IProjectLaborCostDetailService extends IService<ProjectLaborCostDetail> {

    /**
     * 模糊查询带分页
     *
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}{@link ProjectLaborCostDetailVO}>
     */
    StatisticsPage<ProjectLaborCostDetailVO> findPage( ProjectLaborCostDTO projectLaborCostDto);

    /**
     * 导出Excel选中数据
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostDetailVO}>
     */
    List<ProjectLaborCostDetailVO> export(ProjectLaborCostDTO projectLaborCostDTO);
}
