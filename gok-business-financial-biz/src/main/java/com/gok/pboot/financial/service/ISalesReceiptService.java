package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.SalesReceipt;
import com.gok.pboot.financial.dto.SakesReceiptImportBudgetMarkingDTO;
import com.gok.pboot.financial.dto.SakesReceiptImportExcelDTO;
import com.gok.pboot.financial.dto.SalesReceiptDTO;
import com.gok.pboot.financial.dto.SalesReceiptPaymentTaskDTO;
import com.gok.pboot.financial.vo.SalesReceiptPaymentTaskVO;
import com.gok.pboot.financial.vo.SalesReceiptVO;

import java.util.List;

/**
 * 销售收款计划Service
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt(销售收款计划)】的数据库操作Service
 * @createDate 2023-09-27 16:08:24
 */
public interface ISalesReceiptService extends IService<SalesReceipt> {

    /**
     * 分页条件查询
     *
     * @param dto 查询条件
     * @return page
     */
    StatisticsPage<SalesReceiptVO> findPage(SalesReceiptDTO dto);

    /**
     * 项款名称下拉框
     *
     * @param clientId 应用id
     * @param menuCode 菜单
     * @return {@link List}
     */
    List<String> currentPaymentNameBox(Long clientId, String menuCode);

    /**
     * 项目状态下拉框
     *
     * @return {@link List}
     */
    List<String> projectStatusBox();


    /**
     * 导出Excel
     *
     * @param dto dto
     * @return {@link List}
     */
    List<SalesReceiptVO> export(SalesReceiptDTO dto);


    /**
     * 销售收款任务超期提醒
     *
     * @return {@link R <Boolean>}
     */
    Boolean pushMessage();


    /**
     * 保存回款任务详情
     * @param dto
     * @return
     */
    R<String> savePaymentTask(SalesReceiptPaymentTaskDTO dto);

    /**
     * 回款任务详情
     * @param salesReceiptId
     * @return
     */
    SalesReceiptPaymentTaskVO getPaymentTask(Long salesReceiptId);

    /**
     * 销售收款任务跟进提醒
     * @return
     */
    Boolean taskReminder();

    /**
     * 上下页ids列表
     * @param dto
     * @return
     */
    List<Long> findIds(SalesReceiptDTO dto);

    /**
     * 销售收款任务更新提醒
     * @return
     */
    Boolean taskUpdateReminder();

    /**
     * 销售收款任务未更新提醒
     * @return
     */
    Boolean TaskNotUpdatedReminder();

    /**
     * 销售计划任务导入
     * @param sakesReceiptImportExcelDTOList
     * @return
     */
    Boolean importExcel(List<SakesReceiptImportExcelDTO> sakesReceiptImportExcelDTOList);

    /**
     * 导入预算标记
     * @param list
     * @return
     */
    R<String> importBudgetMarking(List<SakesReceiptImportBudgetMarkingDTO> list);

    /**
     * 初始化客户付款审批流程
     */
    void initPaymentProcess();

}
