package com.gok.pboot.financial.common;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 统计分页类，继承 MyBatis Plus 的 Page，增加统计汇总字段
 *
 * <AUTHOR>
 * @date 2025/05/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StatisticsPage<T> extends Page<T> {

    /**
     * 用于存储当前查询范围内的各项合计值
     */
    private Map<String, BigDecimal> statistics = new HashMap<>();

    public StatisticsPage() {
        super();
    }

    public StatisticsPage(long current, long size) {
        super(current, size);
    }

    public StatisticsPage(long current, long size, long total) {
        super(current, size, total);
    }



}
