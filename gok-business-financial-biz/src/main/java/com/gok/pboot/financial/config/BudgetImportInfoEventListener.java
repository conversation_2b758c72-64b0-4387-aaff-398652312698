package com.gok.pboot.financial.config;

import com.alibaba.excel.context.AnalysisContext;
import com.gok.components.common.user.PigxUser;
import com.gok.module.excel.api.domain.vo.ErrorMessageVo;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.module.excel.api.listener.ListAnalysisEventListener;
import com.gok.module.excel.api.support.Validators;
import com.gok.module.file.entity.SysFile;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 自定义Excel导入监听器
 * 支持自定义跳过行数
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Slf4j
public class BudgetImportInfoEventListener extends ListAnalysisEventListener<Object> {

    private final List<Object> list = new ArrayList<>();
    private final List<ErrorMessageVo> errorMessageList = new ArrayList<>();
    private Long lineNum = 1L;
    private Long skipRows = 2L;

    @Override
    public void invoke(Object data, AnalysisContext analysisContext) {
        lineNum++;
        if (lineNum <= skipRows){
            return;
        }
        Set<ConstraintViolation<Object>> violations = Validators.validate(data);
        if (!violations.isEmpty()){
            Set<String> messageSet = violations.stream().map(ConstraintViolation::getMessage)
                    .collect(Collectors.toSet());
            errorMessageList.add(new ErrorMessageVo(lineNum, messageSet));
        } else {
            list.add(data);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.debug("Excel read analysed");
    }

    @Override
    public List<Object> getList() {
        return list;
    }

    @Override
    public <T> void validCustomize(Class aClass, List<T> list, PigxUser pigxUser) throws ParseException {

    }

    @Override
    public void intData(FunctionEnum functionEnum, SysFile sysFile, PigxUser pigxUser, int i) {

    }

    @Override
    public List<ErrorMessageVo> getErrors() {
        return errorMessageList;
    }
} 