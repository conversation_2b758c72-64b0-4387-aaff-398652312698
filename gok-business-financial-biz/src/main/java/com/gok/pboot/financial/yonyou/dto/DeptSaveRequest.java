/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 部门保存请求DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 部门保存请求DTO
 * 用于部门新增/修改，根据状态判断是新增操作还是修改操作，修改时需要录入id
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptSaveRequest {

    /**
     * 部门数据
     */
    private DeptData data;

    /**
     * 部门数据内部类
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DeptData {
        
        /**
         * 所属上级id（组织或部门）,parent和parent_code都为空时，系统默认取上级组织id
         */
        private String parent;
        
        /**
         * 所属上级编码（组织或部门）
         */
        private String parent_code;
        
        /**
         * 排序号
         */
        private Integer sort;
        
        /**
         * 主键ID，部门ID 新增时无需填写，修改时必填
         */
        private String id;
        
        /**
         * 部门编码
         */
        private String code;
        
        /**
         * 部门名称,支持多语
         */
        private MultiLangName name;
        
        /**
         * 所属上级名称
         */
        private String parent_name;
        
        /**
         * 部门性质id
         */
        private String depttype;
        
        /**
         * 部门性质名称
         */
        private String depttype_name;
        
        /**
         * 负责人id
         */
        private String principal;
        
        /**
         * 负责人名称
         */
        private String principal_name;
        
        /**
         * 分管领导id
         */
        private String branchleader;
        
        /**
         * 分管领导名称
         */
        private String branchleader_name;
        
        /**
         * 外部系统主键
         */
        private String objid;
        
        /**
         * 上级组织id，上级组织id和上级组织编码不能同时为空，id和编码同时存在，以id为准
         */
        private String parentorgid;
        
        /**
         * 上级组织编码，上级组织id和上级组织编码不能同时为空，id和编码同时存在，以id为准
         */
        private String parentorgCode;
        
        /**
         * 上级组织名称
         */
        private String parentorgid_name;
        
        /**
         * 状态, 0:未启用、1:启用、2:停用
         */
        private Integer enable;
        
        /**
         * 操作标识, Insert:新增、Update:更新
         */
        private String _status;
    }

    /**
     * 多语言名称
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {
        
        /**
         * 中文名称
         */
        private String zh_CN;
        
        /**
         * 英文名称
         */
        private String en_US;
        
        /**
         * 繁体中文名称
         */
        private String zh_TW;
    }
}