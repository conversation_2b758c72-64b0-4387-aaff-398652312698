package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 凭证实体类
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VoucherDTO {

    /**
     * 外部编码
     */
    private String ExternalCode;

    /**
     * 制单日期
     */
    private String VoucherDate;

    /**
     * 凭证字
     */
    private VoucherDocTypeDTO DocType;

    /**
     * 凭证明细表
     */
    private List<VoucherDetailDTO> Entrys;
}
