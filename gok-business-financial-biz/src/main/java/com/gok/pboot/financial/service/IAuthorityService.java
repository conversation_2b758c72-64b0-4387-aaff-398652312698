package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.bcp.upms.vo.SysUserRoleDataVo;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.dto.AccountingSetDTO;
import com.gok.pboot.financial.dto.AccountingSetPageDTO;
import com.gok.pboot.financial.vo.AccountingSetVO;

import java.util.List;
import java.util.Map;

/**
 * 权限业务层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
public interface IAuthorityService {


    /**
     * 获取当前登录用户能查看到的部门列表、用户列表
     *
     * @param userId   用户id
     * @param menuCode 菜单Code
     * @return
     */
    SysUserRoleDataVo getByClientIdAndRoleId(Long userId, String menuCode);
}
