package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目人工成本分摊明细
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_labor_cost_detail")
@ApiModel("项目人工成本分摊明细")
public class ProjectLaborCostDetail extends Model<ProjectLaborCostDetail> {

    /**
     * 归属月份
     */
    @ApiModelProperty("归属月份")
    private String yearMonthDate;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    @ApiModelProperty("项目状态")
    private String projectStatus;

    /**
     * 业务归属一级部门id
     */
    @ApiModelProperty("业务归属一级部门id")
    private Long incomeDeptId;

    /**
     * 业务归属一级部门
     */
    @ApiModelProperty("业务归属一级部门")
    private String incomeDept;

    /**
     * 业务归属二级部门id
     */
    @ApiModelProperty("业务归属二级部门id")
    private Long incomeSecondaryDeptId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private Long userId;

    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 出勤天数
     */
    @ApiModelProperty("出勤天数")
    private BigDecimal daysAttendance;

    /**
     * 项目耗用天数
     */
    @ApiModelProperty("项目耗用天数")
    private BigDecimal expendDays;

    /**
     * 加班天数
     */
    @ApiModelProperty("加班天数")
    private BigDecimal addDays;

    /**
     * 税前工资
     */
    @ApiModelProperty("税前工资")
    private String salary;

    /**
     * 单位社保/公司承担社保
     */
    @ApiModelProperty("公司承担社保")
    private String unitSocialSecurity;

    /**
     * 单位公积金/公司承担公积金
     */
    @ApiModelProperty("公司承担公积金")
    private String unitProvidentFund;

    /**
     * 社保成本
     */
    @ApiModelProperty("社保成本")
    private String socialSecurityCost;

    /**
     * 公积金成本
     */
    @ApiModelProperty("公积金成本")
    private String providentFundCost;

    /**
     * 工资成本
     */
    @ApiModelProperty("工资成本")
    private String payrollCost;

    /**
     * 合计成本
     */
    @ApiModelProperty("合计成本")
    private String costSum;

    /**
     * 发薪主体
     */
    @ApiModelProperty("发薪主体")
    private String salaryPaidSubject;

    /**
     * 人员归属二级部门id
     */
    @ApiModelProperty("人员归属二级部门id")
    private Long personnelSecondaryDeptId;

    /**
     * 人员归属二级部门
     */
    @ApiModelProperty("人员归属二级部门")
    private String personnelSecondaryDept;

    /**
     * 人员归属一级部门id
     */
    @ApiModelProperty("人员归属一级部门id")
    private Long personnelDeptId;

    /**
     * 人员归属一级部门
     */
    @ApiModelProperty("人员归属一级部门")
    private String personnelDept;

    /**
     * 项目类型
     * {@link com.gok.pboot.financial.enums.ProjectTypeEnum}
     */
    @ApiModelProperty("项目类型")
    private Integer projectType;

    /**
     * 人员类型
     * {@link com.gok.pboot.financial.enums.PersonnelTypeEnum}
     */
    @ApiModelProperty("人员类型")
    private Integer personnelType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;
}