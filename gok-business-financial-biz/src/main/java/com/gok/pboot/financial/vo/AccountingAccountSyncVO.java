package com.gok.pboot.financial.vo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 科目类型
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingAccountSyncVO {
    
    private Integer AccountTypeDTO_ID;

    private String AccountTypeDTO_Code;

    private String AccountTypeDTO_Name;

    private Integer id;

    private String code;

    private String name;

    private String shorthand;

    private Integer accountingyear;

    private Integer isbank;

    private Integer iscashequivalents;

    private Integer iscash;

    private String DCDirection_Code;

    private String DCDirection_Name;

    private Integer idParent;

    private String inId;

    private Integer depth;

    private Integer isEndNode;

    private Integer disabled;

    private Integer isauxaccdepartment;

    private Integer isauxaccperson;

    private Integer isauxacccustomer;

    private Integer isauxaccproject;

    private Integer isauxaccinventory;

    private Integer isexauxacc1;

    private Integer isexauxacc2;

    private Integer isexauxacc3;

    private Integer isexauxacc4;

    private Integer isexauxacc5;

    private Integer isexauxacc6;

    private Integer isexauxacc7;

    private Integer isexauxacc8;

    private Integer isexauxacc9;

    private Integer isexauxacc10;

    private Integer isquantity;

    private Integer isexchange;

    private Integer DefaultCurrencyDTO_ID;

    private String DefaultCurrencyDTO_Code;

    private String DefaultCurrencyDTO_Name;

    private String Unit_ID;

    private String Unit_Code;

    private String Unit_Name;
}