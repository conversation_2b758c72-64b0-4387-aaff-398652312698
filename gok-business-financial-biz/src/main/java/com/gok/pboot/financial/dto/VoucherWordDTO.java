package com.gok.pboot.financial.dto;

import com.gok.pboot.common.core.validation.annotation.IntegerVerify;
import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.Data;

@Data
public class VoucherWordDTO {

    private Long id;

    @StringVerify(name = "凭证字",message = "凭证字不能为空",required = true)
    private String voucherWord;

    @StringVerify(name = "打印模板")
    private String printTemplate;

    @IntegerVerify(name = "是否默认",required = true)
    private Integer setDefault;
}
