package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目回款认领
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_payment_claim")
@ApiModel("项目回款认领")
public class ProjectPaymentClaim extends Model<ProjectPaymentClaim> {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 项目回款id
     */
    @ApiModelProperty("项目回款id")
    private Long projectPaymentId;

    /**
     * 客户经理id
     */
    @ApiModelProperty("客户经理id")
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ApiModelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 归属业务线
     * {@link com.gok.pboot.financial.enums.BusinessLineEnum}
     */
    @ApiModelProperty("归属业务线")
    private Integer businessLine;

    /**
     * 回款类型
     */
    @ApiModelProperty("回款类型")
    private Integer paymentType;

    /**
     * 款项金额
     */
    @ApiModelProperty("款项金额")
    private String paymentMoney;

    /**
     * 项目回款
     * {@link com.gok.pboot.financial.enums.ProjectCollectionEnum}
     */
    @ApiModelProperty("项目回款")
    private Integer projectCollection;

    /**
     * 预算内回款
     * {@link com.gok.pboot.financial.enums.CollectionWithinBudgetEnum}
     */
    @ApiModelProperty("预算内回款")
    private Integer collectionWithinBudget;

    /**
     * 归属合同收款明细id
     */
    @ApiModelProperty("归属合同收款明细id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long contractPaymentId;

    /**
     * 归属合同收款明细
     */
    @ApiModelProperty("归属合同收款明细")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String contractPayment;

    /**
     * 合同id
     */
    @ApiModelProperty("合同id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long contractId;

    /**
     * 合同名称
     */
    @ApiModelProperty("合同名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String contractName;

    /**
     * 合同编号
     */
    @ApiModelProperty("合同编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String contractCode;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectName;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectCode;

    /**
     * 回款一级部门id
     */
    @ApiModelProperty("回款一级部门id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long paymentDeptId;

    /**
     * 回款一级部门
     */
    @ApiModelProperty("回款一级部门")
    private String paymentDept;

    /**
     * 回款二级部门id
     */
    @ApiModelProperty("回款二级部门id")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long paymentSecondaryDeptId;

    /**
     * 回款二级部门
     */
    @ApiModelProperty("回款二级部门")
    private String paymentSecondaryDept;

    /**
     * 归属区域id
     */
    @ApiModelProperty("归属区域id")
    private String belongingAreaId;

    /**
     * 归属区域
     */
    @ApiModelProperty("归属区域")
    private String belongingArea;

    /**
     * 认领人id
     */
    @ApiModelProperty("认领人id")
    private Long claimantId;

    /**
     * 认领人姓名
     */
    @ApiModelProperty("认领人姓名")
    private String claimantName;

    /**
     * 认领日期
     */
    @ApiModelProperty("认领日期")
    private LocalDateTime claimantDate;

    /**
     * 业务板块
     */
    @ApiModelProperty("业务板块")
    private Integer businessBlock;

    /**
     * 技术类型
     */
    @ApiModelProperty("技术类型")
    private Integer skillType;

    /**
     * 保证金编号
     */
    @ApiModelProperty("保证金编号")
    private String marginCode;

    /**
     * 保证金类型
     */
    @ApiModelProperty("保证金类型")
    private Integer marginType;

    /**
     * 保证金金额
     */
    @ApiModelProperty("保证金金额")
    private String marginMoney;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

    /**
     * 认领金额
     */
    @ApiModelProperty("认领金额")
    private BigDecimal claimMoney;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String relatedCustomerName;

    /**
     * 认领备注
     */
    @ApiModelProperty("认领备注")
    private String claimRemark;

    /**
     * 数据是否来源OA
     */
    @ApiModelProperty("数据是否来源OA")
    private Integer sourceOa;
}