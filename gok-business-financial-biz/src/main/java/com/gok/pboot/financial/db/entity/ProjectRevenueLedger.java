package com.gok.pboot.financial.db.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 项目收入台帐
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("project_revenue_ledger")
@ApiModel("项目收入台帐")
public class ProjectRevenueLedger extends Model<ProjectRevenueLedger> {

    /**
     * 流程id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 流程编码
     */
    @ApiModelProperty("流程编码")
    private String processCoding;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long projectId;

    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String projectNo;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;

    /**
     * 合同id
     */
    @ApiModelProperty("合同id")
    private Long contractId;

    /**
     * 合同编码
     */
    @ApiModelProperty("合同编码")
    private String contractCode;

    /**
     * 结项启动条件
     * {@link com.gok.pboot.financial.enums.ClosingStartConditionsEnum}
     */
    @ApiModelProperty("结项启动条件")
    private Integer closingStartConditions;

    /**
     * 收入日期
     */
    @ApiModelProperty("收入日期")
    private String incomeDate;

    /**
     * 收入金额（含税）
     */
    @ApiModelProperty("收入金额（含税）")
    private BigDecimal incomeAmountTax;

    /**
     * 收入金额（不含税）
     */
    @ApiModelProperty("收入金额（不含税）")
    private BigDecimal incomeAmount;

    /**
     * 验收报告（0:是，1:否）
     * {@link com.gok.pboot.financial.enums.AcceptanceReportEnum}
     */
    @ApiModelProperty("验收报告")
    private Integer acceptanceReport;

    /**
     * 验收报告附件
     */
    @ApiModelProperty("验收报告附件")
    private String acceptanceReportFile;

    /**
     * 验收日期
     */
    @ApiModelProperty("验收日期")
    private String acceptanceDate;

    /**
     * 客户id
     */
    @ApiModelProperty("客户id")
    private Long customerId;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 客户编码
     */
    @ApiModelProperty("客户编码")
    private String customerCode;

    /**
     * 业务归属一级部门id
     */
    @ApiModelProperty("业务归属一级部门id")
    private Long businessFirstDeptId;

    /**
     * 业务归属一级部门
     */
    @ApiModelProperty("业务归属一级部门")
    private String businessFirstDept;

    /**
     * 业务归属二级部门id
     */
    @ApiModelProperty("业务归属二级部门id")
    private Long businessSecondaryDeptId;

    /**
     * 业务归属二级部门
     */
    @ApiModelProperty("业务归属二级部门")
    private String businessSecondaryDept;

    /**
     * 科目名称
     */
    @ApiModelProperty("科目名称")
    private String accountName;

    /**
     * 业务方向
     * {@link com.gok.pboot.financial.enums.BusinessDirectionEnum}
     */
    @ApiModelProperty("业务方向")
    private Integer businessDirection;

    /**
     * 合同名称
     */
    @ApiModelProperty("合同名称")
    private String contractName;

    /**
     * 合同金额
     */
    @ApiModelProperty("合同金额")
    private BigDecimal contractMoney;

    /**
     * 项目销售人员ID
     */
    @ApiModelProperty("项目销售人员ID")
    private Long salesmanUserId;

    /**
     * 项目销售人员
     */
    @ApiModelProperty("项目销售人员")
    private String salesmanUserName;

    /**
     * 申请人ID
     */
    @ApiModelProperty("申请人ID")
    private Long applicantUserId;

    /**
     * 申请人员
     */
    @ApiModelProperty("申请人员")
    private String applicantUserName;

    /**
     * 推送状态（0待推送，1已推送，2推送失败）
     */
    @ApiModelProperty("推送状态")
    private Integer pushStatus;

    /**
     * 合同所属公司
     */
    @ApiModelProperty("合同所属公司")
    private String contractCompany;

    /**
     * 备注
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty("删除标记")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

    /**
     * 可以关联多个发票
     */
    @TableField(exist = false)
    private List<ProjectRevenueLedgerInvoicing> projectRevenueLedgerInvoicingList;

    /**
     * 明细
     */
    @TableField(exist = false)
    private List<ProjectRevenueLedgerDetail> projectRevenueLedgerDetailList;

}