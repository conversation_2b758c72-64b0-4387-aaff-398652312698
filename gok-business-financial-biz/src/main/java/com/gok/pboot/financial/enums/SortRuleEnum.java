package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 应收账款排序排序规则 Enum
 *
 * <AUTHOR>
 * @since 2023-09-05
 */
@Getter
public enum SortRuleEnum implements ValueEnum<Integer> {

    /**
     * 升序
     */
    ASC(0, "ASC"),

    /**
     * 降序
     */
    DESC(1, "DESC");

    private final Integer value;

    private final String name;

    SortRuleEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
