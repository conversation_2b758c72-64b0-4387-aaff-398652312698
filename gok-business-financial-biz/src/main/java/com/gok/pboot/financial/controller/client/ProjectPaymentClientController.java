package com.gok.pboot.financial.controller.client;

import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.components.common.util.R;
import com.gok.pboot.common.security.annotation.Inner;
import com.gok.pboot.financial.dto.ProjectPaymentInfoDTO;
import com.gok.pboot.financial.service.IProjectPaymentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 项目回款跟踪外部调用
 *
 * <AUTHOR>
 * @since 2024-02-28
 * @menu 项目回款跟踪外部调用
 */
@Inner(false)
@RestController
@RequiredArgsConstructor
@Api(tags = "项目回款跟踪外部调用")
@RequestMapping("/inner/project-payment")
public class ProjectPaymentClientController {

    private final IProjectPaymentService projectPaymentService;

    /**
     * 根据id更新数据
     *
     * @param projectPaymentInfoDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/update")
    @ApiOperation(value = "根据id更新数据", notes = "根据id更新数据")
    public R<Boolean> updateInner(@Valid @RequestBody ProjectPaymentInfoDTO projectPaymentInfoDTO) {
        return Boolean.TRUE.equals(projectPaymentService.update(projectPaymentInfoDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 认领操作(已认领 -> 待认领)
     *
     * @param id 项目回款跟踪id
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/claim/{id}")
    @ApiOperation(value = "认领操作(已认领 -> 待认领)", notes = "认领操作(已认领 -> 待认领)")
    public R<Boolean> unClaimInner(@PathVariable("id") Long id) {
        return Boolean.TRUE.equals(projectPaymentService.claim(id))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 认领操作(待认领 -> 已认领)
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/claim")
    @ApiOperation(value = "认领操作(待认领 -> 已认领)", notes = "认领操作(待认领 -> 已认领)")
    public R<Boolean> claimInner(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO) {
        return Boolean.TRUE.equals(projectPaymentService.claim(projectPaymentClaimDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

    /**
     * 锁定与取消锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/lock")
    @ApiOperation(value = "锁定与取消锁定", notes = "锁定与取消锁定")
    public R<Boolean> lock(@RequestBody ProjectPaymentDTO projectPaymentDTO) {
        return Boolean.TRUE.equals(projectPaymentService.lock(projectPaymentDTO))
                ? R.ok(Boolean.TRUE) : R.failed(Boolean.FALSE);
    }

}
