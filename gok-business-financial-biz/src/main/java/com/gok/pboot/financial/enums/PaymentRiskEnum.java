package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 回款风险 Enum
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
public enum PaymentRiskEnum  implements ValueEnum<Integer> {

    /**
     * 有
     */
    HAVE(0,"有"),

    /**
     * 无
     */
    NOTHING(1,"无");

    private final Integer value;

    private final String name;

    PaymentRiskEnum(Integer value,String name) {
        this.value = value;
        this.name = name;
    }
}
