package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 跨部门人工成本台账明细展示
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class CrossDeptLaborCostsDetailVO {

    /**
     * 二级部门排序
     */
    @ExcelIgnore
    private Integer secondaryDeptSortOrder;

    /**
     * 人员归属一级部门id
     */
    @ExcelIgnore
    private Long personnelDeptId;

    /**
     * 人员归属二级部门id
     */
    @ExcelIgnore
    private Long personnelSecondaryDeptId;

    /**
     * 人员归属二级部门
     */
    @ColumnWidth(30)
    @ExcelProperty("人员归属二级部门")
    private String personnelSecondaryDept;

    /**
     * 归属月份
     */
    @ColumnWidth(20)
    @ExcelProperty("归属月份")
    private String yearMonthDate;

    /**
     * 人数
     */
    @ExcelProperty("人数")
    private Integer peopleNum;

    /**
     * 人工成本
     */
    @ColumnWidth(20)
    @ExcelProperty("人工成本")
    private String laborCosts;

    /**
     * 项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目成本", "项目成本"})
    private String projectCost;

    /**
     * 项目成本占比
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目成本", "占比"})
    private String projectCostProportion;

    /**
     * 战略性项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"内部项目（战略性）", "战略性项目"})
    private String strategicProjectsCost;

    /**
     * 战略性项目占比
     */
    @ColumnWidth(30)
    @ExcelProperty({"内部项目（战略性）", "占比"})
    private String strategicProjectsCostProportion;

    /**
     * 管理性项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"内部项目（管理性）", "管理性项目"})
    private String manageCostProjectsCost;

    /**
     * 管理性项目占比
     */
    @ColumnWidth(30)
    @ExcelProperty({"内部项目（管理性）", "占比"})
    private String manageCostProjectsCostProportion;

    /**
     * 未分摊成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"未分摊成本", "未分摊成本"})
    private String unallocatedCost;

    /**
     * 未分摊成本占比
     */
    @ColumnWidth(30)
    @ExcelProperty({"未分摊成本", "占比"})
    private String unallocatedCostProportion;

    /**
     * 学员复用（实习生）人数
     */
    @ColumnWidth(15)
    @ExcelProperty({"学员复用（实习生）", "人数"})
    private Integer studentInternPeopleNum;

    /**
     * 学员复用（实习生）人工成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"学员复用（实习生）", "人工成本"})
    private String studentInternCost;

    /**
     * 学员复用（实习生）项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"学员复用（实习生）", "项目成本"})
    private String studentInternProjectsCost;

    /**
     * 学员复用（实习生）内部项目
     */
    @ColumnWidth(30)
    @ExcelProperty({"学员复用（实习生）", "内部项目"})
    private String studentInternInternalProjectsCost;

    /**
     * 学员复用（实习生）未分摊成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"学员复用（实习生）", "未分摊成本"})
    private String studentInternUnallocatedCost;

    /**
     * 学员复用（实习生）复用占比
     */
    @ColumnWidth(30)
    @ExcelProperty({"学员复用（实习生）", "复用占比"})
    private String studentInternCostProportion;

    /**
     * 学员复用（外部）人数
     */
    @ColumnWidth(15)
    @ExcelProperty({"学员复用（外部）", "人数"})
    private Integer studentOutsidePeopleNum;

    /**
     * 学员复用（外部）人工成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"学员复用（外部）", "人工成本"})
    private String studentOutsideCost;

    /**
     * 人均月成本自有人工
     */
    @ColumnWidth(30)
    @ExcelProperty({"人均月成本", "自有人工"})
    private String perCapitaMonthlyCostOwn;

    /**
     * 人均月成本学员复用
     */
    @ColumnWidth(30)
    @ExcelProperty({"人均月成本", "学员复用"})
    private String perCapitaMonthlyCostStusen;

    /**
     * 项目耗用均价（全部人员）项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（全部人员）", "项目成本"})
    private String totalPersonnelProjectCost;

    /**
     * 项目耗用均价（全部人员）项目工时
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（全部人员）", "项目工时"})
    private String totalPersonnelProjectHours;

    /**
     * 项目耗用均价（全部人员）项目单价
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（全部人员）", "项目单价"})
    private String totalPersonnelProjectPrice;

    /**
     * 项目耗用均价（不含实习生）项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（不含实习生）", "项目成本"})
    private String excludingInternsProjectCost;

    /**
     * 项目耗用均价（不含实习生）项目工时
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（不含实习生）", "项目工时"})
    private String excludingInternsProjectHours;

    /**
     * 项目耗用均价（不含实习生）项目单价
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（不含实习生）", "项目单价"})
    private String excludingInternsProjectPrice;

    /**
     * 项目耗用均价（实习生）项目成本
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（实习生）", "项目成本"})
    private String internsProjectCost;

    /**
     * 项目耗用均价（实习生）项目工时
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（实习生）", "项目工时"})
    private String internsProjectHours;

    /**
     * 项目耗用均价（实习生）项目单价
     */
    @ColumnWidth(30)
    @ExcelProperty({"项目耗用均价（实习生）", "项目单价"})
    private String internsProjectPrice;

}