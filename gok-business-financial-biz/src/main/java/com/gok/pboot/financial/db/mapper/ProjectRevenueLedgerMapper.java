package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.data.datascope.BaseMapper;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedger;
import com.gok.pboot.financial.dto.ProjectRevenueLedgerDTO;
import com.gok.pboot.financial.vo.ProjectRevenueLedger2VO;
import com.gok.pboot.financial.vo.ProjectRevenueLedgerPushVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目收入台帐Mapper
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface ProjectRevenueLedgerMapper extends BaseMapper<ProjectRevenueLedger> {

    /**
     * 模糊查询带分页
     *
     * @param page {@link Page}
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link Page}<{@link ProjectRevenueLedger2VO}>
     */
    @BusinessDataScope(deptOrUser= "user",
            scopeUserNameList = {"salesman_user_id", "applicant_user_id"})
    Page<ProjectRevenueLedger2VO> findPage(Page<ProjectRevenueLedger2VO> page, @Param("query") ProjectRevenueLedgerDTO projectRevenueLedgerDTO);

    /**
     * 查询
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link Page}<{@link ProjectRevenueLedger2VO}>
     */
    @BusinessDataScope(deptOrUser= "user",
            scopeUserNameList = {"salesman_user_id", "applicant_user_id"})
    List<ProjectRevenueLedger2VO> findPage( @Param("query") ProjectRevenueLedgerDTO projectRevenueLedgerDTO);

    /**
     * 模糊查询带分页
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link List}<{@link ProjectRevenueLedger}>
     */
    @BusinessDataScope(deptOrUser= "user",
            scopeUserNameList = {"salesman_user_id", "applicant_user_id"})
    List<ProjectRevenueLedger> findList(@Param("query") ProjectRevenueLedgerDTO projectRevenueLedgerDTO);

    /**
     * 更新备注
     *
     * @param id 项目收入id
     * @param remarks 备注
     * @return {@code true} or {@link false}
     */
    Boolean updateProjectById(@Param("id") Long id,
                              @Param("remarks") String remarks);

    /**
     * 根据项目应收id查询信息
     *
     * @param id 项目应收id
     * @return {@link ProjectRevenueLedger}
     */
    ProjectRevenueLedger selectOneById(@Param("id") Long id);


    /**
     * ..
     *
     * @param ids id集合
     * @return {@link List}<{@link ProjectRevenueLedgerPushVO}>
     */
    List<ProjectRevenueLedgerPushVO> selList(@Param("ids")  List<String> ids);

    /**
     * 修改推送状态
     *
     * @param projectRevenueLedger {@link ProjectRevenueLedger}
     */
    void updatePushStatus(@Param("projectRevenueLedger")  ProjectRevenueLedger projectRevenueLedger);


//    /**
//     * 统计收入金额总和
//     *
//     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO}
//     * @return {@link JSONObject }
//     */
//    @BusinessDataScope(deptOrUser= "user", scopeUserNameList = {"salesman_user_id", "applicant_user_id"})
//    Map<String, BigDecimal> statisticsAmount(@Param("query") ProjectRevenueLedgerDTO projectRevenueLedgerDTO);
}