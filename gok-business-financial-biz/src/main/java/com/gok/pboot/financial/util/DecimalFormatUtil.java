package com.gok.pboot.financial.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Optional;

/**
 * 十进制格式化工具
 *
 * <AUTHOR>
 * @date 2023/08/28
 */
public class DecimalFormatUtil {


    public static final String ZERO="0.00";

    public static final String NULL=null;

    /**
     * @param bigDecimal 示例 19.99
     * @param newScale 示例 2
     * @param roundingMode 示例 RoundingMode.DOWN
     * @param decimalFormat 示例 new DecimalFormat("#,##0.00") or new DecimalFormat("0.00")
     * @return {@link String}
     */
    public static String setAndValidate(String bigDecimal, int newScale, RoundingMode roundingMode, DecimalFormat decimalFormat) {
        if (Optional.ofNullable(bigDecimal).isPresent() && !bigDecimal.isEmpty()) {
            BigDecimal decimal = new BigDecimal(bigDecimal);
            decimal.setScale(newScale, roundingMode);
            bigDecimal = decimalFormat.format(decimal);
        }
        return bigDecimal;
    }

    /**
     * 数字保留两位小数，千分位
     * @param bigDecimal
     * @return
     */
    public static String setThousandthAndTwoDecimal(BigDecimal bigDecimal,String defaultValue){
        if (Optional.ofNullable(bigDecimal).isPresent()) {
            // 创建DecimalFormat对象，并设置模式来实现千分位和两位小数
            DecimalFormat formatter = new DecimalFormat("##,##0.00");

            // 格式化数字
            return formatter.format(bigDecimal);
        }
       return defaultValue;
    }

}
