package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目回款跟踪详情展示
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentInfoDetailVO {

    /**
     * id
     */
    private Long id;

    /**
     * 回款信息详情
     */
    private ProjectPaymentDetailVO paymentDetailVo;

    /**
     * 认领信息详情列表
     */
    private List<ProjectPaymentClaimDetailVO> claimDetailList;

}
