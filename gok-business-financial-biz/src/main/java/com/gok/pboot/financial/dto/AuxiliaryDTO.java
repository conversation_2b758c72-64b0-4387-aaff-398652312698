package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  凭证创建辅佐项集合
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuxiliaryDTO {
    /**
     * 部门
     */
    private AuxAccDepartmentDTO AuxAccDepartment;

    /**
     * 项目
     */
    private AuxAccProjectDTO AuxAccProject;

    /**
     * 往来单位
     */
    private AuxAccCustomerDTO AuxAccCustomer;

    /**
     * 扩展辅助项1
     */
    private ExAuxAcc1 ExAuxAcc1;
}
