package com.gok.pboot.financial.vo;

import com.gok.pboot.financial.dto.SalesReceiptPaymentDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 销售收款计划任务进度详情VO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptTaskDetailVO {
    /**
     * 任务id
     */
    private Long id;

    /**
    * 审批节点名称
    */
    private List<String> customerPaymentProcessList;

    /**
     * 客户付款审批流程
     */
    private String customerPaymentProcess;

    /**
     * 当前审批节点
     */
    private String currentApprovalProcess;

    /**
     * 审批进度
     */
    private String approvalProgress;

    /**
     * 更新日期
     */
    private String updateDate;

    /**
     * 所需材料
     */
    private String requiredMaterials;

    /**
     * 回款风险(0有 1无)
     */
    private Integer paymentRisk;

    /**
     * 回款风险(0有 1无)
     */
    private String paymentRiskStr;

    /**
     * 进度说明
     */
    private String progressDescription;

    /**
     * 下一步工作
     */
    private String nextStepWork;

}