package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.components.data.datascope.BusinessDataScope;
import com.gok.pboot.financial.db.entity.ProjectLaborCost;
import com.gok.pboot.financial.dto.CrossDeptLaborShareCostsDTO;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.vo.CrossDeptProjectLaborCostVO;
import com.gok.pboot.financial.vo.ProjectLaborCostVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目人工成本分摊汇总 Mapper
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
public interface ProjectLaborCostMapper extends BaseMapper<ProjectLaborCost> {

    /**
     * 模糊查询带分页
     *
     * @param page                分页请求
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}<{@link ProjectLaborCost}>
     */
    @BusinessDataScope(scopeDeptNameList = {"income_dept_id", "income_secondary_dept_id", "personnel_dept_id", "personnel_secondary_dept_id"})
    Page<ProjectLaborCost> findPage(Page<ProjectLaborCost> page, @Param("query") ProjectLaborCostDTO projectLaborCostDto);


    /**
     * 查找页面
     *
     * @param projectLaborCostDto 项目人工成本 DTO
     * @return {@link Page }<{@link ProjectLaborCost }>
     */
    @BusinessDataScope(scopeDeptNameList = {"income_dept_id", "income_secondary_dept_id", "personnel_dept_id", "personnel_secondary_dept_id"})
    List<ProjectLaborCost> findPage(@Param("query") ProjectLaborCostDTO projectLaborCostDto);

    /**
     * 导出Excel选中数据
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostVO}>
     */
    @BusinessDataScope(scopeDeptNameList = {"income_dept_id", "income_secondary_dept_id", "personnel_dept_id", "personnel_secondary_dept_id"})
    List<ProjectLaborCost> exportList(@Param("query") ProjectLaborCostDTO projectLaborCostDTO);

    /**
     * ..
     *
     * @param ids id集合
     * @return {@link List}<{@link ProjectLaborCost}>
     */
    List<ProjectLaborCost> selList(@Param("ids") List<String> ids);

    /**
     * 修改推送状态
     *
     * @param projectLaborCost {@link ProjectLaborCost}
     */
    void updatePushStatus(@Param("projectLaborCost") ProjectLaborCost projectLaborCost);

    /**
     * 跨部门人工成本所属部门对应收入归属一级部门
     *
     * @param crossDeptLaborShareCostsDTO {@link CrossDeptLaborShareCostsDTO}
     * @return {@link List}<{@link ProjectLaborCost}>
     */
    List<CrossDeptProjectLaborCostVO> incomeDeptList(@Param("query") CrossDeptLaborShareCostsDTO crossDeptLaborShareCostsDTO);
}
