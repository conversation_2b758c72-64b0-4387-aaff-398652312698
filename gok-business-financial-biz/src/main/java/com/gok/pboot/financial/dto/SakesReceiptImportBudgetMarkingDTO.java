package com.gok.pboot.financial.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 销售计划导入预算标记Excel
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SakesReceiptImportBudgetMarkingDTO {

    /**
     * 款项id
     */
    @ExcelProperty(value ="款项ID", index = 0)
    private String paymentId;

    /**
     * 预算标记
     */
    @ExcelProperty(value ="预算标注", index = 1)
    private String budgetMarking;

}
