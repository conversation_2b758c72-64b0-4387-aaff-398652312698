package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.PaymentMethod;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.dto.PaymentMethodDTO;
import com.gok.pboot.financial.vo.PaymentMethodVO;

import java.util.List;

/**
 * 收款方式Service
 *
 * <AUTHOR>
 * @description 针对表【payment_method(收款方式)】的数据库操作Service
 * @createDate 2023-09-27 10:46:49
 */
public interface IPaymentMethodService extends IService<PaymentMethod> {

    /**
     * 分页查询
     *
     * @param page 查询条件
     * @return {@link Page}<{@link PaymentMethodVO}>
     */
    Page<PaymentMethodVO> findPage(Page<PaymentMethodVO> page);

    /**
     * 新增or修改接口
     *
     * @param dto dto类
     * @return {@link R}
     */
    R<String> addOrEdit(PaymentMethodDTO dto);


    /**
     * 删除
     *
     * @param idList id列表
     * @return {@link R}
     */
    R<String> delete(List<Long> idList);

}
