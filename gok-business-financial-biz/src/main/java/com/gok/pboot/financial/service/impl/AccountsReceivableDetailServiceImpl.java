package com.gok.pboot.financial.service.impl;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.db.entity.AccountsReceivable;
import com.gok.pboot.financial.db.entity.AccountsReceivableDetail;
import com.gok.pboot.financial.db.mapper.AccountsReceivableDetailMapper;
import com.gok.pboot.financial.db.mapper.AccountsReceivableMapper;
import com.gok.pboot.financial.enums.ProjectStatusEnum;
import com.gok.pboot.financial.service.IAccountsReceivableDetailService;
import com.gok.pboot.financial.util.DateUtils;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 应收账款详情 Service
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountsReceivableDetailServiceImpl extends ServiceImpl<AccountsReceivableDetailMapper, AccountsReceivableDetail> implements IAccountsReceivableDetailService {

    private final AccountsReceivableMapper accountsReceivableMapper;

    private final DBApi dbApi;

    /**
     * 应收账款明细
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link AccountsReceivableDetailVO}
     */
    @Override
    public AccountsReceivableDetailVO accountsReceivableDetail(Long projectId, Long contractId) {
        // 根据项目id、合同id查询应收账款台账
        AccountsReceivable accountsReceivable = accountsReceivableMapper.selectOneByProjectIdAndContractId(projectId, contractId);
        if (!Optional.ofNullable(accountsReceivable).isPresent()) {
            return new AccountsReceivableDetailVO();
        }
        // 根据项目id、合同id查询应收账款台账明细
        List<AccountsReceivableDetail> accountsReceivableDetailList = baseMapper.selectListByProductId(projectId, contractId);
        if (!Optional.ofNullable(accountsReceivableDetailList).isPresent()) {
            return new AccountsReceivableDetailVO();
        }

        // 所属月份、回款金额、开票金额
        List<MouthForMoneyVO> mouthForMoneyVOList = new LinkedList<>();
        accountsReceivableDetailList.forEach(a -> {
            MouthForMoneyVO mouthForMoneyVO = MouthForMoneyVO.builder()
                    .statisticsTime(DateUtils.dateTrans(a.getStatisticsTime()))
                    .payBack(MoneyUtils.getInstance().transType(a.getPayBack()))
                    .invoicing(MoneyUtils.getInstance().transType(a.getInvoicing()))
                    .build();
            mouthForMoneyVOList.add(mouthForMoneyVO);
        });
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(a,b)->a));
        return AccountsReceivableDetailVO.builder()
                .projectId(projectId)
                .contractId(contractId)
                .projectStatus(accountsReceivable.getProjectStatus())
                .projectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, accountsReceivable.getProjectStatus()))
                .projectNo(accountsReceivable.getProjectNo())
                .projectName(accountsReceivable.getProjectName())
                .customerName(accountsReceivable.getCustomerName())
                .contractCode(accountsReceivable.getContractCode())
                .signingDate(DateUtils.signingDateTrans(accountsReceivable.getSigningDate()))
                .contractMoney(MoneyUtils.getInstance().transType(accountsReceivable.getContractMoney()))
                .attributableSubject(accountsReceivable.getAttributableSubject())
                .attributableSubjectTxt(attributableSubjectMap.getOrDefault(Integer.valueOf(accountsReceivable.getAttributableSubject()), StrUtil.EMPTY))
                .firstDepartment(accountsReceivable.getFirstDepartment())
                .salesmanUserName(accountsReceivable.getSalesmanUserName())
                .managerUserName(accountsReceivable.getManagerUserName())
                .mouthForMoneyVOList(mouthForMoneyVOList)
                .payBack(MoneyUtils.getInstance().transType(accountsReceivable.getPayBack()))
                .invoicing(MoneyUtils.getInstance().transType(accountsReceivable.getInvoicing()))
                .build();
    }

    /**
     * 获取回款与发票月份详情
     *
     * @param yearMonthDate 年月
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link List}<{@link PayAndInvoicingVO}>
     */
    @Override
    public PayAndInvoicingVO payBackAndInvoicingDetailByDate(String yearMonthDate, Long projectId, Long contractId) {
        // 回款
        Map<Integer, String> projectMap = dbApi.projectDict(DictConstant.DEPT_FILED_ID).stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
        List<PayBackDocumentVO> payBackDocumentVOList = dbApi.payBackDetails(String.valueOf(projectId), String.valueOf(contractId))
                .stream()
                .filter(p -> p.getYearMonthDate().contains(DateUtils.dateTrans(yearMonthDate).substring(0, 7)))
                .map(p -> {
                    if (Optional.ofNullable(p.getMoney()).isPresent()) {
                        BigDecimal money = new BigDecimal(p.getMoney());
                        p.setMoney(money.compareTo(BigDecimal.ZERO) != NumberUtils.INTEGER_ZERO
                                ? this.addSeparator(money.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString())
                                : MoneyUtils.TYPE);
                    } else {
                        p.setMoney(null);
                    }
                    p.setFirstDeptName(projectMap.get(Integer.valueOf(p.getFirstDeptName())));
                    return p;
                }).collect(Collectors.toList());
        // 发票
        Map<Integer, String> invoicingTypeMap = dbApi.projectDict(DictConstant.INVOICING_TYPE_ID).stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
        Map<Integer, String> invoicingTaxRateMap = dbApi.projectDict(DictConstant.INVOICING_TAX_RATE_ID).stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
        List<InvoicingDocumentVO> invoicingDocumentVOList = dbApi.invoicingDetails(String.valueOf(projectId), String.valueOf(contractId))
                .stream()
                .filter(i -> i.getYearMonthDate().contains(DateUtils.dateTrans(yearMonthDate).substring(0, 7)))
                .map(i -> {
                    if (Optional.ofNullable(i.getMoney()).isPresent()) {
                        BigDecimal money = new BigDecimal(i.getMoney());
                        i.setMoney(money.compareTo(BigDecimal.ZERO) != NumberUtils.INTEGER_ZERO
                                ? this.addSeparator(money.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString())
                                : MoneyUtils.TYPE);
                    } else {
                        i.setMoney(null);
                    }
                    i.setType(invoicingTypeMap.get(Integer.valueOf(i.getType())));
                    i.setTaxRate(invoicingTaxRateMap.get(Integer.valueOf(i.getTaxRate())));
                    return i;
                })
                .collect(Collectors.toList());
        return PayAndInvoicingVO.builder()
                .payPackDocumentVOList(payBackDocumentVOList)
                .invoicingDocumentVOList(invoicingDocumentVOList)
                .build();
    }

    /**
     * 获取回款与发票详情
     *
     * @param projectId 项目id
     * @param contractId 合同id
     * @return {@link PayAndInvoicingVO}
     */
    @Override
    public PayAndInvoicingVO payBackAndInvoicingDetail(Long projectId, Long contractId) {
        // 回款
        Map<Integer, String> projectMap = dbApi.projectDict(DictConstant.DEPT_FILED_ID).stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
        List<PayBackDocumentVO> payBackDocumentVOList = dbApi.payBackDetails(String.valueOf(projectId), String.valueOf(contractId));
        payBackDocumentVOList.forEach(p -> {
            if (Optional.ofNullable(p.getMoney()).isPresent()) {
                BigDecimal money = new BigDecimal(p.getMoney());
                p.setMoney(money.compareTo(BigDecimal.ZERO) != NumberUtils.INTEGER_ZERO
                        ? this.addSeparator(money.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString())
                        : MoneyUtils.TYPE);
            } else {
                p.setMoney(null);
            }
            p.setFirstDeptName(projectMap.get(Integer.valueOf(p.getFirstDeptName())));
        });
        // 发票
        Map<Integer, String> invoicingTypeMap = dbApi.projectDict(DictConstant.INVOICING_TYPE_ID).stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
        Map<Integer, String> invoicingTaxRateMap = dbApi.projectDict(DictConstant.INVOICING_TAX_RATE_ID).stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
        List<InvoicingDocumentVO> invoicingDocumentVOList = dbApi.invoicingDetails(String.valueOf(projectId), String.valueOf(contractId));
        invoicingDocumentVOList.forEach(i -> {
            if (Optional.ofNullable(i.getMoney()).isPresent()) {
                BigDecimal money = new BigDecimal(i.getMoney());
                i.setMoney(money.compareTo(BigDecimal.ZERO) != NumberUtils.INTEGER_ZERO
                        ? this.addSeparator(money.setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP).toString())
                        : MoneyUtils.TYPE);
            } else {
                i.setMoney(null);
            }
            i.setType(invoicingTypeMap.get(Integer.valueOf(i.getType())));
            i.setTaxRate(invoicingTaxRateMap.get(Integer.valueOf(i.getTaxRate())));
        });

        return PayAndInvoicingVO.builder()
                .payPackDocumentVOList(payBackDocumentVOList)
                .invoicingDocumentVOList(invoicingDocumentVOList)
                .build();
    }

    /**
     * V1.1.1 优化显示：采用千位分隔符（每三位添加一个分割符号）
     *
     * @param num 金额
     * @return 金额
     */
    private String addSeparator(String num) {
        // 切割金额 前者为整数 后者为小数
        String[] numArr = num.split(StrPool.BACKSLASH + StrPool.DOT);
        num = numArr[NumberUtils.INTEGER_ZERO];

        int length = num.length();
        List<String> list = new ArrayList<>();
        while (length > 3) {
            list.add(num.substring(length - 3, length));
            length -= 3;
        }
        // 将前面小于三位的数字添加到ArrayList中
        list.add(num.substring(0, length));
        StringBuilder stringBuilder = new StringBuilder();

        // 倒序拼接
        for (int i = list.size() - 1; i > 0; --i) {
            stringBuilder.append(list.get(i)).append(StrPool.COMMA);
        }

        return stringBuilder.append(list.get(NumberUtils.INTEGER_ZERO))
                + StrPool.DOT
                + numArr[NumberUtils.INTEGER_ONE];
    }

}
