package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.gok.module.excel.api.annotation.CustomMerge;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 项目回款追踪页面展示
 *
 * <AUTHOR>
 * @since 2023-09-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ProjectPaymentClaimExcelVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 单据编号
     */
    @ColumnWidth(20)
    @ExcelProperty("单据编号")
    @CustomMerge()
    private String documentNumber;

    /**
     * 收款公司code
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ExcelIgnore
    private Integer paymentCompany;

    /**
     * 收款公司value
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ColumnWidth(40)
    @ExcelProperty("收款公司")
    @CustomMerge()
    private String paymentCompanyTxt;

    /**
     * 客户名称
     */
    @ColumnWidth(25)
    @ExcelProperty("客户名称")
    @CustomMerge()
    private String customerName;

    /**
     * 企业名称
     */
    @ColumnWidth(25)
    @ExcelProperty("企业名称")
    @CustomMerge()
    private String enterpriseName;

    /**
     * 收款日期
     */
    @ColumnWidth(20)
    @ExcelProperty("收款日期")
    @CustomMerge()
    private String paymentDate;

    /**
     * 收款金额
     */
    @ColumnWidth(20)
    @ExcelProperty("收款金额")
    @CustomMerge()
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    @ColumnWidth(20)
    @ExcelProperty("收款平台")
    @CustomMerge()
    private String paymentPlatform;

    /**
     * 收款备注
     */
    @ColumnWidth(60)
    @ExcelProperty("收款备注")
    @CustomMerge()
    private String paymentNote;

    /**
     * id
     */
    @ExcelIgnore
    private Long projectPaymentId;

    /**
     * 回款类型
     */
    @ExcelIgnore
    private Integer paymentType;

    /**
     * 回款类型
     */
    @ColumnWidth(20)
    @ExcelProperty("回款类型")
    private String paymentTypeText;

    /**
     * 客户经理id
     */
    @ExcelIgnore
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    @ColumnWidth(20)
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编码
     */
    @ColumnWidth(25)
    @ExcelProperty("项目编码")
    private String projectNumber;

    /**
     * 项目名称
     */
    @ColumnWidth(25)
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同编码
     */
    @ColumnWidth(25)
    @ExcelProperty("合同编码")
    private String contractNumber;

    /**
     * 合同名称
     */
    @ColumnWidth(25)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 关联客户
     */
    @ColumnWidth(25)
    @ExcelProperty("关联客户")
    private String relatedCustomerName;

    /**
     * 款项ID
     */
    @ColumnWidth(25)
    @ExcelProperty("款项ID")
    private Long contractPaymentId;

    /**
     * 款项名称
     * 原合同款项明细
     */
    @ColumnWidth(25)
    @ExcelProperty("款项名称")
    private String contractPayment;

    /**
     * 款项金额
     */
    @ColumnWidth(20)
    @ExcelProperty("款项金额")
    private BigDecimal paymentMoney;

    /**
     * 认领金额
     */
    @ColumnWidth(20)
    @ExcelProperty("认领金额")
    private BigDecimal claimMoney;

    /**
     * 回款归属一级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款归属一级部门")
    private String paymentDept;

    /**
     * 回款归属二级部门
     */
    @ColumnWidth(25)
    @ExcelProperty("回款归属二级部门")
    private String paymentSecondaryDept;

    /**
     * 业务板块
     */
    @ExcelIgnore
    private Integer businessBlock;

    /**
     * 业务板块
     */
    @ColumnWidth(20)
    @ExcelProperty("业务板块")
    private String businessBlockTxt;

    /**
     * 技术类型
     */
    @ExcelIgnore
    private Integer skillType;

    /**
     * 技术类型
     */
    @ColumnWidth(20)
    @ExcelProperty("技术类型")
    private String skillTypeTxt;

    /**
     * 保证金编号
     */
    @ColumnWidth(20)
    @ExcelProperty("保证金编号")
    private String marginCode;

    /**
     * 保证金类型
     */
    @ExcelIgnore
    private Integer marginType;

    /**
     * 保证金类型
     */
    @ColumnWidth(20)
    @ExcelProperty("保证金类型")
    private String marginTypeTxt;

    /**
     * 保证金金额
     */
    @ColumnWidth(20)
    @ExcelProperty("保证金金额")
    private BigDecimal marginMoney;

    /**
     * 凭证编号
     */
    @ExcelIgnore
    private String voucherNumber;

    /**
     * 认领状态code（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ExcelIgnore
    private Integer claimStatus;

    /**
     * 认领状态value（0已认领，1未认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("认领状态")
    @CustomMerge()
    private String claimStatusTxt;

    /**
     * 锁定状态code（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ExcelIgnore
    private Integer lockStatus;

    /**
     * 锁定状态value（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("锁定状态")
    @CustomMerge()
    private String lockStatusTxt;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ExcelIgnore
    private Integer pushStatus;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    @ColumnWidth(20)
    @ExcelProperty("推送状态")
    @CustomMerge()
    private String pushStatusTxt;

    /**
     * 创建人名称
     */
    @ColumnWidth(20)
    @ExcelProperty("创建人")
    @CustomMerge()
    private String creatorName;

    /**
     * 认领人id
     */
    @ExcelIgnore
    private Long claimantId;

    /**
     * 认领人姓名
     */
    @ColumnWidth(20)
    @ExcelProperty("认领人")
    @CustomMerge()
    private String claimantName;

    /**
     * 认领日期
     */
    @ExcelProperty("认领日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @CustomMerge()
    private String claimantDate;

    /**
     * 认领备注
     */
    @ColumnWidth(20)
    @ExcelProperty("认领备注")
    private String claimRemark;

    /**
     * 资金账户
     * （原银行账户）
     */
    @ExcelProperty("资金账户")
    @CustomMerge()
    private String bankAccount;


}
