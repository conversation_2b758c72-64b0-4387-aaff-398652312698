package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 项目回款类型 Enum
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Getter
public enum PaymentTypeEnum implements ValueEnum<Integer> {

    /**
     * 项目回款
     */
    PROJECT_PAYMENT(0, "项目回款"),

    /**
     * C端回款
     */
    C_PAYMENT(1, "C端回款"),

    /**
     * 保证金
     */
    MARGIN(2, "保证金"),

    /**
     * 其他回款
     */
    OTHER_PAYMENT(3, "其他回款"),

    /**
     * 利息
     */
    INTEREST(4, "利息");

    private final Integer value;

    private final String name;

    PaymentTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

}
