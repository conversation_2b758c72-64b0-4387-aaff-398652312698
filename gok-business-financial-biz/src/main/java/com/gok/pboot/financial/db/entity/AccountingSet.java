package com.gok.pboot.financial.db.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 会计账套
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("会计账套")
@TableName("accounting_set")
@EqualsAndHashCode(callSuper = true)
public class AccountingSet extends Model<AccountingSet> {

    /**
     * id
     */
    @ApiModelProperty("id")
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 账套编码
     */
    @ApiModelProperty("账套编码")
    private String accountingCode;

    /**
     * 归属主体
     */
    @ApiModelProperty("归属主体")
    private String accountingSubject;

    /**
     * 账套账号
     */
    @ApiModelProperty("账套账号")
    private String accountingAccount;

    /**
     * 账套密码
     */
    @ApiModelProperty("账套密码")
    private String accountingPassword;

    /**
     * 启用状态（0启用；1禁用）
     */
    @ApiModelProperty("启用状态")
    private Integer enableStatus;

    /**
     * 账套备注
     */
    @ApiModelProperty("账套备注")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String accountingRemarks;

    /**
     * 默认资金账户
     */
    @ApiModelProperty("默认资金账户")
    private Long fundAccountId;

    /**
     * 账套类型
     */
    @ApiModelProperty("账套类型")
    private String accountingType;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    @ApiModelProperty("删除标记")
    @TableLogic(value = "0", delval = "1")
    private String delFlag;

    /**
     * 所属租户
     */
    @ApiModelProperty("所属租户")
    private Long tenantId;

}