package com.gok.pboot.financial.util;

import com.alibaba.fastjson.JSONArray;

import com.gok.bcp.upms.dto.OaGetTokenDTO;
import com.gok.pboot.exception.ServiceException;
import com.gok.pboot.financial.common.client.OaClient;
import com.gok.pboot.financial.vo.OaFileVo;
import com.gok.pboot.utils.RSAUtils;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * OA接口接入
 *
 * <AUTHOR>
 * @date 2023/12/7
 */
@Component
@Slf4j
public class OaUtil {

    /**
     * oa的url地址
     */
    @Value("${gok-oa.url}")
    private String url;

    /**
     * oa的资源appId
     */
    @Value("${gok-oa.resourcesAppId}")
    private String appId;

    /**
     * oa的spk
     */
    @Value("${gok-oa.spk}")
    private String spk;

    /**
     * oa的secret
     */
    @Value("${gok-oa.secret}")
    private String secret;

    /**
     * 返回的状态字段
     */
    public static final String CODE = "code";

    /**
     * 是否获取成功的字段
     */
    public static final String SUCCESS = "SUCCESS";

    /**
     * 获取到的字段
     */
    public static final String DATA = "data";

    @Resource
    private OaClient oaClient;

    /**
     * 获取token
     */
    public String getToken() {
        OaGetTokenDTO oaClientToken = oaClient.getToken(url, appId, secret);
        String token = oaClientToken.getToken();
        if (StringUtils.isEmpty(token)) {
            throw new ServiceException("OA调用异常，无法获取token信息");
        }
        return token;
    }

    /**
     * 获取流程相关资源
     */
    public Map getRequestResources(String requestId, String userId) {
        // 获取token
        String token = getToken();
        if (userId == null) {
            throw new ServiceException("必须传入userID");
        }
        // 对userID进行加密
        String encryptUserId = RSAUtils.encrypt(userId, spk);
        Map requestResources = oaClient.getRequestResources(url, requestId, token, appId, encryptUserId);
        return requestResources;
    }

    /**
     * 获取OA返回的资源数据
     */
    public List<OaFileVo> getResourcesData(String requestId, String userId) {
        Map requestResources = this.getRequestResources(requestId, userId);

        if (SUCCESS.equals(requestResources.get(CODE))) {
            JSONArray data = (JSONArray) requestResources.get(DATA);
            return data.toJavaList(OaFileVo.class);
        }

        return ImmutableList.of();
    }

}
