package com.gok.pboot.financial.db.mapper;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.FinanceProcessForm;
import com.gok.pboot.financial.dto.FinancialProcessFormPageDTO;
import com.gok.pboot.financial.vo.FinanceProcessFormBaseVO;
import com.gok.pboot.financial.vo.FinanceProcessFormVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 会计账套数据访问层
 *
 * <AUTHOR>
 * @since 2024-03-25
 */
@Mapper
public interface FinanceProcessFormMapper extends BaseMapper<FinanceProcessForm> {
    /**
     * 条件查询带分页
     *
     * @param dto 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link FinanceProcessFormVO}>
     */
    Page<FinanceProcessFormVO> findPage(Page<FinanceProcessFormVO> page, @Param("query")FinancialProcessFormPageDTO dto);

    List<FinanceProcessFormVO> findPage(@Param("query")FinancialProcessFormPageDTO dto);

    /**
     * 查找基本列表
     *
     * @param dto DTO
     * @return {@link List }<{@link FinanceProcessFormBaseVO }>
     */
    List<FinanceProcessFormBaseVO> findBaseList(@Param("query")FinancialProcessFormPageDTO dto);

    JSONObject getStatistics(@Param("ids") List<Long> ids);


}