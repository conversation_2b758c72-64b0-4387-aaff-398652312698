package com.gok.pboot.financial.service;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.WageCost;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import com.gok.pboot.financial.vo.PushResultVO;
import com.gok.pboot.financial.vo.WageCostVO;

import java.util.List;

/**
 * 工资成本Service
 *
 * <AUTHOR>
 * @since 2023-09-04
 */
public interface IWageCostService extends IService<WageCost> {

    /**
     * 模糊查询带分页
     *
     * @param page        分页请求
     * @param wageCostDTO 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link ProjectLaborCostDetailVO}>>
     */
    StatisticsPage<WageCostVO> findPage(StatisticsPage<WageCostVO> page, WageCostDTO wageCostDTO);

    /**
     * 发薪主体下拉框
     *
     * @return R
     */
    R<List<String>> salaryPaidSubject();

    /**
     * 导出Excel选中的数据
     *
     * @param dto 包含导入id
     * @return {@link List}<{@link WageCostVO}>
     */
    List<WageCostVO> export(WageCostDTO dto);

    /**
     * 推送接口
     *
     * @param ids ids
     * @return PushResultVO
     */
    PushResultVO push(List<String> ids);

    /**
     * 部门树
     *
     * @return {@link List}<{@link Tree}>
     */
    List<Tree<Long>> accountDeptBox();
}
