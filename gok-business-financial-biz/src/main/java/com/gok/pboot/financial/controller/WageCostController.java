package com.gok.pboot.financial.controller;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.dto.UniqueFlagDTO;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.service.IWageCostService;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import com.gok.pboot.financial.vo.PushResultVO;
import com.gok.pboot.financial.vo.WageCostVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 工资成本
 *
 * <AUTHOR>
 * @menu 成本管理-工资汇总
 * @since 2023-09-04
 */
@RestController
@RequestMapping("/wage-cost")
@RequiredArgsConstructor
@Api(tags = "工资成本")
public class WageCostController {

    private final IWageCostService wageCostService;

    /**
     * 模糊查询带分页
     *
     * @param wageCostDTO 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link ProjectLaborCostDetailVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('wagesSummary')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<WageCostVO>> findPage(@RequestBody WageCostDTO wageCostDTO) {
        return R.ok(wageCostService.findPage(new StatisticsPage<>(wageCostDTO.getCurrent(), wageCostDTO.getSize()), wageCostDTO));
    }

    /**
     * 发薪主体下拉框
     *
     * @return R
     */
    @GetMapping("/salaryPaidSubject")
    @ApiOperation(value = "发薪主体下拉框", notes = "发薪主体下拉框")
    public R<List<String>> salaryPaidSubject() {
        return wageCostService.salaryPaidSubject();
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param wageCostDTO {@link WageCostDTO}
     * @return {@link List}
     */
    @PostMapping("/export")
    @ResponseExcel(async = true, name = "工资汇总", functionEnum = FunctionEnum.FINANCIAL_WAGE_COST_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PreAuthorize("@pms.hasPermission('wagesSummary/export')")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List export(@RequestBody WageCostDTO wageCostDTO) {
        return wageCostService.export(wageCostDTO);
    }

    /**
     * 推送接口
     *
     * @param ids ids
     * @return PushResultVO
     */
    @PostMapping("/push")
    @PreAuthorize("@pms.hasPermission('wagesSummary/push')")
    @ApiOperation(value = "推送", notes = "推送")
    public PushResultVO push(@Valid @RequestBody UniqueFlagDTO ids) {
        return wageCostService.push(ids.getIds());
    }

    /**
     * 核算部门下拉框
     *
     * @return R
     */
    @GetMapping("/accountDept")
    @ApiOperation(value = "核算部门下拉框", notes = "核算部门下拉框")
    public R<List<Tree<Long>>> accountDeptBox(){
        return R.ok(wageCostService.accountDeptBox());
    }
}
