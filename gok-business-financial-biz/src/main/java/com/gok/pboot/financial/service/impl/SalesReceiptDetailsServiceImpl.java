package com.gok.pboot.financial.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.db.entity.SalesReceipt;
import com.gok.pboot.financial.db.entity.SalesReceiptDetails;
import com.gok.pboot.financial.db.mapper.SalesReceiptDetailsMapper;
import com.gok.pboot.financial.db.mapper.SalesReceiptMapper;
import com.gok.pboot.financial.enums.InvoicingStatusEnum;
import com.gok.pboot.financial.enums.PaymentStatusEnum;
import com.gok.pboot.financial.enums.ProjectStatusEnum;
import com.gok.pboot.financial.service.ISalesReceiptDetailsService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.MoneyUtils;
import com.gok.pboot.financial.vo.*;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 销售收款明细ServiceImpl
 *
 * <AUTHOR>
 * @description 针对表【sales_receipt_details(销售收款明细)】的数据库操作Service实现
 * @createDate 2023-10-07 11:24:50
 */
@Service
public class SalesReceiptDetailsServiceImpl extends ServiceImpl<SalesReceiptDetailsMapper, SalesReceiptDetails>
        implements ISalesReceiptDetailsService {

    @Resource
    private SalesReceiptMapper salesReceiptMapper;

    @Resource
    private DBApi dbApi;

    @Override
    public SalesReceiptDetailsVO salesReceiptDetailList(Long id, Long contractId) {
        MoneyUtils moneyUtils = MoneyUtils.getInstance();

        //1、查询销售收款明细数据
        List<SalesReceiptDetails> salesReceiptDetailsList = baseMapper.selectByMainId(contractId);
        List<SalesReceiptDetailsTableVO> voList = new ArrayList<>();
        for (SalesReceiptDetails details : salesReceiptDetailsList) {
            SalesReceiptDetailsTableVO vo = configVo(details);
            voList.add(vo);
        }
        List<OaDictVO> oaDictVOS = dbApi.projectDictNew(DictConstant.BELONGING_SUBJECT_MAIN_ID);
        Map<Integer, String> attributableSubjectMap = oaDictVOS.stream().collect(Collectors.toMap(OaDictVO::getDisorder, OaDictVO::getName,(b, c)->b));

        //2、查询销售收款数据
        SalesReceiptInDetailVO salesReceiptInDetailVO = new SalesReceiptInDetailVO();
        SalesReceipt salesReceipt = salesReceiptMapper.selectById(id);
        BeanUtils.copyProperties(salesReceipt, salesReceiptInDetailVO);
        salesReceiptInDetailVO.setAttributableSubject(attributableSubjectMap.getOrDefault(Integer.valueOf(salesReceiptInDetailVO.getAttributableSubject()), StrUtil.EMPTY));
        salesReceiptInDetailVO.setProjectStatus(EnumUtils.getNameByValue(ProjectStatusEnum.class, salesReceiptInDetailVO.getProjectStatus()));
        salesReceiptInDetailVO.setContractMoney(moneyUtils.transType(salesReceipt.getContractMoney()));

        //3、数据组装
        return SalesReceiptDetailsVO.builder()
                .salesReceiptDetailsList(voList)
                .salesReceiptInDetailVO(salesReceiptInDetailVO).build();
    }

    private SalesReceiptDetailsTableVO configVo(SalesReceiptDetails details) {
        SalesReceiptDetailsTableVO vo = new SalesReceiptDetailsTableVO();
        //复制基础信息
        BeanUtils.copyProperties(details, vo);
        MoneyUtils moneyUtils = MoneyUtils.getInstance();
        //设置比例
        if (details.getProportionFunds() != null) {
            BigDecimal ratio = new BigDecimal(details.getProportionFunds()).multiply(new BigDecimal("100")).setScale(NumberUtils.INTEGER_TWO, RoundingMode.HALF_UP);
            vo.setProportionFunds(ratio + "%");
        }
        //设置枚举
        vo.setInvoiceStatusTxt(EnumUtils.getNameByValue(InvoicingStatusEnum.class, details.getInvoiceStatus()));
        vo.setPaymentStatusTxt(EnumUtils.getNameByValue(PaymentStatusEnum.class, String.valueOf(details.getPaymentStatus())));
        //金额类型转换
        if (details.getFundsAmount() != null) {
            vo.setFundsAmount(moneyUtils.transType(details.getFundsAmount()));
        }
        if (details.getPaymentAmountIncludingTax() != null) {
            vo.setPaymentAmountIncludingTax(moneyUtils.transType(details.getPaymentAmountIncludingTax()));
        }
        if (details.getPaymentAmount() != null) {
            vo.setPaymentAmount(moneyUtils.transType(details.getPaymentAmount()));
        }
        //税率转换
        if (details.getTaxRate() != null) {
            Map<Integer, String> projectMap = dbApi.projectDict(DictConstant.SALES_TAX_RATE_ID).stream()
                    .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname));
            vo.setTaxRate(projectMap.get(details.getTaxRate()));
        }
        return vo;
    }


}




