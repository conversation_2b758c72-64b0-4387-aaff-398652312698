package com.gok.pboot.financial.db.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
    * 字典项
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_dict_item")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "字典项表")
public class SysDictItem extends Model<SysDictItem> {
    /**
    * 编号
    */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 字典id
     */
    @ApiModelProperty(value = "字典id")
    private Long dictId;

    /**
     * 字典值
     */
    @ApiModelProperty(value = "字典值")
    private String itemValue;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String label;

    /**
     * 字典类型
     */
    @ApiModelProperty(value = "字典类型")
    private String dictType;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
    * 排序（升序）
    */
    @ApiModelProperty(value = "排序（升序）")
    private Integer sortOrder;

    /**
    * 创建人
    */
    @ApiModelProperty(value = "创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
    * 修改人
    */
    @ApiModelProperty(value = "修改人")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
    * 创建时间
    */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
    * 更新时间
    */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 删除标志
     */
    @TableLogic(value = "0", delval = "1")
    @ApiModelProperty(value = "删除标志")
    private String delFlag;

    /**
    * 所属租户
    */
    @ApiModelProperty(value = "所属租户")
    private Long tenantId;
}