package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 销售收款明细VO
 * <AUTHOR>
 * @since 2023-10-7
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SalesReceiptDetailsVO {

    /**
     * 顶部内容
     */
    private SalesReceiptInDetailVO salesReceiptInDetailVO;

    /**
     * 列表内容
     */
    private List<SalesReceiptDetailsTableVO> salesReceiptDetailsList;
}
