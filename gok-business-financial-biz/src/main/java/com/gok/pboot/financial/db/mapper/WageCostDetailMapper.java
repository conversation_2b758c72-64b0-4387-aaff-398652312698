package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.WageCostDetail;
import com.gok.pboot.financial.dto.WageCostDTO;
import com.gok.pboot.financial.vo.WageCostDetailVO;
import com.gok.pboot.financial.vo.WageCostVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工资汇总明细
 *
 * <AUTHOR>
 */
public interface WageCostDetailMapper  extends BaseMapper<WageCostDetail> {

    /**
     * 模糊分页查询
     *
     * @param page        分页条件
     * @param wageCostDTO 显示内容
     * @return {@link Page}<{@link WageCostVO}>
     */
    Page<WageCostDetailVO> queryDetailPage(@Param("page") Page<WageCostDetailVO> page, @Param("dto") WageCostDTO wageCostDTO);

    /**
     * 查询
     *
     * @param wageCostDTO 显示内容
     * @return {@link Page}<{@link WageCostVO}>
     */
    List<WageCostDetailVO> queryDetailPage(@Param("dto") WageCostDTO wageCostDTO);

    /**
     * 导出
     *
     * @param limitDto {@link WageCostDTO}
     * @return {@link List}<{@link WageCostDetailVO}>
     */
    List<WageCostDetailVO> exportList(@Param("dto") WageCostDTO limitDto);

    List<String> getSalaryPaidSubjects();
}