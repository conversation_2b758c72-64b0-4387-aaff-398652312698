package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.dto.*;
import com.gok.pboot.financial.service.IAccountTypeService;
import com.gok.pboot.financial.vo.AccountTypeVO;
import com.gok.pboot.financial.vo.AccountTypePageVO;
import com.gok.pboot.financial.vo.FundAccountVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 科目类型控制层
 *
 * <AUTHOR>
 * @since 2024-12-20
 * @menu 科目类型
 */
@RestController
@Api(tags = "科目类型")
@RequiredArgsConstructor
@RequestMapping("/accounting-type")
public class AccountTypeController {

    private final IAccountTypeService service;

    /**
     * 模糊查询带分页
     *
     * @param dto {@link AccountTypePageDTO}
     * @return {@link R}<{@link Page}<{@link AccountTypePageVO}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<Page<AccountTypePageVO>> findPage(@RequestBody @Valid AccountTypePageDTO dto) {
        return R.ok(service.findPage(dto));
    }

    /**
     * 查看
     *
     * @return {@link FundAccountVO}
     */
    @GetMapping("info/{id}")
    @ApiOperation(value = "查看", notes = "查看")
    public R<AccountTypeVO> info(@PathVariable Long id) {
        return service.info(id);
    }

    /**
     * 新增
     *
     * @param dto {@link AccountTypeDTO}
     * @return {@link R}<{@link Void>
     */
    @PostMapping("/add-edit")
    @ApiOperation(value = "新增", notes = "新增")
    public R<String> addOrEdit(@RequestBody @Valid AccountTypeDTO dto) {
        return service.addOrEdit(dto);
    }

    /**
     * 删除
     *
     * @param dto {@link AccountTypePageDTO}
     * @return {@link R}<{@link Void>
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除", notes = "删除")
    public R<String> del(@RequestBody AccountTypePageDTO dto) {
        return service.del(dto.getIdList());
    }


    /**
     * 科目类型列表
     * @return
     */
    @PostMapping("/list")
    @ApiOperation(value = "科目类型列表", notes = "科目类型列表")
    public R<List<AccountTypeVO>> findList(@RequestBody AccountTypePageDTO dto) {
        return R.ok(service.findList(dto.getIdList()));
    }
}
