package com.gok.pboot.financial.dto;

import com.gok.pboot.common.core.validation.annotation.StringVerify;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 项目收入台帐条件查询
 *
 * <AUTHOR>
 * @since 2023-10-27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectRevenueLedgerDTO {

    /**
     * 当前页
     */
    private Long current;

    /**
     * 每页条数
     */
    private Long size;

    /**
     * 导出Excel的id集合
     */
    private List<Long> ids;

    /**
     * 涉及【操作】需要传递id
     */
    private Long id;

    /**
     * 流程编号
     */
    private String number;

    /**
     * 项目名称/项目编码
     */
    private String project;

    /**
     * 合同名称/合同编码
     */
    private String contract;

    /**
     * 客户名称/客户编码
     */
    private String customer;

    /**
     * 业务方向
     * {@link com.gok.pboot.financial.enums.BusinessDirectionEnum}
     */
    private Integer businessDirection;

    /**
     * 收入类型
     */
    private String incomeType;

    /**
     * 结项启动条件
     * {@link com.gok.pboot.financial.enums.ClosingStartConditionsEnum}
     */
    private Integer closingStartConditions;

    /**
     * 收入金额(含税)最小值
     */
    private BigDecimal incomeAmountTaxMin;

    /**
     * 收入金额(含税)最大值
     */
    private BigDecimal incomeAmountTaxMax;

    /**
     * 收入/验收日期 开始时间
     */
    private String incomeOrAcceptanceDateStart;

    /**
     * 收入/验收日期 结束时间
     */
    private String incomeOrAcceptanceDateEnd;

    /**
     * 开票主体
     */
    private String invoicingSubject;

    /**
     * 业务归属部门
     */
    private List<Long> businessDeptList;

    /**
     * 开票状态(已开票传空字符串 未开票传未开票) v1.2.4 去除
     */
    private String invoicingStatus;

    /**
     * 申请人名称/客户经理
     */
    private String applicationOrSale;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private Integer pushStatus;

    /**
     * 备注（用于【操作】时传递）
     */
    @StringVerify(name = "备注", maxLen = 300, message = "备注最大字数不得超过300字", required = true)
    private String remarks;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    private List<String> includeExcel;

}
