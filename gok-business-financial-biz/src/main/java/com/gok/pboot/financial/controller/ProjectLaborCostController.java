package com.gok.pboot.financial.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.module.excel.api.enums.FunctionEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectLaborCost;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.dto.UniqueFlagDTO;
import com.gok.pboot.financial.service.IProjectLaborCostService;
import com.gok.pboot.financial.vo.ProjectLaborCostVO;
import com.gok.pboot.financial.vo.PushResultVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 项目人工成本分摊汇总
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@RestController
@RequestMapping("/project-labor-cost")
@RequiredArgsConstructor
@Api(tags = "项目人工成本分摊汇总")
public class ProjectLaborCostController {

    private final IProjectLaborCostService projectLaborCostService;

    /**
     * 模糊查询带分页
     *
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}<{@link Page}<{@link ProjectLaborCostVO}>>
     */
    @PostMapping("/findPage")
    @PreAuthorize("@pms.hasPermission('projectLaborCostSummary')")
    @ApiOperation(value = "模糊查询带分页", notes = "模糊查询带分页")
    public R<StatisticsPage<ProjectLaborCostVO>> findPage(@RequestBody ProjectLaborCostDTO projectLaborCostDto) {
        StatisticsPage<ProjectLaborCost> page = new StatisticsPage<>(projectLaborCostDto.getCurrent(), projectLaborCostDto.getSize());
        return R.ok(projectLaborCostService.findPage(page, projectLaborCostDto));
    }

    /**
     * 导出Excel选中数据（异步导出）
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostVO}>
     */
    @ResponseExcel(async = true, name = "项目人工成本分摊汇总", functionEnum = FunctionEnum.FINANCIAL_PROJECT_EXPORT,
            nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    @PostMapping("/export")
    @PreAuthorize("@pms.hasPermission('projectLaborCostSummary/export')")
    @ApiOperation(value = "导出选中数据", notes = "导出选中数据")
    public List<ProjectLaborCostVO> export(@RequestBody ProjectLaborCostDTO projectLaborCostDTO) {
        return projectLaborCostService.export(projectLaborCostDTO);
    }

    /**
     * 推送
     *
     * @param ids ids
     * @return PushResultVO
     */
    @PostMapping("/push")
    @PreAuthorize("@pms.hasPermission('projectLaborCostSummary/push')")
    @ApiOperation(value = "推送", notes = "推送")
    public PushResultVO push(@Valid @RequestBody UniqueFlagDTO ids) {
        return projectLaborCostService.push(ids.getIds());
    }

    /**
     * 列表发薪主体
     *
     * @return {@link R}<{@link List}>
     */
    @GetMapping("/salary")
    @ApiOperation(value = "列表发薪主体", notes = "列表发薪主体")
    public R<List<String>> getSalaryList() {
        return R.ok(projectLaborCostService.list().stream().map(ProjectLaborCost::getSalaryPaidSubject)
                .filter(p -> Optional.ofNullable(p).isPresent())
                .distinct().collect(Collectors.toList())
        );
    }
}
