package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * oa的文件接口返回值
 *
 * <AUTHOR>
 * @date 2023/12/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class OaFileVo {

    /**
     * 创建时间
     */
    private String createtime;

    /**
     * OA文件下载路径
     */
    private String downloadUrl;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 创建时间
     */
    private String createdate;

    /**
     * 创建人id
     */
    private Integer createrid;

    /**
     * id
     */
    private Integer id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 创建人
     */
    private String createrName;

}
