package com.gok.pboot.financial.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 会计账套分页
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
public class AccountingSetPageDTO implements Serializable {

    /**
     * 当前页
     */
    @NotNull(message = "当前页不能为空")
    private Long current;

    /**
     * 分页条数
     */
    @NotNull(message = "分页条数不能为空")
    private Long size;

    /**
     * 账套编码
     */
    private String accountingCode;

    /**
     * 归属主体
     */
    private String accountingSubject;

    /**
     * 启用状态
     */
    private Integer enableStatus;

    /**
     * ids
     */
    private List<Long> idList;

    private List<String> includeExcel;

}
