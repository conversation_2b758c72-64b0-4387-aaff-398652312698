package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 推送结果VO
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PushResultVO {
    /**
     * 成功个数
     */
    private Integer success;

    /**
     * 失败个数
     */
    private Integer error;

    /**
     * 失败原因
     */
    private List<PushErrorReasonVO> errorReasonList;
}
