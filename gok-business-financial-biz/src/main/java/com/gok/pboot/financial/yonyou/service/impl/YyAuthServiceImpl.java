/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 用友认证服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.service.impl;

import com.dtflys.forest.Forest;
import com.dtflys.forest.config.ForestConfiguration;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.common.RedisConstant;
import com.gok.pboot.financial.yonyou.client.YonyouClient;
import com.gok.pboot.financial.yonyou.conf.YonyouProperties;
import com.gok.pboot.financial.yonyou.crypto.JWTHelper;
import com.gok.pboot.financial.yonyou.crypto.SignHelper;
import com.gok.pboot.financial.yonyou.model.*;
import com.gok.pboot.financial.yonyou.service.IYyAuthService;
import io.jsonwebtoken.Claims;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * 用友认证服务实现类
 * 提供用友开放平台认证相关的业务服务实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class YyAuthServiceImpl implements IYyAuthService {

    private static final Logger LOGGER = LoggerFactory.getLogger(YyAuthServiceImpl.class);

    @Resource
    private YonyouProperties yonyouProperties;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private YonyouClient yonyouClient;

    @PostConstruct
    public void init() {
        // 获取 Forest 全局配置对象
        ForestConfiguration configuration = Forest.config();

        // 设置全局变量: baseUrl -> http://abc.com
        configuration.setVariable("yonyouBaseUrl", yonyouProperties.getOpenApiUrl());
    }

    /**
     * 创建带签名的参数Map
     * 封装通用的签名逻辑，避免代码重复
     *
     * @return {@link Map }<{@link String }, {@link String }>
     */
    private Map<String, String> createSignedParams() {
        Map<String, String> params = new HashMap<>();
        params.put("appKey", yonyouProperties.getAppKey());
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // 计算签名
        try {
            String signature = SignHelper.sign(params, yonyouProperties.getAppSecret());
            params.put("signature", signature);
        } catch (Exception e) {
            throw new BusinessException("签名计算异常");
        }
        return params;
    }

    /**
     * 创建带签名的参数Map（TreeMap版本）
     * 用于需要有序参数的场景
     *
     * @param additionalParams 额外的参数
     * @return {@link Map }<{@link String }, {@link String }>
     */
    private Map<String, String> createSignedParamsWithTreeMap(Map<String, String> additionalParams) {
        Map<String, String> params = new TreeMap<>();
        params.put("appKey", yonyouProperties.getAppKey());
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));

        // 添加额外参数
        if (additionalParams != null) {
            params.putAll(additionalParams);
        }

        // 计算签名
        try {
            String signature = SignHelper.sign(params, yonyouProperties.getAppSecret());
            params.put("signature", signature);
        } catch (Exception e) {
            throw new BusinessException("签名计算异常");
        }

        return params;
    }

    /**
     * 从缓存获取AccessToken
     *
     * @param cacheKey 缓存键
     * @return {@link AccessTokenResponse} 访问令牌响应，如果缓存中不存在则返回null
     */
    private AccessTokenResponse getAccessTokenFromCache(String cacheKey) {
        try {
            Object cachedToken = redisTemplate.opsForValue().get(cacheKey);
            if (cachedToken != null) {
                LOGGER.debug("从缓存中获取到AccessToken: {}", cacheKey);
                AccessTokenResponse response = (AccessTokenResponse) cachedToken;
                Long expire = redisTemplate.getExpire(cacheKey);
                response.setExpire(expire);
                return response;
            }
        } catch (Exception e) {
            LOGGER.warn("从缓存获取AccessToken失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 将AccessToken存入缓存
     *
     * @param cacheKey            缓存键
     * @param accessTokenResponse 访问令牌响应
     */
    private void cacheAccessToken(String cacheKey, AccessTokenResponse accessTokenResponse) {
        try {
            redisTemplate.opsForValue().set(cacheKey, accessTokenResponse, accessTokenResponse.getExpire(), TimeUnit.SECONDS);
            LOGGER.debug("AccessToken已缓存: {}, 过期时间: {}秒", cacheKey, accessTokenResponse.getExpire());
        } catch (Exception e) {
            LOGGER.warn("缓存AccessToken失败: {}", e.getMessage());
        }
    }

    /**
     * 多数据中心适配之前通过appKey获取accessToken
     *
     * @return {@link AccessTokenResponse} 访问令牌响应
     */
    @Override
    public String getAccessToken() {
        // 先从缓存中获取
        AccessTokenResponse cachedToken = getAccessTokenFromCache(RedisConstant.YONYOU_ACCESS_TOKEN);
        if (cachedToken != null) {
            return cachedToken.getAccessToken();
        }

        // 缓存中没有，调用接口获取
        Map<String, String> params = createSignedParams();

        // 请求
        YonyouResult<AccessTokenResponse> response = yonyouClient.getAccessToken(params);


        AccessTokenResponse accessTokenResponse = response.getResult();
        // 将结果缓存
        cacheAccessToken(RedisConstant.YONYOU_ACCESS_TOKEN, accessTokenResponse);
        return accessTokenResponse.getAccessToken();

    }

    /**
     * 多数据中心适配之后通过tenantId和appKey获取accessToken
     *
     * @return {@link AccessTokenResponse} 访问令牌响应
     */
    @Override
    public AccessTokenResponse getAccessTokenV2() {
        // 先从缓存中获取
        AccessTokenResponse cachedToken = getAccessTokenFromCache(RedisConstant.YONYOU_ACCESS_TOKEN_V2);
        if (cachedToken != null) {
            return cachedToken;
        }

        String tokenUrl;
        try {
            DcUrlResult dcUrl = getGateway(yonyouProperties.getTenantId());
            tokenUrl = dcUrl.getTokenUrl();
        } catch (Exception e) {
            LOGGER.error("获取多数据中心获取token地址出现异常", e);
            throw new RuntimeException("获取多数据中心获取token地址出现异常", e);
        }

        // 使用封装的签名方法
        Map<String, String> params = createSignedParams();

        // 使用YonyouClient请求
        YonyouResult<AccessTokenResponse> response = yonyouClient.getAccessTokenV2(tokenUrl, params);

        AccessTokenResponse accessTokenResponse = response.getResult();
        // 将结果缓存
        cacheAccessToken(RedisConstant.YONYOU_ACCESS_TOKEN_V2, accessTokenResponse);
        return accessTokenResponse;

    }

    /**
     * 通过appkey获取租户id
     *
     * @return {@link String} 租户ID
     */
    @Override
    public String getTenantId() {
        // 使用封装的签名方法
        Map<String, String> params = createSignedParams();

        // 使用YonyouClient请求
        YonyouResult<String> response = yonyouClient.getTenantId(params);

        return response.getResult();
    }

    /**
     * 通过accessToken和友空间免登code获取userToken
     *
     * @param accessToken 开放平台accessToken
     * @param code        友空间免登code
     * @return {@link UserTokenResponse} 用户令牌响应
     * @throws Exception 异常
     */
    @Override
    public UserTokenResponse getUserToken(String accessToken, String code) throws Exception {
        Assert.notNull(accessToken, "accessToken must not be null.");
        Assert.notNull(code, "code must not be null.");

        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("access_token", accessToken);
        paramMap.put("code", code);

        // 使用YonyouClient请求
        YonyouResult<UserTokenResponse> response = yonyouClient.getUserToken(paramMap);

        UserTokenResponse userTokenResponse = response.getData();
        // 从返回中获取userToken
        String userToken = userTokenResponse.getUserAccessToken();
        // 从返回中获取refreshToken
        String refreshToken = userTokenResponse.getRefreshToken();
        // 从返回中获取idToken
        String idToken = userTokenResponse.getIdToken();
        // 解析jwtToken
        Claims jwtClaims = JWTHelper.parseJwtToken(idToken, yonyouProperties.getAppSecret());
        // 获取idToken中携带的用户信息
        String tenantId = jwtClaims.get("tenantId", String.class);
        String yhtUserId = jwtClaims.get("yhtUserId", String.class);
        String tenantName = jwtClaims.get("tenantName", String.class);
        String userName = jwtClaims.get("userName", String.class);
        String mobile = jwtClaims.get("mobile", String.class);
        String email = jwtClaims.get("email", String.class);
        String locale = jwtClaims.get("locale", String.class);
        return response.getResult();

    }

    /**
     * 使用refreshToken刷新userToken
     *
     * @param refreshToken 刷新令牌
     * @return {@link RefreshTokenResponse} 刷新令牌响应
     * @throws Exception 异常
     */
    @Override
    public RefreshTokenResponse refreshUserToken(String refreshToken) throws Exception {
        Assert.notNull(refreshToken, "refreshToken must not be null.");

        // 准备额外参数
        Map<String, String> additionalParams = new HashMap<>();
        additionalParams.put("refreshToken", refreshToken);

        // 使用封装的签名方法（TreeMap版本）
        Map<String, String> paramMap = createSignedParamsWithTreeMap(additionalParams);

        // 使用YonyouClient请求
        YonyouResult<RefreshTokenResponse> response = yonyouClient.refreshUserToken(paramMap);

        return response.getResult();
    }

    @Override
    public DcUrlResult getGateway(String tenantId) throws IOException {
//        Map<String, String> params = new HashMap<>();
//        params.put("tenantId", tenantId);
//        String requestUrl = yonyouProperties.getOpenApiUrl() + "/open-auth/dataCenter/getGatewayAddress";
//        YonyouResult<DcUrlResult> response = RequestTool.doGet(requestUrl, params, new TypeReference<YonyouResult<DcUrlResult>>() {
//        });
//        return response.getData();
        return new DcUrlResult();
    }


}