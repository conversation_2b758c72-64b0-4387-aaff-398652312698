package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.components.common.util.R;
import com.gok.components.common.util.SpringContextHolder;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.db.entity.AccountingSet;
import com.gok.pboot.financial.db.entity.AccountingSystem;
import com.gok.pboot.financial.db.entity.FundAccount;
import com.gok.pboot.financial.db.entity.IndicatorAccountRelationship;
import com.gok.pboot.financial.db.mapper.AccountingSystemMapper;
import com.gok.pboot.financial.db.mapper.FundAccountMapper;
import com.gok.pboot.financial.dto.AccountingSystemDTO;
import com.gok.pboot.financial.dto.AccountingSystemImportDTO;
import com.gok.pboot.financial.dto.AccountingSystemPageDTO;
import com.gok.pboot.financial.dto.EducationPaymentExcelDTO;
import com.gok.pboot.financial.enums.EnableStatusEnum;
import com.gok.pboot.financial.enums.FunctionEnum;
import com.gok.pboot.financial.enums.OperationEnum;
import com.gok.pboot.financial.enums.TypeEnum;
import com.gok.pboot.financial.service.IAccountingSystemService;
import com.gok.pboot.financial.service.IIndicatorAccountRelationshipService;
import com.gok.pboot.financial.util.DateUtils;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.util.LogRecordUtils;
import com.gok.pboot.financial.vo.AccountingSetVO;
import com.gok.pboot.financial.vo.AccountingSystemCorrelationVO;
import com.gok.pboot.financial.vo.AccountingSystemVO;
import com.gok.pboot.financial.vo.ProjectPaymentExcelVO;
import com.google.protobuf.ServiceException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 会计体系业务层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
@RequiredArgsConstructor
public class AccountingSystemServiceImpl extends ServiceImpl<AccountingSystemMapper, AccountingSystem>
        implements IAccountingSystemService {

    private final LogRecordUtils logRecordUtils;

    private final IIndicatorAccountRelationshipService relationshipService;

    private static final String CHANGE = "】变更为【";

    @Override
    public Page<AccountingSystemVO> findPage(AccountingSystemPageDTO dto) {
        // 1、获取分页信息
        Page<AccountingSystem> page = new Page<>(dto.getCurrent(), dto.getSize());
        Page<AccountingSystemVO> accountingSystemPage = baseMapper.findPage(page, dto);
        // 2、封装分页信息
        if (ObjectUtils.isNotEmpty(accountingSystemPage.getRecords())) {
            accountingSystemPage.getRecords().forEach(r -> {
                r.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, r.getEnableStatus()));
            });
        }
        return accountingSystemPage;
    }

    @Override
    public List<AccountingSystemVO> exportExcel(AccountingSystemPageDTO dto) {
        List<AccountingSystemVO> accountingSystemList = new ArrayList<>();
        // 导出模板表
        if (CollUtil.isNotEmpty(dto.getIdList())) {
            if (NumberUtils.LONG_MINUS_ONE.equals(dto.getIdList().get(NumberUtils.INTEGER_ZERO))) {
                AccountingSystemVO accountingSystemVO = AccountingSystemVO.builder()
                        .build();
                accountingSystemList.add(accountingSystemVO);
                return accountingSystemList;
            }
        }
        // 1、查询数据
        accountingSystemList = baseMapper.findPage(dto);

        // 2、返回数据
        if (ObjectUtils.isNotEmpty(accountingSystemList)) {
            accountingSystemList.forEach(r -> {
                r.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, r.getEnableStatus()));
            });
        }
        return accountingSystemList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> addOrEdit(AccountingSystemDTO dto) {
        // 1、获取数据
        AccountingSystem as = baseMapper.selectOne(Wrappers.<AccountingSystem>lambdaQuery()
                .eq(AccountingSystem::getId, dto.getId())
        );
        List<String> codeList = baseMapper.selectList(new QueryWrapper<>()).stream().map(AccountingSystem::getSystemCode).collect(Collectors.toList());

        if (Optional.ofNullable(as).isPresent()) {
            if (!as.getSystemCode().equals(dto.getSystemCode())){
                if (codeList.contains(dto.getSystemCode())) {
                    return R.failed("体系编码不可重复");
                }
            }
            // 2.1、记录日志
            editLog(as, dto);
            // 2.2、更新数据
            AccountingSystem accountingSystem = BeanUtil.copyProperties(dto, AccountingSystem.class);

            accountingSystem.setSystemRemarks(dto.getSystemRemarks());
            this.updateById(accountingSystem);
            return R.ok("编辑成功");
        }
        if (codeList.contains(dto.getSystemCode())) {
            return R.failed("体系编码不可重复");
        }
        // 3、新增操作
        AccountingSystem accountingSystem = BeanUtil.copyProperties(dto, AccountingSystem.class, "id");
        accountingSystem.setSystemRemarks(dto.getSystemRemarks());
        // 3.1、记录日志
        final String s = OperationEnum.ADD_ACCOUNTING_SET.getName() + "【"
                + dto.getSystemCode() + "】【" + dto.getSystemName() + "】";
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SET,
                OperationEnum.ADD_ACCOUNTING_SET, s);
        // 3.2、入库
        this.save(accountingSystem);

        // 3.3、初始化会计体系指标科目关系
        relationshipService.initRelationship(Collections.singleton(accountingSystem.getId()));
        return R.ok("新增成功");
    }

    @Override
    public R<String> del(List<Long> idList) {
        // 1、防止空数组删除全数据
        if (ObjectUtils.isEmpty(idList)) {
            throw new BusinessException("删除接口传参有误");
        }
        List<AccountingSystemCorrelationVO> correlationVOList = baseMapper.selCorrelationNum(idList);
        List<Long> filterIds = correlationVOList.stream()
                .filter(c -> NumberUtils.INTEGER_ZERO.equals(c.getAccountNum()) && NumberUtils.INTEGER_ZERO.equals(c.getAccountTypeNum()))
                .collect(Collectors.toList())
                .stream().map(AccountingSystemCorrelationVO::getId)
                .collect(Collectors.toList());

        if (ObjectUtils.isEmpty(filterIds)) {
            return R.ok("删除成功");
        }
        // 2、查询数据记录日志
        StringBuilder s = new StringBuilder();
        s.append(OperationEnum.DELETE_ACCOUNTING_SYSTEM.getName());
        baseMapper.selectList(Wrappers.<AccountingSystem>lambdaQuery().in(AccountingSystem::getId, filterIds)
                .eq(AccountingSystem::getEnableStatus, NumberUtils.INTEGER_ONE))
                .forEach(a -> s.append("【").append(a.getSystemCode()).append(a.getSystemName()).append("】"));
        logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SYSTEM,
                OperationEnum.DELETE_ACCOUNTING_SYSTEM, s.toString());
        // 3、删除数据
        baseMapper.delByIds(filterIds);
        return R.ok("删除成功");
    }

    @Override
    public boolean enable(AccountingSystemDTO dto) {
        List<AccountingSystem> accountingSystemList = baseMapper.selectList(Wrappers.<AccountingSystem>lambdaQuery()
                .in(AccountingSystem::getId, dto.getIdList())
                .eq(AccountingSystem::getEnableStatus,dto.getEnableStatusFlag()?NumberUtils.INTEGER_ONE:NumberUtils.INTEGER_ZERO));

        if (CollectionUtil.isEmpty(accountingSystemList)) {
            return true;
        }
        // 1、启用状态 前者用来修改信息 后者用来记录日志
        Integer enableStatus = EnableStatusEnum.NO.getValue();
        String enableStatusTxt = EnableStatusEnum.YES.getName();
        if (Boolean.TRUE.equals(dto.getEnableStatusFlag())) {
            enableStatus = EnableStatusEnum.YES.getValue();
            enableStatusTxt = EnableStatusEnum.NO.getName();
        }
        // 2、记录日志
        StringBuilder s = new StringBuilder();
        s.append("【").append(enableStatusTxt).append(CHANGE)
                .append(EnumUtils.getNameByValue(EnableStatusEnum.class, enableStatus))
                .append("】: ");
        for (AccountingSystem a : accountingSystemList) {
            if (!enableStatus.equals(a.getEnableStatus())) {
                a.setEnableStatus(enableStatus);
                s.append("【").append(a.getSystemCode()).append(a.getSystemName()).append("】");
            }
        }
        logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SYSTEM, OperationEnum.EDIT_STATUS, s.toString());
        // 3、更新数据库
        return this.updateBatchById(accountingSystemList);
    }

    /**
     * 导入Excel
     *
     * @param accountingSystemImportDTOList {@link List}<{@link AccountingSystemImportDTO}>
     * @return {@link R}
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public R<String> importExcel(List<AccountingSystemImportDTO> accountingSystemImportDTOList) {
        // 遍历封装后插入数据库
        List<AccountingSystem> accountingSystemList = new ArrayList<>();
        if (CollUtil.isNotEmpty(accountingSystemImportDTOList)) {
            Map<String, AccountingSystem> systemByCodeMap = baseMapper.selectList(new QueryWrapper<>()).stream()
                    .collect(Collectors.toMap(AccountingSystem::getSystemCode, a -> a, (a, b) -> a));
            String[] accountingTypeNumbers = new String[]{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};
            StringBuilder error = new StringBuilder();
            AtomicInteger i = new AtomicInteger(2);
            accountingSystemImportDTOList.forEach(p -> {
                // 校验
                this.checkImport(p,accountingTypeNumbers, error, i.getAndIncrement());
                // 1、会计体系
                AccountingSystem accountingSystem = BeanUtil.copyProperties(p, AccountingSystem.class);
                AccountingSystem as = systemByCodeMap.getOrDefault(p.getSystemCode(), new AccountingSystem());
                if (Optional.ofNullable(as).isPresent()) {
                    accountingSystem.setId(as.getId());
                }
                accountingSystem.setEnableStatus(EnumUtils.getValueByName(EnableStatusEnum.class, p.getEnableStatusStr()));
                accountingSystemList.add(accountingSystem);
            });
            if (error.toString().contains("不")) {
                return R.failed("导入失败,原因是" + error.toString());
            }

            // 2、记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.ACCOUNTING_SYSTEM,
                    OperationEnum.IMPORT,
                    OperationEnum.IMPORT.getName() + "【" + accountingSystemList.size() + "条】");
            boolean s = this.saveOrUpdateBatch(accountingSystemList);
            // 3、初始化科目关系
            relationshipService.initRelationship(CollStreamUtil.toSet(accountingSystemList,  AccountingSystem::getId));
            return s ? R.ok("导入成功") : R.failed("导入失败");
        }

        return R.ok("无数据导入");
    }

    @Override
    public R<AccountingSystemVO> info(Long id) {
        AccountingSystem system = baseMapper.selectById(id);
        if(!Optional.ofNullable(system).isPresent()){
            return R.failed("查询不到数据");
        }
        AccountingSystemVO vo = BeanUtil.copyProperties(system, AccountingSystemVO.class);
        vo.setEnableStatusTxt(EnumUtils.getNameByValue(EnableStatusEnum.class, system.getEnableStatus()));

        return R.ok(vo);
    }


    /**
     * 编辑记录日志
     *
     * @param as  {@link AccountingSystem}
     * @param dto {@link AccountingSystemDTO}
     */
    private void editLog(AccountingSystem as, AccountingSystemDTO dto) {
        Integer enableStatus = as.getEnableStatus();
        // 启用状态变更日志记录
        if (!enableStatus.equals(dto.getEnableStatus())) {
            String enable = EnableStatusEnum.YES.getName();
            if (dto.getEnableStatus().equals(EnableStatusEnum.YES.getValue())) {
                enable = EnableStatusEnum.NO.getName();
            }
            final String s = "【" + as.getSystemCode() + "】【" + as.getSystemName() + "】【停用状态"
                    + enable + CHANGE + EnumUtils.getNameByValue(EnableStatusEnum.class, dto.getEnableStatus()) + "】";
            logRecordUtils.logRecord(TypeEnum.CHANCE_CONFIGURATION, FunctionEnum.ACCOUNTING_SYSTEM,
                    OperationEnum.EDIT_STATUS, s);
        }
    }


    /**
     * 校验导入参数
     *
     * @param dto {@link EducationPaymentExcelDTO}
     * @param errorAll {@link StringBuilder}
     * @param row 行数
     */
    private void checkImport(AccountingSystemImportDTO dto,String[] accountingTypeNumbers, StringBuilder errorAll, Integer row) {
        StringBuilder error = new StringBuilder();
        error.append("第").append(row).append("行: ");
        if (CharSequenceUtil.isBlank(dto.getSystemCode())) {
            error.append("体系编码不能为空。");
        }else if (20<dto.getSystemCode().length()){
            error.append("体系编码字符数不能超过20。");
        }
        if (CharSequenceUtil.isBlank(dto.getSystemName())) {
            error.append("体系名称不能为空。");
        }else if (20<dto.getSystemName().length()){
            error.append("体系名称字符数不能超过20。");
        }
        if (CharSequenceUtil.isBlank(dto.getAccountingType())) {
            error.append("账套类型不能为空。");
        }else if (!Arrays.asList(accountingTypeNumbers).contains(dto.getAccountingType())){
            error.append("账套类型不符合规范。");
        }
        if (CharSequenceUtil.isBlank(dto.getEnableStatusStr())) {
            error.append("停用状态不能为空。");
        }

        if (error.toString().contains("不")) {
            errorAll.append(error);
        }
    }
}
