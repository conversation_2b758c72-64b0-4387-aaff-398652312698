package com.gok.pboot.financial.invoice.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.annotation.ResponseExcel;
import com.gok.module.excel.api.enums.ExcelDateEnum;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.invoice.entity.dto.InvoiceOutputItemDTO;
import com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputItemVO;
import com.gok.pboot.financial.invoice.service.IInvoiceOutputItemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 销项发票明细前端控制器
 *
 * <AUTHOR>
 * @create 2025/06/10
 * @menu 销项发票明细
 **/
@Api(tags = "销项发票明细管理")
@RestController
@RequestMapping("/invoiceOutput/items")
@RequiredArgsConstructor
public class InvoiceOutputItemController {

    private final IInvoiceOutputItemService invoiceOutputItemService;

    /**
     * 分页查询
     *
     * @param dto
     * @return
     */
    @ApiOperation("分页查询")
    @PostMapping("/page")
    public R<StatisticsPage<InvoiceOutputItemVO>> page(@RequestBody @Valid InvoiceOutputItemDTO dto) {
        return R.ok(invoiceOutputItemService.findPage(dto));
    }

    /**
     * 导出Excel
     *
     * @param dto {@link InvoiceOutputItemDTO}
     * @return {@link Page}<{@link InvoiceOutputItemVO}>
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(async = true, name = "销项发票明细台账", nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<InvoiceOutputItemVO> exportExcel(@RequestBody InvoiceOutputItemDTO dto) {
        return invoiceOutputItemService.exportExcel(dto);
    }

    /**
     * PMS导出Excel
     *
     * @param dto {@link InvoiceOutputItemDTO}
     * @return {@link Page}<{@link InvoiceOutputItemVO}>
     */
    @PostMapping("/pms/export")
    @ApiOperation(value = "导出Excel", notes = "导出Excel")
    @ResponseExcel(name = "销项发票明细台账", nameWithDate = ExcelDateEnum.DATE_TIME, dateFormat = "yyyy-MM-dd HH:mm:ss", dynamicHeader = true)
    public List<InvoiceOutputItemVO> pmsExportExcel(@RequestBody InvoiceOutputItemDTO dto) {
        return invoiceOutputItemService.exportExcel(dto);
    }

} 