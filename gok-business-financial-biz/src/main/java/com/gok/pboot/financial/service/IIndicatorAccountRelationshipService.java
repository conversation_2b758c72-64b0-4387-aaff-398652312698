package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.base.admin.vo.AccountRelationshipVO;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.IndicatorAccountRelationship;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipDTO;
import com.gok.pboot.financial.dto.IndicatorAccountRelationshipPageDTO;
import com.gok.pboot.financial.vo.IndicatorAccountRelationshipVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 指定科目关系 Service
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
public interface IIndicatorAccountRelationshipService extends IService<IndicatorAccountRelationship> {

    /**
     * 分页查询
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO} 分页条件
     * @return {@link Page}<{@link IndicatorAccountRelationshipVO}>
     */
    Page<IndicatorAccountRelationshipVO> findPage(IndicatorAccountRelationshipPageDTO dto);

    /**
     * 根据id获取详情
     *
     * @param id 科目id
     * @return {@link IndicatorAccountRelationshipVO}
     */
    IndicatorAccountRelationshipVO getOne(Long id);

    /**
     * 导出选中数据
     *
     * @param dto {@link IndicatorAccountRelationshipPageDTO}
     * @return {@link List}<{@link IndicatorAccountRelationshipVO}>
     */
    List<IndicatorAccountRelationshipVO> export(IndicatorAccountRelationshipPageDTO dto);

    /**
     * 导入Excel
     *
     * @param indicatorAccountRelationshipDTOList {@link List}<{@link IndicatorAccountRelationshipDTO}>
     * @return {@link R}
     */
    R<String> importExcel(List<IndicatorAccountRelationshipDTO> indicatorAccountRelationshipDTOList);

    /**
     * 获取核算部门和科目编码的对应关系
     * @return {@link Map}
     */
    Map<String,String> getIndicatorAccountRelationship();

    /**
     * 关联会计科目
     *
     * @param ids        ids
     * @param code 科目编码
     */
    void relatedAccountingSubjects(Set<Long> ids, String code);

    /**
     * 初始化会计体系关联关系
     *
     * @param accountingSystemIds 会计体系 IDs
     */
    void initRelationship(Set<Long> accountingSystemIds);

    /**
     * 同步更新关系
     */
    void syncUpdateRelationship();

    /**
     * 通过会计体系编码查询指标科目关系列表
     *
     * @param systemCode 会计体系编码
     * @return {@link List}<{@link IndicatorAccountRelationshipVO}>
     */
    List<AccountRelationshipVO> getBySystemCode(String systemCode);
}
