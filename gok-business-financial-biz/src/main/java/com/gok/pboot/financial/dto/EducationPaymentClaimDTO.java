package com.gok.pboot.financial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 教育回款编辑/认领
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EducationPaymentClaimDTO {

    private Long id;

    // 回款信息

    /**
     * 收款平台
     */
    private String paymentPlatform;

    /**
     * 收款公司
     */
    private String paymentCompany;

    /**
     * 收款日期
     */
    private String paymentDate;

    /**
     * 交易流水号
     */
    private String transactionNumber;

    /**
     * 到账金额
     */
    private BigDecimal receivedAmount;

    /**
     * 不含税金额
     */
    private BigDecimal amountExcludingTax;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 手续费
     */
    private BigDecimal commission;

    /**
     * 实际到账金额
     */
    private BigDecimal actualAmountReceived;

    /**
     * 回款备注
     */
    private String paymentNote;

    /**
     * 开票状态code（0已开票，1未开票）
     * {@link com.gok.pboot.financial.enums.InvoicingStatusEnum}
     */
    private Integer invoicingStatus;

    /**
     * 开票日期
     */
    private String invoicingDate;

    /**
     * 发票号码
     */
    private String invoicingNumber;

    // 认领信息

    /**
     * 客户经理id
     */
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    private String salesmanUserName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 归属业务线
     */
    private String businessLine;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 大客户经理id
     */
    private Long keyAccountManagerId;

    /**
     * 大客户经理
     */
    private String keyAccountManagerName;

    /**
     * 销售主管id
     */
    private Long salesExecutiveId;

    /**
     * 销售主管
     */
    private String salesExecutiveName;

    /**
     * 客户经理id
     */
    private Long accountManagerId;

    /**
     * 客户经理
     */
    private String accountManagerName;

    /**
     * 咨询顾问id
     */
    private Long consultantUserId;

    /**
     * 咨询顾问
     */
    private String consultantUserName;

    /**
     * 确认收入日期
     */
    private String sureRevenueDate;

    /**
     * 认领备注
     */
    private String claimRemark;
}
