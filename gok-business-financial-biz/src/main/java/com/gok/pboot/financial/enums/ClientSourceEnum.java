package com.gok.pboot.financial.enums;

import cn.hutool.core.util.StrUtil;
import com.gok.bcp.message.entity.enums.SourceEnum;
import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

import java.util.Optional;

/**
 * 类型 Enum
 *
 * <AUTHOR>
 * @since 2023-12-01
 */
@Getter
public enum ClientSourceEnum{

    /**
     * 数据操作
     */
    PROJECT("1698521681078931458", SourceEnum.PROJECT),

    /**
     * 配置变更
     */
    DIGITAL_FINANCE("1697580023369846786",SourceEnum.DIGITAL_FINANCE),
    ;


    private final String value;

    private final SourceEnum name;

    ClientSourceEnum(String value, SourceEnum name) {
        this.value = value;
        this.name = name;
    }

    public static SourceEnum getNameByVal(String value) {
        if (!Optional.ofNullable(value).isPresent()) {
            return null;
        }
        for (ClientSourceEnum data : ClientSourceEnum.values()) {
            if (data.value.equals(value)) {
                return data.getName();
            }
        }
        return null;
    }
}
