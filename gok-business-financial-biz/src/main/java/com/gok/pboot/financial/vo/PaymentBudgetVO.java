package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 预算年月
 *
 * <AUTHOR>
 * @since 2023-10-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentBudgetVO {

    /**
     * 合同名称
     */
    private String customerName;

    /**
     * 本月预算回款金额
     */
    private BigDecimal budgetCollectionAmount;

    /**
     * 所属年月
     */
    private String yearMonth;

}
