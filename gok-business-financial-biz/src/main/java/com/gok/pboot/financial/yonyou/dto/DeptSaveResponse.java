/*
 * Copyright (c) 2024 GOK
 * All rights reserved.
 *
 * 部门保存响应DTO
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
package com.gok.pboot.financial.yonyou.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 部门保存响应DTO
 * 部门保存接口的返回数据结构
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptSaveResponse {

    /**
     * 上级id
     */
    private String parent;
    
    /**
     * 上级编码
     */
    private String parent_code;
    
    /**
     * 层级
     */
    private Integer level;
    
    /**
     * 排序号
     */
    private Integer sort;
    
    /**
     * 是否末级 1是 0否
     */
    private Integer isEnd;
    
    /**
     * 基本组织主键
     */
    private Integer orgid;
    
    /**
     * 部门编码
     */
    private String code;
    
    /**
     * 部门名称,支持多语
     */
    private MultiLangName name;
    
    /**
     * 所属上级名称
     */
    private String parent_name;
    
    /**
     * 部门性质id
     */
    private String depttype;
    
    /**
     * 部门性质名称
     */
    private String depttype_name;
    
    /**
     * 负责人id
     */
    private String principal;
    
    /**
     * 负责人名称
     */
    private String principal_name;
    
    /**
     * 分管领导id
     */
    private String branchleader;
    
    /**
     * 分管领导名称
     */
    private String branchleader_name;
    
    /**
     * 部门ID
     */
    private String id;
    
    /**
     * 外部系统主键
     */
    private String objid;
    
    /**
     * 时间戳,格式为:yyyy-MM-dd HH:mm:ss
     */
    private String pubts;
    
    /**
     * 删除标识 0未删除 1已删除
     */
    private String dr;
    
    /**
     * 上级组织id
     */
    private String parentorgid;
    
    /**
     * 上级组织编码
     */
    private String parentorgCode;
    
    /**
     * 组织单元名称
     */
    private String parentorgid_name;
    
    /**
     * 组织类型 1组织 2部门
     */
    private String orgtype;
    
    /**
     * 状态, 0:未启用、1:启用、2:停用
     */
    private Integer enable;
    
    /**
     * 内部码
     */
    private String innercode;
    
    /**
     * 应用主键
     */
    private String sysid;
    
    /**
     * 启用日期,格式为yyyy-MM-dd HHmmss
     */
    private String effectivedate;
    
    /**
     * 失效日期,格式为yyyy-MM-dd HHmmss
     */
    private String expirationdate;
    
    /**
     * 是否组织单元 1是 0否
     */
    private String is_biz_unit;
    
    /**
     * 创建人
     */
    private String creator;
    
    /**
     * 创建时间
     */
    private String creationtime;
    
    /**
     * 是否业务单元 0 否 1 是 默认否
     */
    private Integer isbizunit;
    
    /**
     * 租户
     */
    private String tenant;
    
    /**
     * 友互通租户标识
     */
    private String yhtTenant;
    
    /**
     * 友互通租户id
     */
    private String yhtTenantId;
    
    /**
     * 租户标识
     */
    private String yTenantId;
    
    /**
     * 部门名称
     */
    private String multiLangName;

    /**
     * 多语言名称
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MultiLangName {
        
        /**
         * 中文名称
         */
        private String zh_CN;
        
        /**
         * 英文名称
         */
        private String en_US;
        
        /**
         * 繁体中文名称
         */
        private String zh_TW;
    }
}