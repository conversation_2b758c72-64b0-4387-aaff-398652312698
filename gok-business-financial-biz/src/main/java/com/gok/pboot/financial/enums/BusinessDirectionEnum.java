package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 业务方向Enum
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
@Getter
public enum BusinessDirectionEnum implements ValueEnum<Integer> {

    /**
     * ICT
     */
    ICT(0, "ICT"),

    /**
     * 信息安全
     */
    INFORMATION_SAFE(1, "信息安全"),

    /**
     * 软件开发
     */
    SORT_DEVELOP(2, "软件开发"),

    /**
     * 综合
     */
    SYNTHESIS(3, "综合");

    private final Integer value;

    private final String name;

    BusinessDirectionEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
