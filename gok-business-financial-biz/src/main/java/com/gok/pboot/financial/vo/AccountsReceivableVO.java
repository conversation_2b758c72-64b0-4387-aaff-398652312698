package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 应收账款台账 展示
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class AccountsReceivableVO {

    /**
     * id
     */
    @ExcelIgnore
    private Long id;



    /**
     * 合同id
     */
    @ExcelIgnore
    private Long contractId;

    /**
     * 合同编号
     */
    @ColumnWidth(25)
    @ExcelProperty("合同编码")
    private String contractCode;

    /**
     * 合同名称
     */
    @ColumnWidth(25)
    @ExcelProperty("合同名称")
    private String contractName;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    @ExcelIgnore
    private String projectStatus;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    @ColumnWidth(15)
    @ExcelProperty("项目状态")
    private String projectStatusTxt;


    /**
     * 30天内
     */
    @ColumnWidth(15)
    @ExcelProperty({"应收账款账龄", "30天内"})
    private String withinThirty;

    /**
     * 30-60天内
     */
    @ColumnWidth(15)
    @ExcelProperty({"应收账款账龄", "30-60天内"})
    private String thirtySixty;

    /**
     * 60-120天内
     */
    @ColumnWidth(15)
    @ExcelProperty({"应收账款账龄", "60-120天内"})
    private String sixtyHundredTwenty;

    /**
     * 120天以上
     */
    @ColumnWidth(15)
    @ExcelProperty({"应收账款账龄", "120天以上"})
    private String aboveHundredTwenty;

    /**
     * 开票
     */
    @ColumnWidth(15)
    @ExcelProperty({"合计", "开票"})
    private String invoicing;

    /**
     * 验收
     */
    @ColumnWidth(15)
    @ExcelProperty({"合计", "验收"})
    private String revenue;

    /**
     * 回款
     */
    @ColumnWidth(15)
    @ExcelProperty({"合计", "回款"})
    private String payBack;

    /**
     * 验收待回款金额
     * （原收入确认待回款金额）
     */
    @ColumnWidth(20)
    @ExcelProperty({"应收账款", "验收待回款金额"})
    private String revenueCollected;

    /**
     * 开票待回款金额
     */
    @ColumnWidth(20)
    @ExcelProperty({"应收账款", "开票待回款金额"})
    private String invoicingCollected;

    /**
     * 合同待回款金额
     */
    @ColumnWidth(20)
    @ExcelProperty({"应收账款", "合同待回款金额"})
    private String customerCollected;

    /**
     * 合同待开票金额
     */
    @ColumnWidth(20)
    @ExcelProperty({"应收账款", "合同待开票金额"})
    private String customerInvoicing;

    /**
     * 客户名称
     */
    @ColumnWidth(35)
    @ExcelProperty("签约客户")
    private String customerName;

    /**
     * 合同签订日期
     */
    @ColumnWidth(20)
    @ExcelProperty("合同签订日期")
    private String signingDate;

    /**
     * 归属主体/主体名称
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ExcelIgnore
    private String attributableSubject;

    /**
     * 归属主体/主体名称
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @ColumnWidth(35)
    @ExcelProperty("归属主体")
    private String attributableSubjectTxt;

    /**
     * 合同金额
     */
    @ColumnWidth(15)
    @ExcelProperty("合同金额")
    private String contractMoney;

    /**
     * 项目id
     */
    @ExcelIgnore
    private Long projectId;

    /**
     * 项目编号
     */
    @ColumnWidth(20)
    @ExcelProperty("项目编码")
    private String projectNo;

    /**
     * 项目名称
     */
    @ColumnWidth(60)
    @ExcelProperty("项目名称")
    @ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.LEFT)
    private String projectName;

    /**
     * 一级部门名称
     */
    @ColumnWidth(25)
    @ExcelProperty("业务归属一级部门")
    private String firstDepartment;

    /**
     * 二级部门名称
     */
    @ColumnWidth(25)
    @ExcelProperty("业务归属二级部门")
    private String secondDepartment;

    /**
     * 项目销售人员姓名
     */
    @ColumnWidth(15)
    @ExcelProperty("客户经理")
    private String salesmanUserName;

    /**
     * 项目经理人员姓名
     */
    @ColumnWidth(15)
    @ExcelProperty("项目经理")
    private String managerUserName;

}
