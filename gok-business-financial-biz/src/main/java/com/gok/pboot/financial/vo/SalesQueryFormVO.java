package com.gok.pboot.financial.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 销售收款-列表查询条件
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SalesQueryFormVO implements Serializable {

    /**
     * 项目状态
     */
    private List<ProjectStatusVO> projectStatusVoList;

    /**
     * 预警等级
     */
    private List<WarningLevelVO> warningLevelVoList;

    /**
     * 款项名称
     */
    private List<PaymentVO> paymentVoList;

    /**
     * 归属主体
     */
    private List<AttributableSubjectVO> attributableSubjectVoList;

    /**
     * 款项状态
     */
    private List<PaymentVO> paymentStatusVoList;
}
