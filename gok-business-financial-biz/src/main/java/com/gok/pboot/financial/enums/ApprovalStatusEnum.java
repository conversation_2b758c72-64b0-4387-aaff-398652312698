package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审批状态 Enum
 *
 * author caihf
 * since 2023-09-01
 */
@Getter
@AllArgsConstructor
public enum ApprovalStatusEnum implements ValueEnum<Integer> {

    // 会计审批
    ACCOUNTING_APPROVAL(0,"会计审批")
    ,

    // 财务审批
    FINANCIAL_APPROVAL(1,"财务审批")
    ,

    // 业务审批
    BUSINESS_APPROVAL(2,"业务审批")
    ,

    // 出纳审批
    CASHIER_APPROVAL(3,"出纳审批")
    ,

    // 归档
    FILE(4,"归档")
   ;


    private final Integer value;

    private final String name;

}
