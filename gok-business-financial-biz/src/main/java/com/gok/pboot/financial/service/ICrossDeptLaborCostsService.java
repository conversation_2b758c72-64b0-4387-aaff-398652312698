package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.pboot.financial.db.entity.CrossDeptLaborCosts;
import com.gok.pboot.financial.dto.CrossDeptLaborShareCostsDTO;
import com.gok.pboot.financial.dto.DeptArtificialCostDTO;
import com.gok.pboot.financial.vo.*;

import java.util.List;

/**
 * 跨部门人工成本台账Service
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface ICrossDeptLaborCostsService extends IService<CrossDeptLaborCosts> {

    /**
     * 列表展示跨部门人工成本台账数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    List<CrossDeptLaborCostsVO> findList(DeptArtificialCostDTO deptArtificialCostDTO);

    /**
     * 跨部门人工成本台账导出（异步导出）
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsVO}>
     */
    List<CrossDeptLaborCostsVO> export(DeptArtificialCostDTO deptArtificialCostDTO);

    /**
     * 列表展示跨部门人工成本台账明细数据
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    List<CrossDeptLaborCostsDetailVO> findDetailList(DeptArtificialCostDTO deptArtificialCostDTO);

    /**
     * 跨部门人工成本台账明细导出（异步导出）
     *
     * @param deptArtificialCostDTO {@link DeptArtificialCostDTO} 条件
     * @return {@link List}<{@link CrossDeptLaborCostsDetailVO}>
     */
    List<CrossDeptLaborCostsDetailVO> detailExport(DeptArtificialCostDTO deptArtificialCostDTO);

    /**
     * 列表展示跨部门人工成本台账分摊数据
     *
     * @param crossDeptLaborShareCostsDTO {@link CrossDeptLaborShareCostsDTO} 条件
     * @return {@link CrossDeptShareVO}
     */
    CrossIncomeDeptVO findShareVoList(CrossDeptLaborShareCostsDTO crossDeptLaborShareCostsDTO);

}

