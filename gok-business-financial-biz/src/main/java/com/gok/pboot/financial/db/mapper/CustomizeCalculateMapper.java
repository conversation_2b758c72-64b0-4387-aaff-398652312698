package com.gok.pboot.financial.db.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gok.pboot.financial.db.entity.CustomizeCalculate;
import com.gok.pboot.financial.db.entity.FundAccount;
import com.gok.pboot.financial.dto.CustomizeCalculateFindDTO;
import com.gok.pboot.financial.vo.CustomizeCalculateVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 自定义辅助核算项数据访问层
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface CustomizeCalculateMapper extends BaseMapper<CustomizeCalculate> {

    Page<CustomizeCalculateVO> queryPage(@Param("page") Page<CustomizeCalculate> page, @Param("dto") CustomizeCalculateFindDTO dto);

    List<CustomizeCalculate> selectByCode(@Param("code") String code);

    List<CustomizeCalculateVO> findList();

    List<CustomizeCalculate> selectByCodes(@Param("codes") List<String> codes);

    void insertOrUpdateBatch(@Param("list") List<CustomizeCalculate> insertList);
}