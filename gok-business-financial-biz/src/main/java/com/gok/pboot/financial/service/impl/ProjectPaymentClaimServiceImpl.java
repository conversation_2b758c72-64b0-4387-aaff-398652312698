package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.stream.CollectorUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.pboot.financial.common.DBApi;
import com.gok.pboot.financial.common.DictConstant;
import com.gok.pboot.financial.db.entity.ProjectPayment;
import com.gok.pboot.financial.db.entity.ProjectPaymentClaim;
import com.gok.pboot.financial.db.mapper.ProjectPaymentClaimMapper;
import com.gok.pboot.financial.dto.IdsDTO;
import com.gok.pboot.financial.enums.MarginTypeEnum;
import com.gok.pboot.financial.enums.PaymentTypeEnum;
import com.gok.pboot.financial.service.IProjectPaymentClaimService;
import com.gok.pboot.financial.service.IProjectPaymentService;
import com.gok.pboot.financial.util.EnumUtils;
import com.gok.pboot.financial.vo.BusinessBlockVO;
import com.gok.pboot.financial.vo.ProjectDictVO;
import com.gok.pboot.financial.vo.ProjectPaymentClaimVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目回款认领 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Service
@RequiredArgsConstructor
public class ProjectPaymentClaimServiceImpl
        extends ServiceImpl<ProjectPaymentClaimMapper, ProjectPaymentClaim> implements IProjectPaymentClaimService {

    private final DBApi dbApi;

    private final IProjectPaymentService projectPaymentService;

    /**
     * 获取列表中的业务板块
     *
     * @return {@link List}<{@link BusinessBlockVO}>
     */
    @Override
    public List<BusinessBlockVO> queryBusinessBlock() {
        // 1、获取字典中的键值对
        Map<Integer, String> map = dbApi.projectDict(DictConstant.BUSINESS_BLOCK_ID)
                .stream()
                .collect(Collectors.toMap(ProjectDictVO::getSelectvalue, ProjectDictVO::getSelectname, (a, b) -> a));
        // 2、返回列表中存在的业务板块
        List<BusinessBlockVO> businessBlock = new ArrayList<>();
        baseMapper.selectList(new LambdaQueryWrapper<>()).stream()
                .map(ProjectPaymentClaim::getBusinessBlock)
                .filter(p -> Optional.ofNullable(p).isPresent())
                .forEach(p ->  businessBlock.add(new BusinessBlockVO(p, map.get(p))));
        return businessBlock.stream()
                .distinct()
                .filter(p -> Optional.ofNullable(p.getName()).isPresent())
                .collect(Collectors.toList());
    }

    @Override
    public List<ProjectPaymentClaimVO> getClaim(IdsDTO idsDTO) {
        LambdaQueryWrapper<ProjectPaymentClaim> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProjectPaymentClaim::getContractId,idsDTO.getIds());
        wrapper.eq(ProjectPaymentClaim::getDelFlag,0);
        List<ProjectPaymentClaim> claimList = baseMapper.selectList(wrapper);
        List<ProjectPaymentClaimVO> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(claimList)){
            Set<Long> projectPaymentIds = claimList.stream()
                    .map(ProjectPaymentClaim::getProjectPaymentId).collect(Collectors.toSet());
            Map<Long, ProjectPayment> projectPaymentMap
                    = projectPaymentService.listByIds(projectPaymentIds)
                    .stream()
                    .collect(Collectors.toMap(ProjectPayment::getId, Function.identity()));

            voList = claimList.stream().map(c -> {
                ProjectPaymentClaimVO projectPaymentClaimVO =
                        BeanUtil.copyProperties(c, ProjectPaymentClaimVO.class, "id");
                ProjectPayment projectPayment = projectPaymentMap.get(c.getProjectPaymentId());
                if(Optional.ofNullable(projectPayment).isPresent()){
                    projectPaymentClaimVO.setPaymentDate(projectPayment.getPaymentDate());
                    projectPaymentClaimVO.setPaymentAmount(projectPayment.getPaymentAmount().toString());
                }
                projectPaymentClaimVO.setPaymentTypeText(EnumUtils.getNameByValue(PaymentTypeEnum.class, c.getPaymentType()));
                projectPaymentClaimVO.setMarginTypeText(EnumUtils.getNameByValue(MarginTypeEnum.class, c.getMarginType()));
                return projectPaymentClaimVO;
            }).collect(Collectors.toList());
        }
        return voList;
    }
}
