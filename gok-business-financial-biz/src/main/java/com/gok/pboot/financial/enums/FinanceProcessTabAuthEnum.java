package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 权限标识 Enum
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Getter
public enum FinanceProcessTabAuthEnum implements ValueEnum<Integer> {


    /**
     * 全部
     */
    ALL(0, "financialprocessingdoc/all"),

    /**
     * 待审批
     */
    APPROVAL(1, "financialprocessingdoc/approval"),

    /**
     * 待审核
     */
    REVIEWED(2, "financialprocessingdoc/reviewed"),

    /**
     * 待结算
     */
    SETTLEMENT(3, "financialprocessingdoc/settlement"),
    /**
     * 部分结算
     */
    PARTSETTLEMENT(4, "financialprocessingdoc/partsettlement"),
    /**
     * 已结算
     */
    SETTLED(5, "financialprocessingdoc/settled"),
    /**
     * 无需结算
     */
    NOSETTLEMENT(6, "financialprocessingdoc/nosettlement")
    ;

    private final Integer value;

    private final String name;

    FinanceProcessTabAuthEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
