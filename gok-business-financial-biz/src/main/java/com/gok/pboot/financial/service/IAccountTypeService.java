package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.db.entity.AccountType;
import com.gok.pboot.financial.dto.AccountTypeDTO;
import com.gok.pboot.financial.dto.AccountTypePageDTO;
import com.gok.pboot.financial.vo.AccountTypeVO;
import com.gok.pboot.financial.vo.AccountTypePageVO;

import java.util.List;

/**
 * 科目类型
 * <AUTHOR>
 */
public interface IAccountTypeService extends IService<AccountType> {

    /**
     * 分页查询
     * @param dto
     * @return
     */
    Page<AccountTypePageVO> findPage(AccountTypePageDTO dto);

    /**
     * 查看
     * @param id
     * @return
     */
    R<AccountTypeVO> info(Long id);

    /**
     * 新增或编辑
     * @param dto
     * @return
     */
    R<String> addOrEdit(AccountTypeDTO dto);

    /**
     * 删除
     * @param idList
     * @return
     */
    R<String> del(List<Long> idList);

    /**
     * 科目类型列表
     * @return
     */
    List<AccountTypeVO> findList(List<Long> idList);
}
