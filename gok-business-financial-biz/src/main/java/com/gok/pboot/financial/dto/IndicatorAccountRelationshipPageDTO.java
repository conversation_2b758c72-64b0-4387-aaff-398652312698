package com.gok.pboot.financial.dto;

import lombok.Data;

import java.util.List;

/**
 * 指标科目关系分页
 *
 * <AUTHOR>
 * @since 2024-02-19
 */
@Data
public class IndicatorAccountRelationshipPageDTO {

    /**
     * 当前页
     */
    private Long current = 1L;

    /**
     * 分页条数
     */
    private Long size = 10L;

    /**
     * 科目名称/科目编码
     */
    private String project;

    /**
     * 核算部门编码集合
     */
    private List<Long> deptCodeList;


    private List<Long> deptIdList;

    /**
     * 会计体系
     */
    private Long accountingSystemId;

    private List<String> includeExcel;

}
