package com.gok.pboot.financial.util;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 财务常量
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Component
public class FinancePropertiesUtils implements InitializingBean {

    /**
     * 应用id
     */
    @Value("${pushMessage.clientId}")
    private Long clientId;

    /**
     * 门户id
     */
    @Value("${pushMessage.portalAppId}")
    private Long portalAppId;

    /**
     * 项目回款URL
     */
    @Value("${pushMessage.projectPaymentUrl}")
    private String projectPaymentUrl;

    /**
     * 教育回款URL
     */
    @Value("${pushMessage.salesReceiptUrl}")
    private String salesReceiptUrl;

    /**
     * 中台-行政组织
     */
    @Value("${middlePlatform.adminDeptCatName}")
    private String adminDeptCatName;

    /**
     * 中台-核算组织
     */
    @Value("${middlePlatform.deptCatName}")
    private String deptCatName;

    public static Long CLIENT_ID;
    public static Long PORTAL_APP_ID;
    public static String PROJECT_PAYMENT_URL;
    public static String SALES_RECEIPT_URL;
    public static String ADMIN_DEPT_CAT_NAME;
    public static String DEPT_CAT_NAME;

    @Override
    public void afterPropertiesSet() throws Exception {
        CLIENT_ID = this.clientId;
        PORTAL_APP_ID = this.portalAppId;
        PROJECT_PAYMENT_URL = this.projectPaymentUrl;
        SALES_RECEIPT_URL = this.salesReceiptUrl;
        ADMIN_DEPT_CAT_NAME = this.adminDeptCatName;
        DEPT_CAT_NAME = this.deptCatName;
    }
}
