package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 凭证创建相关数据 Enum
 *
 * <AUTHOR>
 * @since 2023-10-23
 */
@Getter
public enum VoucherRecordEnum implements ValueEnum<Integer> {

    /**
     * 凭证字
     */
    DOCTYPE(0, "记"),

    /**
     * 货币
     */
    CURRENCY(1, "RMB"),

    /**
     * 项目人工成本汇总-摘要
     */
    LABOR_COST_SUMMARY(2, "项目分摊"),

    /**
     * 项目回款-摘要
     */
    PAYMENT_SUMMARY(3, "*"),
    /**
     * 项目回款-场景一摘要
     */
    PAYMENT_COLLECTION_SUMMARY(4, "项目回款"),
    /**
     * 项目回款-场景二摘要
     */
    PAYMENT_MARGIN_SUMMARY(5, "保证金回款"),
    /**
     * 项目回款-场景一摘要
     */
    PAYMENT_C_SUMMARY(14, "C端回款"),
    /**
     * 项目回款-场景二摘要
     */
    PAYMENT_OTHER_SUMMARY(15, "其他回款"),
    /**
     * 项目回款-场景二摘要
     */
    PAYMENT_INTEREST_SUMMARY(16, "保证金利息收入"),
    /**
     * 工资成本汇总-摘要
     */
    WAGE_COST_SUMMARY(6, "计提"),
    /**
     * 工资
     */
    PAYROLL_COST(7, "工资"),
    /**
     * 社保
     */
    UNIT_SOCIAL_SECURITY(8, "社保"),
    /**
     * 公积金
     */
    VOUCHER_RECORD_ENUM(9, "公积金"),

    /**
     * 离职补偿金
     */
    AGREEMENT_COMPENSATION(14, "离职补偿金"),

    /**
     * 绩效
     */
    PERFORMANCE(15, "绩效"),

    /**
     * null
     */
    NULL_STR(10, "null"),

    /**
     *
     */
    ERROR_STR(11, "摘要所需判定场景字段无值"),

    /**
     * 项目收入台账-摘要
     */
    LEDGER_STR(12, "结转收入"),

    /**
     * 项目收入台账-摘要
     */
    ACCOUNTING_ERROR(13, "获取帐套信息失败");


    private final Integer value;

    private final String name;

    VoucherRecordEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
