package com.gok.pboot.financial.common;

import chanjet.sign.SignatureManage;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.body.RequestBody;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.gok.pboot.common.core.exception.BusinessException;
import com.gok.pboot.financial.dto.VoucherDTO;
import com.gok.pboot.financial.dto.VoucherListDTO;
import com.gok.pboot.financial.enums.VoucherRecordEnum;
import com.gok.pboot.financial.vo.AccountingAccountSyncVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.methods.PostMethod;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * OpenApi
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OpenApi {

    /**
     * url
     */
    @Value("${openApi.url}")
    private String url;

    /**
     * appKey
     */
    @Value("${openApi.appKey}")
    private String appKey;

    /**
     * appSecret
     */
    @Value("${openApi.appSecret}")
    private String appSecret;

    /**
     * orgId
     */
    @Value("${openApi.orgId}")
    private String orgId;

    public static final String CREATE_URL = "/doc/Create";

    public static final String CREATE_BATCH_URL = "/doc/CreateBatch";

    private static final String PEM_FILE = "/opt/deployments/cjet_pri.pkcs8";

    private static final String GET_TOKEN_URL = "/collaborationapp/GetRealNameTPlusToken?IsFree=1";

    private static final String ARGS = "_args";

    private static final String OPERATING_VALUE = "operatingValue";

    public static final String ACCOUNT_QUERY = "/Account/Query";

    /**
     * 登录T+获取token
     *
     * @param username 账号
     * @param password 密码
     * @param accNum  ..
     * @return token
     */
    @SneakyThrows
    public String login(String username, String password, String accNum) {
        SignatureManage manage = new SignatureManage();
        String authStr = manage.CreateAuthorizationHeader(appKey, appSecret, "", PEM_FILE, null);
        String accInfo = "{\"userName\":\"" + username + "\",\"password\":\"" + manage.getMD5(password) + "\",\"accNum\":\"" + accNum + "\"}";
        String token = this.getToken(authStr, accInfo);
        //从登录成功返回的字符串中解析出Token并持久化，以便后面业务请求调用
        return getJson(token, "access_token");
    }

    /**
     * 登录的请求
     */
    private String getToken(String authStr, String accInfo) {
        String jsonstr = "";
        try {
            HttpClient client = new HttpClient();
            PostMethod method = new PostMethod(url + GET_TOKEN_URL);
            method.addRequestHeader(Header.CONTENT_TYPE.getValue(), ContentType.FORM_URLENCODED.getValue());
            method.addRequestHeader(Header.AUTHORIZATION.getValue(), authStr);
            method.addParameter(ARGS, accInfo);
            client.executeMethod(method);
            jsonstr = method.getResponseBodyAsString();
            method.releaseConnection();
        } catch (Exception e) {
            throw new BusinessException("OpenApi get token error");
        }
        return jsonstr;
    }

    /**
     * json解析
     *
     * @param rstr rstr
     * @param rstrpara rstrpara
     * @return 解析
     */
    public static String getJson(String rstr, String rstrpara) {
        JSONObject jsonobj = JSONUtil.parseObj(rstr);
        String jsonstr = "";
        if (jsonobj.get(rstrpara) != null) {
            jsonstr = jsonobj.get(rstrpara).toString();
        }
        return jsonstr;
    }


    /**
     * 创建凭证
     *
     * @param token token
     * @param voucher {@link VoucherDTO}
     * @return 凭证
     */
    public String createVoucher(String token, VoucherDTO voucher) {
        SignatureManage manage = new SignatureManage();

        String authStr = CharSequenceUtil.EMPTY;
        String finallyResult = CharSequenceUtil.EMPTY;
        try {
            authStr = manage.CreateAuthorizationHeader(appKey, appSecret, "", PEM_FILE, token);
            //数据组装
            String voucherStr = JSONUtil.toJsonStr(voucher);
            String params = "{\"dto\":" + voucherStr + "}";

            //调用创建凭证接口
            HttpClient client = new HttpClient();
            PostMethod method = new PostMethod(url + CREATE_URL);
            method.addRequestHeader(Header.CONTENT_TYPE.getValue(), "application/x-www-form-urlencoded;charset=utf-8");
            method.addRequestHeader(Header.AUTHORIZATION.getValue(), authStr);
            method.addParameter(ARGS, params);
            client.executeMethod(method);
            String result = method.getResponseBodyAsString();
            method.releaseConnection();
            if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                //创建成功
                finallyResult = VoucherRecordEnum.NULL_STR.getName();
            } else {
                log.info("凭证创建失败原因: {}", result);

                finallyResult = VoucherRecordEnum.NULL_STR.getName()
                        .equals(String.valueOf(JSONUtil.parseObj(JSONUtil.parseObj(result).get("data")).get(OPERATING_VALUE)))
                        ? String.valueOf(JSONUtil.parseObj(result).get("message"))
                        : String.valueOf(JSONUtil.parseObj(JSONUtil.parseObj(result).get("data")).get(OPERATING_VALUE));
            }
        } catch (Exception e) {
            throw new BusinessException("OpenApi create voucher error");
        }
        return finallyResult;

    }

    /**
     * 批量创建凭证
     *
     * @param token token
     * @param voucherList {@link VoucherListDTO}
     * @return 凭证
     */
    public String createBatchVoucher(String token, VoucherListDTO voucherList) {
        SignatureManage manage = new SignatureManage();

        String authStr = CharSequenceUtil.EMPTY;
        String finallyResult = CharSequenceUtil.EMPTY;
        try {
            authStr = manage.CreateAuthorizationHeader(appKey, appSecret, "", PEM_FILE, token);
            //数据组装
            String voucherListStr = JSONUtil.toJsonStr(voucherList);

            //调用创建凭证接口
            HttpClient client = new HttpClient();
            PostMethod method = new PostMethod(url + CREATE_BATCH_URL);
            method.addRequestHeader(Header.CONTENT_TYPE.getValue(), "application/x-www-form-urlencoded;charset=utf-8");
            method.addRequestHeader(Header.AUTHORIZATION.getValue(), authStr);
            method.addParameter(ARGS, voucherListStr);
            client.executeMethod(method);
            String result = method.getResponseBodyAsString();
            method.releaseConnection();
            if (VoucherRecordEnum.NULL_STR.getName().equals(result)) {
                //创建成功
                finallyResult = VoucherRecordEnum.NULL_STR.getName();
            } else {
                log.info("凭证创建失败原因: {}", result);

                finallyResult = VoucherRecordEnum.NULL_STR.getName()
                        .equals(String.valueOf(JSONUtil.parseObj(JSONUtil.parseObj(result).get("data")).get(OPERATING_VALUE)))
                        ? String.valueOf(JSONUtil.parseObj(result).get("message"))
                        : String.valueOf(JSONUtil.parseObj(JSONUtil.parseObj(result).get("data")).get(OPERATING_VALUE));
            }
        } catch (Exception e) {
            throw new BusinessException("OpenApi create voucher error");
        }
        return finallyResult;

    }


    public List<AccountingAccountSyncVO> accountQuery(String token) {
        SignatureManage manage = new SignatureManage();

        String authStr = CharSequenceUtil.EMPTY;
        List<AccountingAccountSyncVO> syncVO;
        try {
            authStr = manage.CreateAuthorizationHeader(appKey, appSecret, "", PEM_FILE, token);
            //数据组装
            String params = "{\"dto\": {\"pageSize\":1000,\"pageIndex\":1 }}";
            //调用创建凭证接口
            HttpClient client = new HttpClient();
            PostMethod method = new PostMethod(url + ACCOUNT_QUERY);
            method.addRequestHeader(Header.CONTENT_TYPE.getValue(), "application/x-www-form-urlencoded;charset=utf-8");
            method.addRequestHeader(Header.AUTHORIZATION.getValue(), authStr);
            method.addParameter(ARGS, params);
            client.executeMethod(method);
            String result = method.getResponseBodyAsString();
            method.releaseConnection();
            syncVO = JSON.parseArray(String.valueOf(JSONUtil.parseObj(JSONUtil.parseObj(result).get("DataTable")).get("Rows")),AccountingAccountSyncVO.class);

        } catch (Exception e) {
            throw new BusinessException("OpenApi create voucher error");
        }
        return syncVO;
    }
}
