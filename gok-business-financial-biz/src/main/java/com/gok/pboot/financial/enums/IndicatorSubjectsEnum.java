package com.gok.pboot.financial.enums;

import com.gok.module.upms.common.enums.ValueEnum;
import lombok.Getter;

/**
 * 指标科目编码 Enum
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Getter
public enum IndicatorSubjectsEnum implements ValueEnum<Integer> {

    /**
     * 管理费用
     */
    MANAGE(0, "5602"),

    /**
     * 销售费用
     */
    SALE(1, "5601"),

    /**
     * 研发支出
     */
    RESEARCH(2, "4301");


    private final Integer value;

    private final String name;

    IndicatorSubjectsEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }
}
