package com.gok.pboot.financial;

import com.gok.pboot.common.feign.annotation.EnablePigxFeignClients;
import com.gok.pboot.common.security.annotation.EnablePigxResourceServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.ComponentScan;

/**
 * 数字一体化财务
 *
 * <AUTHOR>
 * @date 2023/07/31 数字财务服务
 */
@EnableDiscoveryClient
@EnablePigxResourceServer
//@ForestScan(basePackages = "com.gok.pboot.financial.common.client")
@SpringBootApplication
@MapperScan({"com.gok.module.file.mapper", "com.gok.pboot.financial.db.mapper", "com.gok.pboot.financial.invoice.mapper"})
@EnablePigxFeignClients(basePackages = {"com.gok"})
@ComponentScan(basePackages = {"com.gok.module.excel.api", "com.gok.pboot.financial"})
public class FinancialApplication {
    public static void main(String[] args) {
        SpringApplication.run(FinancialApplication.class, args);
    }
}