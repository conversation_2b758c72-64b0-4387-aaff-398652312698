package com.gok.pboot.financial.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 指标科目关系展示
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@HeadStyle(fillForegroundColor = 40, horizontalAlignment = HorizontalAlignmentEnum.CENTER)
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class IndicatorAccountRelationshipVO {

    /**
     * 科目id
     */
    @ExcelIgnore
    private Long id;

    /**
     * 核算部门编码
     */
    @ColumnWidth(25)
    @ExcelProperty("核算部门编码")
    private String accountingDepartmentCode;

    /**
     * 核算部门
     */
    @ColumnWidth(25)
    @ExcelProperty("核算部门")
    private String accountingDepartment;

    /**
     * 科目编码
     */
    @ColumnWidth(25)
    @ExcelProperty("科目编码")
    private String code;

    /**
     * 科目名称
     */
    @ColumnWidth(25)
    @ExcelProperty("科目名称")
    private String name;

    /**
     * 会计体系
     */
    @ExcelIgnore()
    private Long accountingSystemId;

    /**
     * 会计体系
     */
    @ExcelProperty("会计体系")
    private String accountingSystem;

}
