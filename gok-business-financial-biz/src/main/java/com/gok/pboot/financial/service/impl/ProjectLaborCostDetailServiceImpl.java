package com.gok.pboot.financial.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gok.bcp.upms.dto.MultiDimensionDeptDto;
import com.gok.bcp.upms.feign.RemoteOutMultiDeptService;
import com.gok.components.common.util.R;
import com.gok.module.excel.api.utils.Threads;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectLaborCostDetail;
import com.gok.pboot.financial.db.mapper.ProjectLaborCostDetailMapper;
import com.gok.pboot.financial.dto.ProjectLaborCostDTO;
import com.gok.pboot.financial.enums.*;
import com.gok.pboot.financial.service.IProjectLaborCostDetailService;
import com.gok.pboot.financial.util.*;
import com.gok.pboot.financial.vo.ProjectLaborCostDetailVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 项目人工成本分摊明细 ServiceImpl
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectLaborCostDetailServiceImpl
        extends ServiceImpl<ProjectLaborCostDetailMapper, ProjectLaborCostDetail>
        implements IProjectLaborCostDetailService {

    private final ProjectLaborCostDetailMapper projectLaborCostDetailMapper;

    private final RemoteOutMultiDeptService remoteOutMultiDeptService;

    private final LogRecordUtils logRecordUtils;

    /**
     * 模糊查询带分页
     *
     * @param projectLaborCostDto 模糊查询属性封装实体
     * @return {@link R}{@link ProjectLaborCostDetailVO}>
     */
    @Override
    public StatisticsPage<ProjectLaborCostDetailVO> findPage(ProjectLaborCostDTO projectLaborCostDto) {
        StatisticsPage<ProjectLaborCostDetailVO> page = new StatisticsPage<>(projectLaborCostDto.getCurrent(), projectLaborCostDto.getSize());
        // 分页
        // V1.2.0 试用期展示为正式
        if (projectLaborCostDto.getPersonnelType() != null) {
            List<Integer> list = new ArrayList<>();
            if (PersonnelTypeEnum.FORMAL.getValue().equals(projectLaborCostDto.getPersonnelType())) {
                list.add(PersonnelTypeEnum.FORMAL.getValue());
                list.add(PersonnelTypeEnum.PROBATION.getValue());
            } else {
                list.add(projectLaborCostDto.getPersonnelType());
            }
            projectLaborCostDto.setPersonnelTypeList(list);
        }
        baseMapper.findPage(page, projectLaborCostDto);

        List<ProjectLaborCostDetailVO> projectLaborCostDetailList = page.getRecords();
        if (CollUtil.isEmpty(projectLaborCostDetailList)) {
            return page;
        }
        // 根据汇总详细数据封装为展示详细数据
        // 获取中台部门树结构 部门id对应部门名称
        Map<Long, String> deptMap = remoteOutMultiDeptService.getDeptList("行政组织", null, null).getData()
                .stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId,
                        MultiDimensionDeptDto::getName, (a, b) -> a));
        projectLaborCostDetailList.forEach(vo -> {
            vo.setId(vo.getYearMonthDate() + vo.getProjectId() + vo.getUserId() + vo.getPersonnelType());
            vo.setYearMonthDate(DateUtils.dateTrans(vo.getYearMonthDate()));
            vo.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, vo.getProjectStatus()));
            Integer personnelType = vo.getPersonnelType();
            if (PersonnelTypeEnum.FORMAL.getValue().equals(personnelType) || PersonnelTypeEnum.PROBATION.getValue().equals(personnelType)) {
                vo.setPersonnelTypeTxt(PersonnelTypeEnum.FORMAL.getName());
            } else {
                vo.setPersonnelTypeTxt(EnumUtils.getNameByValue(PersonnelTypeEnum.class, vo.getPersonnelType()));
            }
            vo.setSalary(MoneyUtils.getInstance().transType(vo.getSalary(), true));
            vo.setUnitSocialSecurity(MoneyUtils.getInstance().transType(vo.getUnitSocialSecurity(), true));
            vo.setUnitProvidentFund(MoneyUtils.getInstance().transType(vo.getUnitProvidentFund(), true));
            vo.setSocialSecurityCost(MoneyUtils.getInstance().transType(vo.getSocialSecurityCost(), true));
            vo.setProvidentFundCost(MoneyUtils.getInstance().transType(vo.getProvidentFundCost(), true));
            vo.setPayrollCost(MoneyUtils.getInstance().transType(vo.getPayrollCost(), true));
            vo.setCostSum(MoneyUtils.getInstance().transType(vo.getCostSum(), true));
            // 根据【是否为内部项目】展示改为【项目类型】
            Integer projectType = vo.getProjectType();
            if (InternalProjectEnum.YES.getValue().equals(projectType)) {
                vo.setProjectTypeTxt(ProjectTypeEnum.INTERNAL_PROJECT.getName());
            } else if (InternalProjectEnum.NO.getValue().equals(projectType)) {
                vo.setProjectTypeTxt(ProjectTypeEnum.INCOME_PROJECT.getName());
            }
            // 根据二级部门id获取二级部门名称
            vo.setIncomeSecondaryDept(deptMap.get(vo.getIncomeSecondaryDeptId()));
        });
        statistics(page, projectLaborCostDto);
        return page;
    }

    /**
     * 统计
     *
     * @param page                页
     * @param projectLaborCostDto 项目人工成本 DTO
     */
    public void statistics(StatisticsPage<ProjectLaborCostDetailVO> page, ProjectLaborCostDTO projectLaborCostDto) {
        List<ProjectLaborCostDetailVO> projectLaborCostDetails = baseMapper.findPage(projectLaborCostDto);
        Map<String, BigDecimal> resultMap = new ConcurrentHashMap<>(9);
        resultMap.put("expendDaysTotal", BigDecimal.ZERO);
        resultMap.put("addDaysTotal", BigDecimal.ZERO);
        resultMap.put("salaryTotal", BigDecimal.ZERO);
        resultMap.put("unitSocialSecurityTotal", BigDecimal.ZERO);
        resultMap.put("unitProvidentFundTotal", BigDecimal.ZERO);
        resultMap.put("socialSecurityCostTotal", BigDecimal.ZERO);
        resultMap.put("providentFundCostTotal", BigDecimal.ZERO);
        resultMap.put("payrollCostTotal", BigDecimal.ZERO);
        resultMap.put("costSumTotal", BigDecimal.ZERO);
        projectLaborCostDetails.parallelStream().forEach(item -> {

            BigDecimal expendDays = item.getExpendDays() == null ? BigDecimal.ZERO : item.getExpendDays();
            resultMap.computeIfPresent("expendDaysTotal", (key, oldValue) -> oldValue.add(expendDays));

            BigDecimal addDays = item.getAddDays() == null ? BigDecimal.ZERO : item.getAddDays();
            resultMap.computeIfPresent("addDaysTotal", (key, oldValue) -> oldValue.add(addDays));

            BigDecimal salary = MoneyUtils.decryptToBigDecimal(item.getSalary(), true);
            resultMap.computeIfPresent("salaryTotal", (key, oldValue) -> oldValue.add(salary));

            BigDecimal unitSocialSecurity = MoneyUtils.decryptToBigDecimal(item.getUnitSocialSecurity(), true);
            resultMap.computeIfPresent("unitSocialSecurityTotal", (key, oldValue) -> oldValue.add(unitSocialSecurity));

            BigDecimal unitProvidentFund = MoneyUtils.decryptToBigDecimal(item.getUnitProvidentFund(), true);
            resultMap.computeIfPresent("unitProvidentFundTotal", (key, oldValue) -> oldValue.add(unitProvidentFund));

            BigDecimal socialSecurityCost = MoneyUtils.decryptToBigDecimal(item.getSocialSecurityCost(), true);
            resultMap.computeIfPresent("socialSecurityCostTotal", (key, oldValue) -> oldValue.add(socialSecurityCost));

            BigDecimal providentFundCost = MoneyUtils.decryptToBigDecimal(item.getProvidentFundCost(), true);
            resultMap.computeIfPresent("providentFundCostTotal", (key, oldValue) -> oldValue.add(providentFundCost));

            BigDecimal payrollCost = MoneyUtils.decryptToBigDecimal(item.getPayrollCost(), true);
            resultMap.computeIfPresent("payrollCostTotal", (key, oldValue) -> oldValue.add(payrollCost));

            BigDecimal costSum = MoneyUtils.decryptToBigDecimal(item.getCostSum(), true);
            resultMap.computeIfPresent("costSumTotal", (key, oldValue) -> oldValue.add(costSum));
        });
        page.setStatistics(resultMap);
    }

    /**
     * 导出Excel选中数据
     *
     * @param projectLaborCostDTO {@link ProjectLaborCostDTO}
     * @return {@link List}<{@link ProjectLaborCostDetailVO}>
     */
    @Override
    public List<ProjectLaborCostDetailVO> export(ProjectLaborCostDTO projectLaborCostDTO) {
        long startTime = System.currentTimeMillis();
        // V1.2.0 试用期展示为正式
        if (Optional.ofNullable(projectLaborCostDTO.getPersonnelType()).isPresent()) {
            List<Integer> list = new ArrayList<>();
            if (PersonnelTypeEnum.FORMAL.getValue().equals(projectLaborCostDTO.getPersonnelType())) {
                list.add(PersonnelTypeEnum.FORMAL.getValue());
                list.add(PersonnelTypeEnum.PROBATION.getValue());
            } else {
                list.add(projectLaborCostDTO.getPersonnelType());
            }
            projectLaborCostDTO.setPersonnelTypeList(list);
        }
        List<ProjectLaborCostDetail> projectLaborCostDetailList = projectLaborCostDetailMapper.exportList(projectLaborCostDTO);

        List<ProjectLaborCostDetailVO> projectLaborCostDetailVOList = new ArrayList<>();
        if (CollUtil.isEmpty(projectLaborCostDetailList)) {
            projectLaborCostDetailVOList.add(new ProjectLaborCostDetailVO());
        } else {
            // 记录日志
            logRecordUtils.logRecord(TypeEnum.DATA_OPERATION, FunctionEnum.PROJECT_LABOR_COST_DETAIL,
                    OperationEnum.EXPORT,
                    OperationEnum.EXPORT.getName() + "【" + projectLaborCostDetailList.size() + "条】");
            projectLaborCostDetailVOList = this.addVOToList(projectLaborCostDetailList, false);
        }

        log.info("导出项目人工明细Excel:{}ms", (System.currentTimeMillis() - startTime));
        return projectLaborCostDetailVOList;
    }

    /**
     * 根据项目人工成本分摊明细数据封装为展示对象列表数据
     *
     * @param projectLaborCostDetailList 项目人工成本分摊明细数据
     * @param isPage 是否分页
     * @return {@link List}<{@link ProjectLaborCostDetailVO}>
     */
    private List<ProjectLaborCostDetailVO> addVOToList(List<ProjectLaborCostDetail> projectLaborCostDetailList, boolean isPage) {
        // 获取中台部门树结构 部门id对应部门名称
        Map<Long, String> deptMap = remoteOutMultiDeptService.getDeptList("行政组织", null, null).getData()
                .stream().collect(Collectors.toMap(MultiDimensionDeptDto::getDeptId, MultiDimensionDeptDto::getName, (a, b) -> a));
        List<ProjectLaborCostDetailVO> projectLaborCostDetailVOList = Collections.synchronizedList(new ArrayList<>());
        int size = 100;
        int countDownLatchSize = projectLaborCostDetailList.size() / size;
        int threadCount = projectLaborCostDetailList.size() % size == 0 ? countDownLatchSize : countDownLatchSize + 1;
        CountDownLatch downLatch = new CountDownLatch(threadCount);
        ThreadPoolExecutor poolExecutor = ThreadUtils.getInstance().getThreadPool(threadCount);
        if (!isPage) {
            Threads.countDownLatchThreadLocal.set(downLatch);
        }
        for (int i = 0; i < threadCount; ++i) {
            int j = i;
            poolExecutor.submit(() -> {
                try {
                    if (j != threadCount - 1) {
                        projectLaborCostDetailList.subList((j * size), (j + 1) * size).forEach(p ->
                            projectLaborCostDetailVOList.add(this.projectLaborCostDetailDecrypt(p, deptMap))
                        );
                    } else {
                        projectLaborCostDetailList.subList((j * size), projectLaborCostDetailList.size()).forEach(p ->
                            projectLaborCostDetailVOList.add(this.projectLaborCostDetailDecrypt(p, deptMap))
                        );
                    }
                } catch (Exception e) {
                    log.info("Error in thread: " + Thread.currentThread().getName(), e);
                } finally {
                    downLatch.countDown();
                }
            });
        }
        if (isPage) {
            try {
                downLatch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        poolExecutor.shutdown();
        Comparator<ProjectLaborCostDetailVO> comparator = Comparator.comparing(ProjectLaborCostDetailVO::getYearMonthDate).reversed()
                .thenComparing(ProjectLaborCostDetailVO::getProjectNo, Comparator.nullsLast(String::compareTo).reversed());
        projectLaborCostDetailVOList.sort(comparator);

        return projectLaborCostDetailVOList;
    }

    /**
     * 字段解密并保留两位小数
     *
     * @param p {@link ProjectLaborCostDetail}
     * @return {@link ProjectLaborCostDetailVO}
     */
    private ProjectLaborCostDetailVO projectLaborCostDetailDecrypt(ProjectLaborCostDetail p, Map<Long, String> map) {
        ProjectLaborCostDetailVO vo = BeanUtil.copyProperties(p, ProjectLaborCostDetailVO.class);
        vo.setId(p.getYearMonthDate() + p.getProjectId() + p.getUserId() + p.getPersonnelType());
        vo.setYearMonthDate(DateUtils.dateTrans(p.getYearMonthDate()));
        vo.setProjectStatusTxt(EnumUtils.getNameByValue(ProjectStatusEnum.class, p.getProjectStatus()));
        Integer personnelType = p.getPersonnelType();
        if (PersonnelTypeEnum.FORMAL.getValue().equals(personnelType) || PersonnelTypeEnum.PROBATION.getValue().equals(personnelType)) {
            vo.setPersonnelTypeTxt(PersonnelTypeEnum.FORMAL.getName());
        } else {
            vo.setPersonnelTypeTxt(EnumUtils.getNameByValue(PersonnelTypeEnum.class, p.getPersonnelType()));
        }
        vo.setSalary(MoneyUtils.getInstance().transType(p.getSalary(), true));
        vo.setUnitSocialSecurity(MoneyUtils.getInstance().transType(p.getUnitSocialSecurity(), true));
        vo.setUnitProvidentFund(MoneyUtils.getInstance().transType(p.getUnitProvidentFund(), true));
        vo.setSocialSecurityCost(MoneyUtils.getInstance().transType(p.getSocialSecurityCost(), true));
        vo.setProvidentFundCost(MoneyUtils.getInstance().transType(p.getProvidentFundCost(), true));
        vo.setPayrollCost(MoneyUtils.getInstance().transType(p.getPayrollCost(), true));
        vo.setCostSum(MoneyUtils.getInstance().transType(p.getCostSum(), true));
        // 根据【是否为内部项目】展示改为【项目类型】
        Integer projectType = vo.getProjectType();
        if (InternalProjectEnum.YES.getValue().equals(projectType)) {
            vo.setProjectTypeTxt(ProjectTypeEnum.INTERNAL_PROJECT.getName());
        } else if (InternalProjectEnum.NO.getValue().equals(projectType)) {
            vo.setProjectTypeTxt(ProjectTypeEnum.INCOME_PROJECT.getName());
        }
        // 根据二级部门id获取二级部门名称
        vo.setIncomeSecondaryDept(map.get(p.getIncomeSecondaryDeptId()));
        return vo;
    }

}
