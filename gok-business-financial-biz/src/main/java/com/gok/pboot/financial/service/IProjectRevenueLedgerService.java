package com.gok.pboot.financial.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.components.common.util.R;
import com.gok.pboot.financial.common.StatisticsPage;
import com.gok.pboot.financial.db.entity.ProjectRevenueLedger;
import com.gok.pboot.financial.dto.ProjectRevenueLedgerDTO;
import com.gok.pboot.financial.dto.RelationInvoiceDTO;
import com.gok.pboot.financial.vo.ProjectRevenueLedgerExcelVO;
import com.gok.pboot.financial.vo.ProjectRevenueLedgerInvoicingVO;
import com.gok.pboot.financial.vo.ProjectRevenueLedgerVO;
import com.gok.pboot.financial.vo.PushResultVO;

import java.util.List;

/**
 * 项目收入台帐Service
 *
 * <AUTHOR>
 * @since 2023-11-07
 */
public interface IProjectRevenueLedgerService extends IService<ProjectRevenueLedger> {

    /**
     * 模糊查询带分页
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link Page}<{@link ProjectRevenueLedgerExcelVO}>
     */
    StatisticsPage<ProjectRevenueLedgerVO> findPage(ProjectRevenueLedgerDTO projectRevenueLedgerDTO);

    /**
     * 修改或新增备注
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 备注
     * @return {@code true} or {@code false}
     */
    Boolean updateRemarks(ProjectRevenueLedgerDTO projectRevenueLedgerDTO);

    /**
     * 导出
     *
     * @param projectRevenueLedgerDTO {@link ProjectRevenueLedgerDTO} 条件
     * @return {@link List}<{@link ProjectRevenueLedgerExcelVO}>
     */
    List<ProjectRevenueLedgerExcelVO> export(ProjectRevenueLedgerDTO projectRevenueLedgerDTO);

    /**
     * 获取发票信息
     *
     * @param id 项目收入台帐id
     * @param contractCode 合同编码
     * @return {@link List}<{@link ProjectRevenueLedgerInvoicingVO}>
     */
    List<ProjectRevenueLedgerInvoicingVO> getInvoice(Long id, String contractCode);

    /**
     * 关联发票
     *
     * @param relationInvoiceDTO 项目id与发票集合
     * @return {@link R}
     */
    R<String> relationInvoice(RelationInvoiceDTO relationInvoiceDTO);

    /**
     * 推送
     *
     * @param ids ids
     * @return PushResultVO
     */
    PushResultVO push(List<String> ids);
}
