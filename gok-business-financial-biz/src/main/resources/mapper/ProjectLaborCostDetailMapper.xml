<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.ProjectLaborCostDetailMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.ProjectLaborCostDetail">
        <result column="year_month_date" jdbcType="DATE" property="yearMonthDate" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_no" jdbcType="VARCHAR" property="projectNo" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_status" jdbcType="VARCHAR" property="projectStatus" />
        <result column="income_dept_id" jdbcType="BIGINT" property="incomeDeptId" />
        <result column="income_dept" jdbcType="VARCHAR" property="incomeDept" />
        <result column="income_secondary_dept_id" jdbcType="BIGINT" property="incomeSecondaryDeptId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="days_attendance" jdbcType="DECIMAL" property="daysAttendance" />
        <result column="expend_days" jdbcType="DECIMAL" property="expendDays" />
        <result column="add_days" jdbcType="DECIMAL" property="addDays" />
        <result column="salary" jdbcType="VARCHAR" property="salary" />
        <result column="unit_social_security" jdbcType="VARCHAR" property="unitSocialSecurity" />
        <result column="unit_provident_fund" jdbcType="VARCHAR" property="unitProvidentFund" />
        <result column="social_security_cost" jdbcType="VARCHAR" property="socialSecurityCost" />
        <result column="provident_fund_cost" jdbcType="VARCHAR" property="providentFundCost" />
        <result column="payroll_cost" jdbcType="VARCHAR" property="payrollCost" />
        <result column="cost_sum" jdbcType="VARCHAR" property="costSum" />
        <result column="salary_paid_subject" jdbcType="BOOLEAN" property="salaryPaidSubject" />
        <result column="personnel_secondary_dept_id" jdbcType="BIGINT" property="personnelSecondaryDeptId" />
        <result column="personnel_secondary_dept" jdbcType="VARCHAR" property="personnelSecondaryDept" />
        <result column="personnel_dept_id" jdbcType="BIGINT" property="personnelDeptId" />
        <result column="personnel_dept" jdbcType="VARCHAR" property="personnelDept" />
        <result column="project_type" jdbcType="BOOLEAN" property="projectType" />
        <result column="personnel_type" jdbcType="BOOLEAN" property="personnelType" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>

    <select id="findPage" resultType="com.gok.pboot.financial.vo.ProjectLaborCostDetailVO">
        SELECT
        year_month_date,
        project_id,
        user_id,
        project_no,
        project_name,
        project_status,
        income_dept_id,
        income_dept,
        income_secondary_dept_id,
        `name`,
        days_attendance,
        expend_days,
        add_days,
        salary,
        unit_social_security,
        unit_provident_fund,
        social_security_cost,
        provident_fund_cost,
        payroll_cost,
        cost_sum,
        salary_paid_subject,
        personnel_secondary_dept_id,
        personnel_secondary_dept,
        personnel_dept_id,
        personnel_dept,
        project_type,
        personnel_type,
        remarks,
        create_by,
        update_by,
        create_time,
        update_time,
        del_flag,
        tenant_id
        FROM
        project_labor_cost_detail
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.yearMonthDateStart != null and query.yearMonthDateStart != ''">
                AND year_month_date >= #{query.yearMonthDateStart}
            </if>
            <if test="query.yearMonthDateEnd != null and query.yearMonthDateEnd != ''">
                AND #{query.yearMonthDateEnd} >= year_month_date
            </if>
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%') OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.projectStatus != null">
                AND project_status LIKE CONCAT('%', #{query.projectStatus}, '%')
            </if>
            <if test="query.projectType != null and query.projectType != ''">
                AND project_type = #{query.projectType}
            </if>
            <if test="query.incomeDeptIds != null and query.incomeDeptIds.size() > 0">
                AND (income_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR income_secondary_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.personnelDeptIds != null and query.personnelDeptIds.size() > 0">
                AND
                (
                personnel_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR personnel_secondary_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.personnelTypeList != null and query.personnelTypeList.size() > 0">
                AND personnel_type IN
                <foreach collection="query.personnelTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.name != null and query.name != ''">
                AND `name` LIKE CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND salary_paid_subject = #{query.attributableSubject}
            </if>
        </where>
        ORDER BY
        year_month_date DESC, project_no DESC
    </select>

    <select id="exportList" resultMap="BaseResultMap">
        SELECT
        year_month_date,
        project_id,
        user_id,
        project_no,
        project_name,
        project_status,
        income_dept_id,
        income_dept,
        income_secondary_dept_id,
        `name`,
        days_attendance,
        expend_days,
        add_days,
        salary,
        unit_social_security,
        unit_provident_fund,
        social_security_cost,
        provident_fund_cost,
        payroll_cost,
        cost_sum,
        salary_paid_subject,
        personnel_secondary_dept_id,
        personnel_secondary_dept,
        personnel_dept_id,
        personnel_dept,
        project_type,
        personnel_type,
        remarks,
        create_by,
        update_by,
        create_time,
        update_time,
        del_flag,
        tenant_id
        FROM
        project_labor_cost_detail
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.ids != null and query.ids.size() > 0">
                AND  CONCAT(year_month_date,project_id,user_id,personnel_type)  IN
                <foreach collection="query.ids" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.yearMonthDateStart != null and query.yearMonthDateStart != ''">
                AND year_month_date >= #{query.yearMonthDateStart}
            </if>
            <if test="query.yearMonthDateEnd != null and query.yearMonthDateEnd != ''">
                AND #{query.yearMonthDateEnd} >= year_month_date
            </if>
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%') OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.projectStatus != null">
                AND project_status LIKE CONCAT('%', #{query.projectStatus}, '%')
            </if>
            <if test="query.projectType != null and query.projectType != ''">
                AND project_type = #{query.projectType}
            </if>
            <if test="query.incomeDeptIds != null and query.incomeDeptIds.size() > 0">
                AND (income_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR income_secondary_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.personnelSecondaryDept != null and query.personnelSecondaryDept.size() > 0">
                AND personnel_secondary_dept_id IN
                <foreach collection="query.personnelSecondaryDept" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.personnelDeptIds != null and query.personnelDeptIds.size() > 0">
                AND
                (
                personnel_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR personnel_secondary_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.personnelTypeList != null and query.personnelTypeList.size() > 0">
                AND personnel_type IN
                <foreach collection="query.personnelTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.name != null and query.name != ''">
                AND `name` LIKE CONCAT('%',#{query.name},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND salary_paid_subject = #{query.attributableSubject}
            </if>
        </where>
    </select>

</mapper>