<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.gok.pboot.financial.db.mapper.AccountsReceivableMapper">

    <!--通用查询映射-->
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.AccountsReceivable">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="project_no" property="projectNo"/>
        <result column="project_name" property="projectName"/>
        <result column="project_status" property="projectStatus"/>
        <result column="within_thirty" property="withinThirty"/>
        <result column="thirty_sixty" property="thirtySixty"/>
        <result column="sixty_hundredTwenty" property="sixtyHundredTwenty"/>
        <result column="above_hundredTwenty" property="aboveHundredTwenty"/>
        <result column="pay_back" property="payBack"/>
        <result column="revenue" property="revenue"/>
        <result column="revenue_collected" property="revenueCollected"/>
        <result column="invoicing" property="invoicing"/>
        <result column="invoicing_collected" property="invoicingCollected"/>
        <result column="customer_collected" property="customerCollected"/>
        <result column="customer_invoicing" property="customerInvoicing"/>
        <result column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="contract_id" property="contractId"/>
        <result column="contract_name" property="contractName"/>
        <result column="contract_code" property="contractCode"/>
        <result column="signing_date" property="signingDate"/>
        <result column="contract_money" property="contractMoney"/>
        <result column="attributable_subject" property="attributableSubject"/>
        <result column="first_department_id" property="firstDepartmentId"/>
        <result column="first_department" property="firstDepartment"/>
        <result column="second_department_id" property="secondDepartmentId"/>
        <result column="second_department" property="secondDepartment"/>
        <result column="salesman_user_id" property="salesmanUserId"/>
        <result column="salesman_user_name" property="salesmanUserName"/>
        <result column="manager_user_id" property="managerUserId"/>
        <result column="manager_user_name" property="managerUserName"/>
        <result column="head_user_id" property="headUserId"/>
        <result column="head_user_name" property="headUserName"/>
        <result column="commissioner_user_id" property="commissionerUserId"/>
        <result column="commissioner_user_name" property="commissionerUserName"/>
        <result column="remarks" property="remarks"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        id,
        project_id,
        project_no,
        project_name,
        project_status,
        within_thirty,
        thirty_sixty,
        sixty_hundredTwenty,
        above_hundredTwenty,
        pay_back,
        invoicing,
        revenue,
        revenue_collected,
        invoicing_collected,
        customer_collected,
        customer_invoicing,
        customer_id,
        customer_name,
        contract_id,
        contract_name,
        contract_code,
        signing_date,
        contract_money,
        attributable_subject,
        first_department_id,
        first_department,
        second_department_id,
        second_department,
        salesman_user_id,
        salesman_user_name,
        manager_user_id,
        manager_user_name,
        head_user_id,
        head_user_name,
        commissioner_user_id,
        commissioner_user_name,
        remarks,
        create_by,
        update_by,
        create_time,
        update_time,
        tenant_id
        FROM
        accounts_receivable
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%')
                OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (contract_name LIKE CONCAT('%',#{query.contract},'%')
                OR contract_code LIKE CONCAT('%',#{query.contract},'%'))
            </if>
            <if test="query.projectStatus != null and query.projectStatus != ''">
                AND project_status LIKE CONCAT('%',#{query.projectStatus},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND attributable_subject = #{query.attributableSubject}
            </if>
            <if test="query.firstDepartmentId != null and query.firstDepartmentId.size() > 0">
                AND (first_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                or second_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.contractMoneyMin != null">
                AND contract_money >= #{query.contractMoneyMin}
            </if>
            <if test="query.contractMoneyMax != null">
                AND contract_money <![CDATA[ <= ]]> #{query.contractMoneyMax}
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND (salesman_user_name LIKE CONCAT('%',#{query.leader},'%')
                OR manager_user_name LIKE CONCAT('%',#{query.leader},'%'))
            </if>
            <if test="query.agingTxt != null and query.agingTxt.length > 0">
                AND
                <foreach collection="query.agingTxt" item="item" index="index" open="(" close=")">
                    <if test="index == 0 and item != null and item != ''">
                        ${item} IS NOT NULL AND ${item} != '0.00' AND ${item} != '0.0' AND ${item} != '0'
                    </if>
                    <if test="index != 0 and item != null and item != ''">
                        AND (${item} IS NOT NULL AND ${item} != '0.00' AND ${item} != '0.0' AND ${item} != '0')
                    </if>
                </foreach>
            </if>
            <if test="query.invoicingCollectedMin != null">
                AND invoicing_collected >= #{query.invoicingCollectedMin}
            </if>
            <if test="query.invoicingCollectedMax != null">
                AND invoicing_collected <![CDATA[ <= ]]> #{query.invoicingCollectedMax}
            </if>
        </where>
        <if test="query.sortTxt != null and query.sortTxt != ''">
            ORDER BY ${query.sortTxt}
        </if>
        <if test="query.sortRuleTxt != null and query.sortRuleTxt != ''">
            ${query.sortRuleTxt}
        </if>
    </select>

    <select id="findPageByNow" resultMap="BaseResultMap">
        SELECT
        a1.id,
        a1.project_id,
        a1.project_no,
        a1.project_name,
        a1.project_status,
        a2.within_thirty,
        a2.thirty_sixty,
        a2.sixty_hundredTwenty,
        a2.above_hundredTwenty,
        a2.pay_back,
        a2.invoicing,
        a2.revenue,
        a2.revenue_collected,
        a2.invoicing_collected,
        a2.customer_collected,
        a2.customer_invoicing,
        a2.contract_money,
        a1.customer_id,
        a1.customer_name,
        a1.contract_id,
        a1.contract_name,
        a1.contract_code,
        a1.signing_date,
        a1.attributable_subject,
        a1.first_department_id,
        a1.first_department,
        a1.second_department_id,
        a1.second_department,
        a1.salesman_user_id,
        a1.salesman_user_name,
        a1.manager_user_id,
        a1.manager_user_name,
        a1.head_user_id,
        a1.head_user_name,
        a1.commissioner_user_id,
        a1.commissioner_user_name,
        a1.remarks,
        a1.create_by,
        a1.update_by,
        a1.create_time,
        a1.update_time,
        a1.tenant_id
        FROM
        accounts_receivable AS a1
        LEFT JOIN accounts_receivable_month AS a2 ON a1.id = a2.id
        <where>
            a1.del_flag = ${@<EMAIL>()}
            AND a2.year_month_date LIKE CONCAT (#{query.yearMonth}, '%')
            <if test="query.project != null and query.project != ''">
                AND (a1.project_no LIKE CONCAT('%',#{query.project},'%')
                OR a1.project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND a1.customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (a1.contract_name LIKE CONCAT('%',#{query.contract},'%')
                OR a1.contract_code LIKE CONCAT('%',#{query.contract},'%'))
            </if>
            <if test="query.projectStatus != null and query.projectStatus != ''">
                AND a1.project_status LIKE CONCAT('%',#{query.projectStatus},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND a1.attributable_subject = #{query.attributableSubject}
            </if>
            <if test="query.firstDepartmentId != null and query.firstDepartmentId.size() > 0">
                AND (a1.first_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                or a1.second_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.contractMoneyMin != null">
                AND a2.contract_money >= #{query.contractMoneyMin}
            </if>
            <if test="query.contractMoneyMax != null">
                AND a2.contract_money <![CDATA[ <= ]]> #{query.contractMoneyMax}
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND (a1.salesman_user_name LIKE CONCAT('%',#{query.leader},'%')
                OR a1.manager_user_name LIKE CONCAT('%',#{query.leader},'%'))
            </if>
            <if test="query.agingTxt != null and query.agingTxt.length > 0">
                AND
                <foreach collection="query.agingTxt" item="item" index="index" open="(" close=")">
                    <if test="index == 0 and item != null and item != ''">
                        a2.${item} IS NOT NULL AND a2.${item} != '0.00' AND a2.${item} != '0'
                    </if>
                    <if test="index != 0 and item != null and item != ''">
                        OR (a2.${item} IS NOT NULL AND a2.${item} != '0.00' AND a2.${item} != '0')
                    </if>
                </foreach>
            </if>
            <if test="query.invoicingCollectedMin != null">
                AND a2.invoicing_collected >= #{query.invoicingCollectedMin}
            </if>
            <if test="query.invoicingCollectedMax != null">
                AND a2.invoicing_collected <![CDATA[ <= ]]> #{query.invoicingCollectedMax}
            </if>
        </where>
        <if test="query.sortTxt != null and query.sortTxt != ''">
            ORDER BY ${query.sortTxt}
        </if>
        <if test="query.sortRuleTxt != null and query.sortRuleTxt != ''">
            ${query.sortRuleTxt}
        </if>
    </select>

    <select id="selectOneByProjectIdAndContractId" resultMap="BaseResultMap">
        SELECT project_no,
               project_name,
               project_status,
               IFNULL(pay_back, 0)                   AS pay_back,
               IFNULL(invoicing, 0)                  AS invoicing,
               customer_name,
               contract_code,
               contract_money,
               DATE_FORMAT(signing_date, '%Y/%m/%d') AS signing_date,
               attributable_subject,
               first_department,
               salesman_user_name,
               manager_user_name
        FROM accounts_receivable
        WHERE del_flag = ${@<EMAIL>()}
          AND project_id = #{projectId}
          AND contract_id = #{contractId}
    </select>

    <select id="exportList" resultMap="BaseResultMap">
        SELECT
        id,
        project_id,
        project_no,
        project_name,
        project_status,
        within_thirty,
        thirty_sixty,
        sixty_hundredTwenty,
        above_hundredTwenty,
        pay_back,
        invoicing,
        revenue_collected,
        revenue,
        invoicing_collected,
        customer_collected,
        customer_invoicing,
        customer_id,
        customer_name,
        contract_id,
        contract_name,
        contract_code,
        signing_date,
        contract_money,
        attributable_subject,
        first_department_id,
        first_department,
        second_department_id,
        second_department,
        salesman_user_id,
        salesman_user_name,
        manager_user_id,
        manager_user_name,
        head_user_id,
        head_user_name,
        commissioner_user_id,
        commissioner_user_name,
        remarks,
        create_by,
        update_by,
        create_time,
        update_time,
        tenant_id
        FROM
        accounts_receivable
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.ids != null and query.ids.size() > 0">
                 AND id IN
                <foreach collection="query.ids" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%')
                OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (contract_name LIKE CONCAT('%',#{query.contract},'%')
                OR contract_code LIKE CONCAT('%',#{query.contract},'%'))
            </if>
            <if test="query.projectStatus != null and query.projectStatus != ''">
                AND project_status LIKE CONCAT('%',#{query.projectStatus},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND attributable_subject = #{query.attributableSubject}
            </if>
            <if test="query.firstDepartmentId != null and query.firstDepartmentId.size() > 0">
                AND (first_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR second_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.contractMoneyMin != null">
                AND contract_money >= #{query.contractMoneyMin}
            </if>
            <if test="query.contractMoneyMax != null">
                AND contract_money <![CDATA[ <= ]]> #{query.contractMoneyMax}
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND (salesman_user_name LIKE CONCAT('%',#{query.leader},'%')
                OR manager_user_name LIKE CONCAT('%',#{query.leader},'%'))
            </if>
            <if test="query.agingTxt != null and query.agingTxt.length > 0">
                AND
                <foreach collection="query.agingTxt" item="item" index="index" open="(" close=")">
                    <if test="index == 0 and item != null and item != ''">
                        ${item} IS NOT NULL AND ${item} != '0.00' AND ${item} != '0.0' AND ${item} != '0'
                    </if>
                    <if test="index != 0 and item != null and item != ''">
                        AND (${item} IS NOT NULL AND ${item} != '0.00' AND ${item} != '0.0' AND ${item} != '0')
                    </if>
                </foreach>
            </if>
            <if test="query.invoicingCollectedMin != null">
                AND invoicing_collected >= #{query.invoicingCollectedMin}
            </if>
            <if test="query.invoicingCollectedMax != null">
                AND invoicing_collected <![CDATA[ <= ]]> #{query.invoicingCollectedMax}
            </if>
        </where>
        <if test="query.sortTxt != null and query.sortTxt != ''">
            ORDER BY ${query.sortTxt}
        </if>
        <if test="query.sortRuleTxt != null and query.sortRuleTxt != ''">
            ${query.sortRuleTxt}
        </if>
    </select>

    <select id="exportListByNow" resultMap="BaseResultMap">
        SELECT
        a1.id,
        a1.project_id,
        a1.project_no,
        a1.project_name,
        a1.project_status,
        a2.within_thirty,
        a2.thirty_sixty,
        a2.sixty_hundredTwenty,
        a2.above_hundredTwenty,
        a2.pay_back,
        a2.invoicing,
        a2.revenue,
        a2.revenue_collected,
        a2.invoicing_collected,
        a2.customer_collected,
        a2.customer_invoicing,
        a2.contract_money,
        a1.customer_id,
        a1.customer_name,
        a1.contract_id,
        a1.contract_name,
        a1.contract_code,
        a1.signing_date,
        a1.attributable_subject,
        a1.first_department_id,
        a1.first_department,
        a1.second_department_id,
        a1.second_department,
        a1.salesman_user_id,
        a1.salesman_user_name,
        a1.manager_user_id,
        a1.manager_user_name,
        a1.head_user_id,
        a1.head_user_name,
        a1.commissioner_user_id,
        a1.commissioner_user_name,
        a1.remarks,
        a1.create_by,
        a1.update_by,
        a1.create_time,
        a1.update_time,
        a1.tenant_id
        FROM
        accounts_receivable AS a1
        LEFT JOIN accounts_receivable_month AS a2 ON a1.id = a2.id
        <where>
            a1.del_flag = ${@<EMAIL>()}
            AND a2.year_month_date LIKE CONCAT (#{query.yearMonth}, '%')
            <if test="query.ids != null and query.ids.size() > 0">
                 AND a1.id IN
                <foreach collection="query.ids" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.project != null and query.project != ''">
                AND (a1.project_no LIKE CONCAT('%',#{query.project},'%')
                OR a1.project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND a1.customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (a1.contract_name LIKE CONCAT('%',#{query.contract},'%')
                OR a1.contract_code LIKE CONCAT('%',#{query.contract},'%'))
            </if>
            <if test="query.projectStatus != null and query.projectStatus != ''">
                AND a1.project_status LIKE CONCAT('%',#{query.projectStatus},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND a1.attributable_subject = #{query.attributableSubject}
            </if>
            <if test="query.firstDepartmentId != null and query.firstDepartmentId.size() > 0">
                 AND (a1.first_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR a1.second_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.contractMoneyMin != null">
                AND a2.contract_money >= #{query.contractMoneyMin}
            </if>
            <if test="query.contractMoneyMax != null">
                AND a2.contract_money <![CDATA[ <= ]]> #{query.contractMoneyMax}
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND (a1.salesman_user_name LIKE CONCAT('%',#{query.leader},'%')
                OR a1.manager_user_name LIKE CONCAT('%',#{query.leader},'%'))
            </if>
            <if test="query.agingTxt != null and query.agingTxt.length > 0">
                AND
                <foreach collection="query.agingTxt" item="item" index="index" open="(" close=")">
                    <if test="index == 0 and item != null and item != ''">
                        a2.${item} IS NOT NULL AND a2.${item} != '0.00' AND a2.${item} != '0'
                    </if>
                    <if test="index != 0 and item != null and item != ''">
                        OR (a2.${item} IS NOT NULL AND a2.${item} != '0.00' AND a2.${item} != '0')
                    </if>
                </foreach>
            </if>
            <if test="query.invoicingCollectedMin != null">
                AND a2.invoicing_collected >= #{query.invoicingCollectedMin}
            </if>
            <if test="query.invoicingCollectedMax != null">
                AND a2.invoicing_collected <![CDATA[ <= ]]> #{query.invoicingCollectedMax}
            </if>
        </where>
        <if test="query.sortTxt != null and query.sortTxt != ''">
            ORDER BY ${query.sortTxt}
        </if>
        <if test="query.sortRuleTxt != null and query.sortRuleTxt != ''">
            ${query.sortRuleTxt}
        </if>
    </select>

    <select id="statistics" resultType="java.util.Map">
        SELECT
            IFNULL(SUM(invoicing), 0) as invoicingTotal,
            IFNULL(SUM(pay_back), 0) as payBackTotal,
            IFNULL(SUM(revenue), 0) as revenueTotal
        FROM accounts_receivable
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%')
                OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (contract_name LIKE CONCAT('%',#{query.contract},'%')
                OR contract_code LIKE CONCAT('%',#{query.contract},'%'))
            </if>
            <if test="query.projectStatus != null and query.projectStatus != ''">
                AND project_status LIKE CONCAT('%',#{query.projectStatus},'%')
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND attributable_subject = #{query.attributableSubject}
            </if>
            <if test="query.firstDepartmentId != null and query.firstDepartmentId.size() > 0">
                AND (first_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR second_department_id IN
                <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.contractMoneyMin != null">
                AND contract_money >= #{query.contractMoneyMin}
            </if>
            <if test="query.contractMoneyMax != null">
                AND contract_money <![CDATA[ <= ]]> #{query.contractMoneyMax}
            </if>
            <if test="query.leader != null and query.leader != ''">
                AND (salesman_user_name LIKE CONCAT('%',#{query.leader},'%')
                OR manager_user_name LIKE CONCAT('%',#{query.leader},'%'))
            </if>
            <if test="query.agingTxt != null and query.agingTxt.length > 0">
                AND
                <foreach collection="query.agingTxt" item="item" index="index" open="(" close=")">
                    <if test="index == 0 and item != null and item != ''">
                        ${item} IS NOT NULL AND ${item} != '0.00' AND ${item} != '0.0' AND ${item} != '0'
                    </if>
                    <if test="index != 0 and item != null and item != ''">
                        AND (${item} IS NOT NULL AND ${item} != '0.00' AND ${item} != '0.0' AND ${item} != '0')
                    </if>
                </foreach>
            </if>
            <if test="query.invoicingCollectedMin != null">
                AND invoicing_collected >= #{query.invoicingCollectedMin}
            </if>
            <if test="query.invoicingCollectedMax != null">
                AND invoicing_collected <![CDATA[ <= ]]> #{query.invoicingCollectedMax}
            </if>
        </where>
    </select>

    <select id="statisticsByNow" resultType="java.util.Map">
        SELECT
        IFNULL(SUM(a2.invoicing), 0) as invoicingTotal,
        IFNULL(SUM(a2.pay_back), 0) as payBackTotal,
        IFNULL(SUM(a2.revenue), 0) as revenueTotal
        FROM (
        SELECT DISTINCT a1.id, a2.invoicing, a2.pay_back, a2.revenue
        FROM accounts_receivable AS a1
        LEFT JOIN accounts_receivable_month AS a2 ON a1.id = a2.id
        WHERE a1.del_flag = ${@<EMAIL>()}
        AND a2.year_month_date LIKE CONCAT(#{query.yearMonth}, '%')
        <if test="query.project != null and query.project != ''">
            AND (a1.project_no LIKE CONCAT('%', #{query.project}, '%')
            OR a1.project_name LIKE CONCAT('%', #{query.project}, '%'))
        </if>
        <if test="query.customerName != null and query.customerName != ''">
            AND a1.customer_name LIKE CONCAT('%', #{query.customerName}, '%')
        </if>
        <if test="query.contract != null and query.contract != ''">
            AND (a1.contract_name LIKE CONCAT('%', #{query.contract}, '%')
            OR a1.contract_code LIKE CONCAT('%', #{query.contract}, '%'))
        </if>
        <if test="query.projectStatus != null and query.projectStatus != ''">
            AND a1.project_status LIKE CONCAT('%', #{query.projectStatus}, '%')
        </if>
        <if test="query.attributableSubject != null and query.attributableSubject != ''">
            AND a1.attributable_subject = #{query.attributableSubject}
        </if>
        <if test="query.firstDepartmentId != null and query.firstDepartmentId.size() > 0">
            AND (a1.first_department_id IN
            <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
            OR a1.second_department_id IN
            <foreach collection="query.firstDepartmentId" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.contractMoneyMin != null">
            AND a2.contract_money >= #{query.contractMoneyMin}
        </if>
        <if test="query.contractMoneyMax != null">
            AND a2.contract_money <![CDATA[ <= ]]> #{query.contractMoneyMax}
        </if>
        <if test="query.leader != null and query.leader != ''">
            AND (a1.salesman_user_name LIKE CONCAT('%', #{query.leader}, '%')
            OR a1.manager_user_name LIKE CONCAT('%', #{query.leader}, '%'))
        </if>
        <if test="query.agingTxt != null and query.agingTxt.length > 0">
            AND
            <foreach collection="query.agingTxt" item="item" index="index" open="(" close=")">
                <if test="index == 0 and item != null and item != ''">
                    a2.${item} IS NOT NULL AND a2.${item} != '0.00' AND a2.${item} != '0'
                </if>
                <if test="index != 0 and item != null and item != ''">
                    OR (a2.${item} IS NOT NULL AND a2.${item} != '0.00' AND a2.${item} != '0')
                </if>
            </foreach>
        </if>
        <if test="query.invoicingCollectedMin != null">
            AND a2.invoicing_collected >= #{query.invoicingCollectedMin}
        </if>
        <if test="query.invoicingCollectedMax != null">
            AND a2.invoicing_collected <![CDATA[ <= ]]> #{query.invoicingCollectedMax}
        </if>
        ) AS filtered_accounts;
    </select>

</mapper>