<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.AccountsReceivableDetailMapper">

  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.AccountsReceivableDetail">
    <id column="project_id" jdbcType="BIGINT" property="projectId" />
    <id column="contract_id" jdbcType="BIGINT" property="contractId" />
    <id column="statistics_time" jdbcType="VARCHAR" property="statisticsTime" />
    <result column="pay_back" jdbcType="DECIMAL" property="payBack" />
    <result column="invoicing" jdbcType="DECIMAL" property="invoicing" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>

    <sql id="Base_Column_List">
        project_id, contract_id, statistics_time, pay_back, invoicing, remarks, create_by,
        update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <!-- 查出来的是包含号数的故不做截取处理 -->
    <select id="selectListByProductId" resultMap="BaseResultMap">
        SELECT
            project_id,
            DATE_FORMAT(statistics_time, '%Y-%m') AS statistics_time,
            SUM(IFNULL(pay_back, 0.00)) AS pay_back,
            SUM(IFNULL(invoicing, 0.00)) AS invoicing
        FROM
            accounts_receivable_detail
        WHERE
            del_flag = ${@<EMAIL>()}
            AND project_id = #{projectId}
            AND contract_id = #{contractId}
        GROUP BY
            DATE_FORMAT(statistics_time, '%Y-%m')
    </select>

</mapper>