<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.FundAccountMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.FundAccount">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="fund_account" jdbcType="VARCHAR" property="fundAccount" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, code, fund_account, remarks, create_by, update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <select id="queryPage" resultType="com.gok.pboot.financial.vo.FundAccountVO">
        select
            <include refid="Base_Column_List"/>
        from fund_account
        where
            del_flag = ${@<EMAIL>()}
            <if test="dto.name != null and dto.name != ''">
                and
                (
                    code like CONCAT('%',#{dto.name},'%')
                or  fund_account like CONCAT('%',#{dto.name},'%')
                )
            </if>
    </select>

    <select id="selectByCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fund_account
        where
            del_flag = ${@<EMAIL>()}
            and code = #{code}
    </select>

    <select id="findList" resultType="com.gok.pboot.financial.vo.FundAccountVO">
        select
            <include refid="Base_Column_List"/>
        from fund_account
        where
            del_flag = ${@<EMAIL>()}
    </select>
</mapper>