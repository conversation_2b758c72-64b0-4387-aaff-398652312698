<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.PaymentMethodMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.PaymentMethod">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="exchangeRate" column="exchange_rate" jdbcType="DECIMAL"/>
            <result property="enableStatus" column="enable_status" jdbcType="TINYINT"/>
            <result property="methodRemarks" column="method_remarks" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>


    <select id="queryPaymentMethodPage" resultType="com.gok.pboot.financial.vo.PaymentMethodVO">
        SELECT
            id,
            `code`,
            `name`,
            exchange_rate,
            enable_status,
            method_remarks
        FROM
            payment_method
        WHERE
            del_flag =${@<EMAIL>()}
    </select>
</mapper>
