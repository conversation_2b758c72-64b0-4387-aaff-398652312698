<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.AccountingSystemMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.AccountingSystem">
    <!--@mbg.generated-->
    <!--@Table accounting_system-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="system_code" jdbcType="VARCHAR" property="systemCode" />
    <result column="system_name" jdbcType="VARCHAR" property="systemName" />
    <result column="accounting_type" jdbcType="VARCHAR" property="accountingType" />
    <result column="enable_status" jdbcType="BOOLEAN" property="enableStatus" />
    <result column="system_remarks" jdbcType="VARCHAR" property="systemRemarks" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, system_code, system_name, accounting_type, enable_status, system_remarks, remarks, 
    create_by, update_by, create_time, update_time, del_flag, tenant_id
  </sql>

  <select id="findPage" resultType="com.gok.pboot.financial.vo.AccountingSystemVO">
    select id, system_code, system_name, accounting_type, enable_status, system_remarks
    from accounting_system
            where del_flag = '0'
    <if test="query.systemCode != null and query.systemCode != ''">
      and (system_code like CONCAT('%',#{query.systemCode}
                    ,'%')
           or system_name like CONCAT('%',#{query.systemCode}
                    ,'%'))
    </if>
    <if test="query.accountingType != null and query.accountingType != ''">
      and accounting_type = #{query.accountingType}
    </if>
    <if test="query.enableStatus != null">
      and enable_status = #{query.enableStatus}
    </if>
    <if test="query.systemRemarks != null and query.systemRemarks != ''">
      and system_remarks like CONCAT('%',#{query.systemRemarks},'%')
    </if>
    <if test="query.idList != null and query.idList.size() != 0">
      and id in
      <foreach collection="query.idList" close=")" open="(" item="id" separator=",">
        #{id}
      </foreach>
    </if>
    order by create_time
  </select>

  <delete id="delByIds">
    update accounting_system
        set del_flag = 1
    where del_flag = '0' and enable_status = '1'
    <if test="idList != null and idList.size() != 0">
      and id in
      <foreach collection="idList" close=")" open="(" item="id" separator=",">
        #{id}
      </foreach>
    </if>
  </delete>

  <select id="selCorrelationNum" resultType="com.gok.pboot.financial.vo.AccountingSystemCorrelationVO">
    select a.id,
    count(b.id) as accountNum,
    count(c.id) as accountTypeNum
    from accounting_system a
    left join accounting_account b on b.accounting_system_id = a.id and b.del_flag = '0'
    left join account_type c on c.accounting_system_id = a.id and c.del_flag = '0'
    where
    a.del_flag = '0'
    <if test="idList != null and idList.size() != 0">
      and a.id in
      <foreach collection="idList" close=")" open="(" item="id" separator=",">
        #{id}
      </foreach>
    </if>
    group by a.id

  </select>
</mapper>