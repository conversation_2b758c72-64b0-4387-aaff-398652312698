<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.SalesReceiptPaymentTaskMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.SalesReceiptPaymentTask">
    <!--@mbg.generated-->
    <!--@Table sales_receipt_payment_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sales_receipt_id" jdbcType="BIGINT" property="salesReceiptId" />
    <result column="customer_payment_process" jdbcType="VARCHAR" property="customerPaymentProcess" />
    <result column="current_approval_process" jdbcType="VARCHAR" property="currentApprovalProcess" />
    <result column="approval_progress" jdbcType="VARCHAR" property="approvalProgress" />
    <result column="update_date" jdbcType="VARCHAR" property="updateDate" />
    <result column="required_materials" jdbcType="VARCHAR" property="requiredMaterials" />
    <result column="payment_risk" jdbcType="INTEGER" property="paymentRisk" />
    <result column="progress_description" jdbcType="VARCHAR" property="progressDescription" />
    <result column="next_step_work" jdbcType="VARCHAR" property="nextStepWork" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sales_receipt_id, customer_payment_process, current_approval_process, approval_progress, update_date, required_materials,
    payment_risk, progress_description, next_Step_work, remarks, create_by, update_by, create_time,
    update_time, del_flag, tenant_id
  </sql>

</mapper>