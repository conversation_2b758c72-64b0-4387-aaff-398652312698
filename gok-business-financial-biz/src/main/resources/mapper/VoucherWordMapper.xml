<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.VoucherWordMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.VoucherWord">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="voucher_word" jdbcType="VARCHAR" property="voucherWord" />
        <result column="set_default" jdbcType="VARCHAR" property="setDefault" />
        <result column="print_template" jdbcType="VARCHAR" property="printTemplate" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, voucher_word, set_default,print_template, remarks, create_by, update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <select id="queryPage" resultType="com.gok.pboot.financial.vo.VoucherWordVO">
        select
            <include refid="Base_Column_List"/>
        from voucher_word
        where
            del_flag = ${@<EMAIL>()}
            <if test="dto.voucherWord != null and dto.voucherWord != ''">
                and voucher_word like CONCAT('%',#{dto.voucherWord},'%')
            </if>
            <if test="dto.printTemplate != null and dto.printTemplate != ''">
                and print_template like CONCAT('%',#{dto.printTemplate},'%')
            </if>
    </select>

    <select id="selectByWord" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from voucher_word
        where
            del_flag = ${@<EMAIL>()}
        and voucher_word=#{voucherWord}
    </select>

    <update id="noDefaultAll">
        update voucher_word set set_default=${@<EMAIL>()}
        where del_flag = ${@<EMAIL>()}
        and set_default=${@<EMAIL>()}
    </update>
</mapper>