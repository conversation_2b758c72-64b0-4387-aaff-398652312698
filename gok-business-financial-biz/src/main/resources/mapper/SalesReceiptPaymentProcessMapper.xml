<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.SalesReceiptPaymentProcessMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.SalesReceiptPaymentProcess">
    <!--@mbg.generated-->
    <!--@Table sales_receipt_payment_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sales_receipt_id" jdbcType="BIGINT" property="salesReceiptId" />
    <result column="approval_process" jdbcType="VARCHAR" property="approvalProcess" />
    <result column="approval_status" jdbcType="VARCHAR" property="approvalStatus" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sales_receipt_id, approval_process, approval_status,  remarks, create_by, update_by, create_time,
    update_time, del_flag, tenant_id
  </sql>

    <insert id="batchSave">
        INSERT INTO sales_receipt_payment_process (id,
                                                   sales_receipt_id,
                                                   approval_process,
                                                   approval_status,
                                                   remarks,
                                                   create_by,
                                                   update_by,
                                                   create_time,
                                                   update_time,
                                                   del_flag, tenant_id)
                VALUES
        <foreach collection="processList" item="item" separator=",">
            (#{item.id},
             #{item.salesReceiptId},
             #{item.approvalProcess},
             #{item.approvalStatus},
             #{item.remarks},
             #{item.createBy},
             #{item.updateBy},
             #{item.createTime},
             #{item.updateTime},
             #{item.delFlag},
             #{item.tenantId})
        </foreach>
    </insert>

    <delete id="deleteBySalesReceiptId">
        delete from sales_receipt_payment_process where sales_receipt_id = #{salesReceiptId}
    </delete>
</mapper>