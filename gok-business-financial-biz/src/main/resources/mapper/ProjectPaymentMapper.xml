<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.ProjectPaymentMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.ProjectPayment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="document_number" jdbcType="VARCHAR" property="documentNumber"/>
        <result column="payment_company" jdbcType="BOOLEAN" property="paymentCompany"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="enterprise_name" jdbcType="VARCHAR" property="enterpriseName"/>
        <result column="payment_date" jdbcType="VARCHAR" property="paymentDate"/>
        <result column="payment_amount" jdbcType="DECIMAL" property="paymentAmount"/>
        <result column="payment_platform" jdbcType="VARCHAR" property="paymentPlatform"/>
        <result column="voucher_number" jdbcType="VARCHAR" property="voucherNumber"/>
        <result column="budget_collection_amount" jdbcType="DECIMAL" property="budgetCollectionAmount"/>
        <result column="payment_note" jdbcType="VARCHAR" property="paymentNote"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="claim_status" jdbcType="INTEGER" property="claimStatus"/>
        <result column="lock_status" jdbcType="INTEGER" property="lockStatus"/>
        <result column="auto_lock" jdbcType="INTEGER" property="autoLock"/>
        <result column="push_status" jdbcType="INTEGER" property="pushStatus"/>
        <result column="record_man_id" jdbcType="BIGINT" property="recordManId"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="education_ids" jdbcType="VARCHAR" property="educationIds"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <association property="projectPaymentClaim">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="project_payment_id" jdbcType="BIGINT" property="projectPaymentId"/>
            <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId"/>
            <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName"/>
            <result column="business_line" jdbcType="INTEGER" property="businessLine"/>
            <result column="project_collection" jdbcType="INTEGER" property="projectCollection"/>
            <result column="collection_within_budget" jdbcType="INTEGER" property="collectionWithinBudget"/>
            <result column="contract_payment_id" jdbcType="BIGINT" property="contractPaymentId"/>
            <result column="contract_payment" jdbcType="VARCHAR" property="contractPayment"/>
            <result column="payment_type" jdbcType="INTEGER" property="paymentType"/>
            <result column="payment_money" jdbcType="VARCHAR" property="paymentMoney"/>
            <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
            <result column="contract_name" jdbcType="VARCHAR" property="contractName"/>
            <result column="project_id" jdbcType="BIGINT" property="projectId"/>
            <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
            <result column="payment_dept_id" jdbcType="BIGINT" property="paymentDeptId"/>
            <result column="payment_dept" jdbcType="VARCHAR" property="paymentDept"/>
            <result column="payment_secondary_dept_id" jdbcType="BIGINT" property="paymentSecondaryDeptId"/>
            <result column="payment_secondary_dept" jdbcType="VARCHAR" property="paymentSecondaryDept"/>
            <result column="belonging_area_id" jdbcType="VARCHAR" property="belongingAreaId"/>
            <result column="belonging_area" jdbcType="VARCHAR" property="belongingArea"/>
            <result column="claimant_id" jdbcType="BIGINT" property="claimantId"/>
            <result column="claimant_name" jdbcType="VARCHAR" property="claimantName"/>
            <result column="claimant_date" jdbcType="TIMESTAMP" property="claimantDate"/>
            <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
            <result column="business_block" jdbcType="INTEGER" property="businessBlock" />
            <result column="margin_code" jdbcType="VARCHAR" property="marginCode" />
            <result column="margin_type" jdbcType="INTEGER" property="marginType" />
            <result column="margin_money" jdbcType="VARCHAR" property="marginMoney" />
        </association>
    </resultMap>
    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        p1.id,
        p1.document_number,
        p1.payment_company,
        p1.customer_id,
        p1.customer_name,
        p1.enterprise_name,
        p1.payment_date,
        p1.payment_amount,
        p1.payment_platform,
        p1.voucher_number,
        p1.budget_collection_amount,
        p1.payment_note,
        p1.bank_account,
        p1.claim_status,
        p1.lock_status,
        p1.auto_lock,
        p1.push_status,
        p1.record_man_id,
        p1.creator_id,
        p1.creator_name,
        p1.education_ids,
        p2.salesman_user_id,
        p2.salesman_user_name,
        p2.business_line,
        p2.project_collection,
        p2.collection_within_budget,
        p2.contract_payment_id,
        p2.contract_payment,
        p2.payment_type,
        p2.payment_money,
        p2.contract_id,
        p2.contract_name,
        p2.project_id,
        p2.project_name,
        p2.payment_dept_id,
        p2.payment_dept,
        p2.payment_secondary_dept_id,
        p2.payment_secondary_dept,
        p2.belonging_area_id,
        p2.belonging_area,
        p2.claimant_id,
        p2.claimant_name,
        DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
        p2.business_block,
        p2.skill_type,
        p2.margin_code,
        p2.margin_type,
        p2.margin_money,
        p2.claim_money
        FROM
        project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = ${@<EMAIL>()}
        <where>
            p1.del_flag = ${@<EMAIL>()}
             <if test="query.authority">
                <if test="query.authUserIdList != null and query.authUserIdList.size() > 0">
                    AND (p1.creator_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p1.record_man_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.claimant_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                   )
                </if>
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%')
                OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%'))
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>

            <if test="query.projectName != null and query.projectName != ''">
                AND (p2.project_name LIKE CONCAT('%', #{query.projectName} , '%')
                    or p2.project_code LIKE CONCAT('%', #{query.projectName} , '%')
                )
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND (p2.contract_name LIKE CONCAT('%', #{query.contractName} , '%')
                    or p2.contract_code LIKE CONCAT('%', #{query.contractName} , '%')
                )
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (p2.salesman_user_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                    or p2.claimant_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                    or p1.creator_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                )
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                     AND p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                     and p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    and p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p2.collection_within_budget = #{query.collectionWithinBudget}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0 ">
                AND p2.payment_type = #{query.paymentType}
            </if>
            <if test="query.contractPayment != null and query.contractPayment != ''">
                AND p2.contract_payment LIKE CONCAT('%', #{query.contractPayment} , '%')
            </if>
        </where>
        ORDER BY p1.document_number DESC
    </select>

    <select id="listByAuth" resultMap="BaseResultMap">
        SELECT
            p1.id,
            p1.document_number,
            p1.payment_company,
            p1.customer_id,
            p1.customer_name,
            p1.enterprise_name,
            p1.payment_date,
            p1.payment_amount,
            p1.payment_platform,
            p1.voucher_number,
            p1.budget_collection_amount,
            p1.payment_note,
            p1.bank_account,
            p1.claim_status,
            p1.lock_status,
            p1.auto_lock,
            p1.push_status,
            p1.record_man_id,
            p1.creator_id,
            p1.creator_name,
            p1.education_ids,
            p2.salesman_user_id,
            p2.salesman_user_name,
            p2.business_line,
            p2.project_collection,
            p2.collection_within_budget,
            p2.contract_payment_id,
            p2.contract_payment,
            p2.contract_name,
            p2.project_id,
            p2.project_name,
            p2.payment_dept_id,
            p2.payment_dept,
            p2.payment_secondary_dept_id,
            p2.payment_secondary_dept,
            p2.belonging_area_id,
            p2.belonging_area,
            p2.claimant_id,
            p2.claimant_name,
            DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
            p2.business_block,
            p2.skill_type
        FROM
            project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = ${@<EMAIL>()}
        <where>
            p1.del_flag = ${@<EMAIL>()}
            AND p1.customer_name IS NULL
            AND p1.claim_status = ${@<EMAIL>()}
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%')
                OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%'))
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND (p2.project_name LIKE CONCAT('%', #{query.projectName} , '%')
                    or p2.project_code LIKE CONCAT('%', #{query.projectName} , '%')
                )
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND (p2.contract_name LIKE CONCAT('%', #{query.contractName} , '%')
                    or p2.contract_code LIKE CONCAT('%', #{query.contractName} , '%')
                )
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (p2.salesman_user_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                    or p2.claimant_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                    or p1.creator_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                )
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                     AND p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                     and p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    and p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p2.collection_within_budget = #{query.collectionWithinBudget}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0 ">
                AND p2.payment_type = #{query.paymentType}
            </if>
            <if test="query.contractPayment != null and query.contractPayment != ''">
                AND p2.contract_payment LIKE CONCAT('%', #{query.contractPayment} , '%')
            </if>
        </where>
        ORDER BY p1.document_number DESC
    </select>

    <select id="listByPushOrLock" resultMap="BaseResultMap">
        SELECT
            p1.id,
            p1.document_number,
            p1.payment_company,
            p1.customer_id,
            p1.customer_name,
            p1.enterprise_name,
            p1.payment_date,
            IFNULL(p1.payment_amount, 0.00) AS payment_amount,
            p1.payment_platform,
            p1.voucher_number,
            p1.budget_collection_amount,
            p1.payment_note,
            p1.bank_account,
            p1.claim_status,
            p1.lock_status,
            p1.auto_lock,
            p1.push_status,
            p1.record_man_id,
            p1.creator_id,
            p1.creator_name,
            p1.education_ids,
            p2.salesman_user_id,
            p2.salesman_user_name,
            p2.business_line,
            p2.project_collection,
            p2.collection_within_budget,
            p2.contract_payment_id,
            p2.contract_payment,
            p2.contract_name,
            p2.project_id,
            p2.project_name,
            p2.payment_dept_id,
            p2.payment_dept,
            p2.payment_secondary_dept_id,
            p2.payment_secondary_dept,
            p2.belonging_area_id,
            p2.belonging_area,
            p2.claimant_id,
            p2.claimant_name,
            DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
            p2.business_block,
            p2.skill_type
        FROM
            project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = ${@<EMAIL>()}
        WHERE
            p1.del_flag = ${@<EMAIL>()}
            AND (p1.push_status = ${@com.gok.pboot.financial.enums.PushStatusEnum@SUCCESS_PUSH.getValue()}
            OR p1.lock_status = ${@<EMAIL>()})
            AND p1.education_ids IS NOT NULL
    </select>

    <select id="selPushVo" resultType="com.gok.pboot.financial.vo.ProjectPaymentPushVO">
        SELECT p1.id,
               p1.document_number,
               p1.payment_company,
               p1.customer_id,
               p1.customer_name,
               p1.enterprise_name,
               p1.payment_date,
               IFNULL(p1.payment_amount, 0.00) AS paymentAmount,
               p1.payment_platform,
               p1.voucher_number,
               p1.budget_collection_amount,
               p1.bank_account
        FROM project_payment AS p1
                where  p1.del_flag = '0'
                AND  ( p1.customer_name IS NOT NULL OR p1.enterprise_name IS NOT NULL)
        <if test="ids != null and ids.size() != 0">
            and p1.id in
            <foreach collection="ids" open="(" close=")" separator="," item="id">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="findPageIds" resultType="java.lang.Long">
    SELECT distinct p.id
                FROM (
                SELECT p1.id,p1.document_number
                FROM project_payment AS p1
                LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = '0'
                WHERE p1.del_flag = '0'
                  AND p1.customer_name IS NULL
                  AND p1.claim_status = '1'
        <if test="query.ids != null and query.ids.size() > 0">
            AND p1.id IN
            <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.documentNumber != null and query.documentNumber != ''">
            AND p1.document_number = #{query.documentNumber}
        </if>
        <if test="query.paymentCompany != null">
            AND p1.payment_company = #{query.paymentCompany}
        </if>
        <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
            AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                    OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                    OR p2.related_customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%'))
        </if>
        <if test="query.paymentBegin != null and query.paymentBegin != ''">
            AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND (p2.project_name LIKE CONCAT('%', #{query.projectName}, '%')
                    or p2.project_code LIKE CONCAT('%', #{query.projectName}, '%')
                    )
        </if>
        <if test="query.contractName != null and query.contractName != ''">
            AND (p2.contract_name LIKE CONCAT('%', #{query.contractName}, '%')
                    or p2.contract_code LIKE CONCAT('%', #{query.contractName}, '%')
                    )
        </if>
        <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
            AND (p2.payment_dept_id IN
            <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
            OR p2.payment_secondary_dept_id IN
            <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
            AND (p2.salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                    or p2.claimant_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                    or p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                    )
        </if>
        <if test="query.documentStatus != null and query.documentStatus.size() > 0">
            <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                AND p1.claim_status IN
                <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                and p1.lock_status IN
                <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                and p1.push_status IN
                <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index"
                         item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="query.collectionWithinBudget != null">
            AND p2.collection_within_budget = #{query.collectionWithinBudget}
        </if>
        <if test="query.claimStatus != null">
            AND p1.claim_status = #{query.claimStatus}
            <if test="query.claimStatus == 1">
                AND p1.lock_status != ${@<EMAIL>()}
            </if>
        </if>
        <if test="query.pushStatus != null">
            AND p1.push_status = #{query.pushStatus}
        </if>
        <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0">
            AND p2.payment_type = #{query.paymentType}
        </if>
        <if test="query.contractPayment != null and query.contractPayment != ''">
            AND p2.contract_payment LIKE CONCAT('%'
              , #{query.contractPayment}
              , '%')
        </if>
        <if test="query.contractPayments != null and query.contractPayments.size() > 0">
            and
            <foreach collection="query.contractPayments" item="item" separator="or" index="index" open="(" close=")">
                p2.contract_payment LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        UNION ALL
        SELECT p1.id,p1.document_number
        FROM project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = '0'
        WHERE p1.del_flag = '0'
            <if test="query.authority">
                <if test="query.authUserIdList != null and query.authUserIdList.size() > 0">
                    AND (p1.creator_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p1.record_man_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.claimant_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                   )
                </if>
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%')
                OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise} , '%')
                OR p2.related_customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%'))
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>

            <if test="query.projectName != null and query.projectName != ''">
                AND (p2.project_name LIKE CONCAT('%', #{query.projectName} , '%')
                    or p2.project_code LIKE CONCAT('%', #{query.projectName} , '%')
                )
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND (p2.contract_name LIKE CONCAT('%', #{query.contractName} , '%')
                    or p2.contract_code LIKE CONCAT('%', #{query.contractName} , '%')
                )
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (p2.salesman_user_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                    or p2.claimant_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                    or p1.creator_name LIKE CONCAT ('%', #{query.salesmanUserName} , '%')
                )
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                     AND p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                     and p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    and p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p2.collection_within_budget = #{query.collectionWithinBudget}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0 ">
                AND p2.payment_type = #{query.paymentType}
            </if>
            <if test="query.contractPayment != null and query.contractPayment != ''">
                AND p2.contract_payment LIKE CONCAT('%', #{query.contractPayment} , '%')
            </if>
            <if test="query.contractPayments != null and query.contractPayments.size() > 0">
            and
            <foreach collection="query.contractPayments" item="item" separator="or" index="index" open="(" close=")">
                p2.contract_payment LIKE CONCAT('%', #{item}, '%')
            </foreach>
        </if>
        <if test="query.customerIds != null and query.customerIds.size() > 0">
            UNION ALL
            SELECT p1.id, p1.document_number
            FROM project_payment AS p1
                    LEFT JOIN project_payment_claim AS p2
            ON p1.id = p2.project_payment_id AND p2.del_flag = '0'
                    WHERE p1.del_flag = '0'
            AND p1.customer_id IN
                    <foreach collection="query.customerIds" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                 OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                OR p2.related_customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%'))
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin}
                AND #{query.paymentEnd}
            </if>

            <if test="query.projectName != null and query.projectName != ''">
                AND (p2.project_name LIKE CONCAT('%'
                  , #{query.projectName}
                  , '%')
                 or p2.project_code LIKE CONCAT('%'
                  , #{query.projectName}
                  , '%')
                        )
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND (p2.contract_name LIKE CONCAT('%'
                  , #{query.contractName}
                  , '%')
                 or p2.contract_code LIKE CONCAT('%'
                  , #{query.contractName}
                  , '%')
                        )
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (p2.salesman_user_name LIKE CONCAT ('%'
                  , #{query.salesmanUserName}
                  , '%')
                 or p2.claimant_name LIKE CONCAT ('%'
                  , #{query.salesmanUserName}
                  , '%')
                 or p1.creator_name LIKE CONCAT ('%'
                  , #{query.salesmanUserName}
                  , '%')
                        )
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                    AND p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                    and p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    and p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p2.collection_within_budget = #{query.collectionWithinBudget}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0">
                AND p2.payment_type = #{query.paymentType}
            </if>
            <if test="query.contractPayment != null and query.contractPayment != ''">
                AND p2.contract_payment LIKE CONCAT('%'
                  , #{query.contractPayment}
                  , '%')
            </if>
            <if test="query.contractPayments != null and query.contractPayments.size() > 0">
                and
                <foreach collection="query.contractPayments" item="item" separator="or" index="index" open="(" close=")">
                    p2.contract_payment LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
        </if>
            ORDER BY document_number DESC
            ) p
    </select>
    <select id="findPageIdsV1" resultType="java.lang.Long">
        SELECT p1.id,p1.document_number
        FROM project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = '0'


        WHERE  p1.del_flag = '0'
        and
        (
        (
        p1.del_flag = '0' AND p1.customer_name IS NULL AND p1.claim_status = '1'
        )
        <if test="query.authority">
            OR  ( p1.del_flag = '0')
        </if>
        <if test="query.authUserIdList != null and query.authUserIdList.size() > 0">
                OR (p1.creator_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                    #{item}
                </foreach>
                OR p1.record_man_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                    #{item}
                </foreach>
                OR p1.id in
                (  select project_payment_id from project_payment_claim where del_flag = '0' and
                    claimant_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">#{item}
                    </foreach>
                    )
                )
        </if>
        <if test="query.authSubjectIdList != null and query.authSubjectIdList.size() > 0">
            OR ( p1.payment_company in
                <foreach collection="query.authSubjectIdList" open="(" close=")" item="item" separator="," index="index">
                 #{item}
                </foreach>
                )
        </if>
        <if test="query.customerIds != null and query.customerIds.size() > 0">
            OR  (
            p1.del_flag = '0'
            AND p1.customer_id IN
            <foreach collection="query.customerIds" open="(" close=")" item="item" separator="," index="index">
                #{item}
            </foreach>
            )
        </if>
        )

        <if test="query.ids != null and query.ids.size() > 0">
            AND p1.id IN
            <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.documentNumber != null and query.documentNumber != ''">
            AND p1.document_number = #{query.documentNumber}
        </if>
        <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
            AND p1.id in(
                select project_payment_id from project_payment_claim where del_flag = '0' and
                (
                p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            )
        </if>
        <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
            AND (
            p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                (
                salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                or claimant_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                )
            )
            or p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
            )
        </if>
        <if test="query.documentStatus != null and query.documentStatus.size() > 0">
            <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                AND p1.claim_status IN
                <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                and p1.lock_status IN
                <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index"
                         item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                and p1.push_status IN
                <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index"
                         item="item">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="query.collectionWithinBudget != null">
            AND p1.id in (
            select project_payment_id from project_payment_claim where del_flag = '0' and
            collection_within_budget = #{query.collectionWithinBudget}
            )
        </if>
        <if test="query.claimStatus != null">
            AND p1.claim_status = #{query.claimStatus}
            <if test="query.claimStatus == 1">
                AND p1.lock_status != ${@<EMAIL>()}
            </if>
        </if>
        <if test="query.pushStatus != null">
            AND p1.push_status = #{query.pushStatus}
        </if>
        <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0">
            AND p1.id in (
            select project_payment_id from project_payment_claim where del_flag = '0' and
            payment_type = #{query.paymentType}
            )


        </if>
        <if test="query.contractPayment != null and query.contractPayment != ''">
            AND p1.id in (
            select project_payment_id from project_payment_claim where del_flag = '0' and
            contract_payment LIKE CONCAT('%', #{query.contractPayment}, '%')
            )
        </if>
        <if test="query.contractPayments != null and query.contractPayments.size() > 0">
            AND p1.id in (
            select project_payment_id from project_payment_claim
            where del_flag = '0' and
            <foreach collection="query.contractPayments" item="item" separator="or" index="index" open="(" close=")">
                contract_payment LIKE CONCAT('%', #{item}, '%')
            </foreach>
            )
        </if>
        <if test="query.contractName != null and query.contractName != ''">
            AND p1.id in (
            select project_payment_id from project_payment_claim where del_flag = '0' and (
                contract_name LIKE CONCAT('%', #{query.contractName}, '%')
                or contract_code LIKE CONCAT('%', #{query.contractName}, '%')
                )
            )
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                (
                project_name LIKE CONCAT('%', #{query.projectName}, '%')
                or project_code LIKE CONCAT('%', #{query.projectName}, '%')
                )
            )
        </if>
        <if test="query.paymentBegin != null and query.paymentBegin != ''">
            AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
        </if>
        <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
            AND (
            p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
            OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
            OR p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                related_customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                )
            )
        </if>
        <if test="query.paymentCompany != null">
            AND p1.payment_company = #{query.paymentCompany}
        </if>
        ORDER BY document_number DESC
    </select>
    <select id="findListByIds" resultType="com.gok.pboot.financial.vo.ProjectPaymentClaimExcelVO">
        SELECT
        p1.id,
        p1.document_number,
        p1.payment_company,
        p1.customer_id,
        p1.customer_name,
        p1.enterprise_name,
        p1.payment_date,
        p1.payment_amount,
        p1.payment_platform,
        p1.voucher_number,
        p1.budget_collection_amount,
        p1.payment_note,
        p1.bank_account,
        p1.claim_status,
        p1.lock_status,
        p1.auto_lock,
        p1.push_status,
        p1.record_man_id,
        p1.creator_id,
        p1.creator_name,
        p1.education_ids,
        p2.salesman_user_id,
        p2.salesman_user_name,
        p2.business_line,
        p2.project_collection,
        p2.collection_within_budget,
        p2.contract_payment_id,
        p2.contract_payment,
        p2.payment_type,
        p2.payment_money,
        p2.contract_id,
        p2.contract_name,
        p2.project_id,
        p2.project_name,
        p2.payment_dept_id,
        p2.payment_dept,
        p2.payment_secondary_dept_id,
        p2.payment_secondary_dept,
        p2.belonging_area_id,
        p2.belonging_area,
        p2.claimant_id,
        p2.claimant_name,
        DATE_FORMAT(p2.claimant_date, '%Y-%m-%d') AS claimant_date,
        p2.business_block,
        p2.skill_type,
        p2.margin_code,
        p2.margin_type,
        p2.margin_money,
        p2.claim_money,
        p2.related_customer_name,
        p2.claim_remark
        FROM
        project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id AND p2.del_flag = ${@<EMAIL>()}
        <where>
            p1.del_flag = ${@<EMAIL>()}
            <if test="pageIds != null and pageIds.size() > 0">
                AND p1.id IN
                <foreach collection="pageIds" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            </where>
            ORDER BY p1.document_number DESC
    </select>
    <select id="paymentAmountStatistics" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(payment_amount), 0) AS paymentAmountTotal
        FROM
        project_payment p1
        WHERE
        p1.del_flag = '0'
        <if test="query.authority">
            <if test="query.authUserIdList != null and query.authUserIdList.size() > 0">
                AND (p1.creator_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                OR p1.record_man_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            AND p1.id IN
            <foreach collection="query.ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.documentNumber != null and query.documentNumber != ''">
            AND p1.document_number = #{query.documentNumber}
        </if>
        <if test="query.paymentCompany != null">
            AND p1.payment_company = #{query.paymentCompany}
        </if>
        <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
            AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
            OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%'))
        </if>
        <if test="query.paymentBegin != null and query.paymentBegin != ''">
            AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND EXISTS (
            SELECT 1
            FROM project_payment_claim p2
            WHERE p1.id = p2.project_payment_id
            AND (p2.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            OR p2.project_code LIKE CONCAT('%', #{query.projectName}, '%'))
            )
        </if>
        <if test="query.contractName != null and query.contractName != ''">
            AND EXISTS (
            SELECT 1
            FROM project_payment_claim p2
            WHERE p1.id = p2.project_payment_id
            AND (p2.contract_name LIKE CONCAT('%', #{query.contractName}, '%')
            OR p2.contract_code LIKE CONCAT('%', #{query.contractName}, '%'))
            )
        </if>
        <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
            AND EXISTS (
            SELECT 1
            FROM project_payment_claim p2
            WHERE p1.id = p2.project_payment_id
            AND (p2.payment_dept_id IN
            <foreach collection="query.paymentDeptId" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR p2.payment_secondary_dept_id IN
            <foreach collection="query.paymentDeptId" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
            )
        </if>
        <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
            AND EXISTS (
            SELECT 1
            FROM project_payment_claim p2
            WHERE p1.id = p2.project_payment_id
            AND (p2.salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
            OR p2.claimant_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
            OR p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName}, '%'))
            )
        </if>
        <if test="query.documentStatus != null and query.documentStatus.size() > 0">
            <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                AND p1.claim_status IN
                <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                AND p1.lock_status IN
                <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                AND p1.push_status IN
                <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="query.claimStatus != null">
            AND p1.claim_status = #{query.claimStatus}
            <if test="query.claimStatus == 1">
                AND p1.lock_status != ${@<EMAIL>()}
            </if>
        </if>
        <if test="query.pushStatus != null">
            AND p1.push_status = #{query.pushStatus}
        </if>
    </select>

    <select id="claimMoneyStatistics" resultType="java.math.BigDecimal">
        SELECT
        COALESCE(SUM(claim_money), 0) AS claimMoneyTotal
        FROM
        project_payment_claim p2
        WHERE
        EXISTS (
        SELECT 1
        FROM project_payment p1
        WHERE p1.id = p2.project_payment_id
        AND p1.del_flag = '0'
        <if test="query.authority">
            <if test="query.authUserIdList != null and query.authUserIdList.size() > 0">
                AND (p1.creator_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                OR p1.record_man_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                OR p2.claimant_id IN
                <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
                )
            </if>
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            AND p1.id IN
            <foreach collection="query.ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="query.documentNumber != null and query.documentNumber != ''">
            AND p1.document_number = #{query.documentNumber}
        </if>
        <if test="query.paymentCompany != null">
            AND p1.payment_company = #{query.paymentCompany}
        </if>
        <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
            AND (p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
            OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
            OR p2.related_customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%'))
        </if>
        <if test="query.paymentBegin != null and query.paymentBegin != ''">
            AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
        </if>
        <if test="query.projectName != null and query.projectName != ''">
            AND (p2.project_name LIKE CONCAT('%', #{query.projectName}, '%')
            OR p2.project_code LIKE CONCAT('%', #{query.projectName}, '%'))
        </if>
        <if test="query.contractName != null and query.contractName != ''">
            AND (p2.contract_name LIKE CONCAT('%', #{query.contractName}, '%')
            OR p2.contract_code LIKE CONCAT('%', #{query.contractName}, '%'))
        </if>
        <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
            AND (p2.payment_dept_id IN
            <foreach collection="query.paymentDeptId" separator="," open="(" close=")">
                #{item}
            </foreach>
            OR p2.payment_secondary_dept_id IN
            <foreach collection="query.paymentDeptId" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
            AND (p2.salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
            OR p2.claimant_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
            OR p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName}, '%'))
        </if>
        <if test="query.documentStatus != null and query.documentStatus.size() > 0">
            <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                AND p1.claim_status IN
                <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                AND p1.lock_status IN
                <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                AND p1.push_status IN
                <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>

        <if test="query.claimStatus != null">
            AND p1.claim_status = #{query.claimStatus}
            <if test="query.claimStatus == 1">
                AND p1.lock_status != ${@<EMAIL>()}
            </if>
        </if>
        <if test="query.pushStatus != null">
            AND p1.push_status = #{query.pushStatus}
        </if>
        <if test="query.collectionWithinBudget != null">
            AND p2.collection_within_budget = #{query.collectionWithinBudget}
        </if>
        <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0">
            AND p2.payment_type = #{query.paymentType}
        </if>
        <if test="query.contractPayment != null and query.contractPayment != ''">
            AND p2.contract_payment LIKE CONCAT('%', #{query.contractPayment}, '%')
        </if>
        );
    </select>

    <select id="findContractPaymentIds" resultType="java.lang.Long">
        SELECT distinct p2.contract_payment_id
        FROM project_payment AS p1
        LEFT JOIN project_payment_claim AS p2 ON p1.id = p2.project_payment_id and p2.del_flag = '0'
        <where>
            p1.del_flag = '0'
            and
            (
            (
            p1.del_flag = '0' AND p1.customer_name IS NULL AND p1.claim_status = '1'
            )
            OR  (
            p1.del_flag = '0'
            <if test="query.authority">
                <if test="query.authUserIdList != null and query.authUserIdList.size() > 0">
                    AND (p1.creator_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p1.record_man_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p1.id in
                    (  select project_payment_id from project_payment_claim where del_flag = '0' and
                    claimant_id IN
                    <foreach collection="query.authUserIdList" open="(" close=")" item="item" separator="," index="index">#{item}
                    </foreach>
                    )
                    )
                </if>
            </if>
            )
            <if test="query.customerIds != null and query.customerIds.size() > 0">
                OR  (
                p1.del_flag = '0'
                AND p1.customer_id IN
                <foreach collection="query.customerIds" open="(" close=")" item="item" separator="," index="index">
                    #{item}
                </foreach>
                )
            </if>
            )

            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND p1.document_number = #{query.documentNumber}
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND p1.id in(
                select project_payment_id from project_payment_claim where del_flag = '0' and
                (
                p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (
                p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                (
                salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                or claimant_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                )
                )
                or p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName}, '%')
                )
            </if>
            <if test="query.documentStatus != null and query.documentStatus.size() > 0">
                <if test="query.claimStatusByDocument != null and query.claimStatusByDocument.size() > 0">
                    AND p1.claim_status IN
                    <foreach collection="query.claimStatusByDocument" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.lockStatusByDocument != null and query.lockStatusByDocument.size() > 0">
                    and p1.lock_status IN
                    <foreach collection="query.lockStatusByDocument" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.pushStatusByDocument != null and query.pushStatusByDocument.size() > 0">
                    and p1.push_status IN
                    <foreach collection="query.pushStatusByDocument" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
            </if>
            <if test="query.collectionWithinBudget != null">
                AND p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                collection_within_budget = #{query.collectionWithinBudget}
                )
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.paymentType != null and query.paymentType != '' or query.paymentType == 0">
                AND p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                payment_type = #{query.paymentType}
                )


            </if>
            <if test="query.contractPayment != null and query.contractPayment != ''">
                AND p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                contract_payment LIKE CONCAT('%', #{query.contractPayment}, '%')
                )
            </if>
            <if test="query.contractName != null and query.contractName != ''">
                AND p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and (
                contract_name LIKE CONCAT('%', #{query.contractName}, '%')
                or contract_code LIKE CONCAT('%', #{query.contractName}, '%')
                )
                )
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                (
                project_name LIKE CONCAT('%', #{query.projectName}, '%')
                or project_code LIKE CONCAT('%', #{query.projectName}, '%')
                )
                )
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>
            <if test="query.customerOrEnterprise != null and query.customerOrEnterprise != ''">
                AND (
                p1.customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                OR p1.enterprise_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                OR p1.id in (
                select project_payment_id from project_payment_claim where del_flag = '0' and
                related_customer_name LIKE CONCAT('%', #{query.customerOrEnterprise}, '%')
                )
                )
            </if>
            <if test="query.paymentCompany != null">
                AND p1.payment_company = #{query.paymentCompany}
            </if>
        </where>
    </select>


</mapper>