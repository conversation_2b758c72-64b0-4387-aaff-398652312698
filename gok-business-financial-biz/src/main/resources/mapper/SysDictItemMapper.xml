<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.SysDictItemMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.SysDictItem">
    <!--@mbg.generated-->
    <!--@Table sys_dict_item-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dict_id" jdbcType="BIGINT" property="dictId" />
    <result column="item_value" jdbcType="VARCHAR" property="itemValue" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="dict_type" jdbcType="VARCHAR" property="dictType" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>

    <select id="selByType" resultType="com.gok.pboot.financial.db.entity.SysDictItem">
        select id, dict_id, item_value, label, dict_type, description
        from sys_dict_item
                where del_flag = '0'
        <if test="dictType != null and dictType != ''">
            and dict_type = #{dictType,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>