<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.EducationPaymentMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.EducationPayment">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="document_number" jdbcType="VARCHAR" property="documentNumber"/>
        <result column="payment_platform" jdbcType="VARCHAR" property="paymentPlatform"/>
        <result column="payment_company" jdbcType="VARCHAR" property="paymentCompany"/>
        <result column="payment_date" jdbcType="VARCHAR" property="paymentDate"/>
        <result column="transaction_number" jdbcType="VARCHAR" property="transactionNumber"/>
        <result column="received_amount" jdbcType="DECIMAL" property="receivedAmount"/>
        <result column="amount_excluding_tax" jdbcType="DECIMAL" property="amountExcludingTax"/>
        <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount"/>
        <result column="commission" jdbcType="DECIMAL" property="commission"/>
        <result column="actual_amount_received" jdbcType="DECIMAL" property="actualAmountReceived"/>
        <result column="payment_note" jdbcType="VARCHAR" property="paymentNote"/>
        <result column="invoicing_status" jdbcType="INTEGER" property="invoicingStatus"/>
        <result column="invoicing_date" jdbcType="VARCHAR" property="invoicingDate"/>
        <result column="invoicing_number" jdbcType="VARCHAR" property="invoicingNumber"/>
        <result column="claim_status" jdbcType="INTEGER" property="claimStatus"/>
        <result column="lock_status" jdbcType="INTEGER" property="lockStatus"/>
        <result column="auto_lock" jdbcType="INTEGER" property="autoLock"/>
        <result column="summary_status" jdbcType="INTEGER" property="summaryStatus"/>
        <result column="creator_id" jdbcType="BIGINT" property="creatorId"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="del_flag" jdbcType="CHAR" property="delFlag"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <association property="educationPaymentClaim">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="education_payment_id" jdbcType="BIGINT" property="educationPaymentId"/>
            <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId"/>
            <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName"/>
            <result column="key_account_manager_id" jdbcType="BIGINT" property="keyAccountManagerId"/>
            <result column="key_account_manager_name" jdbcType="VARCHAR" property="keyAccountManagerName"/>
            <result column="sales_executive_id" jdbcType="BIGINT" property="salesExecutiveId"/>
            <result column="sales_executive_name" jdbcType="VARCHAR" property="salesExecutiveName"/>
            <result column="account_manager_id" jdbcType="BIGINT" property="accountManagerId"/>
            <result column="account_manager_name" jdbcType="VARCHAR" property="accountManagerName"/>
            <result column="consultant_user_id" jdbcType="BIGINT" property="consultantUserId"/>
            <result column="consultant_user_name" jdbcType="VARCHAR" property="consultantUserName"/>
            <result column="payment_dept_id" jdbcType="BIGINT" property="paymentDeptId"/>
            <result column="payment_dept" jdbcType="VARCHAR" property="paymentDept"/>
            <result column="payment_secondary_dept_id" jdbcType="BIGINT" property="paymentSecondaryDeptId"/>
            <result column="payment_secondary_dept" jdbcType="VARCHAR" property="paymentSecondaryDept"/>
            <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
            <result column="business_line" jdbcType="VARCHAR" property="businessLine"/>
            <result column="school_name" jdbcType="VARCHAR" property="schoolName"/>
            <result column="project_id" jdbcType="BIGINT" property="projectId"/>
            <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
            <result column="product_name" jdbcType="VARCHAR" property="productName" />
            <result column="claimant_id" jdbcType="BIGINT" property="claimantId"/>
            <result column="claimant_name" jdbcType="VARCHAR" property="claimantName"/>
            <result column="sure_revenue_date" jdbcType="VARCHAR" property="sureRevenueDate"/>
            <result column="create_time" jdbcType="DATE" property="createTime"/>
            <result column="del_flag" jdbcType="CHAR" property="delFlag" />
            <result column="claim_remark" jdbcType="VARCHAR" property="claimRemark" />
        </association>
    </resultMap>
    <sql id="Base_Column_List">
        id, document_number, payment_platform, payment_date, transaction_number, received_amount,
        amount_excluding_tax, tax_amount, commission, actual_amount_received, payment_note,
        invoicing_status, invoicing_date, invoicing_number, claim_status, lock_status, summary_status,
        creator_id, creator_name, remarks, create_by, update_by, create_time, update_time,
        del_flag, tenant_id
    </sql>

    <select id="findPage" resultMap="BaseResultMap">
        SELECT
        p1.id,
        p1.document_number,
        p1.payment_platform,
        p1.payment_company,
        p1.payment_date,
        p1.transaction_number,
        p1.received_amount,
        p1.amount_excluding_tax,
        p1.tax_amount,
        p1.commission,
        p1.actual_amount_received,
        p1.payment_note,
        p1.invoicing_status,
        p1.invoicing_date,
        p1.invoicing_number,
        p1.claim_status,
        p1.lock_status,
        p1.auto_lock,
        p1.summary_status,
        p1.creator_id,
        p1.creator_name,
        p2.salesman_user_id,
        p2.salesman_user_name,
        p2.key_account_manager_id,
        p2.key_account_manager_name,
        p2.sales_executive_id,
        p2.sales_executive_name,
        p2.account_manager_id,
        p2.account_manager_name,
        p2.consultant_user_id,
        p2.consultant_user_name,
        p2.payment_dept_id,
        p2.payment_dept,
        p2.payment_secondary_dept_id,
        p2.payment_secondary_dept,
        p2.customer_name,
        p2.business_line,
        p2.school_name,
        p2.project_id,
        p2.project_name,
        p2.product_name,
        p2.claimant_id,
        p2.claimant_name,
        p2.sure_revenue_date,
        p2.create_time,
        p2.claim_remark
        FROM
        education_payment AS p1
        LEFT JOIN education_payment_claim AS p2 ON p1.id = p2.education_payment_id AND p2.del_flag = ${@<EMAIL>()}
        <where>
            p1.del_flag = ${@<EMAIL>()}
            <!-- 数据权限 部门 || 人员 || 未认领 -->
            <if test="query.authority">
                <if test="query.deptId != null and query.deptId.size() > 0">
                     AND ((p2.payment_dept_id IN
                    <foreach collection="query.deptId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR
                    p2.payment_secondary_dept_id IN
                    <foreach collection="query.deptId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>))
                </if>
                <if test="query.userId != null and query.userId.size() > 0">
                    AND ((p1.creator_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.claimant_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.salesman_user_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.key_account_manager_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.sales_executive_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.account_manager_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.consultant_user_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>)
                    OR (p1.claim_status = ${@<EMAIL>()}
                    AND p2.claimant_name IS NULL))
                </if>
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND (p1.document_number LIKE #{query.documentNumber}
                OR p1.transaction_number LIKE #{query.documentNumber})
            </if>
            <if test="query.paymentPlatform != null and query.paymentPlatform != ''">
                AND p1.payment_platform LIKE CONCAT('%', #{query.paymentPlatform}, '%')
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>
            <if test="query.paymentCompany != null and query.paymentCompany != ''">
                AND p1.payment_company LIKE #{query.paymentCompany}
            </if>
            <if test="query.invoicingStatus != null">
                AND p1.invoicing_status = #{query.invoicingStatus}
            </if>
            <if test="query.invoicingNumber != null and query.invoicingNumber != ''">
                AND p1.invoicing_number LIKE CONCAT('%', #{query.invoicingNumber}, '%')
            </if>
            <if test="query.customerOrSchool != null and query.customerOrSchool != ''">
                AND (p2.customer_name LIKE CONCAT('%', #{query.customerOrSchool}, '%')
                OR p2.school_name LIKE CONCAT('%', #{query.customerOrSchool}, '%'))
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (p2.salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                    or p2.key_account_manager_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                    or p2.sales_executive_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                    or p2.account_manager_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                    or p2.consultant_user_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                    or p2.claimant_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                    or p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                )
            </if>
            <if test="query.businessLineOrProduct != null and query.businessLineOrProduct != ''">
                AND p2.product_name LIKE CONCAT('%', #{query.businessLineOrProduct},'%')
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p2.project_name LIKE CONCAT('%', #{query.projectName},'%')
            </if>

                <if test="query.summaryStatusByDocument != null">
                    AND p1.summary_status = #{query.summaryStatusByDocument}
                </if>
                <if test="query.claimStatusByDocument != null">
                    AND p1.claim_status = #{query.claimStatusByDocument}
                </if>
                <if test="query.lockStatusByDocument != null">
                    AND p1.lock_status = #{query.lockStatusByDocument}
                </if>
                <if test="query.pushStatusByDocument != null">
                    AND p1.push_status = #{query.pushStatusByDocument}
                </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.summaryStatus != null">
                AND p1.summary_status = #{query.summaryStatus}
            </if>
        </where>
        ORDER BY p1.document_number DESC
    </select>

    <select id="statistics" resultType="java.util.Map">
        select
        COALESCE(SUM(received_amount), 0.00) as receivedAmountTotal,
        COALESCE(SUM(amount_excluding_tax), 0.00) as amountExcludingTaxTotal,
        COALESCE(SUM(tax_amount), 0.00) as taxAmountTotal,
        COALESCE(SUM(commission), 0.00) as commissionTotal,
        COALESCE(SUM(actual_amount_received), 0.00) as actualAmountReceivedTotal
        from(
        SELECT
        p1.*
        FROM
        education_payment AS p1
        LEFT JOIN education_payment_claim AS p2 ON p1.id = p2.education_payment_id AND p2.del_flag = ${@<EMAIL>()}
        <where>
            p1.del_flag = ${@<EMAIL>()}
            <!-- 数据权限 部门 || 人员 || 未认领 -->
            <if test="query.authority">
                <if test="query.deptId != null and query.deptId.size() > 0">
                    AND ((p2.payment_dept_id IN
                    <foreach collection="query.deptId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR
                    p2.payment_secondary_dept_id IN
                    <foreach collection="query.deptId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>))
                </if>
                <if test="query.userId != null and query.userId.size() > 0">
                    AND ((p1.creator_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.claimant_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.salesman_user_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.key_account_manager_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.sales_executive_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.account_manager_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>
                    OR p2.consultant_user_id IN
                    <foreach collection="query.userId" open="(" close=")" item="item" separator="," index="index">
                        #{item}
                    </foreach>)
                    OR (p1.claim_status = ${@<EMAIL>()}
                    AND p2.claimant_name IS NULL))
                </if>
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.documentNumber != null and query.documentNumber != ''">
                AND (p1.document_number LIKE #{query.documentNumber}
                OR p1.transaction_number LIKE #{query.documentNumber})
            </if>
            <if test="query.paymentPlatform != null and query.paymentPlatform != ''">
                AND p1.payment_platform LIKE CONCAT('%', #{query.paymentPlatform}, '%')
            </if>
            <if test="query.paymentBegin != null and query.paymentBegin != ''">
                AND p1.payment_date BETWEEN #{query.paymentBegin} AND #{query.paymentEnd}
            </if>
            <if test="query.paymentCompany != null and query.paymentCompany != ''">
                AND p1.payment_company LIKE #{query.paymentCompany}
            </if>
            <if test="query.invoicingStatus != null">
                AND p1.invoicing_status = #{query.invoicingStatus}
            </if>
            <if test="query.invoicingNumber != null and query.invoicingNumber != ''">
                AND p1.invoicing_number LIKE CONCAT('%', #{query.invoicingNumber}, '%')
            </if>
            <if test="query.customerOrSchool != null and query.customerOrSchool != ''">
                AND (p2.customer_name LIKE CONCAT('%', #{query.customerOrSchool}, '%')
                OR p2.school_name LIKE CONCAT('%', #{query.customerOrSchool}, '%'))
            </if>
            <if test="query.paymentDeptId != null and query.paymentDeptId.size() > 0">
                AND (p2.payment_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                OR p2.payment_secondary_dept_id IN
                <foreach collection="query.paymentDeptId" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.salesmanUserName != null and query.salesmanUserName != ''">
                AND (p2.salesman_user_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                or p2.key_account_manager_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                or p2.sales_executive_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                or p2.account_manager_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                or p2.consultant_user_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                or p2.claimant_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                or p1.creator_name LIKE CONCAT('%', #{query.salesmanUserName},'%')
                )
            </if>
            <if test="query.businessLineOrProduct != null and query.businessLineOrProduct != ''">
                AND p2.product_name LIKE CONCAT('%', #{query.businessLineOrProduct},'%')
            </if>
            <if test="query.projectName != null and query.projectName != ''">
                AND p2.project_name LIKE CONCAT('%', #{query.projectName},'%')
            </if>

            <if test="query.summaryStatusByDocument != null">
                AND p1.summary_status = #{query.summaryStatusByDocument}
            </if>
            <if test="query.claimStatusByDocument != null">
                AND p1.claim_status = #{query.claimStatusByDocument}
            </if>
            <if test="query.lockStatusByDocument != null">
                AND p1.lock_status = #{query.lockStatusByDocument}
            </if>
            <if test="query.pushStatusByDocument != null">
                AND p1.push_status = #{query.pushStatusByDocument}
            </if>
            <if test="query.claimStatus != null">
                AND p1.claim_status = #{query.claimStatus}
                <if test="query.claimStatus == 1">
                    AND p1.lock_status != ${@<EMAIL>()}
                </if>
            </if>
            <if test="query.summaryStatus != null">
                AND p1.summary_status = #{query.summaryStatus}
            </if>
        </where>
        group by p1.id) a
    </select>





</mapper>