<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.CustomizeCalculateMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.CustomizeCalculate">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="code" jdbcType="VARCHAR" property="code" />
        <result column="customize_calculate" jdbcType="VARCHAR" property="customizeCalculate" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, code, customize_calculate, remarks, create_by, update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <select id="queryPage" resultType="com.gok.pboot.financial.vo.CustomizeCalculateVO">
        select
            <include refid="Base_Column_List"/>
        from customize_calculate
        where
        del_flag = ${@<EMAIL>()}
        <if test="dto.name != null and dto.name != ''">
            and
            (
            code like CONCAT('%',#{dto.name},'%')
            or  customize_calculate like CONCAT('%',#{dto.name},'%')
            )
        </if>
    </select>

    <select id="selectByCode" resultType="com.gok.pboot.financial.db.entity.CustomizeCalculate">
        select
            <include refid="Base_Column_List"/>
        from customize_calculate
        where
        del_flag = ${@<EMAIL>()}
        and code = #{code}
    </select>

    <select id="findList" resultType="com.gok.pboot.financial.vo.CustomizeCalculateVO">
        select
            <include refid="Base_Column_List"/>
        from customize_calculate
        where
        del_flag = ${@<EMAIL>()}
    </select>

    <select id="selectByCodes" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from customize_calculate
        where
        del_flag = ${@<EMAIL>()}
        and code in
        <foreach collection="codes" item="code" index="index" open="(" separator="," close=")">
            #{code}
        </foreach>

    </select>

    <insert id="insertOrUpdateBatch">
        INSERT INTO customize_calculate (id, code, customize_calculate,create_time,update_time)
        VALUES
        <foreach collection="list" item="item" index="index"  separator="," >
            (#{item.id}, #{item.code}, #{item.customizeCalculate},NOW(),NOW())
        </foreach>
        ON DUPLICATE KEY UPDATE  code = VALUES(code), customize_calculate = VALUES(customize_calculate),update_time = NOW()
    </insert>
</mapper>