<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.AccountingSetMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.AccountingSet">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="accounting_code" jdbcType="VARCHAR" property="accountingCode" />
        <result column="accounting_subject" jdbcType="VARCHAR" property="accountingSubject" />
        <result column="accounting_account" jdbcType="VARCHAR" property="accountingAccount" />
        <result column="accounting_password" jdbcType="VARCHAR" property="accountingPassword" />
        <result column="enable_status" jdbcType="INTEGER" property="enableStatus" />
        <result column="accounting_remarks" jdbcType="VARCHAR" property="accountingRemarks" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <result column="fund_account_id" jdbcType="BIGINT" property="fundAccountId" />
        <result column="accounting_type" jdbcType="VARCHAR" property="accountingType" />
    </resultMap>
    <sql id="Base_Column_List">
        id, accounting_code, accounting_subject, accounting_account, accounting_password
        enable_status, accounting_remarks, remarks, create_by, update_by, create_time, update_time, del_flag, tenant_id,
        fund_account_id,accounting_type
    </sql>

    <select id="findPage" resultType="com.gok.pboot.financial.vo.AccountingSetVO">
        select a.id,
               a.accounting_code,
               a.accounting_subject,
               a.accounting_account,
               a.accounting_password,
               a.enable_status,
               a.accounting_remarks,
               a.fund_account_id,
               b.fund_account,
               a.accounting_type
        from accounting_set a
                     left join fund_account b on a.fund_account_id = b.id
                where
                a.del_flag = '0'
        <if test="query.accountingCode != null and query.accountingCode != ''">
            and a.accounting_code like CONCAT('%',#{query.accountingCode}
                    ,'%')
        </if>
        <if test="query.accountingSubject != null and query.accountingSubject != ''">
            and a.accounting_subject = #{query.accountingSubject}
        </if>
        <if test="query.enableStatus != null">
            and a.enable_status = #{query.enableStatus}
        </if>
        <if test="query.idList != null and query.idList.size() != 0">
            and a.id in
            <foreach collection="query.idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        order by a.create_time desc
    </select>

    <select id="selById" resultType="com.gok.pboot.financial.vo.AccountingSetVO">
        select a.id,
               a.accounting_code,
               a.accounting_subject,
               a.accounting_account,
               a.accounting_password,
               a.enable_status,
               a.accounting_remarks,
               a.fund_account_id,
               b.fund_account,
               a.accounting_type
        from accounting_set a
                     left join fund_account b on a.fund_account_id = b.id
                where
                a.del_flag = '0'
        <if test="id != null and id != ''">
            and a.id = #{id}
        </if>
    </select>

    <delete id="delByIds">
        update accounting_set
        set del_flag = 1
                where enable_status = '1'
        <if test="idList != null and idList.size() != 0">
            and id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>