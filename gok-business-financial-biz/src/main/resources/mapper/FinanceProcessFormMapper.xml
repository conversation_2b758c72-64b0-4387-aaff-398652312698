<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.FinanceProcessFormMapper">


    <select id="findPage" resultType="com.gok.pboot.financial.vo.FinanceProcessFormVO">
        select
        t1.id,
        t1.requestid,
        t1.bill_type,
        t1.billing_number,
        t1.billing_amount,
        t1.payable_amount,
        t1.offset_amount,
        t1.application_date,
        t1.applicant_id,
        t1.applicant_name,
        t1.applicant_dept_id,
        t1.applicant_dept_name,
        t1.belong_company,
        t1.reason,
        t1.financial_doc_number,
        t1.approver_ids,
        t1.approver_names,
        t1.related_ids,
        t1.approval_status,
        t1.receive_date,
        t1.voucher_status,
        t1.voucher_number,
        t1.voucher_date,
        t1.receipt_status,
        t1.receipt_date,
        t1.settlement_person_id,
        t1.settlement_person_name,
        t1.settlement_status,
        t1.settlement_date,
        t1.fund_account
        from finance_process_form t1
        <where>
            <if test="query.ids != null and query.ids.size() > 0">
                AND t1.id IN
                <foreach collection="query.ids" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.ids == null or  query.ids.size() ==0 ">
                <if test="query.billTypes != null and query.billTypes.size() > 0">
                    AND t1.bill_type IN
                    <foreach collection="query.billTypes" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.billNumber != null and query.billNumber != ''">
                    <bind name="billNumberLike" value="'%'+query.billNumber+'%'"/>
                    AND (t1.billing_number LIKE #{billNumberLike}
                    OR t1.financial_doc_number LIKE #{billNumberLike})
                </if>
                <if test="query.applicationReason != null and query.applicationReason != ''">
                    AND t1.reason LIKE CONCAT('%',#{query.applicationReason},'%')
                </if>
                <if test="query.belongCompany != null and query.belongCompany != '' ">
                    AND t1.belong_company = #{query.belongCompany}
                </if>
                <if test="query.deptIds != null and query.deptIds.size() > 0">
                    AND t1.applicant_dept_id IN
                    <foreach collection="query.deptIds" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.approvalStatus != null and query.approvalStatus.size() > 0">
                    AND t1.approval_status IN
                    <foreach collection="query.approvalStatus" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.receiptStatus != null and query.receiptStatus.size() > 0">
                    AND t1.receipt_status IN
                    <foreach collection="query.receiptStatus" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>

                <if test="query.voucherStatus != null">
                    AND t1.voucher_status = #{query.voucherStatus}
                </if>


                <if test="query.relevantPersonnel != null and query.relevantPersonnel != ''">
                    <bind name="relevantPersonnelLike" value="'%'+query.relevantPersonnel+'%'"/>
                    AND (t1.applicant_name LIKE #{relevantPersonnelLike}
                    OR t1.approver_names LIKE #{relevantPersonnelLike}
                    OR t1.settlement_person_name LIKE #{relevantPersonnelLike}
                    )
                </if>
                <if test="query.startDate != null and query.endDate ==null ">
                    AND STR_TO_DATE( t1.application_date, '%Y-%m-%d') >= #{query.startDate}
                </if>
                <if test="query.startDate != null and query.endDate !=null ">
                    AND STR_TO_DATE( t1.application_date, '%Y-%m-%d') >= #{query.startDate}
                    AND #{query.endDate} >= STR_TO_DATE( t1.application_date, '%Y-%m-%d')
                </if>
                <if test="query.startDate == null and query.startDate !=null ">
                    AND #{query.endDate} >= STR_TO_DATE( t1.application_date, '%Y-%m-%d')
                </if>
                <if test="query.payableAmountMin != null and query.payableAmountMax ==null ">
                    AND t1.payable_amount >= #{query.payableAmountMin}
                </if>
                <if test="query.payableAmountMin != null and query.payableAmountMax !=null ">
                    AND t1.payable_amount >= #{query.payableAmountMin}
                    AND #{query.payableAmountMax} >= t1.payable_amount
                </if>
                <if test="query.payableAmountMin == null and query.payableAmountMax !=null ">
                    AND #{query.payableAmountMax} >= t1.payable_amount
                </if>
            </if>
        </where>
        order by t1.application_date desc
    </select>


    <select id="findBaseList" resultType="com.gok.pboot.financial.vo.FinanceProcessFormBaseVO">
        select
        t1.id,
        t1.requestid,
        t1.related_ids,
        t1.approval_status,
        t1.settlement_status,
        t1.application_date,
        t1.billing_amount,
        t1.payable_amount,
        t1.offset_amount
        from finance_process_form t1
        <where>
            <if test="query.ids != null and query.ids.size() > 0">
                AND t1.id IN
                <foreach collection="query.ids" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.ids == null or  query.ids.size() ==0 ">
                <if test="query.billTypes != null and query.billTypes.size() > 0">
                    AND t1.bill_type IN
                    <foreach collection="query.billTypes" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.billNumber != null and query.billNumber != ''">
                    <bind name="billNumberLike" value="'%'+query.billNumber+'%'"/>
                    AND (t1.billing_number LIKE #{billNumberLike}
                    OR t1.financial_doc_number LIKE #{billNumberLike})
                </if>
                <if test="query.applicationReason != null and query.applicationReason != ''">
                    AND t1.reason LIKE CONCAT('%',#{query.applicationReason},'%')
                </if>
                <if test="query.belongCompany != null and query.belongCompany != '' ">
                    AND t1.belong_company = #{query.belongCompany}
                </if>
                <if test="query.deptIds != null and query.deptIds.size() > 0">
                    AND t1.applicant_dept_id IN
                    <foreach collection="query.deptIds" separator="," open="(" close=")" index="index" item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.approvalStatus != null and query.approvalStatus.size() > 0">
                    AND t1.approval_status IN
                    <foreach collection="query.approvalStatus" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>
                <if test="query.receiptStatus != null and query.receiptStatus.size() > 0">
                    AND t1.receipt_status IN
                    <foreach collection="query.receiptStatus" separator="," open="(" close=")" index="index"
                             item="item">
                        #{item}
                    </foreach>
                </if>

                <if test="query.voucherStatus != null">
                    AND t1.voucher_status = #{query.voucherStatus}
                </if>

                <if test="query.relevantPersonnel != null and query.relevantPersonnel != ''">
                    <bind name="relevantPersonnelLike" value="'%'+query.relevantPersonnel+'%'"/>
                    AND (t1.applicant_name LIKE #{relevantPersonnelLike}
                    OR t1.approver_names LIKE #{relevantPersonnelLike}
                    OR t1.settlement_person_name LIKE #{relevantPersonnelLike}
                    )
                </if>
                <if test="query.startDate != null and query.endDate ==null ">
                    AND STR_TO_DATE( t1.application_date, '%Y-%m-%d') >= #{query.startDate}
                </if>
                <if test="query.startDate != null and query.endDate !=null ">
                    AND STR_TO_DATE( t1.application_date, '%Y-%m-%d') >= #{query.startDate}
                    AND #{query.endDate} >= STR_TO_DATE( t1.application_date, '%Y-%m-%d')
                </if>
                <if test="query.startDate == null and query.startDate !=null ">
                    AND #{query.endDate} >= STR_TO_DATE( t1.application_date, '%Y-%m-%d')
                </if>
                <if test="query.payableAmountMin != null and query.payableAmountMax ==null ">
                    AND t1.payable_amount >= #{query.payableAmountMin}
                </if>
                <if test="query.payableAmountMin != null and query.payableAmountMax !=null ">
                    AND t1.payable_amount >= #{query.payableAmountMin}
                    AND #{query.payableAmountMax} >= t1.payable_amount
                </if>
                <if test="query.payableAmountMin == null and query.payableAmountMax !=null ">
                    AND #{query.payableAmountMax} >= t1.payable_amount
                </if>
            </if>
        </where>
    </select>

    <select id="getStatistics" resultType="com.alibaba.fastjson.JSONObject">
        select
        SUM(billing_amount) as billingAmountTotal,
        SUM(payable_amount) as payableAmountTotal,
        SUM(offset_amount) as offsetAmountTotal
        from finance_process_form
        <where>
            <if test="ids != null and ids.size() > 0">
                AND id IN
                <foreach collection="ids" separator="," open="(" close=")" index="index" item="item">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


</mapper>