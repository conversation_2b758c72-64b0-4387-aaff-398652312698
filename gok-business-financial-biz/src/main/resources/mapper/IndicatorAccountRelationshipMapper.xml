<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.IndicatorAccountRelationshipMapper">

    <select id="findPage" resultType="com.gok.pboot.financial.vo.IndicatorAccountRelationshipVO">
        SELECT
        iar.id,
        iar.code,
        aa.account_name as name,
        iar.accounting_department_code,
        iar.accounting_dept_id,
        iar.accounting_department,
        iar.accounting_department_sort,
        iar.enable_status,
        iar.accounting_system_id,
        acs.system_name as accountingSystem
        FROM indicator_account_relationship iar
        left join accounting_account aa on iar.code = aa.account_code and iar.accounting_system_id = aa.accounting_system_id
        left join accounting_system acs on iar.accounting_system_id = acs.id
        <where>
            iar.del_flag = ${@<EMAIL>()}
            AND iar.enable_status = 0
            <if test="query.project != null and query.project != ''">
                AND (aa.account_code LIKE CONCAT('%', #{query.project}, '%')
                OR aa.account_name LIKE CONCAT('%', #{query.project}, '%'))
            </if>
            <if test="query.deptCodeList != null and query.deptCodeList.size() > 0">
                AND iar.accounting_department_code IN
                <foreach collection="query.deptCodeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.deptIdList != null and query.deptIdList.size() > 0">
                AND iar.accounting_dept_id IN
                <foreach collection="query.deptIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.accountingSystemId != null">
                AND iar.accounting_system_id = #{query.accountingSystemId}
            </if>
        </where>
        group by iar.id
        ORDER BY iar.accounting_department_code
    </select>

    <select id="getBySystemCode" resultType="com.gok.base.admin.vo.AccountRelationshipVO">
        SELECT
            b.id,
            b.code,
            b.name,
            b.accounting_department_code,
            b.accounting_dept_id,
            b.accounting_department,
            b.accounting_department_sort,
            b.enable_status,
            b.accounting_system_id,
            a.system_name as accountingSystem
        FROM
            accounting_system a
            LEFT JOIN indicator_account_relationship b ON a.id = b.accounting_system_id
        WHERE
            a.del_flag = 0
            AND b.del_flag = 0
            AND a.system_code = #{systemCode}
        ORDER BY b.accounting_department_code
    </select>
</mapper>