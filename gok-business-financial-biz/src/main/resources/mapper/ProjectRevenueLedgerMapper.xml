<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.ProjectRevenueLedger">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="process_coding" jdbcType="VARCHAR" property="processCoding" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_no" jdbcType="VARCHAR" property="projectNo" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="contract_id" jdbcType="BIGINT" property="contractId" />
        <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
        <result column="closing_start_conditions" jdbcType="INTEGER" property="closingStartConditions" />
        <result column="income_date" jdbcType="VARCHAR" property="incomeDate" />
        <result column="income_amount_tax" jdbcType="DECIMAL" property="incomeAmountTax" />
        <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount" />
        <result column="acceptance_report" jdbcType="INTEGER" property="acceptanceReport" />
        <result column="acceptance_report_file" jdbcType="VARCHAR" property="acceptanceReportFile" />
        <result column="acceptance_date" jdbcType="VARCHAR" property="acceptanceDate" />
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
        <result column="business_first_dept_id" jdbcType="BIGINT" property="businessFirstDeptId" />
        <result column="business_first_dept" jdbcType="VARCHAR" property="businessFirstDept" />
        <result column="business_secondary_dept_id" jdbcType="BIGINT" property="businessSecondaryDeptId" />
        <result column="business_secondary_dept" jdbcType="VARCHAR" property="businessSecondaryDept" />
        <result column="account_name" jdbcType="VARCHAR" property="accountName" />
        <result column="business_direction" jdbcType="INTEGER" property="businessDirection" />
        <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
        <result column="contract_money" jdbcType="DECIMAL" property="contractMoney" />
        <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId" />
        <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName" />
        <result column="applicant_user_id" jdbcType="BIGINT" property="applicantUserId" />
        <result column="applicant_user_name" jdbcType="VARCHAR" property="applicantUserName" />
        <result column="push_status" jdbcType="INTEGER" property="pushStatus" />
        <result column="contract_company" jdbcType="VARCHAR" property="contractCompany" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
        <collection property="projectRevenueLedgerInvoicingList" ofType="com.gok.pboot.financial.db.entity.ProjectRevenueLedgerInvoicing">
            <id column="invoicing_id" jdbcType="BIGINT" property="id" />
            <result column="revenue_ledger_id" jdbcType="BIGINT" property="revenueLedgerId" />
            <result column="invoicing_date" jdbcType="VARCHAR" property="invoicingDate" />
            <result column="invoicing_number" jdbcType="VARCHAR" property="invoicingNumber" />
            <result column="invoicing_amount_including_tax" jdbcType="VARCHAR" property="invoicingAmountIncludingTax" />
            <result column="tax_amount" jdbcType="VARCHAR" property="taxAmount" />
            <result column="invoicing_amount" jdbcType="VARCHAR" property="invoicingAmount" />
            <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
            <result column="invoicing_subject" jdbcType="VARCHAR" property="invoicingSubject" />
        </collection>
        <collection property="projectRevenueLedgerDetailList" ofType="com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail">
            <id column="id" jdbcType="BIGINT" property="id" />
            <id column="mainid" jdbcType="BIGINT" property="mainid" />
            <id column="request_id" jdbcType="BIGINT" property="requestId" />
            <result column="income_detail_amount_tax" jdbcType="DECIMAL" property="incomeDetailAmountTax" />
            <result column="income_detail_amount" jdbcType="DECIMAL" property="incomeDetailAmount" />
            <result column="tax_tate" jdbcType="VARCHAR" property="taxTate" />
            <result column="income_type" jdbcType="VARCHAR" property="incomeType" />
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        id, process_coding, project_id, project_no, project_name, contract_id, contract_code,
        closing_start_conditions, income_date, income_amount_tax, income_amount, acceptance_report,
        acceptance_report_file, acceptance_date, customer_id, customer_name, business_first_dept_id,
        business_first_dept, business_secondary_dept_id, business_secondary_dept, account_name,
        business_direction, contract_name, contract_money, salesman_user_id, salesman_user_name,
        applicant_user_id, applicant_user_name, push_status, contract_company, remarks, create_by, update_by,
        create_time, update_time, del_flag, tenant_id
    </sql>

    <select id="findPage" resultType="com.gok.pboot.financial.vo.ProjectRevenueLedger2VO">
        SELECT
        p1.id,
        p1.process_coding,
        p1.project_id,
        p1.project_no,
        p1.project_name,
        p1.contract_id,
        p1.contract_code,
        p1.closing_start_conditions,
        p1.income_date,
        p1.income_amount_tax,
        IFNULL(p1.income_amount, 0.00) AS income_amount,
        p1.acceptance_report,
        p1.acceptance_report_file,
        p1.acceptance_date,
        p1.customer_id,
        p1.customer_name,
        p1.customer_code,
        p1.business_first_dept_id,
        p1.business_first_dept,
        p1.business_secondary_dept_id,
        p1.business_secondary_dept,
        p1.account_name,
        p1.business_direction,
        p1.contract_name,
        p1.contract_money,
        p1.salesman_user_id,
        p1.salesman_user_name,
        p1.applicant_user_id,
        p1.applicant_user_name,
        p1.push_status,
        p1.contract_company,
        p1.remarks
        FROM project_revenue_ledger AS p1
        LEFT JOIN project_revenue_ledger_detail AS p2 ON p1.id = p2.request_id
        <where>
            p1.del_flag = ${@<EMAIL>()}
            AND (p1.income_amount_tax IS NOT NULL AND p1.income_amount_tax != 0 AND p1.income_amount_tax != 0.00)
            <if test="query.number != null and query.number != ''">
                AND p1.process_coding LIKE CONCAT('%', #{query.number}, '%')
            </if>
            <if test="query.project != null and query.project != ''">
                AND (p1.project_name LIKE CONCAT('%', #{query.project}, '%') OR p1.project_no LIKE CONCAT('%', #{query.project}, '%'))
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (p1.contract_name LIKE CONCAT('%', #{query.contract}, '%') OR p1.contract_code LIKE CONCAT('%', #{query.contract}, '%'))
            </if>
            <if test="query.customer != null and query.customer != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customer}, '%') OR p1.customer_code LIKE CONCAT('%', #{query.customer}, '%'))
            </if>
            <if test="query.closingStartConditions != null">
                AND p1.closing_start_conditions = #{query.closingStartConditions}
            </if>
            <if test="query.incomeAmountTaxMin != null">
                AND p1.income_amount_tax >= #{query.incomeAmountTaxMin}
            </if>
            <if test="query.incomeAmountTaxMax != null">
                AND #{query.incomeAmountTaxMax} >= p1.income_amount_tax
            </if>
            <if test="query.incomeOrAcceptanceDateStart != null and query.incomeOrAcceptanceDateStart != ''">
                AND (p1.income_date >= #{query.incomeOrAcceptanceDateStart} AND #{query.incomeOrAcceptanceDateEnd} >= p1.income_date
                OR p1.acceptance_date >= #{query.incomeOrAcceptanceDateStart} AND #{query.incomeOrAcceptanceDateEnd} >= p1.acceptance_date)
            </if>
            <if test="query.businessDeptList != null and query.businessDeptList.size() > 0">
                AND (p1.business_first_dept_id IN
                <foreach collection="query.businessDeptList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR p1.business_secondary_dept_id IN
                <foreach collection="query.businessDeptList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="query.applicationOrSale != null and query.applicationOrSale != ''">
                AND (p1.applicant_user_name LIKE CONCAT('%', #{query.applicationOrSale}, '%') OR p1.salesman_user_name LIKE CONCAT('%', #{query.applicationOrSale}, '%'))
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 收入结项 -->
            <if test="query.incomeType != null and query.incomeType != ''">
                AND p2.income_type = #{query.incomeType}
            </if>
            <if test="query.invoicingSubject != null and query.invoicingSubject != ''">
                AND p1.contract_company LIKE #{query.invoicingSubject}
            </if>
        </where>
        GROUP BY p1.id
        ORDER BY p1.process_coding DESC
    </select>

    <select id="findList" resultMap="BaseResultMap">
        SELECT
        p1.id,
        p1.process_coding,
        p1.project_id,
        p1.project_no,
        p1.project_name,
        p1.contract_id,
        p1.contract_code,
        p1.closing_start_conditions,
        p1.income_date,
        p1.income_amount_tax,
        IFNULL(p1.income_amount, 0.00) AS income_amount,
        p1.acceptance_report,
        p1.acceptance_report_file,
        p1.acceptance_date,
        p1.customer_id,
        p1.customer_name,
        p1.customer_code,
        p1.business_first_dept_id,
        p1.business_first_dept,
        p1.business_secondary_dept_id,
        p1.business_secondary_dept,
        p1.account_name,
        p1.business_direction,
        p1.contract_name,
        p1.contract_money,
        p1.salesman_user_id,
        p1.salesman_user_name,
        p1.applicant_user_id,
        p1.applicant_user_name,
        p1.push_status,
        p1.contract_company,
        p1.remarks
        FROM project_revenue_ledger AS p1
        LEFT JOIN project_revenue_ledger_detail AS p2 ON p1.id = p2.request_id
        <where>
            p1.del_flag = ${@<EMAIL>()}
            AND (p1.income_amount_tax IS NOT NULL AND p1.income_amount_tax != 0 AND p1.income_amount_tax != 0.00)
            <if test="query.number != null and query.number != ''">
                AND p1.process_coding LIKE CONCAT('%', #{query.number}, '%')
            </if>
            <if test="query.project != null and query.project != ''">
                AND (p1.project_name LIKE CONCAT('%', #{query.project}, '%') OR p1.project_no LIKE CONCAT('%', #{query.project}, '%'))
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (p1.contract_name LIKE CONCAT('%', #{query.contract}, '%') OR p1.contract_code LIKE CONCAT('%', #{query.contract}, '%'))
            </if>
            <if test="query.customer != null and query.customer != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customer}, '%') OR p1.customer_code LIKE CONCAT('%', #{query.customer}, '%'))
            </if>
            <if test="query.closingStartConditions != null">
                AND p1.closing_start_conditions = #{query.closingStartConditions}
            </if>
            <if test="query.incomeAmountTaxMin != null">
                AND p1.income_amount_tax >= #{query.incomeAmountTaxMin}
            </if>
            <if test="query.incomeAmountTaxMax != null">
                AND #{query.incomeAmountTaxMax} >= p1.income_amount_tax
            </if>
            <if test="query.incomeOrAcceptanceDateStart != null and query.incomeOrAcceptanceDateStart != ''">
                AND (p1.income_date >= #{query.incomeOrAcceptanceDateStart} AND #{query.incomeOrAcceptanceDateEnd} >= p1.income_date
                OR p1.acceptance_date >= #{query.incomeOrAcceptanceDateStart} AND #{query.incomeOrAcceptanceDateEnd} >= p1.acceptance_date)
            </if>
            <if test="query.businessDeptList != null and query.businessDeptList.size() > 0">
                AND (p1.business_first_dept_id IN
                <foreach collection="query.businessDeptList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR p1.business_secondary_dept_id IN
                <foreach collection="query.businessDeptList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="query.applicationOrSale != null and query.applicationOrSale != ''">
                AND (p1.applicant_user_name LIKE CONCAT('%', #{query.applicationOrSale}, '%') OR p1.salesman_user_name LIKE CONCAT('%', #{query.applicationOrSale}, '%'))
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 收入结项 -->
            <if test="query.incomeType != null and query.incomeType != ''">
                AND p2.income_type = #{query.incomeType}
            </if>
            <if test="query.invoicingSubject != null and query.invoicingSubject != ''">
                AND p1.contract_company LIKE #{query.invoicingSubject}
            </if>
        </where>
        ORDER BY process_coding DESC
    </select>

    <update id="updateProjectById">
        UPDATE project_revenue_ledger
        SET remarks = #{remarks}
        WHERE id = #{id}
    </update>

    <update id="updatePushStatus">
     UPDATE project_revenue_ledger
        SET push_status =#{projectRevenueLedger.pushStatus,jdbcType=INTEGER},
         update_by = #{projectRevenueLedger.updateBy,jdbcType=VARCHAR},
         update_time = #{projectRevenueLedger.updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{projectRevenueLedger.id,jdbcType=VARCHAR}
    </update>

    <select id="selectOneById" resultMap="BaseResultMap">
        SELECT id, process_coding
        FROM project_revenue_ledger
        WHERE id = #{id} AND del_flag = ${@<EMAIL>()}
    </select>

    <select id="selList" resultType="com.gok.pboot.financial.vo.ProjectRevenueLedgerPushVO">
    SELECT
        p1.id,
        p1.process_coding,
        p1.project_id,
        p1.project_no,
        p1.project_name,
        p1.income_date,
        p1.income_amount_tax,
        IFNULL(p1.income_amount, 0.00) AS income_amount,
        p1.acceptance_date,
        p1.customer_id,
        p1.customer_name,
        p1.customer_code,
        p1.business_first_dept_id,
        p1.business_first_dept,
        p1.business_secondary_dept_id,
        p1.business_secondary_dept,
        p1.contract_company AS invoicing_subject
        FROM project_revenue_ledger AS p1
        where
        p1.del_flag='0'
        AND p1.income_date IS NOT NULL
        AND p1.project_name IS NOT NULL
        AND p1.customer_name IS NOT NULL
        <if test="ids != null and ids.size() > 0">
                AND  p1.id IN
                <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        GROUP BY p1.id
    </select>

    <select id="statisticsAmount" resultType="java.util.Map">
        SELECT
        SUM(IFNULL(p1.income_amount_tax, 0.00)) AS incomeAmountTaxTotal,
        SUM(IFNULL(p1.income_amount, 0.00)) AS incomeAmountTotal,
        IFNULL(SUM(p2.income_detail_amount), 0.00) AS incomeDetailAmountTotal
        FROM
        project_revenue_ledger AS p1
        LEFT JOIN (
        SELECT request_id, SUM(income_detail_amount) AS income_detail_amount
        FROM project_revenue_ledger_detail
        GROUP BY request_id
        ) AS p2 ON p1.id = p2.request_id
        <where>
            p1.del_flag = ${@<EMAIL>()}
            AND (p1.income_amount_tax IS NOT NULL AND p1.income_amount_tax != 0 AND p1.income_amount_tax != 0.00)
            <if test="query.number != null and query.number != ''">
                AND p1.process_coding LIKE CONCAT('%', #{query.number}, '%')
            </if>
            <if test="query.project != null and query.project != ''">
                AND (p1.project_name LIKE CONCAT('%', #{query.project}, '%') OR p1.project_no LIKE CONCAT('%', #{query.project}, '%'))
            </if>
            <if test="query.contract != null and query.contract != ''">
                AND (p1.contract_name LIKE CONCAT('%', #{query.contract}, '%') OR p1.contract_code LIKE CONCAT('%', #{query.contract}, '%'))
            </if>
            <if test="query.customer != null and query.customer != ''">
                AND (p1.customer_name LIKE CONCAT('%', #{query.customer}, '%') OR p1.customer_code LIKE CONCAT('%', #{query.customer}, '%'))
            </if>
            <if test="query.closingStartConditions != null">
                AND p1.closing_start_conditions = #{query.closingStartConditions}
            </if>
            <if test="query.incomeAmountTaxMin != null">
                AND p1.income_amount_tax >= #{query.incomeAmountTaxMin}
            </if>
            <if test="query.incomeAmountTaxMax != null">
                AND #{query.incomeAmountTaxMax} >= p1.income_amount_tax
            </if>
            <if test="query.incomeOrAcceptanceDateStart != null and query.incomeOrAcceptanceDateStart != ''">
                AND (p1.income_date >= #{query.incomeOrAcceptanceDateStart} AND #{query.incomeOrAcceptanceDateEnd} >= p1.income_date
                OR p1.acceptance_date >= #{query.incomeOrAcceptanceDateStart} AND #{query.incomeOrAcceptanceDateEnd} >= p1.acceptance_date)
            </if>
            <if test="query.businessDeptList != null and query.businessDeptList.size() > 0">
                AND (p1.business_first_dept_id IN
                <foreach collection="query.businessDeptList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                OR p1.business_secondary_dept_id IN
                <foreach collection="query.businessDeptList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="query.applicationOrSale != null and query.applicationOrSale != ''">
                AND (p1.applicant_user_name LIKE CONCAT('%', #{query.applicationOrSale}, '%') OR p1.salesman_user_name LIKE CONCAT('%', #{query.applicationOrSale}, '%'))
            </if>
            <if test="query.pushStatus != null">
                AND p1.push_status = #{query.pushStatus}
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND p1.id IN
                <foreach collection="query.ids" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- 收入结项 -->
            <if test="query.incomeType != null and query.incomeType != ''">
                AND p2.income_type = #{query.incomeType}
            </if>
            <if test="query.invoicingSubject != null and query.invoicingSubject != ''">
                AND p1.contract_company LIKE #{query.invoicingSubject}
            </if>
        </where>
    </select>
</mapper>