<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.PushLogMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.PushLog">
    <!--@mbg.generated-->
    <!--@Table push_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="content_id" jdbcType="BIGINT" property="contentId" />
    <result column="content_type" jdbcType="INTEGER" property="contentType" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, content_id, content_type, create_by, update_by, create_time, update_time, del_flag, 
    tenant_id
  </sql>
</mapper>