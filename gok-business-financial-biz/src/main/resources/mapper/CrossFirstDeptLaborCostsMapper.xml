<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.CrossFirstDeptLaborCostsMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.CrossFirstDeptLaborCosts">
      <id column="year_month_date" jdbcType="VARCHAR" property="yearMonthDate" />
      <id column="personnel_dept_id" jdbcType="BIGINT" property="personnelDeptId" />
      <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
      <result column="personnel_dept" jdbcType="VARCHAR" property="personnelDept" />
      <result column="people_num" jdbcType="INTEGER" property="peopleNum" />
      <result column="labor_costs" jdbcType="VARCHAR" property="laborCosts" />
      <result column="project_cost" jdbcType="VARCHAR" property="projectCost" />
      <result column="project_cost_proportion" jdbcType="VARCHAR" property="projectCostProportion" />
      <result column="strategic_projects_cost" jdbcType="VARCHAR" property="strategicProjectsCost" />
      <result column="strategic_projects_cost_proportion" jdbcType="VARCHAR" property="strategicProjectsCostProportion" />
      <result column="manage_cost_projects_cost" jdbcType="VARCHAR" property="manageCostProjectsCost" />
      <result column="manage_cost_projects_cost_proportion" jdbcType="VARCHAR" property="manageCostProjectsCostProportion" />
      <result column="unallocated_cost" jdbcType="VARCHAR" property="unallocatedCost" />
      <result column="unallocated_cost_proportion" jdbcType="VARCHAR" property="unallocatedCostProportion" />
      <result column="student_intern_people_num" jdbcType="INTEGER" property="studentInternPeopleNum" />
      <result column="student_intern_cost" jdbcType="VARCHAR" property="studentInternCost" />
      <result column="student_intern_projects_cost" jdbcType="VARCHAR" property="studentInternProjectsCost" />
      <result column="student_intern_internal_projects_cost" jdbcType="VARCHAR" property="studentInternInternalProjectsCost" />
      <result column="student_intern_unallocated_cost" jdbcType="VARCHAR" property="studentInternUnallocatedCost" />
      <result column="student_intern_cost_proportion" jdbcType="VARCHAR" property="studentInternCostProportion" />
      <result column="student_outside_people_num" jdbcType="INTEGER" property="studentOutsidePeopleNum" />
      <result column="student_outside_cost" jdbcType="VARCHAR" property="studentOutsideCost" />
      <result column="per_capita_monthly_cost_own" jdbcType="VARCHAR" property="perCapitaMonthlyCostOwn" />
      <result column="per_capita_monthly_cost_stusen" jdbcType="VARCHAR" property="perCapitaMonthlyCostStusen" />
      <result column="remarks" jdbcType="VARCHAR" property="remarks" />
      <result column="create_by" jdbcType="VARCHAR" property="createBy" />
      <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
      <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
      <result column="del_flag" jdbcType="CHAR" property="delFlag" />
      <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>

    <select id="findList" resultType="com.gok.pboot.financial.vo.CrossDeptLaborCostsVO">
      SELECT
      year_month_date,
      sort_order,
      personnel_dept_id,
      personnel_dept,
      people_num,
      labor_costs,
      project_cost,
      project_cost_proportion,
      strategic_projects_cost,
      strategic_projects_cost_proportion,
      manage_cost_projects_cost,
      manage_cost_projects_cost_proportion,
      unallocated_cost,
      unallocated_cost_proportion,
      student_intern_people_num,
      student_intern_cost,
      student_intern_projects_cost,
      student_intern_internal_projects_cost,
      student_intern_unallocated_cost,
      student_intern_cost_proportion,
      student_outside_people_num,
      student_outside_cost,
      per_capita_monthly_cost_own,
      per_capita_monthly_cost_stusen
      FROM
      cross_first_dept_labor_costs
      <where>
        del_flag = ${@<EMAIL>()}
        AND personnel_dept_id IS NOT NULL
        <if test="query.yearMonthDayStart != null and query.yearMonthDayStart != ''">
          AND year_month_date >= #{query.yearMonthDayStart} AND #{query.yearMonthDayEnd} >= year_month_date
        </if>
        <if test="query.deptId != null and query.deptId.size() > 0">
          AND personnel_dept_id IN
          <foreach collection="query.deptId" item="id" index="index" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
          AND personnel_dept_id IN
          <foreach collection="query.ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
          </foreach>
        </if>
      </where>
      ORDER BY personnel_dept_id, year_month_date
    </select>
</mapper>