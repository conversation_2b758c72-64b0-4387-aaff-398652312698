<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.WageCostDetailMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.WageCostDetail">
    <id column="year_month_date" jdbcType="VARCHAR" property="yearMonthDate" />
    <id column="salary_paid_subject" jdbcType="VARCHAR" property="salaryPaidSubject" />
    <id column="current_dept_id" jdbcType="BIGINT" property="currentDeptId" />
    <result column="current_dept" jdbcType="VARCHAR" property="currentDept" />
    <result column="primary_dept_id" jdbcType="BIGINT" property="primaryDeptId" />
    <result column="primary_dept" jdbcType="VARCHAR" property="primaryDept" />
    <result column="secondary_dept_id" jdbcType="BIGINT" property="secondaryDeptId" />
    <result column="secondary_dept" jdbcType="VARCHAR" property="secondaryDept" />
    <result column="tertiary_dept_id" jdbcType="BIGINT" property="tertiaryDeptId" />
    <result column="tertiary_dept" jdbcType="VARCHAR" property="tertiaryDept" />
    <result column="salary" jdbcType="VARCHAR" property="salary" />
    <result column="individual_income_tax" jdbcType="VARCHAR" property="individualIncomeTax" />
    <result column="individual_social_security" jdbcType="VARCHAR" property="individualSocialSecurity" />
    <result column="individual_provident_fund" jdbcType="VARCHAR" property="individualProvidentFund" />
    <result column="back_pay" jdbcType="VARCHAR" property="backPay" />
    <result column="income_tax_difference_from_last_month" jdbcType="VARCHAR" property="incomeTaxDifferenceFromLastMonth" />
    <result column="actual_pay" jdbcType="VARCHAR" property="actualPay" />
    <result column="unit_social_security" jdbcType="VARCHAR" property="unitSocialSecurity" />
    <result column="unit_provident_fund" jdbcType="VARCHAR" property="unitProvidentFund" />
    <result column="disabled_security_fund" jdbcType="VARCHAR" property="disabledSecurityFund" />
    <result column="payroll_cost" jdbcType="VARCHAR" property="payrollCost" />
    <result column="people_num" jdbcType="INTEGER" property="peopleNum" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    year_month_date, salary_paid_subject, current_dept_id, current_dept, primary_dept_id,
    primary_dept, secondary_dept_id, secondary_dept, tertiary_dept_id, tertiary_dept,
    salary, individual_income_tax, individual_social_security, individual_provident_fund,
    back_pay, income_tax_difference_from_last_month, actual_pay, unit_social_security,
    unit_provident_fund, disabled_security_fund, payroll_cost, people_num, push_status,
    remarks, create_by, update_by, create_time, update_time, del_flag, tenant_id
  </sql>

  <select id="queryDetailPage" resultType="com.gok.pboot.financial.vo.WageCostDetailVO">
        SELECT
            CONCAT(year_month_date,current_dept_id,salary_paid_subject) as id,
            year_month_date,
            current_dept_id,
            current_dept,
            primary_dept_id,
            primary_dept,
            secondary_dept_id,
            secondary_dept,
            tertiary_dept_id,
            tertiary_dept,
            salary,
            individual_income_tax ,
            individual_social_security,
            individual_provident_fund,
            back_pay,
            income_tax_difference_from_last_month,
            actual_pay,
            unit_social_security,
            unit_provident_fund,
            disabled_security_fund,
            people_num,
            payroll_cost,
            salary_paid_subject,
            agreement_compensation,
            actual_agreement_compensation,
            performance,
            actual_performance,
            actual_paySum
            FROM
            wage_cost_detail
            <where>
                del_flag=${@<EMAIL>()}
                <if test="dto.yearMonthDateStart != null and dto.yearMonthDateStart != ''">
                    AND year_month_date >= #{dto.yearMonthDateStart}
                </if>
                <if test="dto.yearMonthDateEnd != null and dto.yearMonthDateEnd != ''">
                    AND #{dto.yearMonthDateEnd} >= year_month_date
                </if>
                <if test="dto.paySubject!=null and dto.paySubject!=''">
                    AND
                    salary_paid_subject=#{dto.paySubject}
                </if>

                <if test="dto.deptIds!=null and dto.deptIds.size()>0">
                    AND (
                    primary_dept_id IN
                    <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR secondary_dept_id IN
                    <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR tertiary_dept_id IN
                    <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.conditionDeptIdList != null and dto.conditionDeptIdList.size() > 0">
                    AND (
                    primary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR secondary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR tertiary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </where>
            ORDER BY year_month_date DESC
  </select>

    <select id="exportList" resultType="com.gok.pboot.financial.vo.WageCostDetailVO">
        SELECT CONCAT(year_month_date, secondary_dept_id, salary_paid_subject) as id,
               year_month_date,
               current_dept_id,
               primary_dept_id,
               secondary_dept_id,
               tertiary_dept_id,
               current_dept,
               primary_dept,
               secondary_dept,
               tertiary_dept,
               salary,
               individual_income_tax,
               individual_social_security,
               individual_provident_fund,
               back_pay,
               income_tax_difference_from_last_month,
               actual_pay,
               unit_social_security,
               unit_provident_fund,
               disabled_security_fund,
               people_num,
               payroll_cost,
               salary_paid_subject,
               agreement_compensation,
               actual_agreement_compensation,
               performance,
               actual_performance,
               actual_paySum
        FROM wage_cost_detail
        <where>
            del_flag = ${@<EMAIL>()}
             <if test="dto.yearMonthDateStart != null and dto.yearMonthDateStart != ''">
                AND year_month_date >= #{dto.yearMonthDateStart}
            </if>
            <if test="dto.yearMonthDateEnd != null and dto.yearMonthDateEnd != ''">
                AND #{dto.yearMonthDateEnd} >= year_month_date
            </if>
            <if test="dto.paySubject != null and dto.paySubject != ''">
                AND
                        salary_paid_subject = #{dto.paySubject}
            </if>
            <if test="dto.ids != null and dto.ids.size() > 0">
                AND
                        CONCAT(year_month_date, current_dept_id, salary_paid_subject) IN
                <foreach collection="dto.ids" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.deptIds != null and dto.deptIds.size() > 0">
                AND secondary_dept_id IN
                <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="dto.conditionDeptIdList != null and dto.conditionDeptIdList.size() > 0">
               AND (
                    primary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR secondary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR tertiary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
            </if>
        </where>
        ORDER BY year_month_date DESC
    </select>

  <select id="getSalaryPaidSubjects" resultType="java.lang.String">
      SELECT DISTINCT salary_paid_subject
      FROM wage_cost_detail
      WHERE salary_paid_subject != ''
    </select>
</mapper>
