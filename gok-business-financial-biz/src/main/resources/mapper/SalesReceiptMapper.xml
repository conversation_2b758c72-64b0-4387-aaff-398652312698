<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.SalesReceiptMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.SalesReceipt">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="projectNo" column="project_no" jdbcType="VARCHAR"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR"/>
        <result property="projectStatus" column="project_status" jdbcType="VARCHAR"/>
        <result property="contractId" column="contract_id" jdbcType="BIGINT"/>
        <result property="contractName" column="contract_name" jdbcType="VARCHAR"/>
        <result property="contractCode" column="contract_code" jdbcType="VARCHAR"/>
        <result property="signingDate" column="signing_date" jdbcType="VARCHAR"/>
        <result property="contractMoney" column="contract_money" jdbcType="DECIMAL"/>
        <result property="accumulatedAmount" column="accumulated_amount" jdbcType="DECIMAL"/>
        <result property="collectionRatio" column="collection_ratio" jdbcType="VARCHAR"/>
        <result property="currentPaymentMoney" column="current_payment_money" jdbcType="DECIMAL"/>
        <result property="currentPaymentName" column="current_payment_name" jdbcType="VARCHAR"/>
        <result property="collectionDelayDays" column="collection_delay_days" jdbcType="VARCHAR"/>
        <result property="expectedDate" column="expected_date" jdbcType="VARCHAR"/>
        <result property="warningLevel" column="warning_level" jdbcType="VARCHAR"/>
        <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
        <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
        <result property="salesmanUserId" column="salesman_user_id" jdbcType="BIGINT"/>
        <result property="salesmanUserName" column="salesman_user_name" jdbcType="VARCHAR"/>
        <result property="managerUserId" column="manager_user_id" jdbcType="BIGINT"/>
        <result property="managerUserName" column="manager_user_name" jdbcType="VARCHAR"/>
        <result property="headUserId" column="head_user_id" jdbcType="BIGINT"/>
        <result property="headUserName" column="head_user_name" jdbcType="VARCHAR"/>
        <result property="commissionerUserId" column="commissioner_user_id" jdbcType="BIGINT"/>
        <result property="commissionerUserName" column="commissioner_user_name" jdbcType="VARCHAR"/>
        <result property="attributableSubject" column="attributable_subject" jdbcType="VARCHAR"/>
        <result property="firstDepartmentId" column="first_department_id" jdbcType="BIGINT"/>
        <result property="firstDepartment" column="first_department" jdbcType="VARCHAR"/>
        <result property="secondDepartmentId" column="second_department_id" jdbcType="BIGINT"/>
        <result property="secondDepartment" column="second_department" jdbcType="VARCHAR"/>
        <result property="estimateCurrentPaymentMoney" column="estimate_current_payment_money" jdbcType="DECIMAL"/>
        <result property="budgetMarking" column="budget_marking" jdbcType="VARCHAR"/>
        <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
        <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
    </resultMap>

    <select id="querySalesReceiptPage" resultType="com.gok.pboot.financial.db.entity.SalesReceipt">
        SELECT
        ID,
        project_id,
        customer_id,
        project_no,
        project_name,
        contract_code,
        project_status,
        contract_money,
        accumulated_amount,
        collection_ratio,
        payment_id,
        current_payment_money,
        current_payment_name,
        payment_status,
        payment_condition,
        acceptance_date,
        payment_date,
        collection_delay_days,
        expected_date,
        warning_level,
        salesman_user_name,
        customer_name,
        signing_date,
        attributable_subject,
        first_department,
        second_department,
        contract_id,
        contract_Name,
        salesman_user_id,
        salesman_user_name,
        manager_user_id,
        manager_user_name,
        actual_payment_amount,
        payment_difference
        FROM sales_receipt
        <where>
            del_flag=${@<EMAIL>()}
            AND (contract_money IS NOT NULL AND contract_money != 0 AND contract_money != 0.0 AND contract_money != 0.00)
            AND contract_code IS NOT NULL
            <if test="dto.projectNameOrNo!=null and dto.projectNameOrNo!=''">
                AND
                (project_name like CONCAT('%',#{dto.projectNameOrNo},'%')
                OR
                project_no like CONCAT('%',#{dto.projectNameOrNo},'%'))
            </if>
            <if test="dto.contractNameOrNo!=null and dto.contractNameOrNo!=''">
                AND
                (contract_name like CONCAT('%',#{dto.contractNameOrNo},'%')
                OR
                contract_code like CONCAT('%',#{dto.contractNameOrNo},'%'))
            </if>
            <if test="dto.projectStatus!=null and dto.projectStatus!=''">
                AND
                project_status=#{dto.projectStatus}
            </if>
            <if test="dto.contractMoneyLow!=null">
                AND
                contract_money &gt;= #{dto.contractMoneyLow}
            </if>
            <if test="dto.contractMoneyUp!=null">
                AND
                #{dto.contractMoneyUp} &gt;= contract_money
            </if>
            <if test="dto.currentPaymentName!=null and dto.currentPaymentName!=''">
                AND
                current_payment_name=#{dto.currentPaymentName}
            </if>
            <if test="dto.warningLevel!=null">
                AND
                warning_level like #{dto.warningLevel}
            </if>
            <if test="dto.salesmanOrManageUserName!=null and dto.salesmanOrManageUserName!=''">
                AND (salesman_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                    or manager_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                )
            </if>
            <if test="dto.authority">
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    and( first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.authUserIdList != null and dto.authUserIdList.size() > 0">
                    and (salesman_user_id IN
                    <foreach collection="dto.authUserIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR manager_user_id IN
                    <foreach collection="dto.authUserIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="dto.attributableSubject != null and dto.attributableSubject != ''">
                AND attributable_subject = #{dto.attributableSubject}
            </if>
            <if test="dto.deptIdList != null and dto.deptIdList.size() > 0">
                AND (first_department_id IN
                <foreach collection="dto.deptIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                OR second_department_id IN
                <foreach collection="dto.deptIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="dto.expectedStartDate != null and dto.expectedStartDate != ''">
                AND expected_date &gt;=  #{dto.expectedStartDate}
            </if>
            <if test="dto.expectedEndDate != null and dto.expectedEndDate != ''">
                AND expected_date  &lt;=  #{dto.expectedEndDate}
            </if>
            <if test="dto.paymentStatus != null and dto.paymentStatus != ''">
                AND payment_status = #{dto.paymentStatus}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND customer_name  like CONCAT('%',#{dto.customerName},'%')
            </if>
        </where>
        ORDER BY payment_status asc ,expected_date asc, contract_code desc
    </select>

    <select id="currentPaymentNameBox" resultType="java.lang.String">
        SELECT DISTINCT current_payment_name
        FROM sales_receipt
        <where>
            del_flag=${@<EMAIL>()}
            <if test="dto.authority">
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    and ( first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.authUserIdList != null and dto.authUserIdList.size() > 0">
                    and (salesman_user_id IN
                    <foreach collection="dto.authUserIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR manager_user_id IN
                    <foreach collection="dto.authUserIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
        </where>
    </select>

    <select id="projectStatusBox" resultType="java.lang.String">
        SELECT DISTINCT project_status
        FROM sales_receipt
    </select>

    <select id="querySalesReceiptList" resultType="com.gok.pboot.financial.db.entity.SalesReceipt">
       SELECT
        ID,
        project_id,
        customer_id,
        project_no,
        project_name,
        contract_code,
        project_status,
        contract_money,
        accumulated_amount,
        collection_ratio,
        payment_id,
        current_payment_money,
        current_payment_name,
        payment_status,
        payment_condition,
        acceptance_date,
        payment_date,
        collection_delay_days,
        expected_date,
        warning_level,
        salesman_user_name,
        customer_name,
        signing_date,
        attributable_subject,
        first_department,
        second_department,
        contract_id,
        contract_Name,
        salesman_user_id,
        salesman_user_name,
        manager_user_id,
        manager_user_name,
        actual_payment_amount,
        payment_difference
        FROM sales_receipt
        <where>
            del_flag=${@<EMAIL>()}
             <if test="dto.ids != null and dto.ids.size() > 0">
                    AND id IN
                    <foreach collection="dto.ids" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            <if test="dto.projectNameOrNo!=null and dto.projectNameOrNo!=''">
                AND
                (project_name like CONCAT('%',#{dto.projectNameOrNo},'%')
                OR
                project_no like CONCAT('%',#{dto.projectNameOrNo},'%'))
            </if>
            <if test="dto.projectStatus!=null and dto.projectStatus!=''">
                AND
                project_status=#{dto.projectStatus}
            </if>
            <if test="dto.contractMoneyLow!=null">
                AND
                contract_money &gt;= #{dto.contractMoneyLow}
            </if>
            <if test="dto.contractMoneyUp!=null">
                AND
                #{dto.contractMoneyUp} &gt;= contract_money
            </if>
            <if test="dto.currentPaymentName!=null and dto.currentPaymentName!=''">
                AND
                current_payment_name=#{dto.currentPaymentName}
            </if>
            <if test="dto.warningLevel!=null">
                AND
                warning_level LIKE #{dto.warningLevel}
            </if>
            <if test="dto.salesmanOrManageUserName!=null and dto.salesmanOrManageUserName!=''">
                AND (salesman_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                    or manager_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                )
            </if>
            <if test="dto.authority">
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    and ( first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.authUserIdList != null and dto.authUserIdList.size() > 0">
                    and (salesman_user_id IN
                    <foreach collection="dto.authUserIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR manager_user_id IN
                    <foreach collection="dto.authUserIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="dto.attributableSubject != null and dto.attributableSubject != ''">
                AND attributable_subject = #{dto.attributableSubject}
            </if>
            <if test="dto.deptIdList != null and dto.deptIdList.size() > 0">
                AND (first_department_id IN
                <foreach collection="dto.deptIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                OR second_department_id IN
                <foreach collection="dto.deptIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>)
            </if>
             <if test="dto.expectedStartDate != null and dto.expectedStartDate != ''">
                AND expected_date &gt;=  #{dto.expectedStartDate}
            </if>
            <if test="dto.expectedEndDate != null and dto.expectedEndDate != ''">
                AND expected_date  &lt;=  #{dto.expectedEndDate}
            </if>
            <if test="dto.paymentStatus != null and dto.paymentStatus != ''">
                AND payment_status = #{dto.paymentStatus}
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND customer_name  like CONCAT('%',#{dto.customerName},'%')
            </if>
        </where>
        ORDER BY payment_status desc ,expected_date asc, contract_code desc
    </select>


    <select id="selectVoById" resultType="com.gok.pboot.financial.vo.SalesReceiptInDetailVO">
        SELECT
            project_no,
            project_name,
            project_status,
            customer_name,
            contract_code,
            signing_date,
            contract_money,
            attributable_subject,
            first_department,
            salesman_user_name,
            manager_user_name
        FROM
            sales_receipt
        WHERE
            id =#{id}
        AND del_flag=${@<EMAIL>()}
    </select>
    <select id="queryPushVoV1" resultType="com.gok.pboot.financial.vo.SalesReceiptPushVO">
        SELECT
            sr.id,
            sr.salesman_user_id,
            sr.salesman_user_name,
            sr.contract_id,
            sr.contract_code,
            srd.payment_name,
            srd.expected_date,
            sr.first_department_id
        FROM
            sales_receipt AS sr
                RIGHT JOIN sales_receipt_details AS srd ON srd.main_id = sr.contract_id
        WHERE
            sr.del_flag = ${@<EMAIL>()}
          AND srd.del_flag = ${@<EMAIL>()}
          AND srd.payment_status =1
          AND srd.expected_date IS NOT NULL
          AND sr.contract_money is not NULL
	      AND sr.contract_money != 0
          and srd.expected_date &gt;= DATE_FORMAT('2024-01-01', '%Y-%m-%d')
          GROUP BY srd.id
    </select>

    <select id="queryPage" resultType="com.gok.pboot.financial.vo.SalesReceiptVO">
         SELECT
        t1.ID,
        t1.project_id,
        t1.customer_id,
        t1.project_no,
        t1.project_name,
        t1.contract_code,
        t1.project_status,
        t1.contract_money,
        t1.accumulated_amount,
        t1.collection_ratio,
        t1.payment_id,
        t1.current_payment_money,
        t1.current_payment_name,
        t1.payment_status,
        t1.payment_condition,
        t1.acceptance_date,
        t1.payment_date,
        t1.payment_proportion,
        t1.collection_delay_days,
        t1.expected_date,
        t1.warning_level,
        t1.salesman_user_name,
        t1.customer_name,
        t1.signing_date,
        t1.attributable_subject,
        t1.first_department,
        t1.second_department,
        t1.contract_id,
        t1.contract_Name,
        t1.salesman_user_id,
        t1.salesman_user_name,
        t1.manager_user_id,
        t1.manager_user_name,
        t1.actual_payment_amount,
        t1.payment_difference,
        t1.milestones,
        t1.expected_complete_date,
        t1.actual_complete_date,
        t1.collect_days,
        t1.target_payment_date,
        t1.delivery_method,
        t1.payment_method,
        t1.customer_payment_window_period,
        t1.expected_payment_date,
        t1.actual_collection_time,
        t1.estimate_current_payment_money,
        t1.budget_marking,
        t1.customer_market,
        t2.customer_payment_process,
        t2.current_approval_process,
        t2.approval_progress,
        t2.update_date,
        t2.progress_description,
        t2.next_step_work,
        t2.required_materials,
        t2.payment_risk,
        CASE
        WHEN DATEDIFF(CURDATE(),t2.update_date) &lt; 7 THEN '已更新'
		WHEN DATEDIFF(CURDATE(),t2.update_date) &gt;= 7 THEN '未更新'
		ELSE '未维护'
        END AS taskStatus
        FROM sales_receipt t1
        left join sales_receipt_payment_task t2 on t1.id = t2.sales_receipt_id
        <where>
            t1.del_flag=${@<EMAIL>()}
            AND (t1.contract_money IS NOT NULL AND t1.contract_money != 0 AND t1.contract_money != 0.0 AND t1.contract_money != 0.00)
            AND t1.contract_code IS NOT NULL
            <if test="dto.projectNameOrNo!=null and dto.projectNameOrNo!=''">
                AND
                (t1.project_name like CONCAT('%',#{dto.projectNameOrNo},'%')
                OR
                t1.project_no like CONCAT('%',#{dto.projectNameOrNo},'%'))
            </if>
            <if test="dto.contractNameOrNo!=null and dto.contractNameOrNo!=''">
                AND
                (t1.contract_name like CONCAT('%',#{dto.contractNameOrNo},'%')
                OR
                t1.contract_code like CONCAT('%',#{dto.contractNameOrNo},'%'))
            </if>
            <if test="dto.projectStatus!=null and dto.projectStatus!=''">
                AND
                t1.project_status=#{dto.projectStatus}
            </if>
            <if test="dto.contractMoneyLow!=null">
                AND
                t1.contract_money &gt;= #{dto.contractMoneyLow}
            </if>
            <if test="dto.contractMoneyUp!=null">
                AND
                #{dto.contractMoneyUp} &gt;= t1.contract_money
            </if>
            <if test="dto.currentPaymentName!=null and dto.currentPaymentName!=''">
                AND
                t1.current_payment_name=#{dto.currentPaymentName}
            </if>
            <if test="dto.currentPaymentNameList != null and dto.currentPaymentNameList.size() > 0">
                AND t1.current_payment_name IN
                <foreach collection="dto.currentPaymentNameList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="dto.warningLevel!=null">
                AND
                t1.warning_level like #{dto.warningLevel}
            </if>
            <if test="dto.salesmanOrManageUserName!=null and dto.salesmanOrManageUserName!=''">
                AND (t1.salesman_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                    or t1.manager_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                )
            </if>
            <if test="dto.authority">
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    and( t1.first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR t1.second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.authUserIdList != null and dto.authUserIdList.size() > 0">
                    and (t1.salesman_user_id IN
                    <foreach collection="dto.authUserIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR t1.manager_user_id IN
                    <foreach collection="dto.authUserIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="dto.attributableSubject != null and dto.attributableSubject != ''">
                AND t1.attributable_subject = #{dto.attributableSubject}
            </if>
            <if test="dto.deptIdList != null and dto.deptIdList.size() > 0">
                AND (t1.first_department_id IN
                <foreach collection="dto.deptIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                OR t1.second_department_id IN
                <foreach collection="dto.deptIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="dto.expectedStartDate != null and dto.expectedStartDate != ''">
                AND t1.expected_payment_date &gt;=  #{dto.expectedStartDate}
            </if>
            <if test="dto.expectedEndDate != null and dto.expectedEndDate != ''">
                AND t1.expected_payment_date  &lt;=  #{dto.expectedEndDate}
            </if>
            <if test="dto.paymentStatus != null and dto.paymentStatus != ''">
               <choose>
                    <when test="dto.paymentStatus == 4">
                        AND t1.actual_complete_date is null and t1.payment_status = '0'
                    </when>
                    <when test="dto.paymentStatus == 0">
                        AND ((t1.actual_complete_date is not null and t1.payment_status = '0') OR t1.payment_status = '1')
                    </when>
                    <otherwise>
                        AND t1.payment_status = #{dto.paymentStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.paymentStatusList != null and dto.paymentStatusList.size() > 0">
                AND t1.payment_status IN
                <foreach collection="dto.paymentStatusList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND t1.customer_name  like CONCAT('%',#{dto.customerName},'%')
            </if>
            <if test="dto.overdueStatus != null and dto.overdueStatus != '' and dto.overdueStatus == 0 ">
                AND collection_delay_days = '0'
            </if>
            <if test="dto.overdueStatus != null and dto.overdueStatus != '' and dto.overdueStatus == 1 ">
                AND collection_delay_days &gt; '0'
            </if>
             <if test="dto.targetPaymentStartDate != null and dto.targetPaymentStartDate != ''">
                AND t1.target_payment_date &gt;=  #{dto.targetPaymentStartDate}
            </if>
            <if test="dto.targetPaymentEndDate != null and dto.targetPaymentEndDate != ''">
                AND t1.target_payment_date  &lt;=  #{dto.targetPaymentEndDate}
            </if>
            <if test="dto.taskStatus != null and dto.taskStatus != ''">
                <choose>
                    <when test="dto.taskStatus == 0">
                        AND t2.update_date is null
                    </when>
                    <when test="dto.taskStatus == 1">
                        AND DATEDIFF(CURDATE(), t2.update_date) &gt;= 7
                    </when>
                    <when test="dto.taskStatus == 2">
                        AND DATEDIFF(CURDATE(), t2.update_date) &lt; 7
                    </when>
                </choose>
            </if>
            <if test="dto.budgetMarking != null and dto.budgetMarking !=''">
                AND t1.budget_marking like CONCAT('%',#{dto.budgetMarking},'%')
            </if>
        </where>
        ORDER BY
                    <if test="dto.updateDateSort == 0">
                         t2.update_date ASC,
                    </if>
                    <if test="dto.updateDateSort == 1">
                         t2.update_date desc,
                    </if>
        t1.payment_date desc ,t1.collection_delay_days desc, t1.contract_code desc,t1.id desc
    </select>

    <select id="selTaskReminder" resultType="com.gok.pboot.financial.vo.SalesReceiptTaskReminderVO">
        select id,
               contract_id,
               contract_name,
               current_payment_name,
               actual_complete_date,
               target_payment_date,
               salesman_user_id,
               salesman_user_name
        from sales_receipt
        where del_flag = '0'
          and payment_status = '0'
          and actual_complete_date is not null
          and salesman_user_id is not null
          and salesman_user_name is not null
          and id not in (
                select content_id
                from push_log
                where content_type = '1')
    </select>


    <select id="queryPushVo" resultType="com.gok.pboot.financial.vo.SalesReceiptPushVO">
    SELECT
            id,
            salesman_user_id,
            salesman_user_name,
            contract_id,
            contract_code,
            contract_name,
            current_payment_name as paymentName,
            payment_status,
            target_payment_date,
            first_department_id
        FROM
            sales_receipt
        where del_flag = '0'
        and CURRENT_DATE()>target_payment_date
        and actual_complete_date is not null
        and salesman_user_id is not null
        and salesman_user_name is not null
        and id not in (
                select content_id
                from push_log
                where content_type = '0')
    </select>

    <select id="queryTaskUpdateReminderVo" resultType="com.gok.pboot.financial.vo.SalesReceiptPushVO">
        SELECT
            t1.id,
            t1.salesman_user_id,
            t1.salesman_user_name,
            t1.contract_id,
            t1.contract_code,
            t1.contract_name,
            t1.current_payment_name as paymentName,
            t1.payment_status,
            t1.target_payment_date,
            t1.first_department_id,
            DATEDIFF(CURDATE(),t1.actual_complete_date) as dateDifference
        FROM
        sales_receipt t1
        left join sales_receipt_payment_task t2 on t1.id = t2.sales_receipt_id
        where t1.del_flag = '0'
        and DATEDIFF(CURDATE(),t1.actual_complete_date) >= 7
        and DATEDIFF(CURDATE(),t2.update_date) >= 7
        and t1.payment_status in (0,1)
        and t1.actual_complete_date is not null
        and t1.salesman_user_id is not null
        and t1.salesman_user_name is not null
    </select>

    <select id="queryTaskNotUpdateReminderVo" resultType="com.gok.pboot.financial.vo.SalesReceiptPushVO">
    SELECT
            t1.id,
            t1.salesman_user_id,
            t1.salesman_user_name,
            t1.contract_id,
            t1.contract_code,
            t1.contract_name,
            t1.current_payment_name as paymentName,
            t1.payment_status,
            t1.target_payment_date,
            t1.first_department_id,
            t1.actual_complete_date
        FROM
            sales_receipt t1
        left join sales_receipt_payment_task t2 on t1.id = t2.sales_receipt_id
        where t1.del_flag = '0'
        and DATEDIFF(CURDATE(),t1.actual_complete_date) = 3
        and t1.payment_status in (0,1)
        and t2.update_date is null
        and t1.actual_complete_date is not null
        and t1.salesman_user_id is not null
        and t1.salesman_user_name is not null
        and t1.id not in (
                select content_id
                from push_log
                where content_type = '2')
    </select>

    <select id="statistics" resultType="java.util.Map">
        SELECT
        SUM(IFNULL(t1.contract_money, 0.00)) as contractMoneyTotal,
        SUM(IFNULL(t1.accumulated_amount, 0.00)) as accumulatedAmountTotal,
        SUM(IFNULL(t1.current_payment_money, 0.00)) as currentPaymentMoneyTotal,
        SUM(IFNULL(t1.actual_payment_amount, 0.00)) as actualPaymentAmountTotal,
        SUM(IFNULL(t1.payment_difference, 0.00)) as paymentDifferenceTotal,
        SUM(IFNULL(t1.estimate_current_payment_money, 0.00)) as estimateCurrentPaymentMoneyTotal
        FROM sales_receipt t1
        LEFT JOIN (
        SELECT DISTINCT t2.sales_receipt_id
        FROM sales_receipt_payment_task t2
        <where>
            <if test="dto.taskStatus != null and dto.taskStatus != ''">
                <choose>
                    <when test="dto.taskStatus == 0">
                        AND t2.update_date is null
                    </when>
                    <when test="dto.taskStatus == 1">
                        AND DATEDIFF(CURDATE(), t2.update_date) &gt;= 7
                    </when>
                    <when test="dto.taskStatus == 2">
                        AND DATEDIFF(CURDATE(), t2.update_date) &lt; 7
                    </when>
                </choose>
            </if>
        </where>
        ) t2 ON t1.id = t2.sales_receipt_id
        <where>
            t1.del_flag=${@<EMAIL>()}
            AND (t1.contract_money IS NOT NULL AND t1.contract_money != 0 AND t1.contract_money != 0.0 AND
            t1.contract_money != 0.00)
            AND t1.contract_code IS NOT NULL
            <if test="dto.projectNameOrNo!=null and dto.projectNameOrNo!=''">
                AND
                (t1.project_name like CONCAT('%',#{dto.projectNameOrNo},'%')
                OR
                t1.project_no like CONCAT('%',#{dto.projectNameOrNo},'%'))
            </if>
            <if test="dto.contractNameOrNo!=null and dto.contractNameOrNo!=''">
                AND
                (t1.contract_name like CONCAT('%',#{dto.contractNameOrNo},'%')
                OR
                t1.contract_code like CONCAT('%',#{dto.contractNameOrNo},'%'))
            </if>
            <if test="dto.projectStatus!=null and dto.projectStatus!=''">
                AND
                t1.project_status=#{dto.projectStatus}
            </if>
            <if test="dto.contractMoneyLow!=null">
                AND
                t1.contract_money &gt;= #{dto.contractMoneyLow}
            </if>
            <if test="dto.contractMoneyUp!=null">
                AND
                #{dto.contractMoneyUp} &gt;= t1.contract_money
            </if>
            <if test="dto.currentPaymentName!=null and dto.currentPaymentName!=''">
                AND
                t1.current_payment_name=#{dto.currentPaymentName}
            </if>
            <if test="dto.currentPaymentNameList != null and dto.currentPaymentNameList.size() > 0">
                AND t1.current_payment_name IN
                <foreach collection="dto.currentPaymentNameList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="dto.warningLevel!=null">
                AND
                t1.warning_level like #{dto.warningLevel}
            </if>
            <if test="dto.salesmanOrManageUserName!=null and dto.salesmanOrManageUserName!=''">
                AND (t1.salesman_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                    or t1.manager_user_name LIKE CONCAT('%', #{dto.salesmanOrManageUserName},'%')
                )
            </if>
            <if test="dto.authority">
                <if test="dto.authDeptIdList != null and dto.authDeptIdList.size() > 0">
                    and( t1.first_department_id IN
                    <foreach collection="dto.authDeptIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR t1.second_department_id IN
                    <foreach collection="dto.authDeptIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.authUserIdList != null and dto.authUserIdList.size() > 0">
                    and (t1.salesman_user_id IN
                    <foreach collection="dto.authUserIdList" item="item" index="index" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR t1.manager_user_id IN
                    <foreach collection="dto.authUserIdList" separator="," item="item" open="(" close=")">
                        #{item}
                    </foreach>
                    )
                </if>
            </if>
            <if test="dto.attributableSubject != null and dto.attributableSubject != ''">
                AND t1.attributable_subject = #{dto.attributableSubject}
            </if>
            <if test="dto.deptIdList != null and dto.deptIdList.size() > 0">
                AND (t1.first_department_id IN
                <foreach collection="dto.deptIdList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
                OR t1.second_department_id IN
                <foreach collection="dto.deptIdList" separator="," item="item" open="(" close=")">
                    #{item}
                </foreach>)
            </if>
            <if test="dto.expectedStartDate != null and dto.expectedStartDate != ''">
                AND t1.expected_payment_date &gt;=  #{dto.expectedStartDate}
            </if>
            <if test="dto.expectedEndDate != null and dto.expectedEndDate != ''">
                AND t1.expected_payment_date  &lt;=  #{dto.expectedEndDate}
            </if>
            <if test="dto.paymentStatus != null and dto.paymentStatus != ''">
               <choose>
                    <when test="dto.paymentStatus == 4">
                        AND t1.actual_complete_date is null and t1.payment_status = '0'
                    </when>
                    <when test="dto.paymentStatus == 0">
                        AND ((t1.actual_complete_date is not null and t1.payment_status = '0') OR t1.payment_status = '1')
                    </when>
                    <otherwise>
                        AND t1.payment_status = #{dto.paymentStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="dto.paymentStatusList != null and dto.paymentStatusList.size() > 0">
                AND t1.payment_status IN
                <foreach collection="dto.paymentStatusList" separator="," open="(" close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <if test="dto.customerName != null and dto.customerName != ''">
                AND t1.customer_name  like CONCAT('%',#{dto.customerName},'%')
            </if>
            <if test="dto.overdueStatus != null and dto.overdueStatus != '' and dto.overdueStatus == 0 ">
                AND collection_delay_days = '0'
            </if>
            <if test="dto.overdueStatus != null and dto.overdueStatus != '' and dto.overdueStatus == 1 ">
                AND collection_delay_days &gt; '0'
            </if>
             <if test="dto.targetPaymentStartDate != null and dto.targetPaymentStartDate != ''">
                AND t1.target_payment_date &gt;=  #{dto.targetPaymentStartDate}
            </if>
            <if test="dto.targetPaymentEndDate != null and dto.targetPaymentEndDate != ''">
                AND t1.target_payment_date  &lt;=  #{dto.targetPaymentEndDate}
            </if>

            <if test="dto.budgetMarking != null and dto.budgetMarking !=''">
                AND t1.budget_marking like CONCAT('%',#{dto.budgetMarking},'%')
            </if>
        </where>
    </select>
</mapper>
