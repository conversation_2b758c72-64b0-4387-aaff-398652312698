<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.AccountingAccountMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.AccountingAccount">
    <!--@mbg.generated-->
    <!--@Table accounting_account-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_code" jdbcType="VARCHAR" property="accountCode" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="mnemonic_words" jdbcType="VARCHAR" property="mnemonicWords" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="balance_direction" jdbcType="BOOLEAN" property="balanceDirection" />
    <result column="accounting_system_id" jdbcType="BIGINT" property="accountingSystemId" />
    <result column="account_type_id" jdbcType="BIGINT" property="accountTypeId" />
    <result column="cash_classification" jdbcType="BOOLEAN" property="cashClassification" />
    <result column="account_page_format" jdbcType="BOOLEAN" property="accountPageFormat" />
    <result column="enable_status" jdbcType="BOOLEAN" property="enableStatus" />
    <result column="suspension_date" jdbcType="VARCHAR" property="suspensionDate" />
    <result column="auxiliary_calculate_items" jdbcType="VARCHAR" property="auxiliaryCalculateItems" />
    <result column="customize_calculate_items" jdbcType="VARCHAR" property="customizeCalculateItems" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, account_code, account_name, mnemonic_words, level, parent_id, balance_direction, accounting_system_id,
    account_type_id, cash_classification, account_page_format, enable_status, suspension_date, 
    auxiliary_calculate_items, customize_calculate_items, remarks, create_by, update_by, 
    create_time, update_time, del_flag, tenant_id
  </sql>

    <select id="findPage" resultType="com.gok.pboot.financial.vo.AccountingAccountPageVO">
        select a.id,
               a.account_code,
               a.account_name,
               a.mnemonic_words,
               a.level,
               a.parent_id,
               a.balance_direction,
               a.accounting_system_id,
               b.system_name as accountingSystem,
               a.account_type_id,
               c.type_name as accountType,
               a.cash_classification,
               a.account_page_format,
               a.enable_status,
               a.suspension_date,
               a.auxiliary_calculate_items,
               a.customize_calculate_items
        from accounting_account a
        left join accounting_system b on a.accounting_system_id = b.id
        left join account_type c on a.account_type_id = c.id
                where a.del_flag = '0'
        <if test="query.accountTypeId != null">
            and a.account_type_id = #{query.accountTypeId}
        </if>
        <if test="query.accountingSystemId != null">
            and a.accounting_system_id = #{query.accountingSystemId}
        </if>
        <if test="query.accountCode != null and query.accountCode != ''">
            and (a.account_code like CONCAT('%'
              ,#{query.accountCode}
              ,'%')
             or a.account_name like CONCAT('%'
              ,#{query.accountCode}
              ,'%')
             or a.mnemonic_words like CONCAT('%'
              ,#{query.accountCode}
              ,'%'))
        </if>
        <if test="query.level != null">
            and a.level = #{query.level}
        </if>
        <if test="query.balanceDirection != null">
            and a.balance_direction = #{query.balanceDirection}
        </if>
        <if test="query.cashClassification != null">
            and a.cash_classification = #{query.cashClassification}
        </if>
        <if test="query.enableStatus != null">
            and a.enable_status = #{query.enableStatus}
        </if>
        <if test="query.idList != null and query.idList.size() != 0">
            and a.id in
            <foreach collection="query.idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        order by a.account_code
    </select>

  <select id="selById" resultType="com.gok.pboot.financial.vo.AccountingAccountVO">
    select a.id,
               a.account_code,
               a.account_name,
               a.mnemonic_words,
               a.level,
               a.parent_id,
               a.balance_direction,
               a.accounting_system_id,
               b.system_name as accountingSystem,
               a.account_type_id,
               c.type_name as accountType,
               a.cash_classification,
               a.account_page_format,
               a.enable_status,
               a.suspension_date,
               a.auxiliary_calculate_items,
               a.customize_calculate_items
        from accounting_account a
        left join accounting_system b on a.accounting_system_id = b.id
        left join account_type c on a.account_type_id = c.id
                where a.del_flag = '0'
        <if test="id != null">
            and a.id = #{id}
        </if>
  </select>

  <select id="getInfoByDto"  resultType="com.gok.pboot.financial.vo.AccountingAccountVO">
  select a.id,
               a.account_code,
               a.account_name,
               a.mnemonic_words,
               a.level,
               a.parent_id,
               a.balance_direction,
               a.accounting_system_id,
               b.system_name as accountingSystem,
               a.account_type_id,
               c.type_name as accountType,
               a.cash_classification,
               a.account_page_format,
               a.enable_status,
               a.suspension_date,
               a.auxiliary_calculate_items,
               a.customize_calculate_items
        from accounting_account a
        left join accounting_system b on a.accounting_system_id = b.id
        left join account_type c on a.account_type_id = c.id
                where a.del_flag = '0'
        <if test="code != null and code != ''">
            and a.account_code = #{code}
        </if>
  </select>

  <delete id="delByIds">
    update accounting_account
        set del_flag = 1
                where enable_status = '1'
        <if test="idList != null and idList.size() != 0">
            and id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
  </delete>
</mapper>