<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.invoice.mapper.InvoiceOutputItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.invoice.entity.domain.InvoiceOutputItem">
        <id column="id" property="id"/>
        <result column="output_id" property="outputId"/>
        <result column="invoice_plan_id" property="invoicePlanId"/>
        <result column="invoice_no" property="invoiceNo"/>
        <result column="tax_classification_code" property="taxClassificationCode"/>
        <result column="product_name" property="productName"/>
        <result column="model_spec" property="modelSpec"/>
        <result column="unit" property="unit"/>
        <result column="num" property="num"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="actual_amount_included_tax" property="actualAmountIncludedTax"/>
        <result column="actual_amount_excluding_tax" property="actualAmountExcludingTax"/>
        <result column="actual_tax_rate" property="actualTaxRate"/>
        <result column="apply_request_id" property="applyRequestId"/>
        <result column="cancel_request_id" property="cancelRequestId"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, output_id, invoice_plan_id, invoice_no, tax_classification_code, product_name, model_spec, unit, num, unit_price,
        actual_amount_included_tax, actual_amount_excluding_tax, actual_tax_rate, apply_request_id, cancel_request_id,
        create_by, update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <!-- 分页查询 -->
    <select id="findPage" resultType="com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputItemVO">
        SELECT
            ioi.id,
            ioi.output_id ,
            ioi.invoice_no ,
            ioi.invoice_plan_id,
            ino.blue_invoice_no,
            ino.red_and_blue,
            ioi.goods_service_name,
            ioi.tax_classification_code,
            ioi.product_name,
            ioi.model_spec,
            ioi.unit,
            ioi.num,
            ioi.unit_price,
            ioi.actual_amount_included_tax,
            ioi.actual_tax_rate,
            ioi.actual_amount_excluding_tax,
            ioi.apply_request_id,
            ioi.cancel_request_id,
            ino.invoice_customer_id,
            ino.invoice_customer_name,
            ino.invoice_application_id,
            ino.invoice_application,
            ino.contract_id,
            ino.contract_no,
            ino.contract_name,
            ino.contract_customer_id,
            ino.contract_customer_name,
            ino.contract_subject,
            ino.contract_settlement_type,
            ino.contract_start_date,
            ino.contract_end_date,
            ino.business_dept_id,
            ino.business_dept,
            ino.salesman_user_id,
            ino.salesman_user_name,
            ino.manager_user_id,
            ino.manager_user_name,
            ino.invoice_status,
            ino.receipt_date,
            ino.receipt_platform,
            ino.receipt_customer_id,
            ino.receipt_customer_name,
            ino.project_id,
            ino.project_no,
            ino.project_name
        FROM
            invoice_output_item AS ioi
            LEFT JOIN invoice_output AS ino on ioi.output_id = ino.id
        <where>
            ioi.del_flag = ${@<EMAIL>()}
            <if test="query.invoiceNo != null and query.invoiceNo != ''">
                AND( ioi.invoice_no LIKE CONCAT('%', #{query.invoiceNo}, '%')
                    OR ino.blue_invoice_no LIKE CONCAT('%', #{query.invoiceNo}, '%') )
            </if>
            <if test="query.requestIdOrPlanId != null and query.requestIdOrPlanId != ''">
                AND( ioi.invoice_plan_id LIKE CONCAT('%', #{query.requestIdOrPlanId}, '%')
                    OR ioi.apply_request_id LIKE CONCAT('%', #{query.requestIdOrPlanId}, '%')
                    OR ioi.cancel_request_id LIKE CONCAT('%', #{query.requestIdOrPlanId}, '%'))
            </if>
            <if test="query.projectNameOrNo != null and query.projectNameOrNo != ''">
                AND( ino.project_name LIKE CONCAT('%', #{query.projectNameOrNo}, '%')
                    OR ino.project_no LIKE CONCAT('%', #{query.projectNameOrNo}, '%'))
            </if>
            <if test="query.contractNameOrNo != null and query.contractNameOrNo != ''">
                AND ( ino.contract_name LIKE CONCAT('%',#{query.contractNameOrNo},'%')
                OR ino.contract_no LIKE CONCAT('%',#{query.contractNameOrNo},'%') )
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND( ino.contract_customer_name LIKE CONCAT('%', #{query.customerName}, '%'))
            </if>
            <if test="query.goodsServiceName != null">
                AND ioi.goods_service_name = #{query.goodsServiceName}
            </if>
            <if test="query.contractSubject != null">
                AND ino.contract_subject = #{query.contractSubject}
            </if>
            <if test="query.invoiceType != null">
                AND ino.invoice_type = #{query.invoiceType}
            </if>
            <if test="query.invoiceStatus != null">
                AND ino.invoice_status = #{query.invoiceStatus}
            </if>
            <if test="query.taxRate != null">
                AND ioi.actual_tax_rate = #{query.taxRate}
            </if>
            <if test="query.redAndBlue != null">
                AND ino.red_and_blue = #{query.redAndBlue}
            </if>
            <if test="query.businessDeptIds != null and query.businessDeptIds.size() > 0">
                AND ino.business_dept_id IN
                <foreach item="businessDeptId" collection="query.businessDeptIds" separator="," open="(" close=")" index="">
                    #{businessDeptId}
                </foreach>
            </if>
            <if test="query.outputId != null">
                AND ino.output_id = #{query.outputId}
            </if>
        </where>
        ORDER BY ioi.invoice_no DESC
    </select>

    <!-- 获取分页合计值 -->
    <select id="findPageSum" resultType="java.util.Map">
        SELECT
            SUM(ioi.actual_amount_included_tax) as totalAmountIncludedTax,
            SUM(ioi.actual_amount_excluding_tax) as totalAmountExcludingTax
        FROM
            invoice_output_item AS ioi
            LEFT JOIN invoice_output AS ino on ioi.output_id = ino.id
        <where>
            ioi.del_flag = ${@<EMAIL>()}
            <if test="query.invoiceNo != null and query.invoiceNo != ''">
                AND( ioi.invoice_no LIKE CONCAT('%', #{query.invoiceNo}, '%')
                OR ino.blue_invoice_no LIKE CONCAT('%', #{query.invoiceNo}, '%') )
            </if>
            <if test="query.requestIdOrPlanId != null and query.requestIdOrPlanId != ''">
                AND( ioi.invoice_plan_id LIKE CONCAT('%', #{query.requestIdOrPlanId}, '%')
                OR ioi.apply_request_id LIKE CONCAT('%', #{query.requestIdOrPlanId}, '%')
                OR ioi.cancel_request_id LIKE CONCAT('%', #{query.requestIdOrPlanId}, '%'))
            </if>
            <if test="query.projectNameOrNo != null and query.projectNameOrNo != ''">
                AND( ino.project_name LIKE CONCAT('%', #{query.projectNameOrNo}, '%')
                OR ino.project_no LIKE CONCAT('%', #{query.projectNameOrNo}, '%'))
            </if>
            <if test="query.contractNameOrNo != null and query.contractNameOrNo != ''">
                AND ( ino.contract_name LIKE CONCAT('%',#{query.contractNameOrNo},'%')
                OR ino.contract_no LIKE CONCAT('%',#{query.contractNameOrNo},'%') )
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND( ino.contract_customer_name LIKE CONCAT('%', #{query.customerName}, '%'))
            </if>
            <if test="query.goodsServiceName != null">
                AND ioi.goods_service_name = #{query.goodsServiceName}
            </if>
            <if test="query.contractSubject != null">
                AND ino.contract_subject = #{query.contractSubject}
            </if>
            <if test="query.invoiceType != null">
                AND ino.invoice_type = #{query.invoiceType}
            </if>
            <if test="query.invoiceStatus != null">
                AND ino.invoice_status = #{query.invoiceStatus}
            </if>
            <if test="query.taxRate != null">
                AND ioi.actual_tax_rate = #{query.taxRate}
            </if>
            <if test="query.redAndBlue != null">
                AND ino.red_and_blue = #{query.redAndBlue}
            </if>
            <if test="query.businessDeptIds != null and query.businessDeptIds.size() > 0">
                AND ino.business_dept_id IN
                <foreach item="businessDeptId" collection="query.businessDeptIds" separator="," open="(" close=")" index="">
                    #{businessDeptId}
                </foreach>
            </if>
            <if test="query.outputId != null">
                AND ino.output_id = #{query.outputId}
            </if>
        </where>
    </select>

</mapper> 