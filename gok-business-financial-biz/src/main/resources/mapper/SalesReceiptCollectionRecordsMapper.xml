<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.SalesReceiptCollectionRecordsMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.SalesReceiptCollectionRecords">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="sales_receipt_id" jdbcType="BIGINT" property="salesReceiptId" />
        <result column="expected_receipt_date" jdbcType="TIMESTAMP" property="expectedReceiptDate" />
        <result column="collection_date" jdbcType="TIMESTAMP" property="collectionDate" />
        <result column="collection_amount" jdbcType="DECIMAL" property="collectionAmount" />
        <result column="collection_method" jdbcType="BOOLEAN" property="collectionMethod" />
        <result column="collection_situation" jdbcType="VARCHAR" property="collectionSituation" />
        <result column="collection_remarks" jdbcType="VARCHAR" property="collectionRemarks" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, sales_receipt_id, expected_receipt_date, collection_date, collection_amount,
        collection_method, collection_situation, collection_remarks, remarks, create_by,
        update_by, create_time, update_time, del_flag, tenant_id
    </sql>
</mapper>