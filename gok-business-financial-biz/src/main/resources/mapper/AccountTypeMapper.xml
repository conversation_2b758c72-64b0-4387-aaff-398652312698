<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.AccountTypeMapper">
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.AccountType">
    <!--@mbg.generated-->
    <!--@Table account_type-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="accounting_system_id" jdbcType="BIGINT" property="accountingSystemId" />
    <result column="enable_status" jdbcType="BOOLEAN" property="enableStatus" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, type_code, type_name, accounting_system_id, enable_status, remarks, create_by, 
    update_by, create_time, update_time, del_flag, tenant_id
  </sql>

    <select id="findPage" resultType="com.gok.pboot.financial.vo.AccountTypePageVO">
        select a.id,
               a.type_code,
               a.type_name,
               a.accounting_system_id,
               b.system_name as accountingSystem,
               count(c.id)   as associationAccount
        from account_type a
                     left join accounting_system b on b.id = a.accounting_system_id
                     left join accounting_account c on a.id = c.account_type_id and c.del_flag ='0'
                where
                a.del_flag = '0'
        <if test="query.typeCode != null and query.typeCode != ''">
            and (a.type_code like CONCAT('%', #{query.typeCode}
                    , '%')
                    or a.type_name like CONCAT('%', #{query.typeCode}
                    , '%'))
        </if>
        <if test="query.accountingSystemId != null and query.accountingSystemId != ''">
            and a.accounting_system_id = #{query.accountingSystemId}
        </if>
        group by a.id
    </select>

    <select id="selById" resultType="com.gok.pboot.financial.vo.AccountTypeVO">
        select a.id,
               a.type_code,
               a.type_name,
               a.accounting_system_id,
               b.system_name as accountingSystem
        from account_type a
                     left join accounting_system b on b.id = a.accounting_system_id
                where
                a.del_flag = '0'
        <if test="id != null">
            and a.id = #{id}
        </if>
    </select>

    <select id="findList" resultType="com.gok.pboot.financial.vo.AccountTypeVO">
        select a.id,
               a.type_code,
               a.type_name,
               a.accounting_system_id,
               b.system_name as accountingSystem
        from account_type a
                     left join accounting_system b on b.id = a.accounting_system_id
                where
                a.del_flag = '0'
        <if test="idList != null and idList.size() != 0">
            and b.id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="selCorrelationNum" resultType="com.gok.pboot.financial.vo.AccountingSystemCorrelationVO">
        select a.id,
               count(b.id) as accountNum
        from account_type a
                     left join accounting_account b on b.account_type_id = a.id and b.del_flag = '0'
                where
                a.del_flag = '0'
        <if test="idList != null and idList.size() != 0">
            and a.id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
        group by a.id
    </select>

    <delete id="delByIds">
        update account_type
        set del_flag = 1
                where
                del_flag = '0'
        <if test="idList != null and idList.size() != 0">
            and id in
            <foreach collection="idList" close=")" open="(" item="id" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>