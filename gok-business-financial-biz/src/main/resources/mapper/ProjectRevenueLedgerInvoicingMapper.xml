<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerInvoicingMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.ProjectRevenueLedgerInvoicing">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="revenue_ledger_id" jdbcType="BIGINT" property="revenueLedgerId" />
        <result column="invoicing_date" jdbcType="VARCHAR" property="invoicingDate" />
        <result column="invoicing_number" jdbcType="VARCHAR" property="invoicingNumber" />
        <result column="invoicing_amount_including_tax" jdbcType="VARCHAR" property="invoicingAmountIncludingTax" />
        <result column="tax_amount" jdbcType="VARCHAR" property="taxAmount" />
        <result column="invoicing_amount" jdbcType="VARCHAR" property="invoicingAmount" />
        <result column="tax_rate" jdbcType="INTEGER" property="taxRate" />
        <result column="invoicing_subject" jdbcType="VARCHAR" property="invoicingSubject" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, revenue_ledger_id, invoicing_date, invoicing_number, invoicing_amount_including_tax,
        tax_amount, invoicing_amount, tax_rate, invoicing_subject, remarks, create_by, update_by,
        create_time, update_time, del_flag, tenant_id
    </sql>

</mapper>