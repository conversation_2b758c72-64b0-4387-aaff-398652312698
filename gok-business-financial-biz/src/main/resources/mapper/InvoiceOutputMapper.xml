<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.invoice.mapper.InvoiceOutputMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.invoice.entity.domain.InvoiceOutput">
        <id column="id" property="id"/>
        <result column="project_id" property="projectId"/>
        <result column="project_no" property="projectNo"/>
        <result column="project_name" property="projectName"/>
        <result column="invoice_customer_id" property="invoiceCustomerId"/>
        <result column="invoice_customer_name" property="invoiceCustomerName"/>
        <result column="goods_service_name" property="goodsServiceName"/>
        <result column="plan_amount_included_tax" property="planAmountIncludedTax"/>
        <result column="plan_amount_excluding_tax" property="planAmountExcludingTax"/>
        <result column="actual_amount_included_tax" property="actualAmountIncludedTax"/>
        <result column="actual_amount_excluding_tax" property="actualAmountExcludingTax"/>
        <result column="plan_tax_rate" property="planTaxRate"/>
        <result column="actual_tax_rate" property="actualTaxRate"/>
        <result column="invoice_no" property="invoiceNo"/>
        <result column="blue_invoice_no" property="blueInvoiceNo"/>
        <result column="red_and_blue" property="redAndBlue"/>
        <result column="invoice_type" property="invoiceType"/>
        <result column="invoice_status" property="invoiceStatus"/>
        <result column="invoice_date" property="invoiceDate"/>
        <result column="invoice_remark" property="invoiceRemark"/>
        <result column="invoice_application_id" property="invoiceApplicationId"/>
        <result column="invoice_application" property="invoiceApplication"/>
        <result column="apply_request_id" property="applyRequestId"/>
        <result column="cancel_request_id" property="cancelRequestId"/>
        <result column="invoice_plan_id" property="invoicePlanId"/>
        <result column="contract_id" property="contractId"/>
        <result column="contract_no" property="contractNo"/>
        <result column="contract_name" property="contractName"/>
        <result column="contract_customer_id" property="contractCustomerId"/>
        <result column="contract_customer_name" property="contractCustomerName"/>
        <result column="contract_subject" property="contractSubject"/>
        <result column="contract_settlement_type" property="contractSettlementType"/>
        <result column="contract_start_date" property="contractStartDate"/>
        <result column="contract_end_date" property="contractEndDate"/>
        <result column="business_dept_id" property="businessDeptId"/>
        <result column="business_dept" property="businessDept"/>
        <result column="salesman_user_id" property="salesmanUserId"/>
        <result column="salesman_user_name" property="salesmanUserName"/>
        <result column="manager_user_id" property="managerUserId"/>
        <result column="manager_user_name" property="managerUserName"/>
        <result column="receipt_date" property="receiptDate"/>
        <result column="receipt_customer_id" property="receiptCustomerId"/>
        <result column="receipt_customer_name" property="receiptCustomerName"/>
        <result column="receipt_platform" property="receiptPlatform"/>
        <result column="attachment" property="attachment"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, project_no, project_name, invoice_customer_id, invoice_customer_name,
        goods_service_name, plan_amount_included_tax, plan_amount_excluding_tax,
        actual_amount_included_tax, actual_amount_excluding_tax, plan_tax_rate, actual_tax_rate,
        invoice_no, blue_invoice_no, red_and_blue, invoice_type, invoice_status, invoice_date,
        invoice_remark, invoice_application_id, invoice_application, apply_request_id,
        cancel_request_id, invoice_plan_id, contract_id, contract_no, contract_name,
        contract_customer_id, contract_customer_name, contract_subject, contract_settlement_type,
        contract_start_date, contract_end_date, business_dept_id, business_dept,
        salesman_user_id, salesman_user_name, manager_user_id, manager_user_name,
        receipt_date, receipt_customer_id, receipt_customer_name, receipt_platform, attachment,
        create_by, update_by, create_time, update_time, del_flag, tenant_id
    </sql>

    <select id="findPage" resultType="com.gok.pboot.financial.invoice.entity.vo.InvoiceOutputVO">
        SELECT
            *
        FROM
            invoice_output AS ino
        WHERE
            ino.del_flag = ${@<EMAIL>()}
        <if test="query.goodsServiceName != null">
            AND ino.goods_service_name = #{query.goodsServiceName}
        </if>
        <if test="query.invoicePlanId != null">
            AND ino.invoice_plan_id LIKE CONCAT('%',#{query.invoicePlanId},'%')
        </if>
        <if test="query.contractSubject != null">
            AND ino.contract_subject = #{query.contractSubject}
        </if>
        <if test="query.invoiceType != null">
            AND ino.invoice_type = #{query.invoiceType}
        </if>
        <if test="query.customerName != null and query.customerName != ''">
            AND ino.contract_customer_name LIKE CONCAT('%',#{query.customerName},'%')
        </if>
        <if test="query.invoiceStatus != null">
            AND ino.invoice_status = #{query.invoiceStatus}
        </if>
        <if test="query.projectNameOrNo != null and query.projectNameOrNo != ''">
            AND ( ino.project_name LIKE CONCAT('%',#{query.projectNameOrNo},'%') OR ino.project_no LIKE CONCAT('%',#{query.projectNameOrNo},'%') )
        </if>
        <if test="query.contractNameOrNo != null and query.contractNameOrNo != ''">
            AND ( ino.contract_name LIKE CONCAT('%',#{query.contractNameOrNo},'%') OR ino.contract_no LIKE CONCAT('%',#{query.contractNameOrNo},'%') )
        </if>
        <if test="query.taxRate != null">
            AND ( ino.plan_tax_rate = #{query.taxRate} OR ino.actual_tax_rate = #{query.taxRate} )
        </if>
        <if test="query.businessDeptIds != null and query.businessDeptIds.size() > 0">
            AND ino.business_dept_id IN
            <foreach item="businessDeptId" collection="query.businessDeptIds" separator="," open="(" close=")" index="">
                #{businessDeptId}
            </foreach>
        </if>
        <if test="query.memberName != null and query.memberName != ''">
            AND ( ino.salesman_user_name LIKE CONCAT('%',#{query.memberName},'%') OR ino.manager_user_name LIKE CONCAT('%',#{query.memberName},'%') )
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            AND ino.id IN
            <foreach item="id" collection="query.ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
        ORDER BY
            ino.invoice_no DESC
    </select>

    <select id="findPageSum" resultType="java.util.Map">
        SELECT
            SUM(ino.actual_amount_included_tax) AS actualAmountIncludedTaxSum,
            SUM(ino.actual_amount_excluding_tax) AS actualAmountExcludingTaxSum,
            SUM(ino.plan_amount_included_tax) AS planAmountIncludedTaxSum,
            SUM(ino.plan_amount_excluding_tax) AS planAmountExcludingTaxSum
        FROM
            invoice_output AS ino
        WHERE
        ino.del_flag = ${@<EMAIL>()}
        <if test="query.goodsServiceName != null">
            AND ino.goods_service_name = #{query.goodsServiceName}
        </if>
        <if test="query.invoicePlanId != null">
            AND ino.invoice_plan_id LIKE CONCAT('%',#{query.invoicePlanId},'%')
        </if>
        <if test="query.contractSubject != null">
            AND ino.contract_subject = #{query.contractSubject}
        </if>
        <if test="query.invoiceType != null">
            AND ino.invoice_type = #{query.invoiceType}
        </if>
        <if test="query.customerName != null and query.customerName != ''">
            AND ino.contract_customer_name LIKE CONCAT('%',#{query.customerName},'%')
        </if>
        <if test="query.invoiceStatus != null">
            AND ino.invoice_status = #{query.invoiceStatus}
        </if>
        <if test="query.projectNameOrNo != null and query.projectNameOrNo != ''">
            AND ( ino.project_name LIKE CONCAT('%',#{query.projectNameOrNo},'%') OR ino.project_no LIKE CONCAT('%',#{query.projectNameOrNo},'%') )
        </if>
        <if test="query.contractNameOrNo != null and query.contractNameOrNo != ''">
            AND ( ino.contract_name LIKE CONCAT('%',#{query.contractNameOrNo},'%') OR ino.contract_no LIKE CONCAT('%',#{query.contractNameOrNo},'%') )
        </if>
        <if test="query.taxRate != null">
            AND ( ino.plan_tax_rate = #{query.taxRate} OR ino.actual_tax_rate = #{query.taxRate} )
        </if>
        <if test="query.businessDeptIds != null and query.businessDeptIds.size() > 0">
            AND ino.business_dept_id IN
            <foreach item="businessDeptId" collection="query.businessDeptIds" separator="," open="(" close=")" index="">
                #{businessDeptId}
            </foreach>
        </if>
        <if test="query.memberName != null and query.memberName != ''">
            AND ( ino.salesman_user_name LIKE CONCAT('%',#{query.memberName},'%') OR ino.manager_user_name LIKE CONCAT('%',#{query.memberName},'%') )
        </if>
        <if test="query.ids != null and query.ids.size() > 0">
            AND ino.id IN
            <foreach item="id" collection="query.ids" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
    </select>

</mapper> 