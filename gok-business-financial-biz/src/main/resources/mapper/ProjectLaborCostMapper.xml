<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.ProjectLaborCostMapper">

      <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.ProjectLaborCost">
        <result column="year_month_date" jdbcType="DATE" property="yearMonthDate" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_no" jdbcType="VARCHAR" property="projectNo" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_status" jdbcType="VARCHAR" property="projectStatus" />
        <result column="income_dept_id" jdbcType="BIGINT" property="incomeDeptId" />
        <result column="income_dept" jdbcType="VARCHAR" property="incomeDept" />
        <result column="income_secondary_dept_id" jdbcType="BIGINT" property="incomeSecondaryDeptId" />
        <result column="income_secondary_dept" jdbcType="VARCHAR" property="incomeSecondaryDept" />
        <result column="personnel_secondary_dept_id" jdbcType="BIGINT" property="personnelSecondaryDeptId" />
        <result column="personnel_dept_id" jdbcType="BIGINT" property="personnelDeptId" />
        <result column="personnel_dept" jdbcType="VARCHAR" property="personnelDept" />
        <result column="social_security_cost" jdbcType="VARCHAR" property="socialSecurityCost" />
        <result column="provident_fund_cost" jdbcType="VARCHAR" property="providentFundCost" />
        <result column="payroll_cost" jdbcType="VARCHAR" property="payrollCost" />
        <result column="cost_sum" jdbcType="VARCHAR" property="costSum" />
        <result column="salary_paid_subject" jdbcType="INTEGER" property="salaryPaidSubject" />
        <result column="project_type" jdbcType="INTEGER" property="projectType" />
        <result column="personnel_type" jdbcType="INTEGER" property="personnelType" />
        <result column="push_status" jdbcType="INTEGER" property="pushStatus" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
      </resultMap>

    <update id="updatePushStatus">
        UPDATE project_labor_cost
        SET push_status =#{projectLaborCost.pushStatus,jdbcType=INTEGER},
         update_by = #{projectLaborCost.updateBy,jdbcType=VARCHAR},
         update_time = #{projectLaborCost.updateTime,jdbcType=TIMESTAMP}
        WHERE year_month_date = #{projectLaborCost.yearMonthDate,jdbcType=VARCHAR}
          and project_id = #{projectLaborCost.projectId,jdbcType=BIGINT}
          and personnel_secondary_dept_id = #{projectLaborCost.personnelSecondaryDeptId,jdbcType=BIGINT}
          and personnel_type = #{projectLaborCost.personnelType,jdbcType=INTEGER}
          and salary_paid_subject = #{projectLaborCost.salaryPaidSubject,jdbcType=VARCHAR}
    </update>

     <select id="findPage" resultMap="BaseResultMap">
        SELECT
            year_month_date,
            project_id,
            project_no,
            project_name,
            project_status,
            income_dept_id,
            income_dept,
            income_secondary_dept_id,
            personnel_secondary_dept_id,
            personnel_secondary_dept,
            personnel_dept_id,
            personnel_dept,
            IFNULL(social_security_cost, 0) AS social_security_cost,
            IFNULL(provident_fund_cost, 0) AS provident_fund_cost,
            IFNULL(payroll_cost, 0) AS payroll_cost,
            IFNULL(cost_sum, 0) AS cost_sum,
            salary_paid_subject,
            project_type,
            personnel_type,
            push_status,
            remarks,
            create_by,
            update_by,
            create_time,
            update_time,
            del_flag,
            tenant_id
        FROM
            project_labor_cost
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.yearMonthDateStart != null and query.yearMonthDateStart != ''">
                AND year_month_date >= #{query.yearMonthDateStart}
            </if>
            <if test="query.yearMonthDateEnd != null and query.yearMonthDateEnd != ''">
                AND #{query.yearMonthDateEnd} >= year_month_date
            </if>
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%') OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.projectStatus != null">
                AND project_status LIKE CONCAT('%', #{query.projectStatus}, '%')
            </if>
            <if test="query.projectType != null and query.projectType != ''">
                AND project_type = #{query.projectType}
            </if>
            <if test="query.incomeDeptIds != null and query.incomeDeptIds.size() > 0">
                AND (income_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR income_secondary_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.personnelDeptIds != null and query.personnelDeptIds.size() > 0">
                AND
                (
                personnel_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR personnel_secondary_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.personnelTypeList != null and query.personnelTypeList.size() > 0">
                AND personnel_type IN
                <foreach collection="query.personnelTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.pushStatus != null">
                AND push_status = #{query.pushStatus} AND project_type != ${@com.gok.pboot.financial.enums.ProjectTypeEnum@INTERNAL_PROJECT.getValue()}
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND salary_paid_subject LIKE #{query.attributableSubject}
            </if>
        </where>
        ORDER BY
        year_month_date DESC, project_no DESC
    </select>

    <select id="exportList" resultMap="BaseResultMap">
        SELECT
        year_month_date,
        project_id,
        project_no,
        project_name,
        project_status,
        income_dept_id,
        income_dept,
        income_secondary_dept_id,
        personnel_secondary_dept_id,
        personnel_secondary_dept,
        personnel_dept_id,
        personnel_dept,
        IFNULL(social_security_cost, 0.00) AS social_security_cost,
        IFNULL(provident_fund_cost, 0.00) AS provident_fund_cost,
        IFNULL(payroll_cost, 0.00) AS payroll_cost,
        IFNULL(cost_sum, 0.00) AS cost_sum,
        salary_paid_subject,
        project_type,
        personnel_type,
        push_status,
        remarks,
        create_by,
        update_by,
        create_time,
        update_time,
        del_flag,
        tenant_id
        FROM
        project_labor_cost
        <where>
            del_flag = ${@<EMAIL>()}
            <if test="query.ids != null and query.ids.size() > 0">
                AND  CONCAT(year_month_date,project_id,personnel_secondary_dept_id,personnel_type) IN
                <foreach collection="query.ids" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.yearMonthDateStart != null and query.yearMonthDateStart != ''">
                AND year_month_date >= #{query.yearMonthDateStart}
            </if>
            <if test="query.yearMonthDateEnd != null and query.yearMonthDateEnd != ''">
                AND #{query.yearMonthDateEnd} >= year_month_date
            </if>
            <if test="query.project != null and query.project != ''">
                AND (project_no LIKE CONCAT('%',#{query.project},'%') OR project_name LIKE CONCAT('%',#{query.project},'%'))
            </if>
            <if test="query.projectStatus != null">
                AND project_status LIKE CONCAT('%', #{query.projectStatus}, '%')
            </if>
            <if test="query.projectType != null and query.projectType != ''">
                AND project_type = #{query.projectType}
            </if>
            <if test="query.incomeDeptIds != null and query.incomeDeptIds.size() > 0">
                AND (income_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR income_secondary_dept_id IN
                <foreach collection="query.incomeDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
            </if>
            <if test="query.personnelSecondaryDept != null and query.personnelSecondaryDept.size() > 0">
                AND personnel_secondary_dept_id IN
                <foreach collection="query.personnelSecondaryDept" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="query.personnelDeptIds != null and query.personnelDeptIds.size() > 0">
                AND
                (
                personnel_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR personnel_secondary_dept_id IN
                <foreach collection="query.personnelDeptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            <if test="query.personnelTypeList != null and query.personnelTypeList.size() > 0">
                AND personnel_type IN
                <foreach collection="query.personnelTypeList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.pushStatus != null">
                AND push_status = #{query.pushStatus} AND project_type != ${@com.gok.pboot.financial.enums.ProjectTypeEnum@INTERNAL_PROJECT.getValue()}
            </if>
            <if test="query.attributableSubject != null and query.attributableSubject != ''">
                AND salary_paid_subject LIKE #{query.attributableSubject}
            </if>
        </where>
    </select>

    <select id="selList" resultType="com.gok.pboot.financial.db.entity.ProjectLaborCost">
    SELECT
        year_month_date,
        project_id,
        project_no,
        project_name,
        project_status,
        income_dept_id,
        income_dept,
        income_secondary_dept_id,
        personnel_secondary_dept_id,
        personnel_secondary_dept,
        personnel_dept_id,
        personnel_dept,
        social_security_cost,
        provident_fund_cost,
        payroll_cost,
        cost_sum,
        salary_paid_subject,
        project_type,
        personnel_type,
        push_status,
        remarks,
        create_by,
        update_by,
        create_time,
        update_time,
        del_flag,
        tenant_id
        FROM
        project_labor_cost
        where
        del_flag='0'
        and push_status != '1'
        <if test="ids != null and ids.size() > 0">
                AND  CONCAT(year_month_date,project_id,personnel_secondary_dept_id,personnel_type,salary_paid_subject) IN
                <foreach collection="ids" index="index" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="incomeDeptList" resultType="com.gok.pboot.financial.vo.CrossDeptProjectLaborCostVO">
        SELECT
            year_month_date,
            income_dept_id,
            income_dept,
            personnel_dept_id,
            personnel_dept,
            personnel_secondary_dept_id,
            cost_sum,
            project_type
        FROM
            project_labor_cost
        <where>
            del_flag = ${@<EMAIL>()}
            AND project_type = ${@com.gok.pboot.financial.enums.ProjectTypeEnum@INCOME_PROJECT.getValue()}
            <if test="query.deptId != null and query.deptId != ''">
                AND (personnel_dept_id = #{query.deptId} OR personnel_secondary_dept_id = #{query.deptId})
            </if>
            <if test="query.yearMonthDayStart != null and query.yearMonthDayStart != ''">
                AND (year_month_date >= #{query.yearMonthDayStart} AND #{query.yearMonthDayEnd} >= year_month_date)
            </if>
        </where>
        ORDER BY income_dept_id, year_month_date
    </select>

</mapper>