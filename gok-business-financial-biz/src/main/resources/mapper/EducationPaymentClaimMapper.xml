<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.EducationPaymentClaimMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.EducationPaymentClaim">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="education_payment_id" jdbcType="BIGINT" property="educationPaymentId" />
        <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId" />
        <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName" />
        <result column="key_account_manager_id" jdbcType="BIGINT" property="keyAccountManagerId" />
        <result column="key_account_manager_name" jdbcType="VARCHAR" property="keyAccountManagerName" />
        <result column="sales_executive_id" jdbcType="BIGINT" property="salesExecutiveId" />
        <result column="sales_executive_name" jdbcType="VARCHAR" property="salesExecutiveName" />
        <result column="account_manager_id" jdbcType="BIGINT" property="accountManagerId" />
        <result column="account_manager_name" jdbcType="VARCHAR" property="accountManagerName" />
        <result column="consultant_user_id" jdbcType="BIGINT" property="consultantUserId" />
        <result column="consultant_user_name" jdbcType="VARCHAR" property="consultantUserName" />
        <result column="payment_dept_id" jdbcType="BIGINT" property="paymentDeptId" />
        <result column="payment_dept" jdbcType="VARCHAR" property="paymentDept" />
        <result column="payment_secondary_dept_id" jdbcType="BIGINT" property="paymentSecondaryDeptId" />
        <result column="payment_secondary_dept" jdbcType="VARCHAR" property="paymentSecondaryDept" />
        <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
        <result column="business_line" jdbcType="VARCHAR" property="businessLine" />
        <result column="school_name" jdbcType="VARCHAR" property="schoolName" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="claimant_id" jdbcType="BIGINT" property="claimantId" />
        <result column="claimant_name" jdbcType="VARCHAR" property="claimantName" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>
    <sql id="Base_Column_List">
        id, education_payment_id, salesman_user_id, salesman_user_name, key_account_manager_id,
        key_account_manager_name, sales_executive_id, sales_executive_name, account_manager_id,
        account_manager_name, consultant_user_id, consultant_user_name, payment_dept_id,
        payment_dept, payment_secondary_dept_id, payment_secondary_dept, customer_name, business_line,
        school_name, project_name, product_name, claimant_id, claimant_name, remarks, create_by, update_by,
        create_time, update_time, del_flag, tenant_id
    </sql>

    <insert id="saveBatch">
        INSERT INTO education_payment_claim (id, education_payment_id, salesman_user_id, salesman_user_name, key_account_manager_id,
                                             key_account_manager_name, sales_executive_id, sales_executive_name, account_manager_id,
                                             account_manager_name, consultant_user_id, consultant_user_name, payment_dept_id,
                                             payment_dept, payment_secondary_dept_id, payment_secondary_dept, customer_name, business_line,
                                             school_name, project_name, product_name, claimant_id, claimant_name, remarks, create_by, update_by,
                                             create_time, update_time, del_flag, tenant_id)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            ( #{item.id}, #{item.educationPaymentId}, #{item.salesmanUserId}, #{item.salesmanUserName}, #{item.keyAccountManagerId}, #{item.keyAccountManagerName},
            #{item.salesExecutiveId}, #{item.salesExecutiveName}, #{item.accountManagerId}, #{item.accountManagerName}, #{item.consultantUserId},
            #{item.consultantUserName}, #{item.paymentDeptId}, #{item.paymentDept}, #{item.paymentSecondaryDeptId}, #{item.paymentSecondaryDept},
            #{item.customerName}, #{item.businessLine}, #{item.schoolName}, #{item.projectName}, #{item.productName}, #{item.claimantId}, #{item.claimantName},
            #{item.remarks}, #{item.createBy}, #{item.updateBy}, #{item.createTime}, #{item.updateTime}, #{item.delFlag}, #{item.tenantId}
            )
        </foreach>
    </insert>
</mapper>