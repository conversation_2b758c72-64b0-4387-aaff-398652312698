<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.ProjectRevenueLedgerDetailMapper">
    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="mainid" jdbcType="BIGINT" property="mainid" />
        <result column="request_id" jdbcType="BIGINT" property="requestId" />
        <result column="income_detail_amount_tax" jdbcType="DECIMAL" property="incomeDetailAmountTax" />
        <result column="income_detail_amount" jdbcType="DECIMAL" property="incomeDetailAmount" />
        <result column="tax_tate" jdbcType="VARCHAR" property="taxTate" />
        <result column="income_type" jdbcType="VARCHAR" property="incomeType" />
    </resultMap>
    <sql id="Base_Column_List">
      id, mainid, request_id, income_detail_amount_tax, income_detail_amount, tax_tate, income_type
    </sql>
    <select id="getList" resultType="com.gok.pboot.financial.db.entity.ProjectRevenueLedgerDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM project_revenue_ledger_detail
        <where>
            <if test="query.incomeType != null and query.incomeType != ''">
                AND income_type = #{query.incomeType}
            </if>
        </where>
    </select>
</mapper>