<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.WageCostMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.WageCost">
            <result property="yearMonthDate" column="year_month_date" jdbcType="DATE"/>
            <result property="primaryDeptId" column="primary_dept_id" jdbcType="BIGINT"/>
            <result property="primaryDept" column="primary_dept" jdbcType="VARCHAR"/>
            <result property="secondaryDeptId" column="secondary_dept_id" jdbcType="BIGINT"/>
            <result property="secondaryDept" column="secondary_dept" jdbcType="VARCHAR"/>
            <result property="tertiaryDeptId" column="tertiary_dept_id" jdbcType="BIGINT"/>
            <result property="tertiaryDept" column="tertiary_dept" jdbcType="VARCHAR"/>
            <result property="salary" column="salary" jdbcType="VARCHAR"/>
            <result property="individualIncomeTax" column="individual_income_tax" jdbcType="VARCHAR"/>
            <result property="individualSocialSecurity" column="individual_social_security" jdbcType="VARCHAR"/>
            <result property="individualProvidentFund" column="individual_provident_fund" jdbcType="VARCHAR"/>
            <result property="backPay" column="back_pay" jdbcType="VARCHAR"/>
            <result property="incomeTaxDifferenceFromLastMonth" column="income_tax_difference_from_last_month" jdbcType="VARCHAR"/>
            <result property="actualPay" column="actual_pay" jdbcType="VARCHAR"/>
            <result property="unitSocialSecurity" column="unit_social_security" jdbcType="VARCHAR"/>
            <result property="unitProvidentFund" column="unit_provident_fund" jdbcType="VARCHAR"/>
            <result property="disabledSecurityFund" column="disabled_security_fund" jdbcType="VARCHAR"/>
            <result property="payrollCost" column="payroll_cost" jdbcType="VARCHAR"/>
            <result property="peopleNum" column="people_num" jdbcType="INTEGER"/>
            <result property="pushStatus" column="push_status" jdbcType="TINYINT"/>
            <result property="salaryPaidSubject" column="salary_paid_subject" jdbcType="VARCHAR"/>
            <result property="remarks" column="remarks" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="del_flag" jdbcType="CHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="BIGINT"/>
            <result property="salaryPaidSubject" column="salary_paid_subject" jdbcType="VARCHAR"/>
    </resultMap>

    <update id="updatePushStatus">
    UPDATE wage_cost
        SET push_status =#{wageCost.pushStatus,jdbcType=INTEGER},
        update_by = #{wageCost.updateBy,jdbcType=VARCHAR},
        update_time = #{wageCost.updateTime,jdbcType=TIMESTAMP}
        WHERE year_month_date = #{wageCost.yearMonthDate,jdbcType=VARCHAR}
          and current_dept_id = #{wageCost.currentDeptId,jdbcType=BIGINT}
          and salary_paid_subject = #{wageCost.salaryPaidSubject,jdbcType=VARCHAR}

    </update>

        <select id="salaryPaidSubject" resultType="java.lang.String">
                SELECT DISTINCT salary_paid_subject
                FROM wage_cost
        </select>

        <select id="queryWageCostPage" resultType="com.gok.pboot.financial.vo.WageCostVO">
            SELECT
            CONCAT(year_month_date,current_dept_id,salary_paid_subject) as id,
            year_month_date,
            current_dept_id,
            current_dept,
            primary_dept_id,
            primary_dept,
            secondary_dept_id,
            secondary_dept,
            tertiary_dept_id,
            tertiary_dept,
            salary,
            individual_income_tax ,
            individual_social_security,
            individual_provident_fund,
            back_pay,
            income_tax_difference_from_last_month,
            actual_pay,
            unit_social_security,
            unit_provident_fund,
            disabled_security_fund,
            people_num,
            payroll_cost,
            push_status,
            salary_paid_subject,
            agreement_compensation,
            actual_agreement_compensation,
            performance,
            actual_performance,
            actual_paySum
            FROM
            wage_cost
            <where>
                wage_cost.del_flag=${@<EMAIL>()}
                <if test="dto.yearMonthDateStart != null and dto.yearMonthDateStart != ''">
                    AND year_month_date >= #{dto.yearMonthDateStart}
                </if>
                <if test="dto.yearMonthDateEnd != null and dto.yearMonthDateEnd != ''">
                    AND #{dto.yearMonthDateEnd} >= year_month_date
                </if>
                <if test="dto.paySubject!=null and dto.paySubject!=''">
                    AND
                    wage_cost.salary_paid_subject=#{dto.paySubject}
                </if>
                <if test="dto.pushStatus!=null">
                    AND
                    wage_cost.push_status=#{dto.pushStatus}
                </if>
                <if test="dto.deptIds!=null and dto.deptIds.size()>0">
                   AND (
                    wage_cost.primary_dept_id IN
                    <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR wage_cost.secondary_dept_id IN
                    <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR wage_cost.tertiary_dept_id IN
                    <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
                <if test="dto.conditionDeptIdList != null and dto.conditionDeptIdList.size() > 0">
                    AND (
                    wage_cost.primary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR wage_cost.secondary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    OR wage_cost.tertiary_dept_id IN
                    <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                    )
                </if>
            </where>
            ORDER BY year_month_date DESC
        </select>

        <select id="exportList" resultType="com.gok.pboot.financial.vo.WageCostVO">
            SELECT CONCAT(year_month_date,current_dept_id,salary_paid_subject) as id,
                year_month_date,
                primary_dept_id,
                secondary_dept_id,
                tertiary_dept_id,
                primary_dept,
                secondary_dept,
                tertiary_dept,
                salary,
                individual_income_tax ,
                individual_social_security,
                individual_provident_fund,
                back_pay,
                income_tax_difference_from_last_month,
                actual_pay,
                unit_social_security,
                unit_provident_fund,
                disabled_security_fund,
                people_num,
                payroll_cost,
                push_status,
                salary_paid_subject,
                agreement_compensation,
                actual_agreement_compensation,
                performance,
                actual_performance,
                actual_paySum
            FROM  wage_cost
            <where>
                wage_cost.del_flag=${@<EMAIL>()}
                <if test="dto.yearMonthDateStart != null and dto.yearMonthDateStart != ''">
                    AND year_month_date >= #{dto.yearMonthDateStart}
                </if>
                <if test="dto.yearMonthDateEnd != null and dto.yearMonthDateEnd != ''">
                    AND #{dto.yearMonthDateEnd} >= year_month_date
                </if>
                <if test="dto.paySubject!=null and dto.paySubject!=''">
                    AND
                    wage_cost.salary_paid_subject=#{dto.paySubject}
                </if>
                <if test="dto.pushStatus!=null">
                    AND
                    wage_cost.push_status=#{dto.pushStatus}
                </if>
                <if test="dto.ids!=null and dto.ids.size()>0">
                    AND
                    CONCAT(wage_cost.year_month_date,wage_cost.current_dept_id,wage_cost.salary_paid_subject) IN
                    <foreach collection="dto.ids" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.deptIds != null and dto.deptIds.size() > 0">
                AND (
                        wage_cost.primary_dept_id IN
                <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR wage_cost.secondary_dept_id IN
                <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR wage_cost.tertiary_dept_id IN
                <foreach collection="dto.deptIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
                </if>
                <if test="dto.conditionDeptIdList != null and dto.conditionDeptIdList.size() > 0">
                AND (
                        wage_cost.primary_dept_id IN
                <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR wage_cost.secondary_dept_id IN
                <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                OR wage_cost.tertiary_dept_id IN
                <foreach collection="dto.conditionDeptIdList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
                )
            </if>
            </where>
            ORDER BY year_month_date DESC
        </select>

        <select id="selList" resultType="com.gok.pboot.financial.db.entity.WageCost">
            SELECT
                year_month_date,
                primary_dept_id
                primary_dept,
                current_dept_id,
                current_dept,
                secondary_dept_id,
                secondary_dept,
                tertiary_dept_id,
                tertiary_dept,
                salary,
                individual_income_tax ,
                individual_social_security,
                individual_provident_fund,
                back_pay,
                income_tax_difference_from_last_month,
                actual_pay,
                unit_social_security,
                unit_provident_fund,
                disabled_security_fund,
                people_num,
                payroll_cost,
                push_status,
                salary_paid_subject,
                agreement_compensation,
                actual_agreement_compensation,
                performance,
                actual_performance,
                actual_paySum
            FROM
                wage_cost
            where
            del_flag='0'
                <if test="ids!=null and ids.size()>0">
                    AND
                    CONCAT(wage_cost.year_month_date,wage_cost.current_dept_id,wage_cost.salary_paid_subject) IN
                    <foreach collection="ids" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
        </select>

    <update id="batchUpdatePushStatus">
        <foreach collection="wageCostList" item="wageCost" separator=";">
            UPDATE wage_cost
            SET push_status =#{wageCost.pushStatus,jdbcType=INTEGER},
                update_by   = #{wageCost.updateBy,jdbcType=VARCHAR},
                update_time = #{wageCost.updateTime,jdbcType=TIMESTAMP}
            WHERE year_month_date = #{wageCost.yearMonthDate,jdbcType=VARCHAR}
              and current_dept_id = #{wageCost.currentDeptId,jdbcType=BIGINT}
              and salary_paid_subject = #{wageCost.salaryPaidSubject,jdbcType=VARCHAR}
        </foreach>
    </update>


</mapper>
