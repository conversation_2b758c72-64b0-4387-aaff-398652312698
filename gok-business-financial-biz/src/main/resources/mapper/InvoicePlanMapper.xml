<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.invoice.mapper.InvoicePlanMapper">

    <resultMap id="BaseResultMap" type="com.gok.pboot.financial.invoice.entity.domain.InvoicePlan">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_id" jdbcType="BIGINT" property="projectId" />
        <result column="project_no" jdbcType="VARCHAR" property="projectNo" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="contract_id" jdbcType="BIGINT" property="contractId" />
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
        <result column="contract_name" jdbcType="VARCHAR" property="contractName" />
        <result column="business_dept_id" jdbcType="BIGINT" property="businessDeptId" />
        <result column="business_dept" jdbcType="VARCHAR" property="businessDept" />
        <result column="salesman_user_id" jdbcType="BIGINT" property="salesmanUserId" />
        <result column="salesman_user_name" jdbcType="VARCHAR" property="salesmanUserName" />
        <result column="manager_user_id" jdbcType="BIGINT" property="managerUserId" />
        <result column="manager_user_name" jdbcType="VARCHAR" property="managerUserName" />
        <result column="contract_customer_name" jdbcType="VARCHAR" property="contractCustomerName" />
        <result column="contract_customer_id" jdbcType="BIGINT" property="contractCustomerId" />
        <result column="goods_service_name" jdbcType="INTEGER" property="goodsServiceName" />
        <result column="invoice_type" jdbcType="INTEGER" property="invoiceType" />
        <result column="plan_amount_included_tax" jdbcType="DECIMAL" property="planAmountIncludedTax" />
        <result column="plan_amount_excluding_tax" jdbcType="DECIMAL" property="planAmountExcludingTax" />
        <result column="actual_amount_included_tax" jdbcType="DECIMAL" property="actualAmountIncludedTax" />
        <result column="actual_amount_excluding_tax" jdbcType="DECIMAL" property="actualAmountExcludingTax" />
        <result column="pending_amount_included_tax" jdbcType="DECIMAL" property="pendingAmountIncludedTax" />
        <result column="pending_amount_excluding_tax" jdbcType="DECIMAL" property="pendingAmountExcludingTax" />
        <result column="plan_tax_rate" jdbcType="INTEGER" property="planTaxRate" />
        <result column="actual_tax_rate" jdbcType="INTEGER" property="actualTaxRate" />
        <result column="contract_subject" jdbcType="INTEGER" property="contractSubject" />
        <result column="contract_settlement_type" jdbcType="INTEGER" property="contractSettlementType" />
        <result column="contract_start_date" jdbcType="DATE" property="contractStartDate" />
        <result column="contract_end_date" jdbcType="DATE" property="contractEndDate" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="del_flag" jdbcType="CHAR" property="delFlag" />
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    </resultMap>

    <select id="findPage" resultType="com.gok.pboot.financial.invoice.entity.vo.InvoicePlanVO">
        SELECT
            inp.*
        FROM
            invoice_plan AS inp
        <where>
            inp.del_flag = ${@<EMAIL>()}
            <if test="query.id != null">
                AND inp.id LIKE CONCAT('%',#{query.id},'%')
            </if>
            <if test="query.goodsServiceName != null">
                AND inp.goods_service_name = #{query.goodsServiceName}
            </if>
            <if test="query.contractSubject != null">
                AND inp.contract_subject = #{query.contractSubject}
            </if>
            <if test="query.invoiceType != null">
                AND inp.invoice_type = #{query.invoiceType}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND inp.contract_customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.projectNameOrNo != null and query.projectNameOrNo != ''">
                AND ( inp.project_name LIKE CONCAT('%',#{query.projectNameOrNo},'%') OR inp.project_no LIKE CONCAT('%',#{query.projectNameOrNo},'%') )
            </if>
            <if test="query.contractNameOrNo != null and query.contractNameOrNo != ''">
                AND ( inp.contract_name LIKE CONCAT('%',#{query.contractNameOrNo},'%') OR inp.contract_no LIKE CONCAT('%',#{query.contractNameOrNo},'%') )
            </if>
            <if test="query.taxRate != null">
                AND ( inp.plan_tax_rate = #{query.taxRate} OR inp.actual_tax_rate = #{query.taxRate} )
            </if>
            <if test="query.businessDeptIds != null and query.businessDeptIds.size() > 0">
                AND inp.business_dept_id IN
                <foreach item="businessDeptId" collection="query.businessDeptIds" separator="," open="(" close=")" index="">
                    #{businessDeptId}
                </foreach>
            </if>
            <if test="query.memberName != null and query.memberName != ''">
                AND ( inp.salesman_user_name LIKE CONCAT('%',#{query.memberName},'%') OR inp.manager_user_name LIKE CONCAT('%',#{query.memberName},'%') )
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND inp.id IN
                <foreach item="id" collection="query.ids" separator="," open="(" close=")" index="">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY inp.id DESC
    </select>

    <select id="findPageSum" resultType="java.util.Map">
        SELECT
            SUM(inp.actual_amount_included_tax) AS actualAmountIncludedTaxSum,
            SUM(inp.actual_amount_excluding_tax) AS actualAmountExcludingTaxSum,
            SUM(inp.plan_amount_included_tax) AS planAmountIncludedTaxSum,
            SUM(inp.plan_amount_excluding_tax) AS planAmountExcludingTaxSum,
            SUM(inp.pending_amount_included_tax) AS pendingAmountIncludedTaxSum,
            SUM(inp.pending_amount_excluding_tax) AS pendingAmountExcludingTaxSum
        FROM
            invoice_plan AS inp
        <where>
            inp.del_flag = ${@<EMAIL>()}
            <if test="query.id != null">
                AND inp.id LIKE CONCAT('%',#{query.id},'%')
            </if>
            <if test="query.goodsServiceName != null">
                AND inp.goods_service_name = #{query.goodsServiceName}
            </if>
            <if test="query.contractSubject != null">
                AND inp.contract_subject = #{query.contractSubject}
            </if>
            <if test="query.invoiceType != null">
                AND inp.invoice_type = #{query.invoiceType}
            </if>
            <if test="query.customerName != null and query.customerName != ''">
                AND inp.contract_customer_name LIKE CONCAT('%',#{query.customerName},'%')
            </if>
            <if test="query.projectNameOrNo != null and query.projectNameOrNo != ''">
                AND ( inp.project_name LIKE CONCAT('%',#{query.projectNameOrNo},'%') OR inp.project_no LIKE CONCAT('%',#{query.projectNameOrNo},'%') )
            </if>
            <if test="query.contractNameOrNo != null and query.contractNameOrNo != ''">
                AND ( inp.contract_name LIKE CONCAT('%',#{query.contractNameOrNo},'%') OR inp.contract_no LIKE CONCAT('%',#{query.contractNameOrNo},'%') )
            </if>
            <if test="query.taxRate != null">
                AND ( inp.plan_tax_rate = #{query.taxRate} OR inp.actual_tax_rate = #{query.taxRate} )
            </if>
            <if test="query.businessDeptIds != null and query.businessDeptIds.size() > 0">
                AND inp.business_dept_id IN
                <foreach item="businessDeptId" collection="query.businessDeptIds" separator="," open="(" close=")" index="">
                    #{businessDeptId}
                </foreach>
            </if>
            <if test="query.memberName != null and query.memberName != ''">
                AND ( inp.salesman_user_name LIKE CONCAT('%',#{query.memberName},'%') OR inp.manager_user_name LIKE CONCAT('%',#{query.memberName},'%') )
            </if>
            <if test="query.ids != null and query.ids.size() > 0">
                AND inp.id IN
                <foreach item="id" collection="query.ids" separator="," open="(" close=")" index="">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

</mapper>