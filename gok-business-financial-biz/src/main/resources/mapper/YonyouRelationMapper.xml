<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gok.pboot.financial.db.mapper.YyRelationMapper">
  
  <resultMap id="BaseResultMap" type="com.gok.pboot.financial.db.entity.YyRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="relate_id" jdbcType="BIGINT" property="relateId" />
    <result column="relate_type" jdbcType="TINYINT" property="relateType" />
    <result column="yy_id" jdbcType="VARCHAR" property="yyId" />
    <result column="sync_time" jdbcType="TIMESTAMP" property="syncTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_flag" jdbcType="CHAR" property="delFlag" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, relate_id, relate_type, yy_id, sync_time, create_by, update_by, create_time, update_time, del_flag, tenant_id
  </sql>

  <!-- 根据关联ID和关联类型查询用友关联关系 -->
  <select id="selectByRelateIdAndType" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM yy_relation
    WHERE del_flag = '0'
      AND relate_id = #{relateId}
      AND relate_type = #{relateType}
    LIMIT 1
  </select>

  <!-- 根据关联ID列表和关联类型查询用友关联关系列表 -->
  <select id="selectByRelateIdsAndType" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM yy_relation
    WHERE del_flag = '0'
      AND relate_type = #{relateType}
      AND relate_id IN
      <foreach collection="relateIds" item="relateId" open="(" separator="," close=")">
        #{relateId}
      </foreach>
  </select>

  <!-- 根据用友ID查询关联关系 -->
  <select id="selectByYyId" resultMap="BaseResultMap">
    SELECT 
    <include refid="Base_Column_List" />
    FROM yy_relation
    WHERE del_flag = '0'
      AND yy_id = #{yyId}
    LIMIT 1
  </select>



</mapper>
