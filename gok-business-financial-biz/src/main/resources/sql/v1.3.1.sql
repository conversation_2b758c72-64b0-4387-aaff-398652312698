-- 用友关联表
CREATE TABLE `yy_relation` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `relate_id` bigint(20) NOT NULL COMMENT '关联ID（部门ID或人员ID或合同主体ID）',
  `relate_type` tinyint(2) NOT NULL COMMENT '关联类型 1-部门，2-人员，3-公司主体',
  `yy_id` varchar(64) NOT NULL COMMENT '用友ID',
  `sync_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '同步时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(64) DEFAULT NULL COMMENT '修改人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标记',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '所属租户',
  PRIMARY KEY (`id`),
  KEY `idx_relate_id_type` (`relate_id`,`relate_type`),
  KEY `idx_yy_id` (`yy_id`),
  KEY `idx_relate_type` (`relate_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用友关联表';
