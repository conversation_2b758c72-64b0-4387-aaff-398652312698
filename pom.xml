<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.business.cloud</groupId>
        <artifactId>gok-business-cloud</artifactId>
        <version>1.0.178</version>
    </parent>

    <artifactId>gok-business-financial</artifactId>
    <packaging>pom</packaging>
    <version>1.0.178</version>
    <description>数字财务服务</description>

    <modules>
        <module>gok-business-financial-biz</module>
        <module>gok-business-financial-api</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
            <version>1.0.37</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>gok-public</id>
            <url>https://nexus-dev.goktech.cn/repository/maven-public/</url>
        </repository>
    </repositories>
</project>

