package com.gok.base.admin.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 收款平台下拉框
 *
 * <AUTHOR>
 * @since 2023-10-08
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaymentPlatformVO {

    /**
     * 收款id
     */
    private Long id;

    /**
     * 收款名称
     */
    private String name;

    /**
     * 收款汇率/费率
     */
    private BigDecimal exchangeRate;
}
