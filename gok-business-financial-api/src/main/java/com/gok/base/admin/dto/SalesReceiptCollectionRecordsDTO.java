package com.gok.base.admin.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 新增销售收款计划催款记录
 *
 * <AUTHOR>
 * @since 2024-02-20
 */
@Data
public class SalesReceiptCollectionRecordsDTO implements Serializable {

    /**
     * 销售收款id
     */
    private Long salesReceiptId;

    /**
     * 预计收款日期
     */
    @NotNull(message = "预计收款日期不能为空")
    private LocalDate expectedReceiptDate;

    /**
     * 催收日期
     */
    @NotNull(message = "催收日期不能为空")
    private LocalDate collectionDate;

    /**
     * 催收金额
     */
    @NotNull(message = "催收金额不能为空")
    private BigDecimal collectionAmount;

    /**
     * 催款方式
     * {@link com.gok.pboot.financial.enums.CollectionMethodEnum}
     */
    @NotNull(message = "催款方式不能为空")
    private Integer collectionMethod;

    /**
     * 催款情况
     */
    @NotBlank(message = "催款情况不能为空")
    private String collectionSituation;

    /**
     * 催款备注
     */
    private String collectionRemarks;
}
