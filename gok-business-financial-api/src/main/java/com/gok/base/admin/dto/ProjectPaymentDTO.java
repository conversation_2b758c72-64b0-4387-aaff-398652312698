package com.gok.base.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 项目回款条件查询
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentDTO {

    /**
     * 当前页
     */
    private Long current;

    /**
     * 每页条数
     */
    private Long size;

    /**
     * 导出Excel/锁定/删除 所需要的id集合
     */
    private List<Long> ids;

    /**
     * id
     */
    private Long id;

    /**
     * 单据编号
     */
    private String documentNumber;

    /**
     * 收款公司
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private Integer paymentCompany;

    /**
     * 客户名称/企业名称
     */
    private String customerOrEnterprise;

    /**
     * 收款日期开始时间
     */
    private String paymentBegin;

    /**
     * 收款日期结束时间
     */
    private String paymentEnd;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 回款类型
     */
    private Integer paymentType;

    /**
     * 款项名称
     */
    private String contractPayment;

    /**
     * 款项名称
     */
    private List<String> contractPayments;

    /**
     * 回款归属一级部门与二级部门id
     */
    private List<Long> paymentDeptId;

    /**
     * 相关人员
     * 原客户经理
     */
    private String salesmanUserName;

    /**
     * 单据状态（包括认领状态、锁定状态、推送状态）
     * 全部传空字符串
     */
    private List<String> documentStatus;

    /**
     * 认领状态（0已认领，1待认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    private List<Integer> claimStatusByDocument;

    /**
     * 锁定状态（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    private List<Integer> lockStatusByDocument;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private List<Integer> pushStatusByDocument;

    /**
     * 项目状态
     * {@link com.gok.pboot.financial.enums.ProjectStatusEnum}
     */
    private String projectStatus;

    /**
     * 预算内回款
     * {@link com.gok.pboot.financial.enums.CollectionWithinBudgetEnum}
     */
    private Integer collectionWithinBudget;

    /**
     * 锁定状态（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    private Integer lockStatus;

    /**
     * 认领状态（0已认领，1待认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    private Integer claimStatus;

    /**
     * 推送状态(0待推送,1已推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private Integer pushStatus;

    /**
     * 客户端id
     */
    private Long clientId;

    /**
     * 菜单
     */
    private String menuCode;

    /**
     * 当前登录人
     */
    private Long userId;

    /**
     * 是否全部权限表示
     */
    private Boolean authority;

    /**
     * 可以查看的人员id列表
     */
    private List<Long> authUserIdList;

    /**
     * 可以查看的人员主体列表
     */
    private List<Integer> authSubjectIdList;

    /**
     * 可以查看的客户id列表
     */
    private List<Long> customerIds;

    private List<String> includeExcel;

}
