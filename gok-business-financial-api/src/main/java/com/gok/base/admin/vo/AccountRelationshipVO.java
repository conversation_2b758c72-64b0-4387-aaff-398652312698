package com.gok.base.admin.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 指标科目关系展示VO
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountRelationshipVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 科目id
     */
    private Long id;

    /**
     * 核算部门编码
     */
    private String accountingDepartmentCode;

    /**
     * 核算部门
     */
    private String accountingDepartment;

    /**
     * 部门id
     */
    private Long accountingDeptId;

    /**
     * 核算部门排序
     */
    private Integer accountingDepartmentSort;

    /**
     * 科目编码
     */
    private String code;

    /**
     * 科目名称
     */
    private String name;

    /**
     * 启用状态（0启用、1禁用）
     */
    private Integer enableStatus;

    /**
     * 会计体系ID
     */
    private Long accountingSystemId;

    /**
     * 会计体系名称
     */
    private String accountingSystem;
}
