package com.gok.base.admin.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目回款
 *
 * <AUTHOR>
 * @since 2024-02-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentBO {

    /**
     * id
     */
    private Long id;

    /**
     * 单据编号
     */
    private String documentNumber;

    /**
     * 收款公司
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private Integer paymentCompany;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    private String paymentDate;

    /**
     * 收款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private BigDecimal budgetCollectionAmount;

    /**
     * 回款备注
     */
    private String paymentNote;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 认领状态（0已认领，1待认领）
     * {@link com.gok.pboot.financial.enums.ClaimStatusEnum}
     */
    private Integer claimStatus;

    /**
     * 锁定状态（0已锁定，1待锁定）
     * {@link com.gok.pboot.financial.enums.LockStatusEnum}
     */
    private Integer lockStatus;

    /**
     * 自动锁定（0自动，1取消）
     * {@link com.gok.pboot.financial.enums.AutoLockEnum}
     */
    private Integer autoLock;

    /**
     * 推送状态(0已推送,1待推送,2推送失败)
     * {@link com.gok.pboot.financial.enums.PushStatusEnum}
     */
    private Integer pushStatus;

    /**
     * 备案人id
     */
    private Long recordManId;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 创建人id
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除标记
     */
    private String delFlag;

    /**
     * 所属租户
     */
    private Long tenantId;

    /**
     * 教育回款id列表
     */
    private String educationIds;
}
