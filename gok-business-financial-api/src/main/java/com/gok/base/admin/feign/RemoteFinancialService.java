package com.gok.base.admin.feign;

import com.gok.base.admin.common.FinancialConstants;
import com.gok.base.admin.vo.AccountRelationshipVO;
import com.gok.components.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 数字一体化财务Feign
 *
 * <AUTHOR>
 * @date 2018/6/28
 */
@FeignClient(contextId = "remoteFinancialService", value = FinancialConstants.FINANCIAL_SERVICE)
public interface RemoteFinancialService {

    /**
     * 项目回款跟踪定时修改待认领且收款日期超过三十天的数据
     *
     * @param from 是否内部调用
     * @return {@link R}
     */
    @PostMapping("/project-payment/updateLockStatus")
    R<Void> projectUpdateLockStatus(@RequestHeader(FinancialConstants.FROM) String from);

    /**
     * 教育回款跟踪定时修改待认领且收款日期超过三十天的数据
     *
     * @param from 是否内部调用
     * @return {@link R}
     */
    @PostMapping("/education-payment/updateLockStatus")
    R<String> educationUpdateLockStatus(@RequestHeader(FinancialConstants.FROM) String from);


    /**
     * 执行销售收款计划定时推送消息
     *
     * @param from 是否内部调用
     * @return success、false
     */
    @PostMapping("/sales-receipt/pushMessage")
    R<Boolean> pushMessage(@RequestHeader(FinancialConstants.FROM) String from);

    /**
     * 销售收款任务跟进提醒
     * @param from
     * @return
     */
    @PostMapping("/sales-receipt/task/reminder")
    R<String> deadLineNotify(@RequestHeader(FinancialConstants.FROM) String from);

    /**
     * 通过会计体系编码查询指标科目关系列表
     *
     * @param systemCode 会计体系编码
     * @param from 是否内部调用
     * @return {@link R}<{@link List}<{@link AccountRelationshipVO}>>
     */
    @GetMapping("/indicator-account-relationship/getBySystemCode")
    R<List<AccountRelationshipVO>> getIndicatorAccountRelationshipBySystemCode(@RequestParam("systemCode") String systemCode,
                                                                               @RequestHeader(FinancialConstants.FROM) String from);
}
