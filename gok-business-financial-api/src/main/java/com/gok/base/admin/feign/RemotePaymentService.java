package com.gok.base.admin.feign;

import com.gok.base.admin.common.FinancialConstants;
import com.gok.base.admin.dto.IdsDTO;
import com.gok.base.admin.dto.ProjectPaymentClaimDTO;
import com.gok.base.admin.dto.ProjectPaymentDTO;
import com.gok.base.admin.dto.SalesReceiptCollectionRecordsDTO;
import com.gok.base.admin.vo.PaymentPlatformVO;
import com.gok.base.admin.vo.ProjectPaymentClaimVO;
import com.gok.base.admin.vo.SalesReceiptCollectionRecordsVO;
import com.gok.components.common.constant.SecurityConstants;
import com.gok.components.common.util.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 数字一体化财务 项目回款Feign
 *
 * <AUTHOR>
 * @date 2024-02-08
 */
@FeignClient(contextId = "remotePaymentService", value = FinancialConstants.FINANCIAL_SERVICE)
public interface RemotePaymentService {

    /**
     * 收款平台列表
     *
     * @param from {@link String}
     * @return {@link R}<{@link List}<{@link PaymentPlatformVO}>>
     */
    @GetMapping("/payment-method/list")
    R<List<PaymentPlatformVO>> list(@RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 根据id更新数据
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/inner/project-payment/update")
    R<Boolean> updateInner(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO);

    /**
     * 项目回款认领
     *
     * @param projectPaymentClaimDTO {@link ProjectPaymentClaimDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/inner/project-payment/claim")
    R<Boolean> claimInner(@Valid @RequestBody ProjectPaymentClaimDTO projectPaymentClaimDTO);

    /**
     * 项目回款取消认领
     *
     * @param id 项目回款跟踪id
     * @return {@link R}<{@link Boolean}>
     */
    @PutMapping("/inner/project-payment/claim/{id}")
    R<Boolean> unClaimInner(@PathVariable("id") Long id);

    /**
     * 锁定与取消锁定
     *
     * @param projectPaymentDTO {@link ProjectPaymentDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/inner/project-payment/lock")
    R<Boolean> lock(@RequestBody ProjectPaymentDTO projectPaymentDTO);

    /**
     * 新增记录
     *
     * @param dto {@link SalesReceiptCollectionRecordsDTO}
     * @return {@link R}<{@link Boolean}>
     */
    @PostMapping("/inner/sales-receipt/save-records")
    R<Boolean> saveRecords(@RequestBody @Valid SalesReceiptCollectionRecordsDTO dto);


    /**
     * 催款记录
     *
     * @param contractId 合同id
     * @return {@link R}<{@link List}<{@link SalesReceiptCollectionRecordsVO}>>
     */
    @GetMapping("/inner/sales-receipt/salesReceiptRecordsList")
    R<List<SalesReceiptCollectionRecordsVO>> salesReceiptRecordsList(@RequestParam("contractId") Long contractId);
    /**
     * 项目回款认领信息
     *
     * @param idsDTO
     * @return {@link R}<{@link List}<{@link ProjectPaymentClaimVO}>>
     */
    @PostMapping("/project-payment/getClaim")
    R<List<ProjectPaymentClaimVO>> getClaim(@RequestBody IdsDTO idsDTO);
}
