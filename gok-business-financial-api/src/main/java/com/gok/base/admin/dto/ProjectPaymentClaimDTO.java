package com.gok.base.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 更新回款认领
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentClaimDTO {

    /**
     * 项目id
     */
    private Long id;

    // 回款信息

    /**
     * 收款公司
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @NotNull(message = "收款公司不能为空")
    private String paymentCompany;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    @NotBlank(message = "收款日期不能为空")
    private String paymentDate;

    /**
     * 收款金额
     */
    @NotNull(message = "收款金额不能为空")
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    @NotBlank(message = "收款平台不能为空")
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private BigDecimal budgetCollectionAmount;

    /**
     * 备注
     */
    private String paymentNote;

    /**
     * 资金账户
     * （原银行账户）
     */
    @NotBlank(message = "资金账户不能为空")
    private String bankAccount;

    // 认领信息

    /**
     * 回款类型
     */
    private Integer paymentType;

    /**
     * 合同id
     */
    private Long contractId;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同编码
     */
    private String contractCode;

    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPaymentId;

    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPayment;

    /**
     * 款项金额
     */
    private String paymentMoney;

    /**
     * 项目id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 客户经理id
     */
    private Long salesmanUserId;

    /**
     * 客户经理
     */
    private String salesmanUserName;

    /**
     * 保证金编号
     */
    private String marginCode;

    /**
     * 保证金类型
     */
    private Integer marginType;

    /**
     * 保证金金额
     */
    private String marginMoney;

    /**
     * 认领人id
     */
    private Long claimantId;

    /**
     * 认领人
     */
    private String claimantName;

    /**
     * 认领日期
     */
    private String claimantDate;

    /**
     * 业务板块
     */
    private Integer businessBlock;

    /**
     * 技术类型
     */
    private Integer skillType;

    // 内部调用

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String username;

    /**
     * 认领金额
     */
    private BigDecimal claimMoney;
}
