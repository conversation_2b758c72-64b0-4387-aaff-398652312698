package com.gok.base.admin.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 新增回款 / 教育汇总
 *
 * <AUTHOR>
 * @since 2023-09-26
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PaymentDTO {

    /**
     * 教育汇总id集合
     */
    private List<Long> ids;

    /**
     * 收款公司
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    @NotBlank(message = "收款公司不能为空")
    private String paymentCompany;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    @NotBlank(message = "收款日期不能为空")
    private String paymentDate;

    /**
     * 收款金额
     */
    @NotNull(message = "收款金额不能为空")
    private BigDecimal paymentAmount;

    /**
     * 收款平台
     */
    @NotBlank(message = "收款平台不能为空")
    private String paymentPlatform;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private BigDecimal budgetCollectionAmount;

    /**
     * 银行账户
     */
    @NotBlank(message = "银行账户不能为空")
    private String bankAccount;

    /**
     * 备注
     */
    private String paymentNote;

    // 内部调用

    private Long id;

    /**
     * 当前登录人id
     */
    private Long userId;

    /**
     * 当前登录人名称
     */
    private String username;

    /**
     * 单据编号
     */
    private String documentNumber;

}
