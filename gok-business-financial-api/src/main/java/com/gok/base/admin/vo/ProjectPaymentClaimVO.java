package com.gok.base.admin.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目回款跟踪编辑详情展示
 *
 * <AUTHOR>
 * @since 2023-09-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPaymentClaimVO {

    /**
     * id
     */
    private Long id;

    // 回款信息

    /**
     * 收款公司code
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private String paymentCompany;

    /**
     * 收款公司value
     * {@link com.gok.pboot.financial.enums.AttributableSubjectEnum}
     */
    private String paymentCompanyTxt;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 企业名称
     */
    private String enterpriseName;

    /**
     * 收款日期
     */
    private String paymentDate;

    /**
     * 收款金额
     */
    private String paymentAmount;

    /**
     * 收款平台code
     */
    private Integer paymentPlatform;

    /**
     * 收款平台value
     */
    private String paymentPlatformTxt;

    /**
     * 凭证编号
     */
    private String voucherNumber;

    /**
     * 预算回款金额
     */
    private String budgetCollectionAmount;

    /**
     * 备注
     */
    private String paymentNote;

    /**
     * 资金账户
     * （原银行账户）
     */
    private String bankAccount;

    // 认领信息

    /**
     * 客户经理
     */
    private String salesmanUserName;


    /**
     * 款项名称
     * （原归属合同收款明细）
     */
    private String contractPayment;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 回款一级部门
     */
    private String paymentDept;

    /**
     * 回款二级部门
     */
    private String paymentSecondaryDept;

    /**
     * 回款类型
     */
    private Integer paymentType;

    /**
     * 回款类型
     */
    private String paymentTypeText;

    /**
     * 款项金额
     */
    private String paymentMoney;

    /**
     * 保证金编号
     */
    private String marginCode;

    /**
     * 保证金类型
     */
    private Integer marginType;

    /**
     * 保证金类型
     */
    private String marginTypeText;

    /**
     * 保证金金额
     */
    private String marginMoney;

    /**
     * 归属合同收款明细id
     */
    private Long contractPaymentId;

    /**
     * 认领金额
     */
    private String claimMoney;
}
